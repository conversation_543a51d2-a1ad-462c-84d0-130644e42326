# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

#linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at https://dart.dev/lints.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
#  rules:
    # avoid_print: false  # Uncomment to disable the `avoid_print` rule
    # prefer_single_quotes: true  # Uncomment to enable the `prefer_single_quotes` rule

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options


analyzer:
  strong-mode:
    implicit-casts: false # 禁止隐式类型转换
    implicit-dynamic: false # 禁止隐式 dynamic 类型
  errors:
    # 关键错误级别规则
    unused_import: error # 未使用导入
    avoid_void_async: error # 无效的异步 void
    missing_return: error # 缺少返回值
    # 警告级别规则
    prefer_final_locals: warning # 局部变量建议 final

  plugins:
    - custom_lint

linter:
  rules:
    # 代码风格增强
    - always_use_package_imports # 相对路径导入
    - avoid_private_typedef_functions # 避免私有函数类型定义
#    - prefer_relative_imports # 强制相对路径导入 (与上一条二选一)
    - prefer_final_parameters # 方法参数建议 final
    - type_init_formals # 强制类型化初始化形式参数

    # 可维护性规则
    - no_duplicate_case_values # switch 禁止重复 case
    - no_logic_in_create_state # State 类中禁止业务逻辑
    - prefer_const_declarations # 优先 const 声明

    # 文档规范
    - public_member_api_docs # 公共 API 必须写文档注释
    - require_trailing_commas # 强制尾随逗号（增强 diff 可读性）

# 自定义代码度量规则（通过 dart_code_metrics）
dart_code_metrics:
  anti-patterns:
    - long-method:
        lines-count: 50 # 方法行数限制
    - long-parameter-list:
        parameters-count: 4 # 方法参数数量限制
  metrics:
    cyclomatic-complexity: 20 # 圈复杂度阈值
    number-of-parameters: 4 # 方法参数数量阈值
    source-lines-of-code: 50 # 单文件代码行数阈值
  metrics-exclude:
    - test/** # 排除测试文件
    - lib/**/*.g.dart # 排除生成文件