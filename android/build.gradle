buildscript {
    ext.kotlin_version = '1.8.10'
    repositories {
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/jcenter'}
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }
        maven { url 'https://developer.huawei.com/repo/'}
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.huawei.agconnect:agcp:1.6.0.300'
    }
}

allprojects {
    repositories {
        // maven { url 'http://*************:9081/repository/maven-public';allowInsecureProtocol true}
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/jcenter'}
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }
        maven {url 'https://developer.huawei.com/repo/'}

    }
}

// 解决 android.namespace 为空的问题 @https://blog.csdn.net/Crystalqt/article/details/143984202
subprojects {
    afterEvaluate { project ->
        if (project.plugins.hasPlugin('com.android.library') || project.plugins.hasPlugin('com.android.application')) {
            project.android {
                compileSdkVersion 34
                buildToolsVersion "34.0.0"
                
                // 为Java编译器设置JVM目标版本为17
                compileOptions {
                    sourceCompatibility JavaVersion.VERSION_17
                    targetCompatibility JavaVersion.VERSION_17
                }
            }
//            println "project: ${project.name} Namespace get: ${project.android.namespace}"
            //如果需要从 manifest 文件中提取包名，可以使用以下代码
            if (project.android.namespace == null) {
                def manifestFile = project.android.sourceSets.main.manifest.srcFile
                def manifestText = manifestFile.text
                def manifestPattern = /package="([^"]*)"/
                def matcher = manifestText =~ manifestPattern
                if (matcher.find()) {
                    project.android.namespace = matcher.group(1)
                    println "Namespace set to: ${project.android.namespace} for project: ${project.name}"
                }
            }
            
            // 为每个子项目添加Kotlin JVM目标版本设置
            if (project.plugins.hasPlugin('kotlin-android')) {
                project.tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
                    kotlinOptions {
                        jvmTarget = "17"
                    }
                }
            }
        }
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
