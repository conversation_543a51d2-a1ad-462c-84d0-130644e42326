#-keep class com.amap.api.** { *; }

-ignorewarnings
-keepattributes *Annotation*
-keepattributes Exceptions
-keepattributes InnerClasses
-keepattributes Signature
-keepattributes SourceFile,LineNumberTable
-keep class com.amap.api.** { *; }
-keep class com.autonavi.** { *; }
-dontwarn com.amap.api.**

# 高德地图相关
-keep class com.amap.api.maps.** { *; }
-keep class com.autonavi.** { *; }
-keep class com.amap.api.trace.** { *; }
-keep class com.amap.api.navi.** { *; }

-keep class com.autonavi.** { *; }
-keep class com.amap.api.location.** { *; }
-keep class com.amap.api.fence.** { *; }
-keep class com.autonavi.aps.amapapi.model.** { *; }
-keep class com.amap.api.maps.model.** { *; }
-keep class com.amap.api.services.** { *; }

# 如果有用到对应的服务，需要加上对应混淆规则
-keep class com.amap.api.fence.** { *; }
-keep class com.amap.api.location.** { *; }
-keep class com.amap.api.services.** { *; }
-keep class com.amap.api.maps2d.** { *; }

# 第三方开源库
-dontwarn com.amap.api.**
-dontwarn com.autonavi.**
-dontwarn com.alibaba.fastjson.**
-dontwarn org.ligboy.retrofit2.**
-dontwarn org.codehaus.**
-dontwarn java.nio.**
-dontwarn javax.annotation.**

# Flutter相关
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }


-keep class com.aliyun.sls.android.producer.* { *; }
-keep interface com.aliyun.sls.android.producer.* { *; }

# EventBus 相关
-keepattributes *Annotation*
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}

# 忽略 BackEvent 相关的警告
-dontwarn android.window.BackEvent
-dontwarn android.window.**