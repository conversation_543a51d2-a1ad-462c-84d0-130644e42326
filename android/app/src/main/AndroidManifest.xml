<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.almas.dinner"
    xmlns:tools="http://schemas.android.com/tools">

    <queries>

        <!-- If your app checks for SMS support -->
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="sms" />
        </intent>
        <!-- If your app checks for call support -->
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="tel" />
        </intent>
    </queries>

    <permission
        android:name="com.almas.dinner.permission.JPUSH_MESSAGE"
        android:protectionLevel="signature" />
    <uses-permission android:name="com.almas.dinner.permission.JPUSH_MESSAGE" />
    <!--huawei_permission_start-->
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />

    <uses-permission android:name="android.permission.INTERNET" /> <!-- 允许获取精确位置，实时导航为必选 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- 允许获取粗略位置，实时导航为必选 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- 允许获取设备和运营商信息，用于问题排查和网络定位（无gps情况下的定位），若需网络定位功能则必选 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 允许获取wifi网络信息，用于网络定位（无gps情况下的定位），若需网络定位功能则必选 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 允许获取wifi状态改变，用于网络定位（无gps情况下的定位），若需网络定位功能则必选 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- 后台获取位置信息，若需后台定位或持续导航则必选 -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- 获取app安装列表   -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />



    <permission
        android:name="com.almas.dinner.permission.MIPUSH_RECEIVE"
        android:protectionLevel="signature" />
    <uses-permission android:name="com.almas.dinner.permission.MIPUSH_RECEIVE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="com.vivo.notification.permission.BADGE_ICON" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!--jpush_permission_start-->
    <uses-sdk tools:overrideLibrary="
     cn.jpush.android.thirdpush.fcm
    ,cn.jpush.android.thirdpush.huawei
    ,cn.jpush.android.thirdpush.meizu
    ,cn.jpush.android.thirdpush.oppo
    ,cn.jpush.android.thirdpush.vivo
    ,cn.jpush.android.thirdpush.xiaomi
    ,com.google.firebase.firebase_core
    ,com.google.firebase.messaging
    ,com.google.firebase.analytics.connector.impl
    ,com.google.firebase.measurement
    ,com.google.android.gms.measurement.api
    ,com.google.firebase.measurement_impl
    ,com.google.firebase.iid
    ,com.google.firebase
    ,com.google.firebase.iid.internal
    ,com.google.android.gms.base
    ,com.google.android.gms.tasks
    ,com.google.firebase.analytics.connector
    ,com.google.android.gms.stats
    ,com.google.android.gms.ads_identifier
    ,com.google.android.gms.common
    ,com.google.android.gms.measurement_base
    ,com.huawei.android.hms.openid
    ,com.huawei.agconnect.core
    ,com.huawei.hmf.tasks
    ,com.huawei.hms.framework.network.grs
    ,com.huawei.hms.framework.common
    ,com.huawei.android.hms.base
    ,com.huawei.android.hms.push
    ,android.support.mediacompat
    ,android.support.fragment
    ,android.support.coreutils
    ,android.support.coreui
    ,android.support.compat
    ,android.arch.lifecycle" />

    <application
        android:label="Mulazim美滋来"
        android:icon="@mipmap/ic_launcher"
        android:usesCleartextTraffic="true"
        tools:replace="android:label"
        >

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_path" />
        </provider>

        <receiver android:name=".service.LocalNotificationReceiver"/>

        <activity
            android:name="cn.jpush.android.service.JNotifyActivity"
            android:exported="true"
            android:taskAffinity=""
            android:theme="@style/JPushTheme" >
            <intent-filter>
                <action android:name="cn.jpush.android.intent.JNotifyActivity" />
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="${applicationId}" />
            </intent-filter>
        </activity>


        <service android:name=".jpush.PushMessageService"
            android:exported="true">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.SERVICE_MESSAGE" />
                <category android:name="${applicationId}"></category>
            </intent-filter>
        </service>

        <receiver
            android:name="cn.jpush.android.service.SchedulerReceiver"
            tools:replace="android:exported"
            android:exported="false" />


        <service
            android:name="cn.jpush.android.service.PushService"
            android:enabled="true"
            android:exported="false"
            android:process=":pushcore">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.REGISTER" />
                <action android:name="cn.jpush.android.intent.REPORT" />
                <action android:name="cn.jpush.android.intent.PushService" />
                <action android:name="cn.jpush.android.intent.PUSH_TIME" />
            </intent-filter>
        </service>


        <activity
            android:name="cn.android.service.JTransitActivity"
            android:exported="true"
            android:taskAffinity=""
            android:theme="@style/JPushTheme" >
            <intent-filter>
                <action android:name="cn.android.service.JTransitActivity" />
                <category android:name="android.intent.category.DEFAULT"/>

                <category android:name="${applicationId}" />
            </intent-filter>
        </activity>
        <!-- Required SDK核心功能 -->
        <activity
            android:name="cn.jpush.android.ui.PushActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="true"
            android:theme="@android:style/Theme.NoTitleBar">
            <intent-filter>
                <action android:name="cn.jpush.android.ui.PushActivity" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="${JPUSH_PKGNAME}" />
            </intent-filter>
        </activity>
        <activity
            android:name="cn.jpush.android.ui.PopWinActivity"
            android:exported="true"
            android:theme="@style/MyDialogStyle">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />

                <action android:name="cn.jpush.android.ui.PopWinActivity" />

                <category android:name="${JPUSH_PKGNAME}" />
            </intent-filter>
        </activity>
        <!-- since 3.0.9 Required SDK 核心功能 -->
        <provider
            android:name="cn.jpush.android.service.DataProvider"
            android:authorities="${JPUSH_PKGNAME}.DataProvider"
            android:exported="false"
            android:process=":pushcore" />

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />

            <meta-data
                android:name="io.flutter.embedding.android.SplashScreenDrawable"
                android:resource="@drawable/launch_background" />

            <meta-data
                android:name="com.amap.api.v2.apikey"
                android:value="3c2d8bc0add227e536a43db754f23db1" />

            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>



        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />


        <service
            android:name="com.xiaomi.push.service.XMJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":pushservice" />

        <service
            android:name="com.xiaomi.push.service.XMPushService"
            android:enabled="true"
            android:process=":pushservice" />


        <service
            android:name="com.xiaomi.mipush.sdk.PushMessageHandler"
            android:enabled="true"
            android:exported="true" />
        <service
            android:name="com.xiaomi.mipush.sdk.MessageHandleService"
            android:enabled="true" />

        <receiver
            android:name="com.xiaomi.push.service.receivers.NetworkStatusReceiver"
            tools:replace="android:exported"
            android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.xiaomi.push.service.receivers.PingReceiver"
            android:exported="false"
            tools:replace="android:exported"
            android:process=":pushservice">
            <intent-filter>
                <action android:name="com.xiaomi.push.PING_TIMER" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="cn.jpush.android.service.PluginXiaomiPlatformsReceiver"
            tools:replace="android:exported"
            android:exported="true">
            <intent-filter>
                <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.MESSAGE_ARRIVED" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.ERROR" />
            </intent-filter>
        </receiver>


        <meta-data
            android:name="XIAOMI_APPKEY"
            android:value="MI-5421802435968"/>
        <meta-data
            android:name="XIAOMI_APPID"
            android:value="MI-2882303761518024968"/>


        <activity
            android:name="com.xiaomi.mipush.sdk.NotificationClickedActivity"
            android:enabled="true"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
            <meta-data
                android:name="supportStyle"
                android:value="scene|voip" />
        </activity>


        <!-- 添加极光推送相关服务声明 -->
        <service
            android:name=".jpush.MyJPushService"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="cn.jpush.android.intent.SERVICE_MESSAGE" />
                <action android:name="cn.jiguang.user.service.action" />
                <action android:name="cn.jpush.android.intent.REGISTRATION" />
                <action android:name="cn.jpush.android.intent.MESSAGE_RECEIVED" />
                <action android:name="cn.jpush.android.intent.NOTIFICATION_RECEIVED" />
                <action android:name="cn.jpush.android.intent.NOTIFICATION_OPENED" />
                <action android:name="cn.jpush.android.intent.ACTION_RICHPUSH_CALLBACK" />
                <action android:name="cn.jpush.android.intent.CONNECTION" />
                <category android:name="${applicationId}" />
            </intent-filter>
        </service>


        <service
            android:name="io.flutter.plugins.jpush.JPushService"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="cn.jpush.android.intent.NOTIFICATION_RECEIVED_PROXY" />  <!-- Required -->
                <category android:name="${applicationId}" />
            </intent-filter>
        </service>

        <receiver android:name="cn.jpush.android.service.PushReceiver" android:enabled="true" android:exported="false"
            tools:replace="android:exported"
            >
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="cn.jpush.android.intent.NOTIFICATION_RECEIVED_PROXY" /> <!-- Required -->
                <category android:name="${applicationId}" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.USER_PRESENT" />
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
            <!-- Optional -->
            <intent-filter>
                <action android:name="android.intent.action.PACKAGE_ADDED" />
                <action android:name="android.intent.action.PACKAGE_REMOVED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <!-- since JPushv3.6.8 ，oppov2.1.0 oppo 核心功能-->
        <service android:name="cn.jpush.android.service.PluginOppoPushService"
            android:permission="com.coloros.mcs.permission.SEND_MCS_MESSAGE"
            android:exported="true">
            <intent-filter>
                <action android:name="com.coloros.mcs.action.RECEIVE_MCS_MESSAGE" />
            </intent-filter>
        </service>

        <!-- since JPushv3.6.8 ，oppov2.1.0 oppo 核心功能-->
        <service
            android:name="com.heytap.msp.push.service.DataMessageCallbackService"
            android:permission="com.heytap.mcs.permission.SEND_PUSH_MESSAGE"
            android:exported="true">

            <intent-filter>
                <action android:name="com.heytap.mcs.action.RECEIVE_MCS_MESSAGE"/>

                <action android:name="com.heytap.msp.push.RECEIVE_MCS_MESSAGE"/>
            </intent-filter>
        </service> <!--兼容Q版本-->


        <meta-data
            android:name="OPPO_APPKEY"
            android:value="OP-1ecyC9WgkO9W4K4wOsWscgSC8" />

        <meta-data
            android:name="OPPO_APPID"
            android:value="OP-3547200" />

        <meta-data
            android:name="OPPO_APPSECRET"
            android:value="OP-0dA5cD3Ee753edbdee4D7F048e568b37" />


        <receiver android:name="cn.jpush.android.service.PluginVivoMessageReceiver" android:exported="true"
            tools:replace="android:exported"
            >
            <intent-filter>
                <!-- 接收push 消息 -->
                <action android:name="com.vivo.pushclient.action.RECEIVE" />
            </intent-filter>
        </receiver>
        <service
            android:name="com.vivo.push.sdk.service.CommandClientService"
            android:exported="true" />
        <activity
            android:name="com.vivo.push.sdk.LinkProxyClientActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data android:name="com.vivo.push.api_key"
            android:value="aa547c1951369be7c93b1deab6d698ce" />
        <meta-data android:name="com.vivo.push.app_id"
            android:value="101188174" />


        <service
            android:name="cn.jpush.android.service.PluginHuaweiPlatformsService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
            </intent-filter>
        </service>


        <receiver
            android:name="cn.jpush.android.service.AlarmReceiver"
            android:exported="false"
            tools:replace="android:exported"
            />



        <!-- 高德地图服务 -->
        <service android:name="com.amap.api.location.APSService" />
        
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->

        <!--Required_config-->
        <meta-data
            android:name="JPUSH_CHANNEL"
            android:value="developer-default" />
        <meta-data
            android:name="JPUSH_APPKEY"
            android:value="3326fe4b331a003902154e24" />


        <provider
            android:name="cn.jpush.android.service.InitProvider"
            android:authorities="com.almas.dinner.jiguang.InitProvider"
            android:exported="false"
            android:readPermission="com.almas.dinner.permission.JPUSH_MESSAGE"
            android:writePermission="com.almas.dinner.permission.JPUSH_MESSAGE" />
    </application>
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>
</manifest>
