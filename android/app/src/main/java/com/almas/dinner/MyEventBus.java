package com.almas.dinner;

import android.content.Context;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class MyEventBus {

    public static Set<Message> contexts = new HashSet<>();

    public static void register(Message message) {
        contexts.add(message);
    }

    public static void unRegister(Message message) {
        contexts.remove(message);
    }

    public static void post(Class clazz,String info){
        for (Message context : contexts) {
            if (context.getClass()==clazz) {
                context.call(info);
            }
        }
    }

    public interface Message{
        void call(String content);
    }
}
