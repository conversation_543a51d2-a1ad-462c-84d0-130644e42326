package com.almas.dinner.order_line_view;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.Map;

import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.StandardMessageCodec;
import io.flutter.plugin.platform.PlatformViewFactory;

class OrderViewFactory extends PlatformViewFactory {
    private final BinaryMessenger messenger;
    public OrderViewFactory(BinaryMessenger messenger) {
        super(StandardMessageCodec.INSTANCE);
        this.messenger = messenger;

    }

    @NonNull
    @Override
    public OrderLineView create(@NonNull Context context, int id, @Nullable Object args) {
        final Map<String, Object> creationParams = (Map<String, Object>) args;
        return new OrderLineView(context,creationParams,messenger);
    }
}
