PODS:
  - aliyun_log_dart_sdk (1.1.4):
    - AliyunLogProducer (= 4.3.17)
    - Flutter
  - AliyunLogProducer (4.3.17)
  - amap_flutter_location (0.0.1):
    - AMapLocation
    - Flutter
  - AMapFoundation (1.8.2)
  - AMapLocation (2.10.0):
    - AMapFoundation (>= 1.8.0)
  - AMapNavi (10.1.200):
    - AMapFoundation (>= 1.8.2)
  - AMapSearch (9.7.4):
    - AMapFoundation (>= 1.8.0)
  - battery_plus (1.0.0):
    - Flutter
  - code_push_plugin (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - easy_app_installer (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_native_splash (2.4.3):
    - Flutter
  - fluwx (0.0.1):
    - Flutter
    - fluwx/pay (= 0.0.1)
  - fluwx/pay (0.0.1):
    - Flutter
    - WechatOpenSDK-XCFramework (~> 2.0.4)
  - image_picker_ios (0.0.1):
    - Flutter
  - JCore (5.0.2)
  - JPush (5.6.1):
    - JCore (>= 4.8.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - photo_manager (3.7.1):
    - Flutter
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - WechatOpenSDK-XCFramework (2.0.4)

DEPENDENCIES:
  - aliyun_log_dart_sdk (from `.symlinks/plugins/aliyun_log_dart_sdk/ios`)
  - AliyunLogProducer (from `https://gitee.com/aliyun-sls/aliyun-log-ios-sdk.git`, tag `4.3.17`)
  - amap_flutter_location (from `.symlinks/plugins/amap_flutter_location/ios`)
  - AMapNavi
  - AMapSearch
  - battery_plus (from `.symlinks/plugins/battery_plus/ios`)
  - code_push_plugin (from `.symlinks/plugins/code_push_plugin/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - easy_app_installer (from `.symlinks/plugins/easy_app_installer/ios`)
  - Flutter (from `Flutter`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - JCore
  - JPush
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AMapFoundation
    - AMapLocation
    - AMapNavi
    - AMapSearch
    - JCore
    - JPush
    - WechatOpenSDK-XCFramework

EXTERNAL SOURCES:
  aliyun_log_dart_sdk:
    :path: ".symlinks/plugins/aliyun_log_dart_sdk/ios"
  AliyunLogProducer:
    :git: https://gitee.com/aliyun-sls/aliyun-log-ios-sdk.git
    :tag: 4.3.17
  amap_flutter_location:
    :path: ".symlinks/plugins/amap_flutter_location/ios"
  battery_plus:
    :path: ".symlinks/plugins/battery_plus/ios"
  code_push_plugin:
    :path: ".symlinks/plugins/code_push_plugin/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  easy_app_installer:
    :path: ".symlinks/plugins/easy_app_installer/ios"
  Flutter:
    :path: Flutter
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

CHECKOUT OPTIONS:
  AliyunLogProducer:
    :git: https://gitee.com/aliyun-sls/aliyun-log-ios-sdk.git
    :tag: 4.3.17

SPEC CHECKSUMS:
  aliyun_log_dart_sdk: 33d66b32a99be7db670e89b219fd29ea8a148b4c
  AliyunLogProducer: bc5fe503f8ea6ae70488b40d09184664d0f8bad3
  amap_flutter_location: f033c983c2d4319203ff7b523775579534d0d557
  AMapFoundation: 9885c48fc3a78fdfb84a0299a2293e56ea3c9fec
  AMapLocation: 5248aec2455ebb5d104b367813c946430a2ee033
  AMapNavi: 2c5dd0e6e8cd2b5a9e465cd2f9d59e8628bbc75a
  AMapSearch: a30e89212f55a867ac951ce71f800313cd54ad0a
  battery_plus: b42253f6d2dde71712f8c36fef456d99121c5977
  code_push_plugin: 14bfeed5c61ae20f2ee315aa3c07913cdb5832d8
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  easy_app_installer: 0e243a99b09932edb2c90b084b6e51081bb0b99e
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_native_splash: c32d145d68aeda5502d5f543ee38c192065986cf
  fluwx: 6bf9c5a3a99ad31b0de137dd92370a0d10a60f4b
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  JCore: 3effa468af29935edb53e6460ef81fd3c03ee1a8
  JPush: fcd20c0b2d6d8206af63b8517fa2d11e0a83dc6b
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  photo_manager: 1d80ae07a89a67dfbcae95953a1e5a24af7c3e62
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2
  WechatOpenSDK-XCFramework: 36fb2bea0754266c17184adf4963d7e6ff98b69f

PODFILE CHECKSUM: 75b34896fb39ff9dbfd16467335f16588302df4f

COCOAPODS: 1.16.2
