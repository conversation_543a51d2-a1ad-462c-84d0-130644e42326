import Foundation
import AVFoundation
import Photos

class PermissionManager {
    
    /// 检查相机权限
    static func checkCameraPermission(completion: @escaping (Bool) -> Void) {
        let authStatus = AVCaptureDevice.authorizationStatus(for: .video)
        
        switch authStatus {
        case .authorized:
            completion(true)
        case .denied, .restricted:
            completion(false)
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    completion(granted)
                }
            }
        @unknown default:
            completion(false)
        }
    }
    
    /// 检查相册权限
    static func checkPhotoLibraryPermission(completion: @escaping (Bool) -> Void) {
        let authStatus = PHPhotoLibrary.authorizationStatus()
        
        switch authStatus {
        case .authorized:
            completion(true)
        case .denied, .restricted:
            completion(false)
        case .notDetermined:
            PHPhotoLibrary.requestAuthorization { status in
                DispatchQueue.main.async {
                    if #available(iOS 14, *) {
                        completion(status == .authorized || status == .limited)
                    } else {
                        completion(status == .authorized)
                    }
                }
            }
        default:
            if #available(iOS 14, *), authStatus == .limited {
                completion(true)
            } else {
                completion(false)
            }
        }
    }
} 