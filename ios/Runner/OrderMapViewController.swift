//
//  OrderMapViewController.swift
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/16.
//

import UIKit
import Flutter
import AMapSearchKit
import AMapNaviKit

public class OrderMapViewController: NSObject, FlutterPlatformView, MAMapViewDelegate, AMapSearchDelegate {
    // 核心组件
    private let containerView: UIView
    private var mapView: MAMapView!
    private var searchAPI: AMapSearchAPI!
    private var methodChannel: FlutterMethodChannel?
    
    // 位置数据
    private var shipperLocation: CLLocationCoordinate2D?  // 配送员位置
    private var restaurantLocation: CLLocationCoordinate2D?  // 餐厅位置  
    private var userLocation: CLLocationCoordinate2D?  // 用户位置
    private var restaurantLogoUrl: String?  // 餐厅logo URL
    
    // 地图标记
    private var shipperMarker: MAPointAnnotation?
    private var restaurantMarker: MAPointAnnotation?
    private var userMarker: MAPointAnnotation?
    private var routePolyline: MAPolyline?  // 路径线
    
    // 路径规划状态
    private var isCalculatingRoute = false
    private var pendingDistanceResult: FlutterResult?
    
    // 地图视野状态
    private var hasInitializedMapView = false  // 是否已初始化地图视野
    
    // 初始化
    public init(frame: CGRect, viewIdentifier viewId: Int64, arguments args: Any?) {
        containerView = UIView(frame: frame)
        super.init()
        
        // 设置高德地图API Key
        AMapServices.shared()?.apiKey = "b246804aa0216430d4e64979bd0bb1e0"
        
        // 解析传入参数
        if let params = args as? [String: Any] {
            parseInitialParams(params)
        }
        
        // 初始化组件
        setupMapView()
        setupSearchAPI()
        setupMethodChannel()
        
        // 初始化地图内容
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.initializeMapContent()
        }
    }
    
    // 解析初始参数
    private func parseInitialParams(_ params: [String: Any]) {
        // 配送员位置
        if let shipperLatStr = params["shipper_lat"] as? String,
           let shipperLngStr = params["shipper_lng"] as? String,
           let shipperLat = Double(shipperLatStr),
           let shipperLng = Double(shipperLngStr) {
            shipperLocation = CLLocationCoordinate2D(latitude: shipperLat, longitude: shipperLng)
        }
        
        // 餐厅位置
        if let resLatStr = params["res_lat"] as? String,
           let resLngStr = params["res_lng"] as? String,
           let resLat = Double(resLatStr),
           let resLng = Double(resLngStr) {
            restaurantLocation = CLLocationCoordinate2D(latitude: resLat, longitude: resLng)
        }
        
        // 用户位置
        if let buildingLatStr = params["building_lat"] as? String,
           let buildingLngStr = params["building_lng"] as? String,
           let buildingLat = Double(buildingLatStr),
           let buildingLng = Double(buildingLngStr) {
            userLocation = CLLocationCoordinate2D(latitude: buildingLat, longitude: buildingLng)
        }
        
        // 餐厅logo
        if let resLogo = params["res_logo"] as? String {
            restaurantLogoUrl = resLogo
        }
    }
    
    // 设置地图视图
    private func setupMapView() {
        mapView = MAMapView(frame: containerView.bounds)
        mapView.delegate = self
        mapView.showsUserLocation = false  // 不显示系统用户位置
        mapView.zoomLevel = 15
        containerView.addSubview(mapView)
    }
    
    // 设置搜索API
    private func setupSearchAPI() {
        searchAPI = AMapSearchAPI()
        searchAPI.delegate = self
    }
    
    // 设置方法通道
    private func setupMethodChannel() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if let flutterVC = self.findFlutterViewController() {
                self.methodChannel = FlutterMethodChannel(
                    name: "order_line_channel", 
                    binaryMessenger: flutterVC.binaryMessenger
                )
                self.methodChannel?.setMethodCallHandler(self.handleMethodCall)
            }
        }
    }
    
    // 查找FlutterViewController
    private func findFlutterViewController() -> FlutterViewController? {
        var rootVC: UIViewController?
        
        if #available(iOS 13.0, *) {
            rootVC = UIApplication.shared.connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .flatMap { $0.windows }
                .first { $0.isKeyWindow }?.rootViewController
        } else {
            rootVC = UIApplication.shared.keyWindow?.rootViewController
        }
        
        return findFlutterVC(in: rootVC)
    }
    
    private func findFlutterVC(in vc: UIViewController?) -> FlutterViewController? {
        if let flutterVC = vc as? FlutterViewController {
            return flutterVC
        }
        
        for child in vc?.children ?? [] {
            if let found = findFlutterVC(in: child) {
                return found
            }
        }
        
        if let presented = vc?.presentedViewController {
            return findFlutterVC(in: presented)
        }
        
        return nil
    }
    
    // 初始化地图内容
    private func initializeMapContent() {
        addMapMarkers()
        adjustMapView()
        // 初始路径规划：从配送员到用户
        if shipperLocation != nil && userLocation != nil {
            planRouteFromShipperToUser()
        }
    }
    
    // 添加地图标记
    private func addMapMarkers() {
        // 添加配送员标记
        if let shipperLoc = shipperLocation {
            shipperMarker = MAPointAnnotation()
            shipperMarker?.coordinate = shipperLoc
            shipperMarker?.title = "配送员"
            mapView.addAnnotation(shipperMarker!)
        }
        
        // 添加餐厅标记  
        if let restaurantLoc = restaurantLocation {
            restaurantMarker = MAPointAnnotation()
            restaurantMarker?.coordinate = restaurantLoc
            restaurantMarker?.title = "餐厅"
            mapView.addAnnotation(restaurantMarker!)
        }
        
        // 添加用户标记
        if let userLoc = userLocation {
            userMarker = MAPointAnnotation()
            userMarker?.coordinate = userLoc
            userMarker?.title = "收货地址"
            mapView.addAnnotation(userMarker!)
        }
    }
    
    // 调整地图视野（仅在初始化时调用）
    private func adjustMapView() {
        var coordinates: [CLLocationCoordinate2D] = []
        
        if let shipperLoc = shipperLocation {
            coordinates.append(shipperLoc)
        }
        if let restaurantLoc = restaurantLocation {
            coordinates.append(restaurantLoc)
        }
        if let userLoc = userLocation {
            coordinates.append(userLoc)
        }
        
        if coordinates.count >= 2 {
            adjustMapToShowCoordinates(coordinates)
            hasInitializedMapView = true  // 标记已初始化地图视野
        }
    }
    
    // 调整地图以显示指定坐标
    private func adjustMapToShowCoordinates(_ coordinates: [CLLocationCoordinate2D]) {
        guard coordinates.count > 0 else { return }
        
        if coordinates.count == 1 {
            // 只有一个坐标，设置中心点和默认缩放级别
            mapView.setCenter(coordinates[0], animated: true)
            mapView.setZoomLevel(15, animated: true)
            return
        }
        
        // 计算边界
        var minLat = coordinates[0].latitude
        var maxLat = coordinates[0].latitude
        var minLng = coordinates[0].longitude
        var maxLng = coordinates[0].longitude
        
        for coordinate in coordinates {
            minLat = min(minLat, coordinate.latitude)
            maxLat = max(maxLat, coordinate.latitude)
            minLng = min(minLng, coordinate.longitude)
            maxLng = max(maxLng, coordinate.longitude)
        }
        
        // 计算中心点
        let centerLat = (minLat + maxLat) / 2
        let centerLng = (minLng + maxLng) / 2
        let centerCoordinate = CLLocationCoordinate2D(latitude: centerLat, longitude: centerLng)
        
        // 计算跨度（增加一些边距）
        let latDelta = (maxLat - minLat) * 1.5  // 增加50%的边距
        let lngDelta = (maxLng - minLng) * 1.5
        
        // 确保最小跨度
        let minDelta: CLLocationDegrees = 0.01  // 大约1公里
        let finalLatDelta = max(latDelta, minDelta)
        let finalLngDelta = max(lngDelta, minDelta)
        
        // 创建区域并设置
        let region = MACoordinateRegion(
            center: centerCoordinate,
            span: MACoordinateSpan(latitudeDelta: finalLatDelta, longitudeDelta: finalLngDelta)
        )
        
        mapView.setRegion(region, animated: true)
    }
    
    // 计算两点之间的直线距离（米）
    private func calculateStraightLineDistance(from: CLLocationCoordinate2D, to: CLLocationCoordinate2D) -> Double {
        let fromLocation = CLLocation(latitude: from.latitude, longitude: from.longitude)
        let toLocation = CLLocation(latitude: to.latitude, longitude: to.longitude)
        return fromLocation.distance(from: toLocation)
    }
    
    // 规划从配送员到用户的路径
    private func planRouteFromShipperToUser() {
        guard let shipperLoc = shipperLocation,
              let userLoc = userLocation else { 
            print("❌ 缺少位置信息，无法规划路径")
            return 
        }
        
        if isCalculatingRoute {
            print("⚠️ 正在计算路径中，跳过本次请求")
            return
        }
        
        print("🗺️ 开始规划配送员到用户的路径：从(\(shipperLoc.latitude), \(shipperLoc.longitude)) 到(\(userLoc.latitude), \(userLoc.longitude))")
        
        // 验证坐标有效性
        if !CLLocationCoordinate2DIsValid(shipperLoc) {
            print("❌ 配送员坐标无效: \(shipperLoc)")
            handleRouteSearchFailure()
            return
        }
        
        if !CLLocationCoordinate2DIsValid(userLoc) {
            print("❌ 用户坐标无效: \(userLoc)")
            handleRouteSearchFailure()
            return
        }

        // 检查起点和终点的距离
        let distance = calculateStraightLineDistance(from: shipperLoc, to: userLoc)
        
        if distance < 5 {
            print("⚠️ 起终点距离太近(\(String(format: "%.2f", distance))米)，使用直线路径")
            createFallbackRoute()
            return
        } else if distance > 100000 {
            print("⚠️ 起终点距离太远(\(String(format: "%.2f", distance))米)，可能超出服务范围")
        }
        
        // 构造步行路径规划请求（按照官方文档规范）
        let request = AMapWalkingRouteSearchRequest()
        
        // 设置起点和终点 - 使用正确的坐标格式
        request.origin = AMapGeoPoint.location(withLatitude: CGFloat(shipperLoc.latitude), longitude: CGFloat(shipperLoc.longitude))
        request.destination = AMapGeoPoint.location(withLatitude: CGFloat(userLoc.latitude), longitude: CGFloat(userLoc.longitude))
        
        // 根据官方文档设置请求参数 - 尝试获取详细路径信息
        // 注意：某些属性可能在不同版本的SDK中不存在
        
        // 尝试设置扩展信息（如果支持）
        if request.responds(to: NSSelectorFromString("setRequireExtension:")) {
            request.setValue(true, forKey: "requireExtension")
            print("🔧 设置requireExtension为true")
        } else {
            print("⚠️ 当前SDK版本不支持requireExtension属性")
        }
        
        // 尝试设置显示字段类型（如果支持）
        if request.responds(to: NSSelectorFromString("setShowFieldsType:")) {
            // 尝试设置为返回所有字段
            request.setValue(NSNumber(value: 0xFFFFFFFF), forKey: "showFieldsType")
        } else {
            print("⚠️ 当前SDK版本不支持showFieldsType属性")
        }
        
        // 尝试设置多路径搜索（如果支持）
        if request.responds(to: NSSelectorFromString("setMultipath:")) {
            request.setValue(NSNumber(value: 1), forKey: "multipath")
        } else {
            print("⚠️ 当前SDK版本不支持multipath属性")
        }
        
        isCalculatingRoute = true
        
        // 发送路径规划请求
        print("🌐 正在向高德服务器发送请求...")
        searchAPI.aMapWalkingRouteSearch(request)
    }
    
    // MARK: - FlutterPlatformView
    public func view() -> UIView {
        return containerView
    }
    
    // MARK: - 方法调用处理
    private func handleMethodCall(call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "updateShipperPosition":
            handleUpdateShipperPosition(call: call, result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    // 处理配送员位置更新
    private func handleUpdateShipperPosition(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let params = call.arguments as? [String: Any],
              let shipperLatStr = params["shipper_lat"] as? String,
              let shipperLngStr = params["shipper_lng"] as? String,
              let shipperLat = Double(shipperLatStr),
              let shipperLng = Double(shipperLngStr) else {
            result(FlutterError(code: "INVALID_ARGUMENT", message: "Invalid shipper coordinates", details: nil))
            return
        }
        
        let newShipperLocation = CLLocationCoordinate2D(latitude: shipperLat, longitude: shipperLng)
        
        print("📍 更新配送员位置: \(shipperLat), \(shipperLng)")
        
        // 更新配送员位置
        shipperLocation = newShipperLocation
        
        // 更新地图标记
        if let marker = shipperMarker {
            marker.coordinate = newShipperLocation
        }
        
        // 存储结果回调，等待路径规划完成后返回距离
        pendingDistanceResult = result
        
        // 重新规划从配送员到用户的路径
        if userLocation != nil {
            planRouteFromShipperToUser()
        } else {
            // 如果没有用户位置，返回直线距离
            if let userLoc = userLocation {
                let distance = calculateStraightLineDistance(from: newShipperLocation, to: userLoc)
                result(Int(distance))
            } else {
                result(0)
            }
        }
    }
    
    // MARK: - AMapSearchDelegate
    public func onRouteSearchDone(_ request: AMapRouteSearchBaseRequest!, response: AMapRouteSearchResponse!) {
        isCalculatingRoute = false
        
        // 先清除之前添加的覆盖物
        mapView.removeOverlays(mapView.overlays)
        
        // 详细检查响应数据
        guard let response = response else {
            print("❌ 响应为空")
            handleRouteSearchFailure()
            return
        }
        
        // 检查路线数据
        guard let route = response.route else {
            print("❌ 响应中没有路线数据")
            handleRouteSearchFailure()
            return
        }

        guard let paths = route.paths, !paths.isEmpty else {
            print("❌ 路线中没有路径数组或路径数组为空")
            handleRouteSearchFailure()
            return
        }
        
        let path = paths[0] // 使用第一条路径
        print("✅ 路径规划成功，距离: \(path.distance)米")
        
        var coordinates: [CLLocationCoordinate2D] = []
        
        // 检查是否有步骤数据
        guard let steps = path.steps, !steps.isEmpty else {
            print("❌ 路径中没有步骤数据，使用直线路径")
            createFallbackRoute()
            return
        }
        
        print("🔍 解析\(steps.count)个步骤的路径数据...")
        
        // 解析每个步骤中的坐标点
        for (index, step) in steps.enumerated() {
            print("📍 处理步骤\(index + 1): \(step.instruction ?? "")")
            
            if let polyline = step.polyline, !polyline.isEmpty {
                let coords = polyline.components(separatedBy: ";")
                for coordString in coords {
                    let trimmedCoord = coordString.trimmingCharacters(in: .whitespacesAndNewlines)
                    if trimmedCoord.isEmpty { continue }
                    
                    let latLng = trimmedCoord.components(separatedBy: ",")
                    if latLng.count >= 2,
                       let longitude = Double(latLng[0].trimmingCharacters(in: .whitespacesAndNewlines)),
                       let latitude = Double(latLng[1].trimmingCharacters(in: .whitespacesAndNewlines)) {
                        
                        if latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180 {
                            let coordinate = CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
                            coordinates.append(coordinate)
                        }
                    }
                }
            } else {
                print("⚠️ 步骤\(index + 1)没有polyline数据，跳过此步骤")
                // 如果某个步骤没有polyline数据，继续下一个步骤
                // 不需要手动添加起终点，因为我们最后会确保路径包含正确的起终点
            }
        }
        
        print("📊 总共解析到\(coordinates.count)个坐标点")
        
        // 确保路径至少包含起点和终点
        if coordinates.isEmpty {
            print("⚠️ 没有解析到任何坐标，使用备用路径")
            createFallbackRoute()
            return
        } else if coordinates.count == 1 {
            // 只有一个点，添加终点
            if let userLoc = userLocation {
                coordinates.append(userLoc)
            }
        }
        
        // 确保路径的第一个点精确匹配配送员位置
        if let shipperLoc = shipperLocation, !coordinates.isEmpty {
            coordinates[0] = shipperLoc
        }
        
        // 确保路径的最后一个点精确匹配用户位置
        if let userLoc = userLocation, coordinates.count > 1 {
            coordinates[coordinates.count - 1] = userLoc
        }

        // 创建并添加路径
        if coordinates.count >= 2 {
            createRouteWithCoordinates(coordinates)
        } else {
            createFallbackRoute()
        }
        
        // 返回距离结果
        if let result = pendingDistanceResult {
            result(Int(path.distance))
            pendingDistanceResult = nil
        }
    }
    
    public func aMapSearchRequest(_ request: Any!, didFailWithError error: Error!) {
        isCalculatingRoute = false
        print("❌ 路径搜索失败: \(error?.localizedDescription ?? "未知错误")")
        handleRouteSearchFailure()
    }
    
    // 创建详细路径
    private func createRouteWithCoordinates(_ coordinates: [CLLocationCoordinate2D]) {
        print("🎨 开始绘制详细路径，包含\(coordinates.count)个点")
        
        // 将坐标点分成多个小段以提高渲染效果
        let segmentSize = 5 // 每5个点创建一个段
        
        for i in stride(from: 0, to: coordinates.count - 1, by: segmentSize) {
            let endIndex = min(i + segmentSize, coordinates.count - 1)
            let segment = Array(coordinates[i...endIndex])
            
            var segmentCoordinates = segment
            let polyline = MAPolyline(coordinates: &segmentCoordinates, count: UInt(segment.count))
            if let polyline = polyline {
                mapView.add(polyline)
                print("✅ 添加路径段 \(i/segmentSize + 1)，包含\(segment.count)个点")
            }
        }
        
        // 只在首次加载时调整地图视野，后续更新不重新调整
        if !hasInitializedMapView {
            hasInitializedMapView = true
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
                self?.adjustMapViewToShowRoute()
            }
        }
    }
    
    // 创建备用路径（直线）
    private func createFallbackRoute() {
        print("🔄 创建备用直线路径")
        
        guard let shipper = shipperLocation, let user = userLocation else {
            print("❌ 缺少必要的位置信息")
            if let result = pendingDistanceResult {
                result(0)
                pendingDistanceResult = nil
            }
            return
        }
        
        // 创建直线路径，确保与标记点坐标完全一致
        var coordinates = [shipper, user]
        let polyline = MAPolyline(coordinates: &coordinates, count: UInt(coordinates.count))
        
        if let polyline = polyline {
            print("✅ 创建直线路径覆盖物")
            mapView.add(polyline)
            
            // 只在首次加载时调整地图视野
            if !hasInitializedMapView {
                hasInitializedMapView = true
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                    self?.adjustMapViewToShowRoute()
                }
            }
        }
        
        // 计算直线距离
        let distance = calculateStraightLineDistance(from: shipper, to: user)
        print("📏 直线距离计算结果: \(distance)米")
        
        // 返回直线距离结果
        if let result = pendingDistanceResult {
            result(Int(distance))
            pendingDistanceResult = nil
        }
    }
    
    // 调整地图视野以显示路径
    private func adjustMapViewToShowRoute() {
        let edgePadding = UIEdgeInsets(top: 60, left: 40, bottom: 60, right: 40)
        
        // 显示所有覆盖物
        if let overlays = mapView.overlays, !overlays.isEmpty {
            mapView.showOverlays(overlays, edgePadding: edgePadding, animated: true)
        }
        
        // 确保标注点也在视野中
        if let annotations = mapView.annotations, !annotations.isEmpty {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                guard let self = self else { return }
                self.mapView.showAnnotations(annotations, edgePadding: edgePadding, animated: true)
            }
        }
    }
    
    // 处理路径搜索失败
    private func handleRouteSearchFailure() {
        print("🔄 路径规划失败，使用直线距离作为备用方案")
        createFallbackRoute()
    }
    
    // MARK: - MAMapViewDelegate
    public func mapView(_ mapView: MAMapView!, viewFor annotation: MAAnnotation!) -> MAAnnotationView! {
        if annotation === shipperMarker {
            return createShipperAnnotationView(for: annotation)
        } else if annotation === restaurantMarker {
            return createRestaurantAnnotationView(for: annotation)
        } else if annotation === userMarker {
            return createUserAnnotationView(for: annotation)
        }
        return nil
    }
    
    // 路径渲染
    public func mapView(_ mapView: MAMapView!, rendererFor overlay: MAOverlay!) -> MAOverlayRenderer! {
        if overlay is MAPolyline {
            let renderer = MAPolylineRenderer(polyline: overlay as! MAPolyline)
            
            // 根据坐标点数量判断路径类型并设置样式
            if let polyline = overlay as? MAPolyline {
                let pointCount = polyline.pointCount
                print("🎨 绘制路线，包含\(pointCount)个点")
                
                if pointCount <= 2 {
                    // 直线路径 - 使用蓝色表示简化路径
                    renderer?.lineWidth = 6.0
                    renderer?.strokeColor = UIColor.systemBlue
                    renderer?.alpha = 0.8
                    print("📏 渲染简化路径（蓝色线条，\(pointCount)个点）")
                } else if pointCount <= 5 {
                    // 少量点的路径 - 使用蓝色表示估算路径
                    renderer?.lineWidth = 8.0
                    renderer?.strokeColor = UIColor.systemBlue
                    renderer?.alpha = 0.8
                    print("📏 渲染估算路径（蓝色线条，\(pointCount)个点）")
                } else {
                    // 真实详细路径 - 使用蓝色实线
                    renderer?.lineWidth = 10.0
                    renderer?.strokeColor = UIColor.systemBlue
                    renderer?.alpha = 0.9
                    print("📏 渲染详细步行路径（蓝色实线，\(pointCount)个点）")
                }
                
                // 设置线条样式
                renderer?.lineCapType = kMALineCapRound
                renderer?.lineJoinType = kMALineJoinRound
            }
            
            return renderer
        }
        return nil
    }
    
    // 创建配送员标注视图
    private func createShipperAnnotationView(for annotation: MAAnnotation) -> MAAnnotationView {
        let identifier = "ShipperAnnotationView"
        var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)
        
        if annotationView == nil {
            annotationView = MAAnnotationView(annotation: annotation, reuseIdentifier: identifier)
            annotationView?.image = createShipperIcon()
        }
        
        return annotationView!
    }
    
    // 创建配送员图标
    private func createShipperIcon() -> UIImage {
        let size = CGSize(width: 40, height: 40)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            let rect = CGRect(origin: .zero, size: size)
            let ctx = context.cgContext
            
            // 绘制阴影
            ctx.setShadow(offset: CGSize(width: 0, height: 2), blur: 4, color: UIColor.black.withAlphaComponent(0.3).cgColor)
            
            // 绘制背景圆形
            ctx.setFillColor(UIColor.systemOrange.cgColor)
            ctx.fillEllipse(in: rect.insetBy(dx: 2, dy: 2))
            
            // 重置阴影
            ctx.setShadow(offset: .zero, blur: 0, color: nil)
            
            // 绘制白色边框
            ctx.setStrokeColor(UIColor.white.cgColor)
            ctx.setLineWidth(2.0)
            ctx.strokeEllipse(in: rect.insetBy(dx: 3, dy: 3))
            
            // 绘制摩托车图标
            ctx.setFillColor(UIColor.white.cgColor)
            
            // 简化的摩托车形状
            let vehicleRect = rect.insetBy(dx: 8, dy: 8)
            let wheelRadius: CGFloat = 6
            let bodyHeight: CGFloat = 4
            
            // 后轮
            let rearWheelCenter = CGPoint(x: vehicleRect.minX + wheelRadius, y: vehicleRect.maxY - wheelRadius)
            ctx.fillEllipse(in: CGRect(x: rearWheelCenter.x - wheelRadius, y: rearWheelCenter.y - wheelRadius, width: wheelRadius * 2, height: wheelRadius * 2))
            
            // 前轮
            let frontWheelCenter = CGPoint(x: vehicleRect.maxX - wheelRadius, y: vehicleRect.maxY - wheelRadius)
            ctx.fillEllipse(in: CGRect(x: frontWheelCenter.x - wheelRadius, y: frontWheelCenter.y - wheelRadius, width: wheelRadius * 2, height: wheelRadius * 2))
            
            // 车身
            let bodyRect = CGRect(x: rearWheelCenter.x - 2, y: rearWheelCenter.y - wheelRadius - bodyHeight, width: frontWheelCenter.x - rearWheelCenter.x + 4, height: bodyHeight)
            ctx.fill(bodyRect)
        }
    }
    
    // 创建餐厅标注视图
    private func createRestaurantAnnotationView(for annotation: MAAnnotation) -> MAAnnotationView {
        let identifier = "RestaurantAnnotationView"
        var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)
        
        if annotationView == nil {
            annotationView = MAAnnotationView(annotation: annotation, reuseIdentifier: identifier)
            
            // 先设置默认图标
            annotationView?.image = createDefaultRestaurantIcon()
            
            // 如果有餐厅logo URL，尝试异步加载
            if let logoUrl = restaurantLogoUrl,
               !logoUrl.isEmpty,
               let url = URL(string: logoUrl) {
                loadRestaurantLogo(url: url, annotationView: annotationView!)
            }
        }
        
        return annotationView!
    }
    
    // 创建用户标注视图
    private func createUserAnnotationView(for annotation: MAAnnotation) -> MAAnnotationView {
        let identifier = "UserAnnotationView"
        var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)
        
        if annotationView == nil {
            annotationView = MAAnnotationView(annotation: annotation, reuseIdentifier: identifier)
            annotationView?.image = createUserLocationIcon()
        }
        
        return annotationView!
    }
    
    // 创建用户位置图标
    private func createUserLocationIcon() -> UIImage {
        let size = CGSize(width: 40, height: 40)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            let rect = CGRect(origin: .zero, size: size)
            let ctx = context.cgContext
            
            // 绘制阴影
            ctx.setShadow(offset: CGSize(width: 0, height: 2), blur: 4, color: UIColor.black.withAlphaComponent(0.3).cgColor)
            
            // 绘制主背景圆
            ctx.setFillColor(UIColor.systemBlue.cgColor)
            ctx.fillEllipse(in: rect.insetBy(dx: 2, dy: 2))
            
            // 重置阴影
            ctx.setShadow(offset: .zero, blur: 0, color: nil)
            
            // 绘制白色边框
            ctx.setStrokeColor(UIColor.white.cgColor)
            ctx.setLineWidth(2.0)
            ctx.strokeEllipse(in: rect.insetBy(dx: 3, dy: 3))
            
            // 尝试使用SF Symbols图标，如果失败则使用自定义绘制
            if #available(iOS 13.0, *) {
                // 使用SF Symbols的house.fill图标
                if let houseSymbol = UIImage(systemName: "house.fill") {
                    let symbolSize = CGSize(width: 20, height: 20)
                    let symbolRect = CGRect(
                        x: (size.width - symbolSize.width) / 2,
                        y: (size.height - symbolSize.height) / 2,
                        width: symbolSize.width,
                        height: symbolSize.height
                    )
                    
                    let whiteSymbol = houseSymbol.withTintColor(.white, renderingMode: .alwaysOriginal)
                    whiteSymbol.draw(in: symbolRect)
                } else {
                    drawCustomHouseIcon(in: ctx, rect: rect)
                }
            } else {
                drawCustomHouseIcon(in: ctx, rect: rect)
            }
        }
    }
    
    // 绘制自定义房屋图标
    private func drawCustomHouseIcon(in ctx: CGContext, rect: CGRect) {
        ctx.setFillColor(UIColor.white.cgColor)
        
        let houseRect = rect.insetBy(dx: 8, dy: 8)
        
        // 房屋主体（矩形）
        let houseBodyRect = CGRect(
            x: houseRect.minX + 4,
            y: houseRect.midY,
            width: houseRect.width - 8,
            height: houseRect.height - houseRect.height/3
        )
        ctx.fill(houseBodyRect)
        
        // 屋顶（三角形）
        let roofPath = CGMutablePath()
        roofPath.move(to: CGPoint(x: houseRect.minX, y: houseRect.midY))
        roofPath.addLine(to: CGPoint(x: houseRect.midX, y: houseRect.minY + 2))
        roofPath.addLine(to: CGPoint(x: houseRect.maxX, y: houseRect.midY))
        roofPath.closeSubpath()
        
        ctx.addPath(roofPath)
        ctx.fillPath()
        
        // 门（小矩形）
        let doorRect = CGRect(
            x: houseBodyRect.midX - 2,
            y: houseBodyRect.maxY - 6,
            width: 4,
            height: 6
        )
        ctx.setFillColor(UIColor.systemBlue.cgColor)
        ctx.fill(doorRect)
    }
    
    // 加载餐厅logo
    private func loadRestaurantLogo(url: URL, annotationView: MAAnnotationView) {
        // 添加超时时间
        var request = URLRequest(url: url)
        request.timeoutInterval = 10.0
        
        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            DispatchQueue.main.async {
                guard let self = self else { return }
                
                if let error = error {
                    print("餐厅logo加载失败: \(error.localizedDescription)")
                    return  // 保持默认图标
                }
                
                if let data = data, let image = UIImage(data: data) {
                    // 成功加载图片，创建圆形图标
                    let processedImage = self.createCircularRestaurantIcon(from: image)
                    annotationView.image = processedImage
                }
            }
        }.resume()
    }
    
    // 创建默认餐厅图标
    private func createDefaultRestaurantIcon() -> UIImage {
        let size = CGSize(width: 40, height: 40)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            let rect = CGRect(origin: .zero, size: size)
            let ctx = context.cgContext
            
            // 绘制阴影
            ctx.setShadow(offset: CGSize(width: 0, height: 2), blur: 4, color: UIColor.black.withAlphaComponent(0.3).cgColor)
            
            // 绘制背景圆形
            ctx.setFillColor(UIColor.systemRed.cgColor)
            ctx.fillEllipse(in: rect.insetBy(dx: 2, dy: 2))
            
            // 重置阴影
            ctx.setShadow(offset: .zero, blur: 0, color: nil)
            
            // 绘制白色边框
            ctx.setStrokeColor(UIColor.white.cgColor)
            ctx.setLineWidth(2.0)
            ctx.strokeEllipse(in: rect.insetBy(dx: 3, dy: 3))
            
            // 尝试使用SF Symbols图标
            if #available(iOS 13.0, *) {
                if let restaurantSymbol = UIImage(systemName: "fork.knife") {
                    let symbolSize = CGSize(width: 18, height: 18)
                    let symbolRect = CGRect(
                        x: (size.width - symbolSize.width) / 2,
                        y: (size.height - symbolSize.height) / 2,
                        width: symbolSize.width,
                        height: symbolSize.height
                    )
                    
                    let whiteSymbol = restaurantSymbol.withTintColor(.white, renderingMode: .alwaysOriginal)
                    whiteSymbol.draw(in: symbolRect)
                } else {
                    drawCustomRestaurantIcon(in: ctx, rect: rect)
                }
            } else {
                drawCustomRestaurantIcon(in: ctx, rect: rect)
            }
        }
    }
    
    // 绘制自定义餐厅图标
    private func drawCustomRestaurantIcon(in ctx: CGContext, rect: CGRect) {
        ctx.setFillColor(UIColor.white.cgColor)
        
        let iconRect = rect.insetBy(dx: 10, dy: 10)
        
        // 简化的餐具图标 - 叉子和刀
        let forkX = iconRect.minX + iconRect.width * 0.3
        let knifeX = iconRect.minX + iconRect.width * 0.7
        
        // 叉子（三条竖线）
        for i in 0..<3 {
            let lineX = forkX + CGFloat(i) * 2
            let forkRect = CGRect(x: lineX, y: iconRect.minY, width: 1, height: iconRect.height * 0.6)
            ctx.fill(forkRect)
        }
        
        // 叉子柄
        let forkHandleRect = CGRect(x: forkX + 1, y: iconRect.minY + iconRect.height * 0.6, width: 1, height: iconRect.height * 0.4)
        ctx.fill(forkHandleRect)
        
        // 刀
        let knifeBladeRect = CGRect(x: knifeX, y: iconRect.minY, width: 2, height: iconRect.height * 0.7)
        ctx.fill(knifeBladeRect)
        
        // 刀柄
        let knifeHandleRect = CGRect(x: knifeX, y: iconRect.minY + iconRect.height * 0.7, width: 2, height: iconRect.height * 0.3)
        ctx.fill(knifeHandleRect)
    }
    
    // 创建圆形餐厅图标
    private func createCircularRestaurantIcon(from image: UIImage) -> UIImage {
        let targetSize = CGSize(width: 40, height: 40)
        let renderer = UIGraphicsImageRenderer(size: targetSize)
        
        return renderer.image { context in
            let rect = CGRect(origin: .zero, size: targetSize)
            let ctx = context.cgContext
            
            // 绘制阴影
            ctx.setShadow(offset: CGSize(width: 0, height: 2), blur: 4, color: UIColor.black.withAlphaComponent(0.3).cgColor)
            
            // 创建圆形裁剪路径
            let imageRect = rect.insetBy(dx: 2, dy: 2)
            ctx.addEllipse(in: imageRect)
            ctx.clip()
            
            // 绘制图片
            image.draw(in: imageRect)
            
            // 重置阴影和裁剪
            ctx.resetClip()
            ctx.setShadow(offset: .zero, blur: 0, color: nil)
            
            // 绘制白色边框
            ctx.setStrokeColor(UIColor.white.cgColor)
            ctx.setLineWidth(2.0)
            ctx.strokeEllipse(in: rect.insetBy(dx: 3, dy: 3))
        }
    }
}

// MARK: - Factory和Plugin
public class OrderMapViewFactory: NSObject, FlutterPlatformViewFactory {
    public func create(withFrame frame: CGRect, viewIdentifier viewId: Int64, arguments args: Any?) -> FlutterPlatformView {
        return OrderMapViewController(frame: frame, viewIdentifier: viewId, arguments: args)
    }
    
    public func createArgsCodec() -> FlutterMessageCodec & NSObjectProtocol {
        return FlutterStandardMessageCodec.sharedInstance()
    }
}

public class OrderMapViewPlugin: NSObject, FlutterPlugin {
    public static func register(with registrar: FlutterPluginRegistrar) {
        let factory = OrderMapViewFactory()
        registrar.register(factory, withId: "plugin/order-map")
    }
}
