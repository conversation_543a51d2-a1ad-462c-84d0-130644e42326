//
//  JPushManager.swift
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/26.
//

import Foundation
import UIKit
import Flutter
import UserNotifications

@available(iOS 13.0.0, *)
class JPushManager: NSObject, JPUSHRegisterDelegate {
    
    // MARK: - 单例
    static let shared = JPushManager()
    
    // MARK: - 属性
    private var methodChannel: FlutterMethodChannel?
    private var flutterViewController: FlutterViewController?
    
    // MARK: - 初始化
    override init() {
        super.init()
    }
    
    // MARK: - 配置方法
    /// 配置JPush和Flutter方法通道
    func configure(with flutterViewController: FlutterViewController, launchOptions: [UIApplication.LaunchOptionsKey: Any]?) {
        self.flutterViewController = flutterViewController
        
        // 设置方法通道
        setupMethodChannel()
        
        // 设置JPush
        setupJPush(with: launchOptions)
    }
    
    /// 设置Flutter方法通道
    private func setupMethodChannel() {
        guard let controller = flutterViewController else { return }
        
        methodChannel = FlutterMethodChannel(
            name: "com.almas.dinner",
            binaryMessenger: controller.binaryMessenger
        )
        
        methodChannel?.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
            self?.handleMethodCall(call, result: result)
        }
    }
    
    /// 设置JPush
    private func setupJPush(with launchOptions: [UIApplication.LaunchOptionsKey: Any]?) {
        // 设置 JPush
        let entity = JPUSHRegisterEntity()
        entity.types = Int(JPAuthorizationOptions.alert.rawValue) | 
                      Int(JPAuthorizationOptions.badge.rawValue) | 
                      Int(JPAuthorizationOptions.sound.rawValue) | 
                      Int(JPAuthorizationOptions.providesAppNotificationSettings.rawValue)
        
        JPUSHService.setDebugMode()
        JPUSHService.register(forRemoteNotificationConfig: entity, delegate: self)
        JPUSHService.setup(withOption: launchOptions, appKey: "3326fe4b331a003902154e24", channel: "theChannel", apsForProduction: true)
    }
    
    // MARK: - 方法通道处理
    private func handleMethodCall(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "registerJPush":
            let regid: String = JPUSHService.registrationID()
            result(regid)
            
        case "checkAuthorization":
            checkNotificationAuthorization(result: result)
            
        case "checkCameraPermission":
            PermissionManager.checkCameraPermission { granted in
                result(granted)
            }
            
        case "checkPhotoLibraryPermission":
            PermissionManager.checkPhotoLibraryPermission { granted in
                result(granted)
            }
            
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    /// 检查通知授权状态
    private func checkNotificationAuthorization(result: @escaping FlutterResult) {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                switch settings.authorizationStatus {
                case .authorized:
                    result("Authorized")
                case .denied:
                    result("Denied")
                case .notDetermined:
                    result("Not determined")
                case .provisional:
                    result("Provisional")
                @unknown default:
                    result("Unknown")
                }
            }
        }
    }
    
    // MARK: - 应用生命周期方法
    func applicationWillEnterForeground() {
        if #available(iOS 16.0, *) {
            UNUserNotificationCenter.current().setBadgeCount(0)
        } else {
            UIApplication.shared.applicationIconBadgeNumber = 0
        }
    }
    
    func didRegisterForRemoteNotificationsWithDeviceToken(_ deviceToken: Data) {
        JPUSHService.registerDeviceToken(deviceToken)
    }
    
    func didFailToRegisterForRemoteNotificationsWithError(_ error: Error) {
        print("Failed to register for remote notifications with error: \(error.localizedDescription)")
    }
    
    func didReceiveRemoteNotification(_ userInfo: [AnyHashable: Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        print("Hello push 222")
        JPUSHService.handleRemoteNotification(userInfo)
        completionHandler(.newData)
    }
}

// MARK: - JPUSHRegisterDelegate
@available(iOS 13.0.0, *)
extension JPushManager {
    
    /// 用户点击通知后的处理
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo
        
        // 打印接收到的推送数据，用于调试
        print("Received userInfo:", userInfo)
        
        // 检查userInfo中是否存在page
        guard let page = userInfo["page"] else {
            print("Required keys (page) not found.")
            completionHandler()
            return
        }
        
        // 处理不同的页面跳转
        if let pageString = userInfo["page"] as? String {
            switch pageString {
            case "chat_page":
                methodChannel?.invokeMethod("callFlutterFunction", arguments: [
                    "page": page,
                    "order_id": userInfo["order_id"] ?? 0
                ])
                
            case "group_marketing_detail":
                methodChannel?.invokeMethod("callFlutterFunction", arguments: [
                    "page": page,
                    "id": userInfo["id"] ?? 0
                ])
                
            default:
                print("Unknown page type: \(pageString)")
            }
        }
        
        // 处理完毕后调用completionHandler
        completionHandler()
    }
    
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, openSettingsFor notification: UNNotification) {
        print("Hello push - open settings")
    }
    
    func jpushNotificationAuthorization(_ status: JPAuthorizationStatus, withInfo info: [AnyHashable: Any]?) {
        print("JPush authorization status: \(status)")
    }
    
    /// 处理前台收到通知的回调
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        print("Hello push 444 - will present notification")
        completionHandler([.alert, .badge, .sound])
    }
    
    /// iOS 15+ 的通知处理方法
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification) async -> Int {
        print("Hello push 666 - async will present")
        let userInfo = notification.request.content.userInfo
        JPUSHService.handleRemoteNotification(userInfo)
        return Int(JPAuthorizationOptions.alert.rawValue) | 
               Int(JPAuthorizationOptions.badge.rawValue) | 
               Int(JPAuthorizationOptions.sound.rawValue) | 
               Int(JPAuthorizationOptions.providesAppNotificationSettings.rawValue)
    }
}
