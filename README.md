# 订单价格计算 
# （餐厅页面）
- 秒杀价格
- 优惠价格
- 美食原价

tamak etibari = 优惠 + 秒杀 


# 活动类型
- 减配送费活动
- 满减活动
- 转盘活动（抽奖活动）
- 优惠卷


# 配送费价格计算流程
1、秒杀价格（如果有秒杀活动）
2、优惠价格（如果有优惠活动）
3、原价（超出优惠或秒杀数量的部分）

[x] 4、满减优惠计算
[x] 5、减配送费计算


### 计算进度
[x] 配送费

[x] 饭盒费用

[x] 减配送费

// 直接使用ConnectionStatus
final connectionStatus = ConnectionStatus();
final isConnected = await connectionStatus.checkConnection();

// 或者使用Provider（推荐）
final isConnected = ref.watch(connectionStateProvider);
if (isConnected) {
  // 有网络连接时的逻辑
} else {
  // 无网络连接时的逻辑
}