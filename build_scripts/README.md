# 🚀 Flutter 通用构建脚本

强大的多环境、多平台自动化构建工具，支持 Android 和 iOS 平台的各种环境构建。

## ✨ 功能特性

- 🎯 **多环境支持**: development、staging、production
- 📱 **多平台支持**: Android APK、iOS IPA
- 🔧 **多构建模式**: debug、profile、release
- 📦 **智能文件命名**: 根据应用名称、环境和版本自动命名
- 📁 **自动打开目录**: 构建完成后自动打开输出目录
- 🎨 **彩色输出**: 美观的命令行界面和日志
- ⏱️ **构建统计**: 显示构建时间和文件大小
- 🛡️ **参数验证**: 智能参数验证和错误提示

## 🚀 快速开始

### macOS/Linux
```bash
# 给脚本添加执行权限
chmod +x build_scripts/build.sh

# 使用默认参数构建 (android staging release)
./build_scripts/build.sh

# 构建测试环境 Android 版本
./build_scripts/build.sh android staging release

# 构建生产环境 iOS 版本
./build_scripts/build.sh ios production release

# 构建所有平台
./build_scripts/build.sh all staging release
```

### Windows
```cmd
# 使用默认参数构建
build_scripts\build.bat

# 构建测试环境 Android 版本
build_scripts\build.bat android staging release

# 构建所有平台
build_scripts\build.bat all staging release
```

## 📋 命令格式

```bash
./build.sh [platform] [environment] [build_mode]
```

### 参数说明

| 参数 | 选项 | 描述 | 默认值 |
|------|------|------|--------|
| **platform** | `android`\|`ios`\|`all` | 构建平台 | `android` |
| **environment** | `development`\|`staging`\|`production` | 目标环境 | `staging` |
| **build_mode** | `debug`\|`profile`\|`release` | 构建模式 | `release` |

### 帮助信息
```bash
./build.sh --help
# 或
./build.sh -h
```

## 🌍 环境配置

### 🔧 Development (开发环境)
- **API地址**: `https://smart.d.almas.biz/`
- **Socket地址**: `wss://chat-socket.d.almas.biz`
- **日志启用**: `true`
- **微信支付**: `preview`
- **用途**: 本地开发和调试

### 🧪 Staging (测试环境)
- **API地址**: `https://smart.d.almas.biz/`
- **Socket地址**: `wss://chat-socket.d.almas.biz`
- **日志启用**: `true`
- **微信支付**: `preview`
- **用途**: 测试版本发布

### 🚀 Production (生产环境)
- **API地址**: `https://smart.mulazim.com/`
- **Socket地址**: `wss://chat-socket.mulazim.com`
- **日志启用**: `false`
- **微信支付**: `release`
- **用途**: 正式版本发布

## 📁 输出文件

### 文件命名规则
```
{APP_NAME}_{ENVIRONMENT}_v{VERSION}.{EXTENSION}
```

**示例**:
- `mulazim_staging_v3.1.7.apk`
- `mulazim_production_v3.1.7.ipa`

### 输出位置

#### Android APK
- **原始位置**: `build/app/outputs/flutter-apk/app-release.apk`
- **重命名位置**: `build/outputs/mulazim_staging_v3.1.7.apk`

#### iOS IPA
- **输出位置**: `build/outputs/mulazim_production_v3.1.7.ipa`

## 💡 使用示例

### 常用场景

#### 1. 给测试人员打包
```bash
# 快速构建测试版本（使用默认参数）
./build.sh

# 或明确指定
./build.sh android staging release
```

#### 2. 正式版本发布
```bash
# Android 生产版本
./build.sh android production release

# iOS 生产版本（仅限 macOS）
./build.sh ios production release

# 所有平台生产版本
./build.sh all production release
```

#### 3. 开发版本调试
```bash
# 开发环境 Debug 版本
./build.sh android development debug

# 开发环境 Profile 版本（用于性能分析）
./build.sh android development profile
```

### 高级用法

#### 批量构建不同环境
```bash
# 构建所有环境的 Android 版本
for env in development staging production; do
    ./build.sh android $env release
done
```

#### CI/CD 集成
```bash
# Jenkins/GitHub Actions 中使用
./build.sh android production release
```

## 🔍 构建日志

脚本提供详细的构建日志，包括：

- 📝 **构建信息**: 应用名称、版本、环境等
- ⏱️ **时间戳**: 每个步骤的时间记录
- 📊 **文件信息**: 文件大小、路径等
- 📈 **构建统计**: 总耗时、成功/失败状态

### 日志示例
```
[STEP] 2024-01-15 10:30:00 - 准备构建环境...
[INFO] 2024-01-15 10:30:05 - 执行命令: flutter build apk --release --dart-define=ENV=staging
[SUCCESS] 2024-01-15 10:32:30 - Android APK 构建成功
[INFO] 2024-01-15 10:32:31 - APK 文件大小: 25.6M
```

## 🛠️ 系统要求

### 通用要求
- Flutter SDK (推荐最新稳定版)
- Dart SDK
- 项目在 Flutter 项目根目录执行

### Android 构建
- Android SDK
- Android 工具链配置

### iOS 构建 (仅限 macOS)
- Xcode
- iOS 工具链配置
- CocoaPods

## 🔧 故障排除

### 常见问题

#### 1. 权限问题 (macOS/Linux)
```bash
chmod +x build_scripts/build.sh
```

#### 2. 找不到 Flutter 命令
确保 Flutter 已添加到 PATH 环境变量

#### 3. Android 构建失败
检查 Android SDK 和工具链配置

#### 4. iOS 构建失败 (macOS)
检查 Xcode 和 CocoaPods 安装

#### 5. 环境参数不生效
确保使用了 `--dart-define=ENV=xxx` 参数

### 调试模式
如需详细的构建日志，可以在 Flutter 命令后添加 `--verbose` 参数：

修改脚本中的构建命令：
```bash
flutter build apk --release --dart-define=ENV=staging --verbose
```

## 🎯 最佳实践

1. **环境隔离**: 不同环境使用不同的配置，避免混淆
2. **版本管理**: 定期更新 `pubspec.yaml` 中的版本号
3. **自动化**: 集成到 CI/CD 流水线中
4. **测试**: 构建前确保代码测试通过
5. **文档**: 记录每次发布的变更内容

## 🔄 更新日志

### v1.0.0 (2024-01-15)
- ✨ 初始版本发布
- 🎯 支持多环境、多平台构建
- 📦 智能文件命名和目录管理
- 🎨 美观的命令行界面
- 📊 详细的构建统计和日志

---

> 💡 **提示**: 如有问题或建议，请联系开发团队或查看项目文档。 