# Riverpod 2.6.1 开发规范与最佳实践

## 一、概述

Riverpod 是一个反应式缓存和数据绑定框架，它是 Provider 包的完全重写版本，旨在提供更好的类型安全性、可测试性和可维护性。Riverpod 2.6.1 带来了重大改进，包括简化的 API 和代码生成能力。本文档旨在为团队提供统一的 Riverpod 2.6.1 开发规范和最佳实践指南。

## 二、Provider 类型选择指南

### 1. Provider

```dart
// 方式一：常规定义
final apiClientProvider = Provider<ApiClient>((ref) {
  return ApiClient();
});

// 方式二：使用代码生成（Riverpod 2.0推荐）
@riverpod
ApiClient apiClient(ApiClientRef ref) {
  return ApiClient();
}
```

**适用场景**: 服务实例化、常量、配置、计算值

### 2. StateProvider

```dart
// 方式一：常规定义
final counterProvider = StateProvider<int>((ref) {
  return 0;
});

// 方式二：使用代码生成（Riverpod 2.0推荐）
@riverpod
class Counter extends _$Counter {
  @override
  int build() => 0;
  
  void increment() => state++;
  void decrement() => state--;
}
```

**适用场景**: 简单状态、简单表单字段、开关状态

### 3. FutureProvider

```dart
// 方式一：常规定义
final userProvider = FutureProvider.autoDispose<User>((ref) async {
  final repository = ref.watch(userRepositoryProvider);
  return repository.getUser();
});

// 方式二：使用代码生成（Riverpod 2.0推荐）
@riverpod
Future<User> user(UserRef ref) async {
  final repository = ref.watch(userRepositoryProvider);
  return repository.getUser();
}
```

**适用场景**: API请求、数据加载、异步初始化

### 4. StreamProvider

```dart
// 方式一：常规定义
final messagesProvider = StreamProvider<List<Message>>((ref) {
  final repository = ref.watch(messageRepositoryProvider);
  return repository.getMessages();
});

// 方式二：使用代码生成（Riverpod 2.0推荐）
@riverpod
Stream<List<Message>> messages(MessagesRef ref) {
  final repository = ref.watch(messageRepositoryProvider);
  return repository.getMessages();
}
```

**适用场景**: WebSocket连接、数据变更监听、长连接推送、本地数据流、传感器数据

### 5. AsyncNotifier (Riverpod 2.0新增)

```dart
// 使用代码生成
@riverpod
class TodoList extends _$TodoList {
  @override
  FutureOr<List<Todo>> build() async {
    final repository = ref.watch(todoRepositoryProvider);
    return repository.getTodos();
  }
  
  Future<void> addTodo(Todo todo) async {
    // AsyncValue的使用简化了异步状态处理
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final repository = ref.read(todoRepositoryProvider);
      final newTodo = await repository.addTodo(todo);
      return [...?state.valueOrNull, newTodo];
    });
  }
}
```

**适用场景**: 需要管理异步状态的复杂业务逻辑

### 6. Notifier (替代StateNotifier)

```dart
// Riverpod 2.0推荐，用于替代StateNotifierProvider
@riverpod
class Cart extends _$Cart {
  @override
  List<Product> build() {
    return [];
  }
  
  void addProduct(Product product) {
    state = [...state, product];
  }
  
  void removeProduct(String productId) {
    state = state.where((p) => p.id != productId).toList();
  }
  
  double get totalPrice => 
    state.fold(0, (total, product) => total + product.price);
}
```

**适用场景**: 复杂状态管理，购物车、多步表单

## 三、命名规范

### 1. Provider命名

使用代码生成时:
- 函数式Provider: 使用描述性小写名称，如 `user`, `messages`
- 类式Provider: 使用PascalCase命名，如 `Cart`, `AuthController`

使用常规定义时:
- 一律使用 `xxxProvider` 格式，如 `userProvider`, `cartProvider`

### 2. Ref命名

代码生成会自动创建ref类型:
- 函数式Provider: `XxxRef`，如 `UserRef`
- 类式Provider: `_$Xxx`，如 `_$Cart`

### 3. State命名

- 状态类: `XxxState`
- 存储在provider中的状态直接命名为 `state`(Riverpod 2.0语法)

## 四、代码规范

### 1. 使用代码生成简化Provider定义

设置好以下依赖:
```yaml
dependencies:
  flutter:
    sdk: flutter
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1

dev_dependencies:
  build_runner: ^2.4.8
  custom_lint: ^2.0.0
  riverpod_generator: ^2.6.5
  riverpod_lint: ^2.6.5
```

启用代码生成和代码规范检查:

在项目的 `analysis_options.yaml` 中添加:
```yaml
analyzer:
  plugins:
    - custom_lint

custom_lint:
  rules:
    - riverpod_lint
```

启用代码生成:
```dart
import 'package:riverpod_annotation/riverpod_annotation.dart';

// 必须添加这一行，指向生成的文件
part 'file_name.g.dart';

// 函数式Provider
@riverpod
User user(UserRef ref) {
  return User('default');
}

// 类式Provider
@riverpod
class Counter extends _$Counter {
  @override
  int build() => 0;
  
  void increment() => state++;
}
```

运行代码生成:
```bash
flutter pub run build_runner build
# 或监听文件更改自动生成
flutter pub run build_runner watch
```

### 2. 使用不可变状态

```dart
// 推荐使用freezed进行不可变类定义
@freezed
class UserState with _$UserState {
  const factory UserState({
    User? user,
    @Default(false) bool isLoading,
    String? error,
  }) = _UserState;
}

// 在Notifier中使用
@riverpod
class UserController extends _$UserController {
  @override
  UserState build() => const UserState();
  
  Future<void> login(String username, String password) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final user = await ref.read(authRepositoryProvider).login(
        username, 
        password,
      );
      state = state.copyWith(user: user, isLoading: false);
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
      );
    }
  }
}
```

### 3. 使用AsyncValue处理异步状态

```dart
// Riverpod 2.0提供了AsyncValue简化异步状态管理
@riverpod
class Products extends _$Products {
  @override
  FutureOr<List<Product>> build() {
    return _fetchProducts();
  }
  
  Future<List<Product>> _fetchProducts() async {
    final repository = ref.watch(productRepositoryProvider);
    return repository.getProducts();
  }
  
  Future<void> addProduct(Product product) async {
    // AsyncValue.guard 简化了异步错误处理
    state = await AsyncValue.guard(() async {
      final repository = ref.read(productRepositoryProvider);
      await repository.addProduct(product);
      return [...?state.valueOrNull, product];
    });
  }
}
```

### 4. 使用消费者组件

```dart
// 常规消费Provider
class ProductScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 使用 ref.watch 响应式监听
    final products = ref.watch(productsProvider);
    
    return products.when(
      data: (data) => ProductList(products: data),
      loading: () => const CircularProgressIndicator(),
      error: (error, stack) => ErrorWidget(message: error.toString()),
    );
  }
}

// 需要StatefulWidget功能时
class ProductFormScreen extends ConsumerStatefulWidget {
  @override
  ConsumerState<ProductFormScreen> createState() => _ProductFormScreenState();
}

class _ProductFormScreenState extends ConsumerState<ProductFormScreen> {
  late final TextEditingController _nameController;
  
  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    // ...
  }
}
```

## 五、最佳实践

### 1. 使用 ref.watch、ref.read 和 ref.listen

```dart
// ✅ 在build方法中使用watch获取最新值
@override
Widget build(BuildContext context, WidgetRef ref) {
  // 自动在值变化时重建widget
  final user = ref.watch(userProvider);
  return Text(user.name);
}

// ✅ 在事件处理器中使用read获取当前值和方法
ElevatedButton(
  onPressed: () {
    // 只获取provider的值或方法，不建立依赖
    ref.read(counterProvider.notifier).increment();
  },
  child: const Text('Increment'),
)

// ✅ 使用listen监听变化并响应
@override
Widget build(BuildContext context, WidgetRef ref) {
  // 当auth状态变化时执行副作用
  ref.listen<AsyncValue<User?>>(authProvider, (previous, next) {
    next.whenData((user) {
      if (user != null) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    });
  });
  // ...
}
```

### 2. 使用修饰符控制生命周期

```dart
// 自动处理Provider的生命周期
@riverpod
@Riverpod(keepAlive: false) // 等同于.autoDispose
Future<User> user(UserRef ref) async {
  // 会在不再使用时自动释放
  return fetchUser();
}

// 保持存活
@riverpod
@Riverpod(keepAlive: true) // 等同于常规provider
Future<Settings> settings(SettingsRef ref) async {
  return fetchSettings();
}

// 代码中动态控制
@riverpod
Future<User> user(UserRef ref) async {
  // 手动控制生命周期
  ref.keepAlive(); // 防止自动销毁
  return fetchUser();
}
```

### 3. 传递参数

```dart
// 方法1: 使用代码生成的family功能
@riverpod
Future<User> user(UserRef ref, {required String id}) async {
  final repository = ref.watch(userRepositoryProvider);
  return repository.getUser(id);
}

// 使用方式
final user = ref.watch(userProvider(id: '123'));

// 方法2: 对于复杂参数
@riverpod
Future<SearchResults> search(
  SearchRef ref, {
  required String query,
  required int page,
  required int pageSize,
  String? sortBy,
}) async {
  final repository = ref.watch(searchRepositoryProvider);
  return repository.search(query, page, pageSize, sortBy: sortBy);
}

// 使用方式
final results = ref.watch(searchProvider(
  query: 'flutter',
  page: 1,
  pageSize: 20,
  sortBy: 'date',
));
```

### 4. 缓存控制

```dart
// 强制刷新
ref.invalidate(userProvider); // 完全重建
ref.refresh(userProvider); // 返回新值

// 在特定条件下刷新
@riverpod
Future<Weather> weather(WeatherRef ref, {required String city}) async {
  // 每小时自动刷新天气
  final cacheKey = '${city}_${DateTime.now().hour}';
  ref.keepAliveFor(const Duration(hours: 1));
  
  // 设置缓存存活时间
  final repository = ref.watch(weatherRepositoryProvider);
  return repository.getWeather(city);
}
```

### 5. 使用ref.select优化性能

```dart
// 只监听状态的一部分变化
@override
Widget build(BuildContext context, WidgetRef ref) {
  // 只有当用户名变化时才会重建
  final username = ref.watch(
    userProvider.select((user) => user.valueOrNull?.name)
  );
  
  // 只有当错误状态变化时才会重建
  final hasError = ref.watch(
    userProvider.select((user) => user.hasError)
  );
  
  return Text(username ?? 'Guest');
}
```

### 6. 组合多个Provider

```dart
// 创建派生状态
@riverpod
List<Todo> filteredTodos(FilteredTodosRef ref) {
  final todos = ref.watch(todosProvider);
  final filter = ref.watch(filterProvider);
  
  // 使用Dart 3.0模式匹配语法
  return switch (todos) {
    AsyncData(:final value) => switch (filter) {
      'completed' => value.where((todo) => todo.completed).toList(),
      'active' => value.where((todo) => !todo.completed).toList(),
      _ => value,
    },
    _ => [], // 处理loading和error状态
  };
}
```

### 7. 异步状态管理的简化模式

```dart
// 使用AsyncValue.when处理所有状态
@override
Widget build(BuildContext context, WidgetRef ref) {
  final products = ref.watch(productsProvider);
  
  // Riverpod 2.0推荐的异步状态处理方式
  return products.when(
    data: (data) => ProductList(products: data),
    loading: () => const Center(child: CircularProgressIndicator()),
    error: (error, stack) => ErrorView(
      message: error.toString(),
      onRetry: () => ref.refresh(productsProvider),
    ),
  );
  
  // 或者使用whenData简化处理
  return products.whenData(
    (data) => ProductList(products: data),
  ).valueOrNull ?? const Center(child: CircularProgressIndicator());
}
```

### 8. 测试

```dart
// 测试代码生成的provider
void main() {
  test('userProvider 返回正确的用户信息', () async {
    final container = ProviderContainer(
      overrides: [
        // 覆盖依赖
        userRepositoryProvider.overrideWithValue(MockUserRepository())
      ],
    );
    addTearDown(container.dispose);
    
    // 首次读取会触发加载状态
    expect(container.read(userProvider), const AsyncValue<User>.loading());
    
    // 等待异步操作完成
    await container.read(userProvider.future);
    
    // 验证数据状态
    expect(container.read(userProvider).valueOrNull?.name, 'Test User');
  });
  
  test('counterProvider 正确递增', () {
    final container = ProviderContainer();
    addTearDown(container.dispose);
    
    expect(container.read(counterProvider), 0);
    
    // 调用notifier方法
    container.read(counterProvider.notifier).increment();
    
    expect(container.read(counterProvider), 1);
  });
}
```

## 六、注意事项 (DO/DON'T)

### Do:

- ✅ 使用代码生成简化Provider定义 (`@riverpod`)
- ✅ 使用不可变的状态对象 (使用`freezed`或记录类型)
- ✅ 为异步操作使用`AsyncValue`和`.when()`模式
- ✅ 使用`ref.select`优化性能，只监听需要的状态部分
- ✅ 使用`ref.listen`响应状态变化执行副作用
- ✅ 使用适当的生命周期控制策略 (keepAlive, autoDispose)
- ✅ 在UI层使用Riverpod的Consumer组件
- ✅ 为所有Provider编写测试

### Don't:

- ❌ 在build方法外使用`ref.watch`
- ❌ 在无状态服务中注入状态
- ❌ 创建循环依赖
- ❌ 在provider中直接执行副作用
- ❌ 过度使用全局状态
- ❌ 忽略状态的不可变性
- ❌ 在widget中包含复杂业务逻辑
- ❌ 忽略Riverpod的警告和依赖错误

## 七、案例实践: Todo应用 (Riverpod 2.0版本)

下面是一个使用Riverpod 2.0的Todo应用实现:

### 1. 模型定义

```dart
// todo_model.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'todo_model.freezed.dart';
part 'todo_model.g.dart';

@freezed
class Todo with _$Todo {
  const factory Todo({
    required String id,
    required String title,
    @Default(false) bool completed,
  }) = _Todo;

  factory Todo.fromJson(Map<String, dynamic> json) => _$TodoFromJson(json);
}
```

### 2. 仓库实现

```dart
// todo_repository.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:data/models/todo_model.dart';

part 'todo_repository.g.dart';

class TodoRepository {
  Future<List<Todo>> getTodos() async {
    // 模拟API调用
    await Future.delayed(const Duration(seconds: 1));
    return [
      Todo(id: '1', title: '学习Flutter'),
      Todo(id: '2', title: '学习Riverpod 2.0'),
    ];
  }
  
  Future<Todo> addTodo(String title) async {
    // 模拟API调用
    await Future.delayed(const Duration(milliseconds: 300));
    return Todo(id: DateTime.now().toString(), title: title);
  }
  
  Future<void> toggleTodo(String id) async {
    // 模拟API调用
    await Future.delayed(const Duration(milliseconds: 300));
  }
}

@riverpod
TodoRepository todoRepository(TodoRepositoryRef ref) {
  return TodoRepository();
}
```

### 3. 控制器实现 (Riverpod 2.0)

```dart
// todo_controller.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:data/models/todo_model.dart';
import 'package:data/repositories/todo_repository.dart';

part 'todo_controller.g.dart';

@riverpod
class TodoList extends _$TodoList {
  @override
  FutureOr<List<Todo>> build() {
    return _fetchTodos();
  }
  
  Future<List<Todo>> _fetchTodos() async {
    final repository = ref.watch(todoRepositoryProvider);
    return repository.getTodos();
  }
  
  Future<void> addTodo(String title) async {
    final repository = ref.watch(todoRepositoryProvider);
    
    // 使用AsyncValue.guard简化异步错误处理
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final newTodo = await repository.addTodo(title);
      return [...?state.valueOrNull, newTodo];
    });
  }
  
  Future<void> toggleTodo(String id) async {
    final repository = ref.watch(todoRepositoryProvider);
    
    // 乐观更新UI
    state = await AsyncValue.guard(() async {
      final newState = [...?state.valueOrNull];
      final index = newState.indexWhere((todo) => todo.id == id);
      if (index >= 0) {
        newState[index] = newState[index].copyWith(
          completed: !newState[index].completed,
        );
      }
      
      // 然后进行API调用
      await repository.toggleTodo(id);
      return newState;
    });
  }
}

@riverpod
class TodoFilter extends _$TodoFilter {
  @override
  String build() => 'all'; // all, active, completed
  
  void setFilter(String filter) => state = filter;
}

@riverpod
List<Todo> filteredTodos(FilteredTodosRef ref) {
  final todos = ref.watch(todoListProvider);
  final filter = ref.watch(todoFilterProvider);
  
  // 使用Dart 3.0模式匹配语法
  return switch (todos) {
    AsyncData(:final value) => switch (filter) {
      'completed' => value.where((todo) => todo.completed).toList(),
      'active' => value.where((todo) => !todo.completed).toList(),
      _ => value,
    },
    _ => [], // 处理loading和error状态
  };
}
```

### 4. 页面实现

```dart
// todo_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:data/controllers/todo_controller.dart';

class TodoPage extends ConsumerWidget {
  const TodoPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听过滤后的待办事项
    final todos = ref.watch(todoListProvider);
    final filter = ref.watch(todoFilterProvider);
    
    // 使用过滤功能
    final filteredList = ref.watch(filteredTodosProvider);
    
    // 处理状态变化
    ref.listen<AsyncValue<List<Todo>>>(todoListProvider, (previous, next) {
      next.whenOrNull(
        error: (error, _) => ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('错误: $error')),
        ),
      );
    });
    
    return Scaffold(
      appBar: AppBar(title: const Text('Todo应用')),
      body: todos.when(
        data: (data) => ListView.builder(
          itemCount: filteredList.length,
          itemBuilder: (context, index) {
            final todo = filteredList[index];
            return ListTile(
              title: Text(todo.title),
              leading: Checkbox(
                value: todo.completed,
                onChanged: (_) {
                  ref.read(todoListProvider.notifier).toggleTodo(todo.id);
                },
              ),
            );
          },
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Error: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(todoListProvider),
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddTodoDialog(context, ref),
        child: const Icon(Icons.add),
      ),
      bottomNavigationBar: BottomAppBar(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _filterButton(context, ref, 'all', '全部'),
            _filterButton(context, ref, 'active', '进行中'),
            _filterButton(context, ref, 'completed', '已完成'),
          ],
        ),
      ),
    );
  }
  
  Widget _filterButton(BuildContext context, WidgetRef ref, String filter, String label) {
    final currentFilter = ref.watch(todoFilterProvider);
    return TextButton(
      onPressed: () => ref.read(todoFilterProvider.notifier).setFilter(filter),
      style: TextButton.styleFrom(
        foregroundColor: currentFilter == filter ? Colors.blue : Colors.grey,
      ),
      child: Text(label),
    );
  }
  
  void _showAddTodoDialog(BuildContext context, WidgetRef ref) {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加新任务'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(hintText: '输入任务名称'),
          autofocus: true,
          onSubmitted: (_) => _addTodo(context, ref, controller),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => _addTodo(context, ref, controller),
            child: const Text('添加'),
          ),
        ],
      ),
    );
  }
  
  void _addTodo(BuildContext context, WidgetRef ref, TextEditingController controller) {
    if (controller.text.isNotEmpty) {
      ref.read(todoListProvider.notifier).addTodo(controller.text);
      Navigator.pop(context);
    }
  }
}
```

## 九、ref.watch 的使用位置最佳实践

在使用 Riverpod 时，关于 `ref.watch` 的放置位置有两种常见策略，根据应用场景选择合适的方式可以优化代码可读性和性能：

#### 策略一：在 build 方法顶部集中声明

```dart
@override
Widget build(BuildContext context, WidgetRef ref) {
  // 集中监听所有状态
  final user = ref.watch(userProvider);
  final cart = ref.watch(cartProvider);
  
  return YourWidget(...);
}
```

**适用场景**：
- 小型组件或页面
- 状态简单且较少的场景
- 组件主要依赖这些状态渲染
- 代码可读性和组织优先于极致性能

#### 策略二：在 UI 结构中内联使用 Consumer

```dart
@override
Widget build(BuildContext context, WidgetRef ref) {
  return Scaffold(
    body: Column(
      children: [
        Consumer(
          builder: (context, ref, child) {
            final user = ref.watch(userProvider.select((u) => u.name));
            return Text(user);
          },
        ),
        // 其他UI元素...
      ],
    ),
  );
}
```

**适用场景**：
- 中大型复杂组件或页面
- 多状态且彼此独立的场景
- 性能优先的场景
- 需要精确控制重建范围的场景

#### 混合策略：结合使用两种方式

在实际开发中，通常建议采用混合策略：
- 对于影响整个页面的全局状态，在 build 方法顶部声明
- 对于只影响特定UI区域的局部状态，使用 Consumer 包装该区域
- 对于可独立的复杂组件，提取为单独的 ConsumerWidget

```dart
@override
Widget build(BuildContext context, WidgetRef ref) {
  // 全局状态在顶部声明
  final theme = ref.watch(themeProvider);
  
  return Scaffold(
    appBar: AppBar(
      backgroundColor: theme.primaryColor,
      title: Text('我的应用'),
    ),
    body: Column(
      children: [
        // 局部状态使用Consumer
        Consumer(
          builder: (context, ref, child) {
            final user = ref.watch(userProvider.select((u) => u.profile));
            return UserProfileCard(user: user);
          },
        ),
        
        // 独立的功能区域
        const ShoppingCartSection(),
        const RecentOrdersSection(),
      ],
    ),
  );
}
```

通过合理选择 `ref.watch` 的使用位置和范围，可以在代码可读性和性能之间取得良好的平衡。

## 九、总结

Riverpod 2.6.1通过代码生成、AsyncValue和更简洁的API极大地改善了状态管理体验。遵循本文档提供的最佳实践可以帮助您:

1. 减少样板代码并提高开发效率
2. 更好地处理异步状态和错误
3. 创建可测试、可维护的代码
4. 分离业务逻辑和UI
5. 高效管理应用状态

## 10、参考资料

- [Riverpod 2.6.1官方文档](https://riverpod.dev/docs)
- [Riverpod代码生成](https://riverpod.dev/docs/concepts/about_code_generation)
- [Flutter App Architecture with Riverpod](https://codewithandrea.com/articles/flutter-app-architecture-riverpod-introduction/)
- [Riverpod 2.6.1: DO/DON'T](https://riverpod.dev/docs/essentials/do_dont) 