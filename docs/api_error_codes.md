# API 错误码文档

本文档详细说明了项目中使用的所有 API 错误码的含义和使用方式。

## 错误码常量

所有错误码都定义在 `ApiErrorCode` 类中，位于 `lib/core/network/models/api_error_code.dart` 文件中。

### 成功状态码

| 错误码 | 常量名 | 含义 |
|-------|-------|-----|
| 200 | SUCCESS | 请求成功 |

### 业务错误状态码

| 错误码 | 常量名 | 含义 |
|-------|-------|-----|
| -1000 | BUSINESS_ERROR | 业务错误，通常是服务器返回的业务逻辑错误 |

### 网络错误状态码

| 错误码 | 常量名 | 含义 |
|-------|-------|-----|
| 1001 | NETWORK_ERROR | 网络连接错误，通常是网络不可用或无法连接到服务器 |
| 1002 | TIMEOUT_ERROR | 请求超时，通常是网络连接超时或服务器响应超时 |
| 1003 | PARSING_ERROR | 数据解析错误，通常是服务器返回的数据格式不正确 |

### 常用 HTTP 状态码

| 错误码 | 常量名 | 含义 |
|-------|-------|-----|
| 401 | UNAUTHORIZED | 未授权，通常是用户未登录或登录已过期 |
| 404 | NOT_FOUND | 资源不存在，通常是请求的 API 路径不存在 |
| 500 | SERVER_ERROR | 服务器错误，通常是服务器内部错误 |

## 错误处理策略

根据错误类型，我们应该提供不同的用户体验：

1. **网络错误（NETWORK_ERROR）**：
   - 显示重试按钮
   - 提示用户检查网络连接
   - 示例消息：`"请检查网络连接"`

2. **超时错误（TIMEOUT_ERROR）**：
   - 显示重试按钮
   - 提示用户稍后再试
   - 示例消息：`"请求超时，请稍后再试"`

3. **解析错误（PARSING_ERROR）**：
   - 记录日志
   - 显示友好的错误消息
   - 不显示重试按钮
   - 示例消息：`"数据解析错误"`

4. **业务错误（BUSINESS_ERROR）**：
   - 显示错误消息
   - 不显示重试按钮
   - 示例消息：根据服务器返回的错误消息显示

5. **未授权错误（UNAUTHORIZED）**：
   - 退出登录
   - 跳转到登录页面
   - 示例消息：`"请重新登录"`

6. **资源不存在错误（NOT_FOUND）**：
   - 显示友好的错误消息
   - 不显示重试按钮
   - 示例消息：`"请求的资源不存在"`

7. **服务器错误（SERVER_ERROR）**：
   - 显示友好的错误消息
   - 显示重试按钮
   - 示例消息：`"服务器错误，请稍后再试"`

## 使用示例

### 在 UI 层处理错误

```dart
final result = await userService.getUserInfo();

if (result.isSuccess && result.data != null) {
  // 处理成功情况
  final user = result.data;
  // 更新UI
} else {
  // 处理错误情况
  final errorMsg = result.msg;
  final errorCode = result.status;
  
  // 根据错误类型提供不同的用户体验
  if (result.isNetworkError || result.isTimeoutError) {
    // 显示重试按钮
    showRetryButton();
  }
  
  // 显示错误消息
  showErrorMessage(errorMsg);
}
```

### 在 Repository 层处理错误

```dart
Future<ApiResult<UserModel>> getUserInfo() async {
  try {
    final response = await _apiClient.get(
      '/user/info',
      fromJson: (json) => UserModel.fromJson(json),
    );
    
    return response;
  } catch (e) {
    return ApiResult.fromException(e);
  }
}
```

### 在 Service 层处理错误

```dart
Future<ApiResult<UserModel>> getUserInfo() async {
  // 直接传递 Repository 返回的结果
  return await _repository.getUserInfo();
}
```

## 最佳实践

1. 所有 API 调用都必须返回 `ApiResult`，保持一致性。
2. Service 层如果没有特殊处理，应直接传递 Repository 返回的 ApiResult。
3. 在 UI 层应该提供友好的错误提示，并根据错误类型考虑重试功能。
4. 使用常量而不是硬编码的数字表示错误码，提高代码的可读性和可维护性。
