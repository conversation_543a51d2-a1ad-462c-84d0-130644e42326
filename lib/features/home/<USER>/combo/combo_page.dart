import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/home/<USER>/combo/widgets/combo_list_widget.dart';

class ComboPage extends ConsumerWidget {
  final int buildingId;
  const ComboPage({super.key, required this.buildingId});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: Text(S.current.combo_title)),
      body: ComboListWidget(
        buildingId: buildingId,
      ),
    );
  }
}
