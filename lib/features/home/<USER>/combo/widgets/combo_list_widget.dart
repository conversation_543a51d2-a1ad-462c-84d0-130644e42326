import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/widgets/custom_refresh_indicator.dart';
import 'package:user_app/core/widgets/empty_view.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/home/<USER>/combo/combo_controller.dart';
import 'package:user_app/features/home/<USER>/combo/widgets/combo_item_widget.dart';

class ComboListWidget extends ConsumerWidget {
  final int buildingId;
  const ComboListWidget({super.key, required this.buildingId});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
      final comboList = ref
        .watch(comboControllerProvider(buildingId).select((final s) => s.comboList));
    final hasMore =
        ref.watch(comboControllerProvider(buildingId).select((final s) => s.hasMore));
    final isInitialLoading = ref
        .watch(comboControllerProvider(buildingId).select((final s) => s.isInitialLoading));
    if (isInitialLoading) {
      return const LoadingWidget();
    }
    if (!isInitialLoading && comboList.isEmpty) {
      return EmptyView(
        onRetry: () async {
          await ref
              .read(comboControllerProvider(buildingId).notifier)
              .loadCombos(isLoadMore: false);
        },
      );
    }
    return CustomRefreshIndicator(
      onRefresh: () async {
        await ref
            .read(comboControllerProvider(buildingId).notifier)
            .loadCombos(isLoadMore: false);
      },
      onLoading: () async {
        await ref
            .read(comboControllerProvider(buildingId).notifier)
            .loadCombos(isLoadMore: true);
      },
      hasMoreData: hasMore,
      enablePullUp: true,
      child: ListView.builder(
        itemCount: comboList.length,
        itemBuilder: (final context, final index) {
          final item = comboList[index];
          return ComboItemWidget(
            buildingId: buildingId,
            item: item,
          );
        },
      ),
    );
  }
}
