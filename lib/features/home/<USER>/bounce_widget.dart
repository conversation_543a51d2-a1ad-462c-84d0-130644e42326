import 'package:flutter/material.dart';

class BounceWidget extends StatefulWidget {
  final Widget child;

  const BounceWidget({
    super.key,
    required this.child,
  });

  @override
  State<BounceWidget> createState() => _BounceWidgetState();
}

class _BounceWidgetState extends State<BounceWidget>
    with SingleTickerProviderStateMixin {
  late Animation<double> animation;
  late AnimationController animController;

  @override
  void initState() {
    super.initState();
    animController = AnimationController(
      reverseDuration: const Duration(milliseconds: 400),
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    animation = Tween<double>(
      begin: 0.75,
      end: 1.05,
    ).animate(animController)
      ..addListener(() {
        setState(() {});
      })
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          animController.reverse();
        } else if (status == AnimationStatus.dismissed) {
          animController.forward();
        }
      });
    animController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Transform.scale(
      scale: animation.value,
      child: widget.child,
    );
  }

  @override
  void dispose() {
    animController.dispose();
    super.dispose();
  }
}