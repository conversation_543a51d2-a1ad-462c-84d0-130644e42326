import 'dart:ui';
import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/utils/wechat_util.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/activity/pages/seckill_page.dart';
import 'package:user_app/features/activity/pages/special/special_page.dart';
import 'package:user_app/features/index/pages/main_page.dart';
import 'package:user_app/routes/app_router.dart';
import 'package:user_app/routes/paths.dart';

class RecommendDialog extends Dialog {
  final String title;
  final String content;
  final double screenWidth;
  final int buildingId;
  List<PopupAdver>? popupAdver;
  // 构造函数赋值
  RecommendDialog({
      Key? key,
      this.title = "",
      this.content = "",
      this.screenWidth = 0.0,
      this.buildingId = 0,
      this.popupAdver})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
      child: Material(
        textStyle: TextStyle(
          fontFamily: AppConstants.mainFont, // 添加维语字体
        ),
          type: MaterialType.transparency,
          child: Builder(
            builder: (dialogContext) => Center(
              child: Container(
                width: screenWidth - 60.w,
                // height:Adapt.setPx(140),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.w),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Container(
                      height: 520.w,
                      // color: Colors.red,
                      child: _swiperPart(popupAdver!, dialogContext),
                    ),
                    SizedBox(
                      height: 20.w,
                    ),

                    GestureDetector(
                      onTap: (){
                        Navigator.of(dialogContext).pop();
                      },
                      child: Container(
                          margin: EdgeInsets.only(left: 5.w),
                          alignment: Alignment.center,
                          width: 45.w,
                          height: 45.w,
                          child: Image.asset('assets/images/message_exit.png')),
                    )
                  ],
                ),
              ),
            ),
          )),
    );
  }

  // List<String> swiperListStr = [
  //   'assets/images/marketing_swiper_pic.png',
  //   'assets/images/marketing_swiper_pic.png',
  //   'assets/images/marketing_swiper_pic.png',
  // ];

  ///幻灯片部分
  Widget _swiperPart(List<PopupAdver> popupAdver, BuildContext context) {
    return Swiper(
      itemBuilder: (BuildContext context, int index) {
        // var swiperItem = swiperListStr[index];
        var adverImgListItem = popupAdver[index];
        return GestureDetector(
          onTap: () {
            _handleNavigation(adverImgListItem,context);
          },
          child: PhysicalModel(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(10.h),
            clipBehavior: Clip.antiAlias,
            child: Center(
              child: PrefectImage(
                imageUrl: adverImgListItem.imageUrl!,
                width: screenWidth,
                height: 500.w,
                fit: BoxFit.fitHeight,
              ),
            ),
          ),
        );
      },
      viewportFraction: 1,
      scale: 1,
      autoplay: true,
      itemCount: popupAdver.length,
      autoplayDelay: 4000, // 自动播放延迟（单位：毫秒）
      duration: 500, // 设置滚动动画的持续时间（单位：毫秒）
      pagination: SwiperPagination(
          margin: EdgeInsets.zero,
          builder: SwiperCustomPagination(builder: (context, config) {
            return ConstrainedBox(
              constraints: BoxConstraints.expand(height: 10.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center, // 居中显示
                children: List.generate(config.itemCount, (index) {
                  // 自定义 Container 作为分页指示器
                  return Container(
                    width: index == config.activeIndex
                        ? 18.0.w
                        : 10.0.w, // 当前激活的点更宽
                    height: 6.0.h, // 点的高度
                    margin: EdgeInsets.symmetric(horizontal: 3.0), // 点之间的间距
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5.w),
                      color: index == config.activeIndex
                          ? Colors.white // 当前激活的点为红色
                          : Colors.grey.withOpacity(0.6), // 非激活的点为灰色
                    ),
                  );
                }),
              ),
            );
          })),
    );
  }

  Future<void> _handleNavigation(PopupAdver adver , BuildContext context) async {
    try {
      switch (adver.linkType) {
        case 1:
          router.push(AppPaths.restaurantDetailPage, extra: {
            'restaurantId':adver.linkId ?? 0,
            'buildingId': buildingId,
            'ids': [],
          });
          Navigator.of(context).pop();
          break;

        case 2:
          router.push(AppPaths.restaurantDetailPage, extra: {
            'restaurantId': adver.storeId ?? 0,
            'buildingId': buildingId,
            'ids': [],
          });
          Navigator.of(context).pop();
          break;
        case 0:
          if (adver.linkUrl != null && adver.linkUrl!.isNotEmpty) {
            router.push(AppPaths.webViewPage, extra: {
              'url': adver.linkUrl,
              'title': 'مۇلازىم',
            });
          }
          Navigator.of(context).pop();
          break;
        case 3:
          if(adver.miniProgramId != null && adver.miniProgramId != ''){
            await WechatUtil().launchMiniProgram(adver.miniProgramId!);
          }
          Navigator.of(context).pop();
          break;

        case 4:
        case 5:
          // 处理其他类型的跳转
      // 跳转优惠券
          if(AppContext().currentContext != null){
            if(adver.miniProgramLinkPage != null && adver.miniProgramLinkPage != ''){
              if(adver.miniProgramLinkPage!.contains('special')){
                Navigator.of(context).pop();
                await Future.delayed(Duration(milliseconds: 300));
                Navigator.of(AppContext().currentContext!).push(MaterialPageRoute(builder: (context) => SpecialPage(buildingId: buildingId,),));
              }else if(adver.miniProgramLinkPage!.contains('discount')){
                Navigator.of(context).pop();
                await Future.delayed(Duration(milliseconds: 300));
                Navigator.of(AppContext().currentContext!).push(MaterialPageRoute(builder: (context) => SeckillPage(buildingId: buildingId,),));
              }else{
                MainPageTabs.navigateToTab(AppContext().currentContext!, MainPageTabs.discount);
                Navigator.of(context).pop();
              }
            }else{
              Navigator.of(context).pop();
            }
          }else{
            Navigator.of(context).pop();
          }
          break;

        default:
          if(AppContext().currentContext != null){
            MainPageTabs.navigateToTab(AppContext().currentContext!, MainPageTabs.discount);
          }
          Navigator.of(context).pop();
          break;
      }
    } catch (e) {
      debugPrint('路由跳转错误: $e');
    }
  }
}
