import 'dart:io';
import 'dart:ui';
import 'package:easy_app_installer/easy_app_installer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:path_provider/path_provider.dart';
import 'package:user_app/core/storage/storage_service.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/generated/l10n.dart';

class UpgradeDialog extends Dialog {
  final String title;
  final String content;
  final String fileUrl;
  final double screenWidth;
  final int forceUpdate;
  // Discount? discount;
  // 构造函数赋值
  UpgradeDialog(
      {Key? key,
      this.title = "",
      this.content = "",
      this.fileUrl = "",
      this.screenWidth = 0.0,
      this.forceUpdate = 1,
      // this.discount
      })
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 调用方法
    // _showTimer(context);
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
      child: Material(
          type: MaterialType.transparency,
          child: Center(
            child: Container(
              width: screenWidth - 60.w,
              // height:Adapt.setPx(140),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.w),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Container(

                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.w),
                      color: Colors.white,
                    ),
                    child: Column(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.only(
                            topRight:Radius.circular(10.w),
                            topLeft:Radius.circular(10.w),
                          ),
                          child: Image.asset(
                            'assets/images/update_bg.jpg',
                            fit: BoxFit.fill,
                            width: screenWidth - 60.w,
                          ),
                        ),
                        Container(
                          alignment: Alignment.center,
                          padding: EdgeInsets.only(right: 30.w,left: 30.w,top: 20.w,bottom: 15.w),
                          color: Colors.white,
                          child: Text(S.current.discover_the_new_version,style: TextStyle(fontSize: mainSize,color: Colors.black,fontFamily: 'UkijTuzTom',height: 2),textAlign: TextAlign.center,),
                        ),

                        Consumer(builder: (context, ref, child) {
                          return InkWell(
                            onTap: () async {
                              Navigator.of(context).pop();
                              installApp(fileUrl);
                            },
                            child: Container(
                              margin: EdgeInsets.only(right: 20.w,left:20.w,bottom: 20.w),
                              padding: EdgeInsets.symmetric(horizontal: 100.w,vertical: 10.w),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.w),
                                  color: AppColors.baseGreenColor
                              ),
                              child: Text(S.current.update,style: TextStyle(color: Colors.white,fontSize: titleSize,fontFamily: 'UkijTuzTom'),),
                            ),
                          );
                        }),
                      ],
                    )
                    // child: _swiperPart(popupAdver!),
                  ),
                  if(forceUpdate == 0)InkWell(
                    child: Container(
                        margin: EdgeInsets.only(left: 5.w,top: 20.w),
                        alignment: Alignment.center,
                        width: 45.w,
                        height: 45.w,
                        child: Image.asset('assets/images/message_exit.png')),
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            ),
          )),
    );
  }


  /// 下载并安装APK
  /// 显示下载进度，下载完成后自动安装
  Future installApp(String fileUrl) async {
    Directory appDocDir = await getApplicationDocumentsDirectory();
    String appDocPath = appDocDir.path;

    EasyAppInstaller.instance.downloadAndInstallApk(
        fileUrl: fileUrl,
        fileDirectory: appDocPath,
        fileName: "mulazim.apk",
        onDownloadingListener: (progress) {
          if (progress < 100) {
            EasyLoading.showProgress(progress / 100, status: 'چۈشىۋاتىدۇ');
          } else {
            EasyLoading.showSuccess("下载成功");
          }
        },
        onCancelTagListener: (cancelTag) {
          print('_cancelTag -- > $cancelTag');
        },
        onStateListener: (newState, attachParam) async {

          print('onStateListener ${newState.name.toString()}');


          if (newState.name.toString() == 'onSuccess') {
            String downLoadFileLocation = attachParam.toString();
            print('attachParam.toString() ${attachParam.toString()}');
            await StorageService().write('downLoadFileLocation', downLoadFileLocation);
          }
        }
    );
  }

}
