import "dart:async";
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/generated/l10n.dart';

// 用于跟踪当前是否有对话框显示
bool _isDialogShowing = false;

class CustomAlertDialog extends Dialog {
  final String title;
  final String content;
  final double screenWidth;

  const CustomAlertDialog(
      {this.title = "", this.content = "", this.screenWidth = 0.0});

  // 显示对话框的静态方法
  static Future<void> show(BuildContext context,
      {String title = "",
      String content = "",
      double screenWidth = 0.0}) async {
    // 如果已经有对话框在显示,则不再显示新的对话框
    if (_isDialogShowing) {
      return;
    }

    _isDialogShowing = true;

    try {
      await showDialog(
        context: context,
        builder: (context) => CustomAlertDialog(
          title: title,
          content: content,
          screenWidth: screenWidth,
        ),
      );
    } finally {
      _isDialogShowing = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
        type: MaterialType.transparency,
        textStyle: TextStyle(
          fontFamily: AppConstants.mainFont, // 添加维语字体
        ),
        child: Center(
          child: Container(
            width: screenWidth - 74.0.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.w),
              color: Colors.white,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Padding(
                  padding: EdgeInsets.only(top: 15.w, bottom: 10.w),
                  child: Stack(
                    children: <Widget>[
                      Container(
                        padding: EdgeInsets.only(left: 20.w, right: 20.w),
                        alignment: Alignment.center,
                        child: Text(
                          content,
                          style: TextStyle(fontSize: mainSize, height: 1.5),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(),
                InkWell(
                  child: Container(
                      padding: EdgeInsets.only(top: 10.w, bottom: 20.w),
                      width: double.infinity,
                      child: Text(
                        S.of(context).i_get_it,
                        style: TextStyle(
                            fontSize: mainSize,
                            color: AppColors.baseGreenColor),
                        textAlign: TextAlign.center,
                      )),
                  onTap: () {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        ));
  }
}
