
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/utils/status_bar_util.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/routes/paths.dart';

class SelectLanguagePage extends ConsumerStatefulWidget {
  const SelectLanguagePage({super.key});

  @override
  ConsumerState createState() => _SelectLanguagePageState();
}

class _SelectLanguagePageState extends ConsumerState<SelectLanguagePage> {
    @override
  void initState() {
    super.initState();
    // 进入页面时设置状态栏为深色模式（白色背景，深色内容）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      StatusBarUtil.setDarkMode();
    });
  }

  @override
  void dispose() {
    // 离开页面时恢复默认的浅色模式（深色背景，白色内容）
    // StatusBarUtil.setLightMode();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.78],
            colors: [
              Color.fromRGBO(222, 238, 242, 0.0), // rgba(222, 238, 242, 0)
              Theme.of(context).primaryColor.withOpacity(0.1), // rgba(255, 255, 255, 1)
            ],
          ),
        ),
        child: Stack(
          children: [
            Positioned(
              child: Image.asset(
                'assets/images/tag.png',
                width: MediaQuery.of(context).size.width,
                fit: BoxFit.fitHeight,
              ),
            ),
            Positioned(
              right: MediaQuery.of(context).size.width * 0.1,
              top: 180.w,
              child: Container(
                width: MediaQuery.of(context).size.width * 0.8,
                height: 510.w,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30.w),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: [0.0, 0.78],
                      colors: [
                        Color.fromRGBO(222, 238, 242, 0.0), // rgba(222, 238, 242, 0)
                        Color.fromRGBO(255, 255, 255, 1.0), // rgba(255, 255, 255, 1)
                      ],
                    ),
                ),
                child: Column(
                  children: [
                    Image.asset(
                      'assets/images/logo.png',
                      width: MediaQuery.of(context).size.width * 0.5,
                      fit: BoxFit.fitHeight,
                    ),
                    SizedBox(height: 70.w,),

                    Image.asset(
                      'assets/images/path.png',
                      width: MediaQuery.of(context).size.width * 0.2,
                      fit: BoxFit.fitHeight,
                    ),
                    SizedBox(height: 70.w,),
                    Column(
                      children: [
                        InkWell(
                          child: Container(
                            alignment: Alignment.center,
                            height: 50.w,
                            width: MediaQuery.of(context).size.width * 0.5,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(30.w),
                              color: AppColors.baseGreenColor
                            ),
                            child: Text('ئۇيغۇرچە',style: TextStyle(color: Colors.white,fontSize: titleSize),),
                          ),
                          onTap: () {
                            final storageService = ref.read(localStorageRepositoryProvider);
                            storageService.saveLang("ug");
                            ref.read(languageProvider.notifier).state = 'ug';
                            context.go(AppPaths.privacyPage);
                          },
                        ),
                        SizedBox(height: 24.w,),
                        InkWell(
                          child: Container(
                            alignment: Alignment.center,
                            height: 50.w,
                            width: MediaQuery.of(context).size.width * 0.5,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(30.w),
                                color: Colors.white,
                                border: Border.all(
                                  color: AppColors.baseGreenColor,
                                  width: 1.w,
                                ),
                            ),
                            child: Text('中文(简体)',style: TextStyle(color: AppColors.baseGreenColor,fontSize: titleSize),),
                          ),
                          onTap: () {
                            final storageService = ref.read(localStorageRepositoryProvider);
                            storageService.saveLang("zh");
                            ref.read(languageProvider.notifier).state = 'zh';
                            context.go(AppPaths.privacyPage);
                          },
                        )
                      ],
                    )
                  ],
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              left: 0,
              child: Container(
                height: 70.w,
                // color: Colors.yellow,
                child: Column(
                  children: [
                    Text('شىنجاڭ ئالماس يۇمشاق دىتال چەكلىك شىركىتى',style: TextStyle(color: AppColors.textSecondaryColor,fontSize: secondSize),),
                    SizedBox(height: 5.w,),
                    Text('新疆金钻软件有限公司',style: TextStyle(color: AppColors.textSecondaryColor,fontSize: mainSize,letterSpacing: 9.5,))
                  ],
                ),
              )
            ),
          ],
        ),
      ),
    );
  }
}
