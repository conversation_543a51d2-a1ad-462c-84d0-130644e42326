import 'package:user_app/data/models/location/location_model.dart';
import 'package:user_app/data/models/home/<USER>';

/// 首页状态类
class HomeState {
  /// 构造函数
  const HomeState({
    this.isLoading = false,
    this.isLocationLoading = true,
    this.isRestaurantLoading = true,
    this.isLoadingMore = false,
    this.isInitialLoading = true,
    this.error,
    this.hasHomeInfo = false,
    this.locationInfo,
    this.selectedTag = '1',
    this.pageNum = 1,
    this.restaurantList = const [],
    this.canAddRestaurantData = true,
    this.homeData,
    this.isNoServiceAgent = false,
    this.isPermissionError = false,
  });

  /// 是否全局加载中
  final bool isLoading;

  /// 位置信息是否加载中
  final bool isLocationLoading;

  /// 餐厅列表是否加载中
  final bool isRestaurantLoading;

  /// 是否正在加载更多
  final bool isLoadingMore;

  /// 是否是初始加载（用于区分初始加载和下拉刷新）
  final bool isInitialLoading;

  /// 错误信息
  final String? error;

  /// 是否已获取首页信息
  final bool hasHomeInfo;

  /// 位置信息
  final LocationModel? locationInfo;

  /// 当前选中的标签
  final String selectedTag;

  /// 当前页码
  final int pageNum;

  /// 餐厅列表
  final List<RestaurantItems> restaurantList;

  /// 是否可以加载更多餐厅
  final bool canAddRestaurantData;

  /// 首页数据
  final HomeData? homeData;

  /// 是否没有服务代理商
  final bool isNoServiceAgent;

  /// 是否是定位权限错误
  final bool isPermissionError;

  /// 拷贝方法
  HomeState copyWith({
    final bool? isLoading,
    final bool? isLocationLoading,
    final bool? isRestaurantLoading,
    final bool? isLoadingMore,
    final bool? isInitialLoading,
    final String? error,
    final bool? hasHomeInfo,
    final LocationModel? locationInfo,
    final String? selectedTag,
    final int? pageNum,
    final List<RestaurantItems>? restaurantList,
    final bool? canAddRestaurantData,
    final HomeData? homeData,
    final bool? isNoServiceAgent,
    final bool? isPermissionError,
  }) {
    return HomeState(
      isLoading: isLoading ?? this.isLoading,
      isLocationLoading: isLocationLoading ?? this.isLocationLoading,
      isRestaurantLoading: isRestaurantLoading ?? this.isRestaurantLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      isInitialLoading: isInitialLoading ?? this.isInitialLoading,
      error: error ?? this.error,
      hasHomeInfo: hasHomeInfo ?? this.hasHomeInfo,
      locationInfo: locationInfo ?? this.locationInfo,
      selectedTag: selectedTag ?? this.selectedTag,
      pageNum: pageNum ?? this.pageNum,
      restaurantList: restaurantList ?? this.restaurantList,
      canAddRestaurantData: canAddRestaurantData ?? this.canAddRestaurantData,
      homeData: homeData ?? this.homeData,
      isNoServiceAgent: isNoServiceAgent ?? this.isNoServiceAgent,
      isPermissionError: isPermissionError ?? this.isPermissionError,
    );
  }
}
