import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/app/app_init.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/utils/status_bar_util.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/features/address/dialog/privacy_confirm_dialog.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/app_router.dart';
import 'package:user_app/routes/paths.dart';

class PrivacyPage extends ConsumerStatefulWidget {
  const PrivacyPage({super.key});

  @override
  ConsumerState createState() => _PrivacyPageState();
}

class _PrivacyPageState extends ConsumerState<PrivacyPage> {
  @override
  void initState() {
    super.initState();
    // 进入页面时设置状态栏为深色模式（白色背景，深色内容）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      StatusBarUtil.setDarkMode();
    });
  }

  @override
  void dispose() {
    // 离开页面时恢复默认的浅色模式（深色背景，白色内容）
    // StatusBarUtil.setLightMode();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Column(
          children: [
            Container(
              padding: EdgeInsets.only(
                top: 60.w,
                bottom: 20.w,
                right: 20.w,
                left: 20.w,
              ),
              alignment: Alignment.center,
              color: Colors.white,
              child: Text(
                S.current.lovely_alert,
                style: TextStyle(color: Colors.black, fontSize: titleSize),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Directionality(
                  textDirection: ref.watch(languageProvider) == 'ug'
                      ? TextDirection.rtl
                      : TextDirection.ltr,
                  child: Column(
                    children: [
                      Container(
                        alignment: ref.watch(languageProvider) == 'ug'
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                        margin:
                            EdgeInsets.only(right: 10.w, left: 10.w, top: 10.w),
                        padding: EdgeInsets.all(5.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.w),
                          // color: AppColors.secondGreenColor
                        ),
                        child: Text(
                          S.current.privacy_content_1,
                          textAlign: TextAlign.justify,
                        ),
                      ),
                      Container(
                        alignment: ref.watch(languageProvider) == 'ug'
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                        padding: EdgeInsets.symmetric(
                          horizontal: 5.w,
                          vertical: 5.w,
                        ),

                        margin:
                            EdgeInsets.only(right: 10.w, left: 10.w, top: 10.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.w),
                          // color: AppColors.secondGreenColor
                        ),
                        child: RichText(
                          textDirection: ref.watch(languageProvider) == 'ug'
                              ? TextDirection.rtl
                              : TextDirection.ltr,
                          textAlign: TextAlign.justify,
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: S.current.privacy_content_2_1,
                                style: TextStyle(
                                  height: 1.6,
                                  color: Colors.black,
                                  fontFamily: "UkijTuzTom",
                                ),
                                )
                                ,TextSpan(
                                text: S.current.privacy_link_1,
                                style: TextStyle(
                                  height: 1.6,
                                  color: Colors.red,
                                  fontFamily: "UkijTuzTom",
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    // 点击事件处理
                                    router.push(
                                      AppPaths.webViewPage,
                                      extra: {
                                        'url': UrlConstants.userAgreementUrl,
                                        'title': S.current.app_name,
                                      },
                                    );
                                  },
                              ),
                              TextSpan(
                                text: S.current.privacy_content_2,
                                style: TextStyle(
                                  height: 1.6,
                                  color: Colors.black,
                                  fontFamily: "UkijTuzTom",
                                ),
                              ),
                              TextSpan(
                                text: S.current.privacy_link,
                                style: TextStyle(
                                  height: 1.6,
                                  color: Colors.red,
                                  fontFamily: "UkijTuzTom",
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    // 点击事件处理
                                    router.push(
                                      AppPaths.webViewPage,
                                      extra: {
                                        'url':
                                            'https://smart.mulazim.com/zh/v1/about/detail?type=1',
                                        'title': S.current.app_name,
                                      },
                                    );
                                  },
                              ),
                              TextSpan(
                                text: S.current.privacy_content_2_2,
                                style: TextStyle(
                                  height: 1.6,
                                  color: Colors.black,
                                  fontFamily: "UkijTuzTom",
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Text(S.current.privacy_content_2,style: TextStyle(height: 1.6),textAlign: TextAlign.justify,),
                      ),
                      Container(
                        alignment: ref.watch(languageProvider) == 'ug'
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                        padding: EdgeInsets.symmetric(
                          horizontal: 5.w,
                          vertical: 5.w,
                        ),
                        margin: EdgeInsets.only(right: 10.w, left: 10.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.w),
                          // color: AppColors.secondGreenColor
                        ),
                        child: Text(
                          S.current.privacy_content_3,
                          style: TextStyle(height: 1.6),
                          textAlign: TextAlign.justify,
                        ),
                      ),
                      Container(
                        alignment: ref.watch(languageProvider) == 'ug'
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                        padding: EdgeInsets.symmetric(
                          horizontal: 5.w,
                          vertical: 5.w,
                        ),
                        margin: EdgeInsets.only(right: 10.w, left: 10.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.w),
                          // color: AppColors.secondGreenColor
                        ),
                        child: Text(
                          S.current.privacy_content_4,
                          style: TextStyle(height: 1.6),
                          textAlign: TextAlign.justify,
                        ),
                      ),
                      Container(
                        alignment: ref.watch(languageProvider) == 'ug'
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                        padding: EdgeInsets.symmetric(
                          horizontal: 5.w,
                          vertical: 5.w,
                        ),
                        margin: EdgeInsets.only(right: 10.w, left: 10.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.w),
                          // color: AppColors.secondGreenColor
                        ),
                        child: Text(
                          S.current.privacy_content_5,
                          style: TextStyle(height: 1.6),
                          textAlign: TextAlign.justify,
                        ),
                      ),
                      Container(
                        alignment: ref.watch(languageProvider) == 'ug'
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                        padding: EdgeInsets.symmetric(
                          horizontal: 5.w,
                          vertical: 5.w,
                        ),
                        margin: EdgeInsets.only(right: 10.w, left: 10.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.w),
                          // color: AppColors.secondGreenColor
                        ),
                        child: Text(
                          S.current.privacy_content_6,
                          style: TextStyle(height: 1.6),
                          textAlign: TextAlign.justify,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        bottomNavigationBar: Container(
          height: 140.w,
          color: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 10.w),
          child: Column(
            children: [
              Expanded(
                flex: 1,
                child: InkWell(
                  onTap: () async {
                    await ref
                        .read(localStorageRepositoryProvider)
                        .saveIsAgreePrivacyPolicy(true);
                    await AppInit.init();
                    if (mounted) {
                      router.go(AppPaths.mainPage);
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 10.w),
                    padding:
                        EdgeInsets.symmetric(vertical: 15.w, horizontal: 20.w),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(30.w),
                      color: AppColors.baseGreenColor,
                    ),
                    child: Text(
                      S.current.i_agree,
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 10.w,
              ),
              Expanded(
                flex: 1,
                child: InkWell(
                  onTap: () {
                    PrivacyConfirmDialog(
                      context,
                      MediaQuery.of(context).size.width,
                      ref.watch(languageProvider),
                      () {
                        // Navigator.pop(context);
                        // ref.read(orderDetailProvider.notifier).cancelOrder(orderId: item.id ?? 0);
                      },
                    );
                  },
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 10.w),
                    padding:
                        EdgeInsets.symmetric(vertical: 15.w, horizontal: 20.w),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(30.w),
                      color: Colors.white,
                      border: Border.all(
                        color: AppColors.searchBackColor,
                      ),
                    ),
                    child: Text(
                      S.current.i_no_agree,
                      style: TextStyle(color: Colors.black),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
