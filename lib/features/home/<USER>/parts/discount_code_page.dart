
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/generated/l10n.dart';

class DiscountCodePage extends ConsumerWidget {
  const DiscountCodePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String lang = ref.watch(languageProvider);
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.discountRedColor,
        foregroundColor: Colors.white,
        title: Text(S.current.discount_foods_info,style: TextStyle(fontSize: soBigSize,color: Colors.white),),
      ),
      body: Stack(
        children: [
          Container(
            color: Colors.red,
            child:ClipRRect(
              child: PrefectImage(
                imageUrl: 'https://acdn.mulazim.com/wechat_mini/img/work/work-wechat-detail-discount-${lang}.png',
                fit: BoxFit.fill,
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
              ),
            ),
          ),
          Positioned(
            top: (MediaQuery.of(context).size.height / 2) - (MediaQuery.of(context).size.width / 2.74),
            right: (MediaQuery.of(context).size.width / 4),
            child: Container(
              color: Colors.red,
              child:ClipRRect(
                child: PrefectImage(
                  imageUrl: 'https://acdn.mulazim.com/upload/work-wechat/202402/29/5ff29640a2bbc193486fa175889ca864.png',
                  fit: BoxFit.fill,
                  width: MediaQuery.of(context).size.width / 2,
                  height: MediaQuery.of(context).size.width / 1.9,
                ),
              ),
            ),
          )
        ],
      )
    );
  }
}
