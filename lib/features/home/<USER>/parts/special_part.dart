import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:shimmer/shimmer.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/dialogs/login_confirm_dialog.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/activity/pages/seckill_page.dart';
import 'package:user_app/features/activity/pages/special/special_page.dart';
import 'package:user_app/features/home/<USER>/parts/seckill_part.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart';
import 'package:user_app/routes/index.dart';

class SpecialPart extends ConsumerStatefulWidget {
  SpecialPart({super.key, required this.special, required this.buildingId});
  List<Special>? special;
  int buildingId;

  @override
  ConsumerState createState() => _SpecialPartState();
}

class _SpecialPartState extends ConsumerState<SpecialPart>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化进度条动画控制器
    _progressController = AnimationController(
      duration: const Duration(seconds: 2), // 2秒动画
      vsync: this,
    );

    // 创建从0到0.6的动画（60%）
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 0.6,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    // 启动动画
    _progressController.forward();
  }

  @override
  void dispose() {
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return specialPart(widget.special, context);
  }

  ///优惠部分
  Widget specialPart(List<Special>? special, BuildContext context) {
    return InkWell(
      onTap: () {
        //判断是否登录
        ref.read(isLoggedInProvider)
            ? Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => SpecialPage(
                    buildingId: widget.buildingId,
                  ),
                ),
              )
            : LoginConfirmDialog.show(
                context,
                onConfirm: () => context.push(AppPaths.login).then((_) {
                  if (ref.read(isLoggedInProvider)) {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => SpecialPage(
                          buildingId: widget.buildingId,
                        ),
                      ),
                    );
                  }
                }),
              );
      },
      child: Container(
        margin: EdgeInsets.only(right: 10.w, left: 10.w, bottom: 10.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.w),
            color: AppColors.baseGreenColor),
        // color: Colors.yellow,
        padding: EdgeInsets.only(left: 10.w, right: 10.w),
        child: Column(
          children: [
            Container(
              height: 46.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.w),
                color: AppColors.baseGreenColor,
              ),
              child: CustomPaint(
                painter: CheckerboardPainter(),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Shimmer.fromColors(
                        baseColor: Colors.white,
                        highlightColor: AppColors.baseYellowColor,
                        child: Row(
                          children: [
                            Text(
                              S.current.one_food,
                              style: TextStyle(
                                  fontSize: soBigSize, color: Colors.white),
                            ),
                            Text(
                              '${special?[0].price ?? 0}',
                              style: TextStyle(
                                  fontSize: 26.sp,
                                  color: Colors.white,
                                  fontFamily: 'NumberFont'),
                            ),
                            Text(
                              S.current.yuan,
                              style: TextStyle(
                                  fontSize: soBigSize, color: Colors.white),
                            )
                          ],
                        )),
                    if ((special?[0].shipmentFee ?? 0) != 0 &&
                        (special?[0].shipmentType ?? 0) == 1)
                      Text(
                        '${S.current.cart_delivery_fee}${special?[0].shipmentFee ?? 0}￥',
                        style:
                            TextStyle(fontSize: mainSize, color: Colors.white),
                      ),
                    Row(
                      children: [
                        Text(
                          S.current.get_more,
                          style: TextStyle(
                              fontSize: titleSize, color: Colors.white),
                        ),
                        SizedBox(
                          width: 6.w,
                        ),
                        ClipOval(
                          child: Container(
                            color: AppColors.baseYellowColor,
                            width: 22.w,
                            height: 22.w,
                            child: Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.black,
                              size: 16.sp,
                            ),
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
            (special ?? []).length == 1
                ? _foodItem(special![0], context)
                : Container(
                    alignment: ref.watch(languageProvider) == 'ug'
                        ? Alignment.centerRight
                        : Alignment.centerLeft,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.w),
                        color: Colors.white),
                    padding:
                        EdgeInsets.only(top: 10.w, right: 10.w, bottom: 10.w),
                    margin: EdgeInsets.only(bottom: 10.w, top: 2.w),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: List.generate((special ?? []).length,
                            (index) => _specialItem(special![index])),
                      ),
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  Widget _foodItem(Special special, BuildContext context) {
    double lineWidth = MediaQuery.of(context).size.width - 115.w - 70.w;
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.w), color: Colors.white),
      margin: EdgeInsets.only(top: 10.w, bottom: 10.w),
      padding: EdgeInsets.all(10.w),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(10.w),
            child: PrefectImage(
              imageUrl: special.image ?? '',
              width: 115.w,
              height: 105.w,
              fit: BoxFit.fill,
            ),
          ),
          SizedBox(
            width: 10.w,
          ),
          Expanded(
            child: Container(
              child: Column(
                children: [
                  Container(
                      alignment: ref.watch(languageProvider) == 'ug'
                          ? Alignment.centerRight
                          : Alignment.centerLeft,
                      child: Text(
                        '${special.name}',
                        textAlign: TextAlign.start,
                        style:
                            TextStyle(fontSize: titleSize, color: Colors.black),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      )),
                  SizedBox(
                    height: 5.w,
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          SizedBox(
                            width: 5.w,
                          ),
                          Text('${special.price}',
                              style: TextStyle(
                                fontSize: 20.sp,
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'NumberFont',
                              )),
                          Text('￥',
                              style: TextStyle(
                                  fontSize: mainSize, color: Colors.red)),
                          Text(
                            '${special.oldPrice}',
                            style: TextStyle(
                                fontSize: mainSize,
                                decoration: TextDecoration.lineThrough,
                                decorationColor: AppColors.textSecondaryColor,
                                color: AppColors.textSecondaryColor),
                          ),
                          Text('￥',
                              style: TextStyle(
                                  decoration: TextDecoration.lineThrough,
                                  fontSize: littleSize,
                                  decorationColor: AppColors.textSecondaryColor,
                                  color: AppColors.textSecondaryColor)),
                          SizedBox(
                            width: 5.w,
                          ),
                        ],
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 5.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(30.w),
                            color: AppColors.baseGreenColor),
                        child: Text(
                          S.current.buy,
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: mainSize,
                              fontWeight: FontWeight.bold),
                        ),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 12.w,
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        width: lineWidth,
                        child: AnimatedBuilder(
                          animation: _progressAnimation,
                          builder: (context, child) {
                            return CustomLinearProgressIndicator(
                              progress: int.parse(
                                      (special.saledCount ?? 0).toString()) /
                                  ((special.totalCount ?? 0)),
                              width: lineWidth,
                              color: AppColors.baseGreenColor,
                              height: 15.w,
                            );
                          },
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  ///优惠元素
  Widget _specialItem(Special special) {
    return Container(
      color: Colors.white,
      // margin: EdgeInsets.only(right:10.w,bottom: 10.w),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: 10.w, bottom: 4.h),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10.w),
              child: PrefectImage(
                imageUrl: special.image ?? '',
                width: 105.w,
                height: 90.w,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Container(
            width: 100.w,
            margin: EdgeInsets.only(left: 10.w),
            alignment: ref.watch(languageProvider) == 'ug'
                ? Alignment.centerRight
                : Alignment.centerLeft,
            child: Text(
              '${special.name}', style: TextStyle(fontSize: mainSize),
              overflow: TextOverflow.ellipsis, // 超出部分显示省略号
              textAlign: TextAlign.center,
              maxLines: 1, // 只显示一行
            ),
          ),
          SizedBox(
            height: 3.h,
          ),
          Stack(
            children: [
              Container(
                height: 20.h,
                width: 100.w,
                margin: EdgeInsets.only(left: 10.w),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.w),
                    color: AppColors.secOrangeColor),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Text(
                      '￥${special.oldPrice ?? 0}',
                      style: TextStyle(
                        fontSize: soLittleSize,
                        color: AppColors.littleGreenColor,
                        decoration: TextDecoration.lineThrough,
                        decorationColor: AppColors.littleGreenColor,
                      ),
                    ),
                    Text(
                      '￥${special.price ?? 0}',
                      style: TextStyle(
                        fontSize: littleSize,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'NumberFont',
                      ),
                    ),
                    Image.asset(
                      'assets/images/fireyellow.png',
                      fit: BoxFit.cover,
                      width: 10.w,
                      height: 12.w,
                    ),
                  ],
                ),
              ),
              Positioned(
                top: 0,
                right: 0,
                left: 0,
                bottom: 0,
                child: CustomShimmerEffect(
                  kval: 0.8,
                  highlightColor: Colors.white,
                  duration: const Duration(milliseconds: 2000),
                  child: Container(
                    color: Colors.white,
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}

/// 棋盘格背景绘制器
class CheckerboardPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = const Color(0x15FFFFFF); // #ffffff15

    const squareSize = 15.0; // 调小棋盘格尺寸，让效果更精致

    // 绘制棋盘格图案
    for (int i = 0; i < (size.width / squareSize).ceil(); i++) {
      for (int j = 0; j < (size.height / squareSize).ceil(); j++) {
        if ((i + j) % 2 == 0) {
          canvas.drawRect(
            Rect.fromLTWH(
                i * squareSize, j * squareSize, squareSize, squareSize),
            paint,
          );
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
