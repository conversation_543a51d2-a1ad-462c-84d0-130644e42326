import 'package:flutter/material.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/features/home/<USER>/dialogs/coupon_dialog.dart';
import 'package:user_app/features/home/<USER>/dialogs/discount_dialog.dart';
import 'package:user_app/features/home/<USER>/dialogs/recommend_dialog.dart';
import 'package:user_app/features/home/<USER>/dialogs/upgrade_dialog.dart';
import 'package:user_app/features/my_order/pages/dialog/require_dialog.dart';

Future<void> showRecommendDialogTask(final List<PopupAdver>? popupAdver,final int buildingId,
    final BuildContext context) async {
  final BuildContext? safeContext = AppContext().currentContext ?? context;

  if (safeContext == null) return;

  showGeneralDialog(
    transitionDuration: const Duration(milliseconds: 100),
    transitionBuilder: (final BuildContext context,
        final Animation<double> animation,
        final Animation<double> secondaryAnimation,
        final Widget child) {
      return ScaleTransition(scale: animation, child: child);
    },
    context: safeContext,
    pageBuilder: (final BuildContext context, final Animation<double> animation,
        final Animation<double> secondaryAnimation) {
      return RecommendDialog(
        buildingId: buildingId,
        title: "",
        content: "",
        screenWidth:  MediaQuery.of(context).size.width,
        popupAdver: popupAdver,
      );
    },
  );
}


Future<void> showCouponDialogTask(final List<CouponListItem>? coupon,final int buildingId,final int areaId,
    final BuildContext context) async {
  final BuildContext? safeContext = AppContext().currentContext ?? context;

  if (safeContext == null) return;

  showGeneralDialog(
    transitionDuration: const Duration(milliseconds: 100),
    transitionBuilder: (final BuildContext context,
        final Animation<double> animation,
        final Animation<double> secondaryAnimation,
        final Widget child) {
      return ScaleTransition(scale: animation, child: child);
    },
    context: safeContext,
    pageBuilder: (final BuildContext context, final Animation<double> animation,
        final Animation<double> secondaryAnimation) {
      return CouponDialog(
        buildingId: buildingId,
        areaId: areaId,
        title: "",
        content: "",
        screenWidth:  MediaQuery.of(context).size.width,
        coupon: coupon,
      );
    },
  );
}


Future<void> showDiscountDialogTask(final Discount? discount,int buildingId, BuildContext context) async {
  // BuildContext? safeContext = AppContext().currentContext ?? context;
  //
  // if (safeContext == null) return;

  showGeneralDialog(
    transitionDuration: const Duration(milliseconds: 100),
    transitionBuilder: (BuildContext context,
        final Animation<double> animation,
        final Animation<double> secondaryAnimation,
        final Widget child) {
      return ScaleTransition(scale: animation, child: child);
    },
    context: context,
    pageBuilder: (BuildContext context, final Animation<double> animation,final Animation<double> secondaryAnimation) {
      return DiscountDialog(
        title: "",
        content: "",
        screenWidth:  MediaQuery.of(context).size.width,
        discount: discount,
        buildingId: buildingId,
      );
    },
  );
}

Future<void> showRequireDialogTask(final int? orderId, final String? mobile, final Delayed? delayed, final double screenWidth, BuildContext context) async {
  showGeneralDialog(
    transitionDuration: const Duration(milliseconds: 100),
    transitionBuilder: (BuildContext context,
        final Animation<double> animation,
        final Animation<double> secondaryAnimation,
        final Widget child) {
      return ScaleTransition(scale: animation, child: child);
    },
    context: context,
    pageBuilder: (BuildContext context, final Animation<double> animation,final Animation<double> secondaryAnimation) {
      return RequireDialog(
        title: "",
        content: "",
        screenWidth: screenWidth,
        delayed: delayed,
        orderId: orderId,
        mobile: mobile,
      );
    },
  );
}


Future<void> showUpgradeDialogTask(
    int? forceUpdate,
    String? fileUrl,
    BuildContext context) async {
  showGeneralDialog(
    transitionDuration: const Duration(milliseconds: 100),
    transitionBuilder: (BuildContext context, Animation<double> animation,
        Animation<double> secondaryAnimation, Widget child) {
      return ScaleTransition(scale: animation, child: child);
    },
    context: context,
    pageBuilder: (BuildContext context, Animation<double> animation,
        Animation<double> secondaryAnimation) {
      return UpgradeDialog(
        title: "",
        content: "",
        fileUrl: fileUrl ?? '',
        screenWidth:  MediaQuery.of(context).size.width,
        forceUpdate: forceUpdate ?? 1,
        // discount: discount,
      );
    },
  );
}
