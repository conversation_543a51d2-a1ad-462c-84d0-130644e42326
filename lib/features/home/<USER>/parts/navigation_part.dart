import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/home/<USER>/combo/combo_page.dart';
import 'package:user_app/features/home/<USER>/category_pages/quick_page.dart';
import 'package:user_app/features/home/<USER>/category_pages/self_take_page.dart';
// import 'package:user_app/features/home/<USER>/quick_page.dart';
// import 'package:user_app/features/home/<USER>/self_take_page.dart';
import 'package:user_app/features/home/<USER>/bounce_widget.dart';
import 'package:user_app/generated/l10n.dart';

class NavigationPart extends StatelessWidget {
  NavigationPart(
      {super.key, required this.categories, required this.buildingId});
  List<Categories> categories;
  int buildingId;
  @override
  Widget build(BuildContext context) {
    return navigationPart(categories, context);
  }

  ///导航部分
  Widget navigationPart(List<Categories> categories, BuildContext context) {
    List<Categories> thisCategories = [];
    for (var element in categories) {
      if (thisCategories.length < 5) thisCategories.add(element);
    }
    return Container(
      padding: EdgeInsets.only(top: 15.w, right: 5.w, left: 5.w),
      margin: EdgeInsets.only(bottom: 10.w),
      color: Colors.white,
      child: Row(
        children: List.generate(
          thisCategories.length,
          (index) => _navigationItem(thisCategories[index], context,
              isLargeSize: thisCategories.length < 5),
        ),
      ),
    );
  }

  // ///获取jpush的注册id
  // Future<String> getRegId() async {
  //   String regId = await NativeCall().getRegId();
  //   return regId;
  // }

  ///导航元素
  Widget _navigationItem(Categories categories, BuildContext context,
      {bool isLargeSize = false}) {
    return Expanded(
      child: Stack(
        children: [
          InkWell(
            onTap: () async {
              // String regId = await getRegId();
              // print('regId -> $regId');
              if (categories.id == 3) {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => SelfTakePage(
                    buildingId: buildingId,
                  ),
                ));
                debugPrint('router: SelfTakePage');
              } else if (categories.id == 5) {
                // 跳转到套餐
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => ComboPage(buildingId: buildingId),
                ));
                debugPrint('router: ComboPage');
              } else {
                // 1，4，30
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => QuickPage(
                    buildingId: buildingId,
                    categoryId: categories.id ?? 0,
                  ),
                ));
                debugPrint('router: QuickPage');
              }
            },
            child: Container(
              alignment: Alignment.center,
              // color: Colors.green,
              margin: EdgeInsets.only(top: 8.w),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Stack(
                    children: [
                      PrefectImage(
                        imageUrl: categories.icon ?? '',
                        width: 54.w,
                        height: 54.w,
                        fit: BoxFit.fitWidth,
                      ),
                      if ((categories.cartoonType ?? 1) == 2)
                        Positioned(
                          top: 0,
                          right: 0.w,
                          left: 0.w,
                          child: BounceWidget(
                            child: PrefectImage(
                              imageUrl: categories.cartoonIcon ?? '',
                              width: 54.w,
                              height: 54.w,
                              fit: BoxFit.fitWidth,
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(
                    height: 6.h,
                  ),
                  Text(
                    categories.name ?? '',
                    style: TextStyle(
                      fontSize: isLargeSize ? mainSize : mainSize - 4.sp,
                      color: (categories.textColor ?? '') != ''
                          ? FormatUtil.parseColor(categories.textColor!)
                          : Colors.black,
                    ),
                  ),
                  SizedBox(
                    height: 12.h,
                  ),
                ],
              ),
            ),
          ),
          if ((categories.cartoonType ?? 1) == 2)
            Positioned(
              top: 0,
              right: 12.w,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.w),
                  color: Colors.red,
                ),
                child: Text(
                  S.current.new_label,
                  style: TextStyle(
                    fontSize: secondSize,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
