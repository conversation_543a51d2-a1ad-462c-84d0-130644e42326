import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/dialogs/login_confirm_dialog.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/home/<USER>/index/home_controller.dart';
import 'package:user_app/features/home/<USER>/parts/seckill_part.dart';
import 'package:user_app/features/index/pages/main_page.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart';
import 'package:user_app/routes/paths.dart';

class CouponPart extends ConsumerStatefulWidget {
  CouponPart({super.key, required this.couponPin, required this.coupon});
  List<CouponListItem>? couponPin;
  List<CouponListItem>? coupon;
  @override
  ConsumerState createState() => _CouponPartState();
}

class _CouponPartState extends ConsumerState<CouponPart> {
  @override
  Widget build(BuildContext context) {
    num totalPrice = 0;
    for(int i = 0; i < (widget.couponPin ?? []).length; i++){
      totalPrice = totalPrice + (widget.couponPin?[i].price ?? 0);
    }
    return InkWell(
      onTap: (){
        final isLogin = ref.read(isLoggedInProvider);
        if(isLogin){
          if((widget.coupon ?? []).isNotEmpty){
            ref.read(homeControllerProvider.notifier).loadCouponDialog();
          }else{
            MainPageTabs.navigateToTab(context, MainPageTabs.discount);
          }
        }else{
          LoginConfirmDialog.show(context, onConfirm: () => context.push(AppPaths.courtesyPage));
        }
      },
      child: Container(
        // height: 80.w,
        margin: EdgeInsets.only(right:10.w,left: 10.w,bottom: 10.w),
        // height: 80.w,
        child: Stack(
          children: [
            PrefectImage(
              imageUrl: 'https://acdn.mulazim.com/wechat_mini/img/resources/coupon-home-${ref.read(languageProvider)}.png',
              fit: BoxFit.fill,
              width: MediaQuery.of(context).size.width,
              height:60.w
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Directionality(
                      textDirection:TextDirection.ltr,
                      child: Container(
                        width: MediaQuery.of(context).size.width / 6,
                        height:60.w,
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(bottom: 5.w),
                        child: Row(
                          children: [
                            Text('￥',style: TextStyle(color: AppColors.couponPriceColor,fontSize: 20.sp,fontWeight: FontWeight.bold),),
                            Text('${totalPrice}',style: TextStyle(color: AppColors.couponPriceColor,fontSize: 32.sp,fontWeight: FontWeight.bold),),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w,),
                    Text(S.current.enjoy_coupon,style: TextStyle(color: Colors.black,fontSize: ref.read(languageProvider) == 'ug' ? mainSize : titleSize),),
                  ],
                ),
                Container(
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(horizontal: 20.w,vertical: 6),
                  margin: ref.read(languageProvider) == 'ug' ? EdgeInsets.only(left: 10.w) : EdgeInsets.only(right: 10.w),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30.w),
                    gradient: LinearGradient(
                      colors: [AppColors.couponbtnColor1, AppColors.couponbtnColor2], // 渐变的颜色列表
                      begin: Alignment.centerRight, // 渐变开始位置
                      end: Alignment.centerLeft, // 渐变结束位置
                    ),
                  ),
                  child: Text(S.current.courtesy_page_new,style: TextStyle(color: Colors.white,fontSize: mainSize,fontWeight: FontWeight.bold),),
                )
              ],
            ),
            Positioned(
              top: 0,
              right: 0,
              left: 0,
              bottom: 0,
              child: CustomShimmerEffect(
                kval: 0.8,
                highlightColor: Colors.white,
                duration: const Duration(milliseconds: 2000),
                child: Container(
                  color: Colors.white,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
