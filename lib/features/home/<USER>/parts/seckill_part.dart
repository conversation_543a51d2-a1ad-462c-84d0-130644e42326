import 'dart:async';
import 'dart:developer' as developer;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/activity/pages/seckill_page.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:visibility_detector/visibility_detector.dart';

class SeckillPart extends ConsumerWidget {
  const SeckillPart({
    super.key,
    required this.foods,
    required this.totalSecond,
    required this.seckill,
    required this.buildingId,
  });

  final List<SecKillFoods> foods;
  final int totalSecond;
  final int buildingId;
  final Seckill seckill;

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return InkWell(
      onTap: () {
        Navigator.of(context).push(MaterialPageRoute(
          builder: (final context) => SeckillPage(
            buildingId: buildingId,
          ),
        ));
      },
      child: Container(
        margin: EdgeInsets.only(right: 10.w, left: 10.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.w), color: Colors.white),
        padding: EdgeInsets.only(left: 10.w),
        child: Column(
          children: [
            Container(
              height: 46.h,
              padding: EdgeInsets.only(right: 10.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Shimmer.fromColors(
                      baseColor: Colors.black,
                      highlightColor: AppColors.baseGreenColor,
                      child: Text(
                        S.current.sec_kill,
                        style: TextStyle(fontSize: soBigSize),
                      )),
                  Row(
                    children: [
                      Text(
                        S.current.get_more,
                        style: TextStyle(fontSize: titleSize),
                      ),
                      SizedBox(
                        width: 6.w,
                      ),
                      ClipOval(
                        child: Container(
                          color: AppColors.baseGreenColor,
                          width: 22.w,
                          height: 22.w,
                          child: Icon(
                            Icons.arrow_forward_ios,
                            color: Colors.white,
                            size: 16.sp,
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
            // 使用RepaintBoundary包裹产品列表，避免倒计时导致产品列表重绘
            RepaintBoundary(
              child: SecKillProductList(foods: foods,secKillActive: seckill.seckillActive ?? 1,),
            ),

            // 将倒计时部分提取为独立组件
            Stack(
              children: [
                CountdownWidget(
                  totalSecond: totalSecond,
                  seckill: seckill,
                ),
                Positioned(
                  top: 0,
                  right: 0,
                  left: 0,
                  bottom: 0,
                  child: CustomShimmerEffect(
                    kval: 0.2,
                    highlightColor: Colors.white,
                    duration: const Duration(milliseconds: 2000),
                    child: Container(
                      margin: EdgeInsets.only(right: 10.w, bottom: 10.h),
                      height: 36.h,
                      color: Colors.white,
                    ),
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// 独立的倒计时组件
class CountdownWidget extends ConsumerStatefulWidget {
  const CountdownWidget({
    super.key,
    required this.totalSecond,
    required this.seckill,
  });

  final int totalSecond;
  final Seckill seckill;

  @override
  ConsumerState createState() => _CountdownWidgetState();
}

class _CountdownWidgetState extends ConsumerState<CountdownWidget>
    with WidgetsBindingObserver {
  Timer? _timer;
  late int _remainingSeconds;
  bool _isTimerActive = false;
  bool _isVisible = false;

  // 使用绝对结束时间而不是相对倒计时
  late DateTime _endTime;

  @override
  void initState() {
    super.initState();
    // 初始化结束时间
    _remainingSeconds = widget.totalSecond;
    _calculateEndTime();

    // 注册应用生命周期观察者
    WidgetsBinding.instance.addObserver(this);

    developer
        .log('CountdownWidget initialized, totalSecond: ${widget.totalSecond}');
  }

  @override
  Widget build(final BuildContext context) {
    return VisibilityDetector(
      // 使用唯一的key确保不会冲突
      key: Key('countdown_visibility_${widget.seckill.seckillingTime}'),
      onVisibilityChanged: (final visibilityInfo) {
        // 当可见比例超过10%时认为是可见的
        _handleVisibilityChanged(visibilityInfo.visibleFraction > 0.1);
      },
      child: _buildCountdownUI(),
    );
  }

  // 计算绝对结束时间
  void _calculateEndTime() {
    _endTime = DateTime.now().add(Duration(seconds: _remainingSeconds));
    developer.log('End time calculated: $_endTime');
  }

  // 根据结束时间计算剩余秒数
  void _updateRemainingTime() {
    final now = DateTime.now();
    final remaining = _endTime.difference(now).inSeconds;

    // 确保剩余时间不为负
    _remainingSeconds = remaining > 0 ? remaining : 0;

    developer.log('Remaining time updated: $_remainingSeconds seconds');
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 如果组件挂载时可见则启动计时器
    if (_isVisible) {
      startCountdown();
    }
  }

  @override
  void didUpdateWidget(final CountdownWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果总秒数发生变化，重置倒计时
    if (widget.totalSecond != oldWidget.totalSecond) {
      _remainingSeconds = widget.totalSecond;
      _calculateEndTime();
      if (_isVisible && !_isTimerActive) {
        startCountdown();
      }
      developer.log('Widget updated with new time: ${widget.totalSecond}');
    }
  }

  @override
  void didChangeAppLifecycleState(final AppLifecycleState state) {
    developer.log('App lifecycle changed: $state');
    // 监听应用生命周期变化
    if (state == AppLifecycleState.resumed) {
      // 应用返回前台，检查并更新倒计时
      if (_isVisible && !_isTimerActive) {
        _updateRemainingTime();
        startCountdown();
        developer.log('App resumed, remaining seconds: $_remainingSeconds');
      }
    } else if (state == AppLifecycleState.paused) {
      // 应用进入后台，暂停计时器
      pauseCountdown();
      developer.log('App paused, timer stopped');
    }
  }

  // 每秒更新一次倒计时
  void startCountdown() {
    if (_isTimerActive) {
      developer.log('Timer already active, not starting again');
      return; // 防止重复启动
    }

    // 再次检查和更新剩余时间，确保计时准确
    _updateRemainingTime();

    developer.log('Starting countdown, remaining: $_remainingSeconds seconds');
    _isTimerActive = true;
    _timer = Timer.periodic(const Duration(seconds: 1), (final timer) {
      if (!mounted) {
        timer.cancel();
        _isTimerActive = false;
        developer.log('Widget unmounted, timer cancelled');
        return;
      }

      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
          // 每10秒重新同步一次时间，确保准确性
          if (_remainingSeconds % 10 == 0) {
            _updateRemainingTime();
            developer.log(
                'Time resynchronized, remaining: $_remainingSeconds seconds');
          }
        } else {
          pauseCountdown(); // 倒计时结束时停止定时器
          developer.log('Countdown finished, timer stopped');
        }
      });
    });
  }

  // 暂停倒计时
  void pauseCountdown() {
    if (_timer != null) {
      _timer!.cancel();
      _timer = null;
      _isTimerActive = false;
      developer.log('Countdown paused');
    }
  }

  // 处理可见性变化
  void _handleVisibilityChanged(final bool isVisible) {
    developer.log('Visibility changed: $isVisible');
    // 避免重复设置相同的可见性
    if (_isVisible != isVisible) {
      _isVisible = isVisible;
      if (isVisible) {
        // 可见时更新时间并启动计时器
        _updateRemainingTime();
        startCountdown();
        developer.log('Became visible, remaining seconds: $_remainingSeconds');
      } else {
        // 不可见时暂停计时器
        pauseCountdown();
        developer.log('Became invisible, timer stopped');
      }
    }
  }

  Map<String, String> convertSeconds(final int totalSeconds) {
    int minutes = totalSeconds ~/ 60; // 计算分钟
    int seconds = totalSeconds % 60; // 计算剩余的秒数

    // 格式化为 2 位数，分钟和秒数都小于 10 时前面补零
    String formattedMinutes = minutes.toString().padLeft(2, '0');
    String formattedSeconds = seconds.toString().padLeft(2, '0');

    return {'minutes': formattedMinutes, 'seconds': formattedSeconds};
  }

  /// 构建倒计时UI
  Widget _buildCountdownUI() {
    Map<String, String> timeMap = convertSeconds(_remainingSeconds);
    String lang = ref.watch(languageProvider);
    int secKillActive = widget.seckill.seckillActive ?? 1;

    return Container(
      margin: EdgeInsets.only(right: 10.w, bottom: 10.h),
      height: 36.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.w),
          color: AppColors.baseGreenColor),
      child: secKillActive == 1 ? Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            width: 5.w,
          ),
          Image.asset(
            'assets/images/seckill_${lang}.png',
            fit: BoxFit.cover,
            width: 18.w,
            height: 18.w,
          ),
          SizedBox(
            width: 10.w,
          ),
          _timeWidget(widget.seckill.seckillingTime ?? '', lang),
          Text(
            S.current.in_activity,
            style: TextStyle(
                fontSize: lang == 'ug' ? littleSize : mainSize,
                color: Colors.white,
                fontWeight: FontWeight.bold),
          ),
          _timeWidget('${timeMap['minutes']}', lang),
          Text(
            S.current.in_minute,
            style: TextStyle(
                fontSize: littleSize,
                color: Colors.white,
                fontWeight: FontWeight.bold),
          ),
          _timeWidget('${timeMap['seconds']}', lang),
          Text(
            S.current.in_end,
            style: TextStyle(
                fontSize: lang == 'ug' ? littleSize : mainSize,
                color: Colors.white,
                fontWeight: FontWeight.bold),
          ),
        ],
      ) : Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            width: 5.w,
          ),
          Image.asset(
            'assets/images/seckill_${lang}.png',
            fit: BoxFit.cover,
            width: 18.w,
            height: 18.w,
          ),
          SizedBox(
            width: 10.w,
          ),
          Text(
            S.current.has_food_pre,
            style: TextStyle(
                fontSize: lang == 'ug' ? littleSize : mainSize,
                color: Colors.white,
                fontWeight: FontWeight.bold),
          ),
          SizedBox(
            width: 5.w,
          ),
          _timeWidget('${timeMap['minutes']}', lang),
          Text(
            S.current.in_minute,
            style: TextStyle(
                fontSize: littleSize,
                color: Colors.white,
                fontWeight: FontWeight.bold),
          ),
          _timeWidget('${timeMap['seconds']}', lang),
          Text(
            S.current.in_start,
            style: TextStyle(
                fontSize: lang == 'ug' ? littleSize : mainSize,
                color: Colors.white,
                fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  ///时间元素
  Widget _timeWidget(final String timeVal, final String lang) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(2.w), color: Colors.white),
      child: Text(
        timeVal,
        style: TextStyle(
            fontSize: lang == 'ug' ? littleSize : secondSize,
            color: Colors.red,
            fontWeight: FontWeight.bold),
      ),
    );
  }

  @override
  void dispose() {
    // 移除观察者并清理定时器
    WidgetsBinding.instance.removeObserver(this);
    pauseCountdown();
    developer.log('CountdownWidget disposed');
    super.dispose();
  }
}

/// 秒杀产品列表组件，与计时器状态分离
class SecKillProductList extends StatelessWidget {
  final List<SecKillFoods> foods;
  final int secKillActive;

  const SecKillProductList({super.key, required this.foods, required this.secKillActive});

  @override
  Widget build(final BuildContext context) {
    return foods.length == 1 ?
      Container(
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10.w),
              child: PrefectImage(
                key: ValueKey('seckill_${foods[0].id}'),
                imageUrl: foods[0].image ?? '',
                width: 100.w,
                height: 100.w,
                fit: BoxFit.fill,
                cacheKey: 'seckill_${foods[0].id}',
              ),
            ),
            Expanded(
              child: Column(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 10.w,vertical: 8.w),
                    child: Row(
                      children: [
                        Text('${foods[0].name}',style: TextStyle(fontSize: titleSize,fontWeight: FontWeight.bold),)
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 10.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(width: 3.w,),
                            Image.asset(
                              'assets/images/huo.png',
                              fit: BoxFit.fill,
                              width: 15.w,
                              height: 17.w,
                            ),
                            SizedBox(width: 5.w,),

                            Text(
                              '￥',
                              style: TextStyle(
                                fontSize: littleSize,
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'NumberFont',
                              ),
                            ),

                            Text(
                              '${FormatUtil.formatAmount(foods[0].price ?? 0)}',
                              style: TextStyle(
                                fontSize: titleSize,
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'NumberFont',
                              ),
                            ),



                            SizedBox(width: 5.w,),
                            Padding(
                              padding: EdgeInsets.only(top: 5.w),
                              child: Text(
                                '￥${FormatUtil.formatAmount(foods[0].oldPrice ?? 0)}',
                                style: TextStyle(
                                  fontSize: secondSize,
                                  color: AppColors.textSecondaryColor,
                                  decoration: TextDecoration.lineThrough,
                                  decorationColor: AppColors.textSecondaryColor,
                                ),
                              ),
                            ),

                          ],
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 15.w,vertical: 5.w),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(30.w),
                              color: secKillActive == 1 ? AppColors.baseGreenColor : AppColors.textSecondColor.withAlpha(180)
                          ),
                          child: Text(S.current.sec_kill_buy,style: TextStyle(color: Colors.white,fontSize: titleSize),),
                        )
                      ],
                    ),
                  )
                ],
              ),
            )

          ],
        ),
      ) :
      SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: List.generate(
            foods.length,
            (final index) => _secKillItem(foods[index]),
          ),
        ),
      );
  }

  ///秒杀元素
  Widget _secKillItem(final SecKillFoods food) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.only(right: 10.w, bottom: 8.h),
          child: Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(10.w),
                child: PrefectImage(
                  key: ValueKey('seckill_${food.id}'),
                  imageUrl: food.image ?? '',
                  width: 100.w,
                  height: 100.w,
                  fit: BoxFit.fill,
                  cacheKey: 'seckill_${food.id}',
                ),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(10.w),
                        bottomLeft: Radius.circular(10.w),
                      ),
                      color: AppColors.baseGreenColor),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Text(
                        '￥${FormatUtil.formatAmount(food.oldPrice ?? 0)}',
                        style: TextStyle(
                          fontSize: soLittleSize,
                          color: AppColors.littleGreenColor,
                          decoration: TextDecoration.lineThrough,
                          decorationColor: AppColors.littleGreenColor,
                        ),
                      ),
                      Text(
                        '￥${FormatUtil.formatAmount(food.price ?? 0)}',
                        style: TextStyle(
                          fontSize: littleSize,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'NumberFont',
                        ),
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
        Container(
          margin: EdgeInsets.only(right: 10.w),
          width: 100.w,
          child: Text(
            '${food.name}',
            style: TextStyle(fontSize: mainSize),
            overflow: TextOverflow.ellipsis, // 超出部分显示省略号
            textAlign: TextAlign.center,
            maxLines: 1, // 只显示一行
          ),
        ),
        SizedBox(
          height: 8.h,
        ),
      ],
    );
  }
}

/// 自定义闪光动画效果
class CustomShimmerEffect extends StatefulWidget {
  final Widget child;
  final Color highlightColor;
  final double kval;
  final double highlightWidth;
  final Duration duration;

  const CustomShimmerEffect({
    final Key? key,
    required this.child,
    this.highlightColor = Colors.white,
    this.kval = 0.2, // 闪光区域的宽度比例，默认0.3
    this.highlightWidth = 0.3, // 闪光区域的宽度比例，默认0.3
    this.duration = const Duration(milliseconds: 1500),
  }) : super(key: key);

  @override
  State<CustomShimmerEffect> createState() => _CustomShimmerEffectState();
}

class _CustomShimmerEffectState extends State<CustomShimmerEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late Widget _child;
  late double mkVal;

  @override
  void initState() {
    super.initState();
    mkVal = 0 - widget.kval;
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    )..repeat();

    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(_controller);
    _child = AnimatedBuilder(
      animation: _animation,
      builder: (final context, final child) {
        return ShaderMask(
          shaderCallback: (final bounds) {
            return LinearGradient(
              // begin: Alignment.topRight,
              // end: Alignment.bottomLeft,
              begin: Alignment(widget.kval, -1.7), // 更靠右上
              end: Alignment(mkVal, 1.7),
              colors: [
                Colors.white.withOpacity(0.0),
                widget.highlightColor,
                Colors.white.withOpacity(0.0),
              ],
              stops: [
                0.1,
                0.5,
                0.9,
              ],
              transform: _SlidingGradientTransform(
                slidePercent: _animation.value,
                highlightWidth: widget.highlightWidth,
              ),
            ).createShader(bounds);
          },
          child: child,
        );
      },
      child: widget.child,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return _child ?? SizedBox.shrink();
  }
}

class _SlidingGradientTransform extends GradientTransform {
  const _SlidingGradientTransform({
    required this.slidePercent,
    required this.highlightWidth,
  });

  final double slidePercent;
  final double highlightWidth;

  @override
  Matrix4? transform(final Rect bounds, {final TextDirection? textDirection}) {
    // 根据highlightWidth调整滑动范围
    final slideRange = 1.0 + highlightWidth;
    final slideOffset = slidePercent * slideRange - (highlightWidth / 2);
    return Matrix4.translationValues(bounds.width * slideOffset, 0.0, 0.0);
  }
}
