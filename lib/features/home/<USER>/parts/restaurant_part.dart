import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/home/<USER>/parts/restaurant_item_part.dart';
import 'package:user_app/features/home/<USER>/home_provider.dart';


class RestaurantPart extends ConsumerWidget {
  const RestaurantPart({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    List<RestaurantItems> restaurantItem = ref.watch(restaurantListProvider);
    return ListView.builder(
      padding: EdgeInsets.zero,
      itemCount: restaurantItem.length,
      itemBuilder: (final BuildContext context, final int index) {
        return RestaurantItemPart(restaurantItem: restaurantItem[index]);
      },
    );
  }
}
