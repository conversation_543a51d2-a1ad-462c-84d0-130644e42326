import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/utils/wechat_util.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/activity/pages/seckill_page.dart';
import 'package:user_app/features/activity/pages/special/special_page.dart';
import 'package:user_app/features/index/pages/main_page.dart';
import 'package:user_app/routes/app_router.dart';
import 'package:user_app/routes/paths.dart';

///幻灯片部分
Widget swiperPart(final List<PopupAdver> adverList, final double screenWidth,int buildingId) {
  //缓存_cardItem
  final List<Widget> swiperItems = adverList.map((final adver) {
    return _cardItem(adver, screenWidth, adver.id ?? 0,buildingId);
  }).toList();

  return Container(
    width: screenWidth,
    height: 140.h,
    padding: EdgeInsets.only(top: 10.h),
    decoration: BoxDecoration(
        // borderRadius: BorderRadius.only(
        //   topLeft: Radius.circular(15.h),
        //   topRight: Radius.circular(15.h),
        // ),
        color: Colors.white),
    child: Swiper(
      itemBuilder: (final BuildContext context, final int index) {
        // 包装在RepaintBoundary中避免不必要的重绘
        return RepaintBoundary(
          child: swiperItems[index],
        );
      },
      viewportFraction: 0.95,
      scale: 0.92,
      autoplay: true,
      itemCount: adverList.length,
      autoplayDelay: 4000, // 自动播放延迟（单位：毫秒）
      duration: 500, // 设置滚动动画的持续时间（单位：毫秒）
      pagination: SwiperPagination(
          margin: EdgeInsets.zero,
          builder: SwiperCustomPagination(builder: (context, config) {
            return ConstrainedBox(
              constraints: const BoxConstraints.expand(height: 30.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center, // 居中显示
                children: List.generate(config.itemCount, (index) {
                  // 自定义 Container 作为分页指示器
                  return Container(
                    width: index == config.activeIndex
                        ? 18.0.w
                        : 10.0.w, // 当前激活的点更宽
                    height: 6.0.h, // 点的高度
                    margin:
                        const EdgeInsets.symmetric(horizontal: 3.0), // 点之间的间距
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(5.h),
                        bottomLeft: Radius.circular(5.h),
                      ),
                      color: index == config.activeIndex
                          ? AppColors.primaryGreenColor
                              .withOpacity(0.8) // 当前激活的点为红色
                          : Colors.grey.withOpacity(0.6), // 非激活的点为灰色
                    ),
                  );
                }),
              ),
            );
          })),
    ),
  );
}

Widget _cardItem(PopupAdver adverImgListItem, double screenWidth, int index,int buildingId) {
  // 确保图片URL存在
  if (adverImgListItem.imageUrl == null || adverImgListItem.imageUrl!.isEmpty) {
    return Container(); // 如果没有图片URL，返回空容器
  }

  return GestureDetector(
    onTap: () {
      _handleNavigation(adverImgListItem,buildingId);
    },
    child: PhysicalModel(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(10.h),
      clipBehavior: Clip.antiAlias,
      // child: swiperItem,
      child: Center(
        child: PrefectImage(
          key: ValueKey('banner_${adverImgListItem.id ?? index}'),
          imageUrl: adverImgListItem.imageUrl!,
          width: screenWidth,
          height: 170.h,
          fit: BoxFit.fill,
          cacheKey: 'banner_${adverImgListItem.id ?? index}',
        ),
      ),
    ),
  );
}

Future<void> _handleNavigation(PopupAdver adver,int buildingId) async {
  try {
    switch (adver.linkType) {
      case 4:
      case 5:
        // 跳转优惠券
        if(AppContext().currentContext != null){
          if(adver.miniProgramLinkPage != null && adver.miniProgramLinkPage != ''){
            if(adver.miniProgramLinkPage!.contains('special')){
              Navigator.of(AppContext().currentContext!).push(MaterialPageRoute(builder: (context) => SpecialPage(buildingId: buildingId,),));
            }else if(adver.miniProgramLinkPage!.contains('discount')){
              Navigator.of(AppContext().currentContext!).push(MaterialPageRoute(builder: (context) => SeckillPage(buildingId: buildingId,),));
            }else{
              MainPageTabs.navigateToTab(AppContext().currentContext!, MainPageTabs.discount);
            }
          }
        }
        break;

      case 3:
        // 跳转别的小程序
        if(adver.miniProgramId != null && adver.miniProgramId != ''){
          await WechatUtil().launchMiniProgram(adver.miniProgramId!);
        }
        break;

      case 1:
        // 跳转美食列表
        router.push(AppPaths.restaurantDetailPage, extra: {
          'restaurantId': adver.linkId ?? 0,
          'buildingId': 0,
          'ids': [],
        });
        break;

      case 2:
        // 跳转美食列表
        router.push(AppPaths.restaurantDetailPage, extra: {
          'restaurantId': adver.storeId ?? 0,
          'buildingId': 0,
          'ids': [],
        });
        break;

      case 0:
        if (adver.linkUrl != null && adver.linkUrl!.isNotEmpty) {
          router.push(AppPaths.webViewPage, extra: {
            'url': adver.linkUrl,
            'title': 'مۇلازىم',
          });
        }
        break;

      default:
        if(AppContext().currentContext != null){
          MainPageTabs.navigateToTab(AppContext().currentContext!, MainPageTabs.discount);
        }
        break;
    }
  } catch (e) {
    debugPrint('路由跳转错误: $e');
    // 可以在这里添加错误处理逻辑
  }
}
