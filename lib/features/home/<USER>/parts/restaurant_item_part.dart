import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/widgets/prefect_image_height.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/generated/l10n.dart';

import 'package:user_app/routes/paths.dart';

import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';

class RestaurantItemPart extends ConsumerWidget {
  final RestaurantItems restaurantItem;
  const RestaurantItemPart({super.key, required this.restaurantItem});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return _restaurantItem(context, ref, restaurantItem);
  }
}

///餐厅元素
Widget _restaurantItem(final BuildContext context, final WidgetRef ref,
    final RestaurantItems item) {
  final buildingId = ref.watch(homeNoticeProvider).value?.location?.id ?? 0;
  List<MarketTag> allTag = [];
  List<MarketTag> marketTag = item.marketTag ?? [];
  List<TakeTag> takeTag = item.takeTag ?? [];
  allTag.addAll(marketTag);
  takeTag.forEach((final v) {
    allTag.add(MarketTag(
        type: 3,
        color: v.color,
        title: v.title,
        titleUg: v.title,
        titleZh: v.title,
        background: v.background,
        image: '',
        borderColor: v.borderColor));
  });

  bool isMulti = false;
  for (int i = 0; i < allTag.length; i++) {
    if (allTag[i].image != '') {
      isMulti = true;
      break;
    }
  }
  bool isShow = false;
  return StatefulBuilder(builder: (final inContext, final setInState) {
    return InkWell(
      onTap: () {
        context.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': item.id ?? 0,
            'buildingId': buildingId,
            'ids': [],
          },
        );
      },
      child: Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.w), color: Colors.white),
          margin: EdgeInsets.only(right: 10.w, left: 10.w, bottom: 10.w),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                      padding: EdgeInsets.all(10.w),
                      child: Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(10.w),
                            child: PrefectImage(
                              imageUrl: item.logo ?? '',
                              width: 90.w,
                              height: 90.w,
                              fit: BoxFit.fitWidth,
                            ),
                          ),
                          if (((item.isNew ?? 0) > 0 || (item.hasFoodPre ?? 0) > 0 || (item.marketTag ?? []).isNotEmpty) && (item.resting ?? 0) == 0)
                            Positioned(
                              bottom: 0,
                              child: Container(
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.only(
                                    bottomRight: Radius.circular(10.w),
                                    bottomLeft: Radius.circular(10.w),
                                  ),
                                  gradient: LinearGradient(
                                    colors: [
                                      AppColors.restaurantBgStartColor,
                                      AppColors.restaurantBgEndColor
                                    ], // 渐变的颜色列表
                                    begin: Alignment.centerRight, // 渐变开始位置
                                    end: Alignment.centerLeft, // 渐变结束位置
                                  ),
                                  border: Border(
                                      top: BorderSide(
                                          color: Colors.white, width: 1.w)),
                                ),
                                height: 24.w,
                                width: 90.w,
                                child: ((item.isNew ?? 0) > 0 && (item.resting ?? 0) == 0)
                                    ? Text(
                                        S.current.is_new,
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontSize: secondSize),
                                        textAlign: TextAlign.center,
                                      )
                                    : SizedBox(),
                              ),
                            ),


                          (((item.hasFoodPre ?? 0) > 0 || (item.marketTag ?? []).isNotEmpty) && (item.resting ?? 0) == 0 && (item.isNew ?? 0) == 0)
                              ? Positioned(
                                  bottom: 3.w,
                                  right: 0,
                                  left: 0,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      Image.asset(
                                        'assets/images/fire.png',
                                        fit: BoxFit.cover,
                                        width: 20.w,
                                      ),
                                      SizedBox(
                                        width: 3.w,
                                      ),
                                      Text(
                                        S.current.has_food_pre,
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontSize: secondSize),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ))
                              : SizedBox(),
                          (item.resting ?? 0) == 1
                              ? Positioned(
                                  child: Container(
                                    alignment: Alignment.center,
                                    width: 90.w,
                                    height: 90.w,
                                    decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(10.w),
                                        color: Colors.grey.withOpacity(0.75)),
                                    child: Text(
                                      S.current.resting,
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontSize: secondSize,
                                          fontWeight: FontWeight.bold),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                )
                              : SizedBox()
                        ],
                      )),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.name ?? '',
                          style: TextStyle(fontSize: mainSize),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(
                          height: 5.h,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  '${item.starAvg ?? 5}',
                                  style: TextStyle(
                                      fontSize: secondSize,
                                      color: AppColors.baseOrangeColor,
                                      fontWeight: FontWeight.bold),
                                ),
                                Icon(
                                  Icons.star,
                                  color: AppColors.baseOrangeColor,
                                  size: mainSize,
                                ),
                                SizedBox(
                                  width: 10.w,
                                ),
                                Text(
                                  '${S.current.month_order_count}:${item.monthOrderCount ?? ''}',
                                  style: TextStyle(
                                      fontSize: secondSize,
                                      color: AppColors.textSecondColor),
                                ),
                                SizedBox(
                                  width: 10.w,
                                ),
                                Text(
                                  '${item.avgDeliveryTime ?? ''} ${S.current.minute}',
                                  style: TextStyle(
                                      fontSize: secondSize,
                                      color: AppColors.textSecondColor),
                                ),
                              ],
                            ),
                            Text(
                              '${item.distance ?? '0'}km',
                              style: TextStyle(
                                  fontSize: secondSize,
                                  color: AppColors.textSecondColor),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 10.h,
                        ),
                        // Wrap(
                        //   spacing: 5.w,
                        //   runSpacing: 4.w,
                        //   // mainAxisAlignment: MainAxisAlignment.start,
                        //   children: List.generate(allTag.length, (tagIndex)=>_borderContainer(allTag[tagIndex])),
                        //   // children: [
                        //   //   _borderContainer(baseGreenColor,'مۇلازىم يەتكۈزىدۇ'),
                        //   //   SizedBox(width: 10.w,),
                        //   //   _borderContainer(baseOrangeColor,'ئۆزۈم ئېلۋالاي'),
                        //   // ],
                        // ),
                        Row(
                          children: [
                            isShow
                                ? _borderContainer(allTag[0], ref)
                                : Expanded(
                                    child: SingleChildScrollView(
                                        scrollDirection: Axis.horizontal,
                                        child: InkWell(
                                          onTap: (){
                                            setInState(() {
                                              isShow = !isShow;
                                            });
                                          },
                                          child: Row(
                                            children: List.generate(
                                                allTag.length,
                                                (final tagIndex) => _borderContainer(
                                                    allTag[tagIndex], ref)),
                                          ),
                                        )),
                                  ),
                            if (isMulti)
                              !isShow
                                  ? InkWell(
                                    onTap: (){
                                      setInState(() {
                                        isShow = !isShow;
                                      });
                                    },
                                    child: SizedBox(
                                        width: 30.w,
                                        child: Icon(
                                          Icons.keyboard_arrow_down,
                                          color: Colors.black,
                                          size: 20.w,
                                        )),
                                  )
                                  : SizedBox()
                          ],
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 10.w,
                  )
                ],
              ),
              if (isShow)
                Container(
                  width: MediaQuery.of(inContext).size.width - 40.w,
                  margin:
                      EdgeInsets.only(right: 10.w, left: 10.w, bottom: 12.w),
                  child: InkWell(
                    onTap: (){
                      setInState(() {
                        isShow = !isShow;
                      });
                    },
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Expanded(
                          child: Wrap(
                            runSpacing: 10.w,
                            // mainAxisAlignment: MainAxisAlignment.start,
                            children: List.generate(
                                allTag.length - 1,
                                (final tagIndex) =>
                                    _borderContainer(allTag[tagIndex + 1], ref)),
                          ),
                        ),
                        SizedBox(
                            width: 30.w,
                            child: Icon(
                              Icons.keyboard_arrow_up,
                              color: Colors.black,
                              size: 20.w,
                            )),
                      ],
                    ),
                  ),
                )
            ],
          )),
    );
  });
}

Widget _borderContainer(final MarketTag takeTag, final WidgetRef ref) {
  return Container(
    margin: ref.watch(languageProvider) == 'ug'
        ? EdgeInsets.only(left: 10.w)
        : EdgeInsets.only(right: 10.w),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Stack(
          children: [
            Container(
              padding: ref.watch(languageProvider) == 'ug'
                  ? EdgeInsets.only(
                      right: takeTag.image != ''
                          ? (takeTag.type != 2 ? 28.w : 38.w)
                          : 5.w,
                      left: 5.w)
                  : EdgeInsets.only(
                      left: takeTag.image != ''
                          ? (takeTag.type != 2 ? 28.w : 58.w)
                          : 5.w,
                      right: 5.w),
              alignment: Alignment.center,
              height: 20.w,
              decoration: BoxDecoration(
                  border: takeTag.image != ''
                      ? (ref.watch(languageProvider) == 'ug'
                          ? Border(
                              bottom: BorderSide(
                                  width: 0.5.w,
                                  color: FormatUtil.parseColor(
                                      takeTag.color ?? '00FF00')),
                              left: BorderSide(
                                  width: 0.5.w,
                                  color: FormatUtil.parseColor(
                                      takeTag.color ?? '00FF00')),
                              top: BorderSide(
                                  width: 0.5.w,
                                  color: FormatUtil.parseColor(
                                      takeTag.color ?? '00FF00')),
                            )
                          : Border(
                              bottom: BorderSide(
                                  width: 0.5.w,
                                  color: FormatUtil.parseColor(
                                      takeTag.color ?? '00FF00')),
                              right: BorderSide(
                                  width: 0.5.w,
                                  color: FormatUtil.parseColor(
                                      takeTag.color ?? '00FF00')),
                              top: BorderSide(
                                  width: 0.5.w,
                                  color: FormatUtil.parseColor(
                                      takeTag.color ?? '00FF00')),
                            ))
                      : Border.all(
                          color:
                              FormatUtil.parseColor(takeTag.color ?? '00FF00'),
                          width: 0.5.w,
                        ),
                  borderRadius: BorderRadius.circular(5.w),
                  color: Colors.white),
              child: Text(
                takeTag.title ?? '',
                style: TextStyle(
                    color: FormatUtil.parseColor(takeTag.color ?? '00FF00'),
                    fontSize: littleSize),
              ),
            ),
            if (takeTag.image != '')
              Positioned(
                top: 0,
                child: Container(
                  height: 22.w,
                  padding: EdgeInsets.only(bottom: 2.w),
                  child: PrefectImageHeight(
                    imageUrl: takeTag.image ?? '',
                    height: 22.w,
                    fit: BoxFit.fitHeight,
                  ),
                ),
              )
          ],
        )
      ],
    ),
  );
}
