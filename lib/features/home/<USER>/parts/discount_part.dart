import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/activity/pages/discount_detail_page.dart';
import 'package:user_app/features/home/<USER>/parts/seckill_part.dart';
import 'package:user_app/generated/l10n.dart';


class DiscountPart extends ConsumerWidget {
  DiscountPart({super.key, required this.themActivePreferential, required this.themActive,required this.buildingId,required this.areaId, required this.secKillFoods});
  List<ThemActivePreferential>? themActivePreferential;
  List<SecKillFoods>? secKillFoods;
  ThemActive themActive;
  int buildingId;
  int areaId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return discountPart(context,ref);
  }


  ///优惠部分
  Widget discountPart(BuildContext context, WidgetRef ref){
    return InkWell(
      onTap: (){
        Navigator.of(context).push(MaterialPageRoute(builder: (context) => DiscountDetailPage(themActive: themActive,themActivePreferential:themActivePreferential,buildingId: buildingId,secKillFoods: secKillFoods,),));
      },
      child: Container(
        margin: EdgeInsets.only(right:10.w,left: 10.w,bottom: 10.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.w),
            color: FormatUtil.parseColor(themActive.color ?? 'fb3455')
            // color: AppColors.basePinkColor
        ),
        // color: Colors.yellow,
        padding: EdgeInsets.only(left:10.w),
        child: Column(
          children: [
            Container(
              height: 46.h,
              padding: EdgeInsets.only(right: 10.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Image.asset(
                        'assets/images/fireyellow.png',
                        fit: BoxFit.cover,
                        width: 16.w,
                      ),
                      SizedBox(width: 3.w,),
                      Shimmer.fromColors(
                          baseColor: Colors.white,
                          highlightColor: AppColors.baseYellowColor,
                          child: Text('${themActive.name}',style: TextStyle(fontSize: soBigSize,color: Colors.white),)
                      ),
                    ],
                  ),


                  Row(
                    children: [
                      Text(S.current.get_more,style: TextStyle(fontSize: titleSize,color: Colors.white),),
                      SizedBox(width: 6.w,),
                      ClipOval(
                        child: Container(
                          color: AppColors.baseYellowColor,
                          width: 22.w,
                          height: 22.w,
                          child: Icon(Icons.arrow_forward_ios,color: Colors.black,size: 16.sp,),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),

            (themActivePreferential ?? []).length == 1 ?
            _foodItem(themActivePreferential![0],context,ref):
            Container(
              alignment: ref.watch(languageProvider) == 'ug' ? Alignment.centerRight : Alignment.centerLeft,
              child: SingleChildScrollView(
                scrollDirection:Axis.horizontal,
                child: Row(
                  children: List.generate((themActivePreferential ?? []).length, (index)=>_discountItem(themActivePreferential![index],ref)),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///优惠元素
  Widget _discountItem(ThemActivePreferential themActivePreferential, WidgetRef ref){
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.w),
          color: Colors.white
      ),
      width: 120.w,
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      margin: EdgeInsets.only(right:10.w,bottom: 10.w),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top:10.w,bottom: 4.h),
            child:
            // ClipRRect(
            //   borderRadius: BorderRadius.circular(10.w),
            //   child: Image.asset(
            //     'assets/images/sec_2.png',
            //     fit: BoxFit.cover,
            //     width: 100.w,
            //     height: 80.w,
            //   ),
            // ),
            ClipRRect(
              borderRadius: BorderRadius.circular(8.w),
              child: PrefectImage(
                imageUrl: themActivePreferential.image ?? '',
                width: 116.w,
                height: 80.w,
                fit: BoxFit.fill,
              ),
            ),


          ),
          Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              alignment: ref.watch(languageProvider) == 'ug' ? Alignment.centerRight : Alignment.centerLeft,
              child: Text(themActivePreferential.foodName ?? '',style: TextStyle(fontSize: mainSize),maxLines: 1,overflow: TextOverflow.ellipsis,textAlign: TextAlign.start,)
          ),
          SizedBox(height: 2.h,),

          Stack(
            children: [
              Container(
                height: 24.h,
                width: 100.w,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.w),
                    color: AppColors.baseYellowColor
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Text('￥${themActivePreferential.originPrice}',style: TextStyle(fontSize: littleSize,color: AppColors.textSecondColor,decoration: TextDecoration.lineThrough,decorationColor: AppColors.littleGreenColor,),),
                    Text('￥${themActivePreferential.price}',style: TextStyle(fontSize: secondSize,color: Colors.black,fontWeight: FontWeight.bold,fontFamily: 'NumberFont',),),
                  ],
                ),
              ),
              Positioned(
                top: 0,
                right: 0,
                left: 0,
                bottom: 0,
                child: CustomShimmerEffect(
                  kval: 0.8,
                  highlightColor: Colors.white,
                  duration: const Duration(milliseconds: 2000),
                  child: Container(
                    color: Colors.white,
                  ),
                ),
              )
            ],
          ),
          SizedBox(height: 8.h,),
        ],
      ),
    );
  }



  Widget _foodItem(ThemActivePreferential themActivePreferential, BuildContext context,WidgetRef ref){
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.w),
          color: Colors.white
      ),
      margin: EdgeInsets.only(top: 10.w,bottom: 10.w,right: 10.w),
      padding: EdgeInsets.all(10.w),
      child: Row(
        children: [

          ClipRRect(
            borderRadius: BorderRadius.circular(10.w),
            child: PrefectImage(
              imageUrl: themActivePreferential.image ?? '',
              width: 115.w,
              height: 105.w,
              fit: BoxFit.fill,
            ),
          ),
          SizedBox(width: 10.w,),

          Expanded(
            child: Container(
              child: Column(
                children: [
                  Container(
                      alignment: ref.watch(languageProvider) == 'ug' ? Alignment.centerRight : Alignment.centerLeft,
                      child:Text('${themActivePreferential.foodName}',textAlign:TextAlign.start,style: TextStyle(fontSize: titleSize,color: Colors.black),maxLines: 1,overflow: TextOverflow.ellipsis,)
                  ),
                  SizedBox(height: 5.w,),


                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          SizedBox(width: 5.w,),
                          Text('${themActivePreferential.price}',style: TextStyle(fontSize: 20.sp,color: Colors.red,fontWeight: FontWeight.bold,fontFamily: 'NumberFont',)),
                          Text('￥',style: TextStyle(fontSize: mainSize,color: Colors.red)),

                          Text('${themActivePreferential.originPrice}',  style: TextStyle(fontSize: mainSize,decoration: TextDecoration.lineThrough,decorationColor: AppColors.textSecondaryColor,color: AppColors.textSecondaryColor),),
                          Text('￥',style: TextStyle(decoration: TextDecoration.lineThrough,decorationColor: AppColors.textSecondaryColor,fontSize: littleSize,color: AppColors.textSecondaryColor)),
                          SizedBox(width: 5.w,),

                        ],
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 12.w,vertical: 5.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(30.w),
                            gradient: LinearGradient(
                              colors: [AppColors.discountRedSeColor, AppColors.discountRedColor], // 渐变的颜色列表
                              begin: Alignment.centerLeft, // 渐变开始位置
                              end: Alignment.centerRight, // 渐变结束位置
                            ),
                        ),
                        child: Text(S.current.sec_kill_buy,style: TextStyle(color: Colors.white,fontSize: mainSize,fontWeight: FontWeight.bold),),
                      )
                    ],
                  ),

                  SizedBox(height: 5.w,),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Text(S.current.sell_count,style: TextStyle(fontSize: mainSize,color: AppColors.textSecondaryColor),),
                      SizedBox(width: 5.w,),
                      Text('${themActivePreferential.monthOrderCount}',style: TextStyle(fontSize: mainSize,color: AppColors.textSecondaryColor)),
                    ],
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }


}

