import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/repositories/home/<USER>';

///获取地址列表按定位数据
Future<SelfTakeData?> getSelfTakeData(Ref ref,
    {required int buildingId, required int page,required int limit}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'building_id': buildingId,
    'page': page,
    'limit': 10,
  };
  // 秒杀活动数据
  final homeRepository = HomeRepository(apiClient: ref.read(apiClientProvider));
  final selfTakeInfo = await homeRepository.getSelfTake(param);
  return selfTakeInfo?.data;
}

///地址列表按定位数据提供者类
class SelfTakeProvider extends StateNotifier<AsyncValue<SelfTakeData?>> {
  SelfTakeProvider(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> fetchSelfTakeData({required int buildingId, required int page,required int limit}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final response = await getSelfTakeData(ref, buildingId: buildingId,page: page,limit: limit);
      state = AsyncValue.data(response);

      if ((response?.dataList ?? []).length < 10) {
        ref.watch(canSelfTakeDataProvider.notifier).state = false;
      } else {
        ref.watch(canSelfTakeDataProvider.notifier).state = true;
      }

      if (page > 1) {
        ref
            .read(selfTakeListProvider.notifier)
            .state
            .addAll(state.value?.dataList ?? []);
      } else {
        ref.watch(selfTakeListProvider.notifier).state =
            state.value?.dataList ?? [];
      }

    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final selfTakeProvider = StateNotifierProvider.autoDispose<
    SelfTakeProvider, AsyncValue<SelfTakeData?>>(
  (ref) => SelfTakeProvider(ref),
);

final canSelfTakeDataProvider = StateProvider.autoDispose<bool>((ref) => true);

final selfTakeListProvider = StateProvider.autoDispose<List<DataList>>((ref) => []);

