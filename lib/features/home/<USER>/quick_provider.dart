import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/repositories/home/<USER>';


//https://smart.mulazim.com/ug/v1/restaurant/home?pref=0&category_id=1&limit=10&cat_child_id=0&page=1&building_id=1520&sort=auto

//https://smart.mulazim.com/ug/v1/restaurant/home?pref=0&category_id=4&limit=10&cat_child_id=0&page=1&building_id=1520&sort=auto&business_type=2

//https://smart.mulazim.com/ug/v1/restaurant/home?pref=0&category_id=30&limit=10&cat_child_id=0&page=1&building_id=1520&sort=auto&business_type=4

///获取地址列表按定位数据
Future<QuickInfoData?> getQuickInfo(Ref ref,
    {required int buildingId,required int categoryId,required int catChildId}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'pref': 0,
    'category_id': categoryId,
    'limit': 10,
    'cat_child_id': catChildId,
    'page': 1,
    'building_id': buildingId,
    'sort': 'auto',
    'business_type': 2,
  };
  // 秒杀活动数据
  final homeRepository = HomeRepository(apiClient: ref.read(apiClientProvider));
  final selfTakeInfo = await homeRepository.quickInfo(param,categoryId);
  return selfTakeInfo?.data;
}

///地址列表按定位数据提供者类
class QuickProvider extends StateNotifier<AsyncValue<QuickInfoData?>> {
  QuickProvider(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> fetchQuickData({required int buildingId,required int categoryId,required int catChildId}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final response = await getQuickInfo(ref, buildingId: buildingId,categoryId: categoryId,catChildId:catChildId );
      state = AsyncValue.data(response);
      // if ((response?.dataList ?? []).length < 10) {
      //   ref.watch(canSelfTakeDataProvider.notifier).state = false;
      // } else {
      //   ref.watch(canSelfTakeDataProvider.notifier).state = true;
      // }

      // if (page > 1) {
      //   ref
      //       .read(selfTakeListProvider.notifier)
      //       .state
      //       .addAll(state.value?.dataList ?? []);
      // } else {
      //   ref.watch(selfTakeListProvider.notifier).state =
      //       state.value?.dataList ?? [];
      // }

    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

}

final quickProvider = StateNotifierProvider.autoDispose<
    QuickProvider, AsyncValue<QuickInfoData?>>(
  (ref) => QuickProvider(ref),
);

// final canSelfTakeDataProvider = StateProvider.autoDispose<bool>((ref) => true);

