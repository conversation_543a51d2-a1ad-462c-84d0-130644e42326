import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/repositories/home/<USER>';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';

///地址
Future<ApiResult<HomeNoticeData>> getHomeNotice(
  final Ref ref, {
  final int? buildingId,
  final int? areaId,
  final String? lat,
  final String? lng,
}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {};
  if (buildingId != null && areaId != null) {
    param['building_id'] = buildingId;
    param['area_id'] = areaId;
  }
  // 如果有经纬度信息，添加到参数中
  if (lat != null && lng != null) {
    param['lat'] = lat;
    param['lng'] = lng;
  }
  // 如果lat和lng空的时候不允许buildingId为空
  if (lat == null && lng == null && buildingId == null) {
    param['building_id'] = 1979;
    param['area_id'] = 1;
  }

  if (buildingId == 0 || areaId == 0) {
    if ((lat?.isEmpty ?? true) || (lng?.isEmpty ?? true)) {
      param['building_id'] = 1979;
      param['area_id'] = 1;
    }
  }

  log("获取首页通知参数: $param");
  final homeRepository = HomeRepository(apiClient: ref.read(apiClientProvider));
  return await homeRepository.getHomeNotice(param);
}

class HomeNoticeNotifier extends StateNotifier<AsyncValue<HomeNoticeData>> {
  HomeNoticeNotifier(this.ref) : super(const AsyncValue.loading());
  final Ref ref;

  Future<void> fetchHomeData({
    final int? buildingId,
    final int? areaId,
    final String? lat,
    final String? lng,
  }) async {
    try {
      log("开始获取首页通知: buildingId=$buildingId, areaId=$areaId, lat=$lat, lng=$lng");
      // 进行异步请求前先检查是否 mounted
      if (!mounted) {
        log("HomeNoticeNotifier 未挂载，取消请求");
        return;
      }

      final response = await getHomeNotice(
        ref,
        buildingId: buildingId,
        areaId: areaId,
        lng: lng,
        lat: lat,
      );

      log("首页通知获取成功: ${response != null ? '有数据' : '无数据'}");
      final data = response.data;
      if (data == null) {
        state = AsyncValue.data(HomeNoticeData());
        return;
      }
      // 保存本地客服电话
      if (data?.location?.servicePhone != null) {
        log("保存客服电话: ${data.location!.servicePhone}");
        final storage = ref.read(localStorageRepositoryProvider);
        await storage.saveServicePhone(data.location!.servicePhone);
      }

      /// 保存本地信息区域
      if (data?.location != null) {
        log("保存位置信息: buildingId=${data!.location!.id}, areaId=${data.location!.areaId}");
        final storage = ref.read(localStorageRepositoryProvider);
        await storage.saveLocationInfo(data.location!);
      }

      /// 保存企业微信二维码
      if (data?.appConfig != null) {
        log("保存企业微信二维码");
        final storage = ref.read(localStorageRepositoryProvider);
        await storage.saveAppConfig(data.appConfig!);
      }

      state = AsyncValue.data(data);
    } catch (error, stackTrace) {
      log("获取首页通知失败: $error");
      log("错误堆栈: $stackTrace");
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final homeNoticeProvider =
    StateNotifierProvider<HomeNoticeNotifier, AsyncValue<HomeNoticeData>>(
  (final ref) => HomeNoticeNotifier(ref),
);
