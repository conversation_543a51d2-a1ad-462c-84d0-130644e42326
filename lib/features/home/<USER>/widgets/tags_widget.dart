import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/home/<USER>/index/home_controller.dart';

/// 标签选择组件
class TagsWidget extends ConsumerWidget {
  final HomeData? data;

  const TagsWidget({
    final Key? key,
    required this.data,
  }) : super(key: key);

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 直接从homeControllerProvider监听homeData数据
    final homeData = ref
        .watch(homeControllerProvider.select((final state) => state.homeData));

    final selectedTag = ref.watch(
        homeControllerProvider.select((final state) => state.selectedTag));

    final screenWidth = MediaQuery.of(context).size.width;
    List<String> tagIdList = [];

    if (selectedTag.length > 1) {
      tagIdList = selectedTag.split(",");
    } else {
      tagIdList.add(selectedTag);
    }

    // 优先使用从provider获取的homeData中的tags，如果为空再使用传入的data中的tags
    List<Tags> tags = homeData?.tags ?? data?.tags ?? [];

    List<int> tagsState = [];
    for (int i = 0; i < tags.length; i++) {
      tagsState.add(0);
    }

    for (int i = 0; i < tags.length; i++) {
      for (int j = 0; j < tagIdList.length; j++) {
        if (tags[i].id == int.parse(tagIdList[j])) {
          tagsState[i] = 1;
        }
      }
    }

    return StatefulBuilder(builder: (final tagPartContext, final setTagState) {
      return Container(
        // color: Colors.yellow,
        width: screenWidth,
        height: 54.h,
        margin: EdgeInsets.only(top: 8.h),
        padding: EdgeInsets.only(left: 10.w),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: List.generate(
                tags.length,
                (final index) => _tagItem(
                    ref, tags[index], tagsState, setTagState, index, tags)),
          ),
        ),
      );
    });
  }

  Widget _tagItem(
      final WidgetRef ref,
      final Tags? tagItem,
      final List<int> tagsState,
      final StateSetter setTagState,
      final int tagIndex,
      final List<Tags> allTags) {
    return InkWell(
      onTap: () async {
        ///点击tag时切换的算法，id小于10时单元切换，id大于10时多元切换，id大小相互切换时单元切换
        bool clear = false;
        if ((tagItem?.id ?? 0) < 10) {
          clear = true;
        } else {
          for (int i = 0; i < tagsState.length; i++) {
            if ((allTags[i].id ?? 0) < 10 && tagsState[i] == 1) {
              clear = true;
            }
          }
        }
        if (clear) {
          for (int i = 0; i < tagsState.length; i++) {
            if (i == tagIndex) continue;
            tagsState[i] = 0;
          }
          tagsState[tagIndex] = tagsState[tagIndex] == 1 ? 0 : 1;
        } else {
          tagsState[tagIndex] = tagsState[tagIndex] == 1 ? 0 : 1;
        }
        
        for(int i = 0; i < tagsState.length; i++){
          if(tagIndex == 3 && tagsState[2] == 1){
            tagsState[2] = 0;
          }

          if(tagIndex == 2 && tagsState[3] == 1){
            tagsState[3] = 0;
          }
        }

        String ids = '';
        for (int i = 0; i < tagsState.length; i++) {
          if (tagsState[i] == 1) {
            ids += ',${allTags[i].id.toString()}';
          }
        }

        setTagState(() {});

        String selectedTags = '1';
        if (ids.length > 1) {
          selectedTags = ids.substring(1);
        } else {
          selectedTags = '1';
        }
        // 使用HomeController更新选中的标签
        ref
            .read(homeControllerProvider.notifier)
            .updateSelectedTag(selectedTags, context: ref.context);
      },
      child: Stack(
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 10.w, horizontal: 20.w),
            margin: EdgeInsets.only(right: 10.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.w),
                color: tagsState[tagIndex] == 1
                    ? AppColors.baseGreenColor
                    : Colors.white),
            child: Text(
              (allTags[tagIndex].name ?? ''),
              style: TextStyle(
                  fontSize: mainSize,
                  color: tagsState[tagIndex] == 1 ? Colors.white : Colors.black),
            ),
          ),
          if(tagsState[tagIndex] == 1)
          ref.watch(languageProvider) == 'ug' ?
          Positioned(
            top: 2.w,
              right: 12.w,
              child: Container(
                child: Icon(Icons.clear,color: Colors.white,size: 16.sp,),
              )
          ):
          Positioned(
              top: 2.w,
              left: 4.w,
              child: Container(
                child: Icon(Icons.clear,color: Colors.white,size: 16.sp,),
              )
          )
        ],
      ),


    );
  }
}
