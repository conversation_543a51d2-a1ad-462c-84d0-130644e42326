import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/icon_font.dart';
import 'package:user_app/features/home/<USER>/index/home_controller.dart';
import 'package:user_app/features/search/pages/search_page.dart';
import 'package:user_app/features/search/widgets/custom_page_route.dart';
import 'package:user_app/generated/l10n.dart';

/// 头部搜索和语言切换组件
class HeaderWidget extends ConsumerWidget {
  const HeaderWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 使用select只监听language变化
    final language = ref.watch(languageProvider);

    return Container(
      // color: Colors.black,
      padding: EdgeInsets.only(top: 50.w),
      child:
      Directionality(
        textDirection: TextDirection.rtl,
        child:
        Row(
          children: [
            SizedBox(width: 20.w),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  Navigator.of(context).push(CustomPageRoute(SearchPage()));
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30.w),
                    color: Colors.white,
                  ),
                  padding:EdgeInsets.symmetric(vertical: 6.5.w, horizontal: 16.w),
                  child: Directionality(
                    textDirection: ref.watch(languageProvider) == 'ug' ? TextDirection.rtl : TextDirection.ltr,
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(IconFont.search,
                            size: 20.w, color: AppColors.homeSearchIconColor),
                        SizedBox(width: 6.w),
                        Expanded(
                          child: Text(
                            S.current.search_place_holder_text,
                            style: TextStyle(
                                fontSize: mainSize,
                                color: AppColors.homeSearchPlaceHolderColor),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: 10.w),
            InkWell(
              onTap: () {
                ref
                    .read(homeControllerProvider.notifier)
                    .toggleLanguage(context);
              },
              child: Container(
                margin: EdgeInsets.only(top: 2.w),
                padding: EdgeInsets.all(3.5.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100.w),
                  color: AppColors.languageBgColor,
                ),
                child: Image.asset(
                  language == 'ug'
                      ? 'assets/images/ug1.png'
                      : 'assets/images/zh1.png',
                  fit: BoxFit.fill,
                  height: 30.w,
                ),
              ),
            ),
            SizedBox(width: 10.w),
          ],
        ),
      ),
    );
  }
}
