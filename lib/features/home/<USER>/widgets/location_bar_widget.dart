import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/utils/wechat_util.dart';
import 'package:user_app/features/home/<USER>/index/home_controller.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';
import 'package:user_app/generated/l10n.dart';

/// 位置信息栏组件
class LocationBarWidget extends ConsumerWidget {
  const LocationBarWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 使用select监听位置信息变化
    final buildingName = ref.watch(homeNoticeProvider.select((state) =>
        state.value?.location?.buildingName ?? S.current.select_location));

    final controller = ref.read(homeControllerProvider.notifier);

    return InkWell(
      onTap: () {
        controller.handleSelectAddress();
      },
      child: Container(
          // color: Colors.black,
          padding: EdgeInsets.symmetric(vertical: 10.w, horizontal: 25.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            mainAxisSize: MainAxisSize.max,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Icon(
                    Icons.location_on,
                    color: Colors.white,
                    size: 24.w,
                  ),
                  SizedBox(
                      width: MediaQuery.of(context).size.width - 120.w,
                      child: Text(
                        buildingName,
                        style:
                            TextStyle(fontSize: titleSize, color: Colors.white),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      )),
                ],
              ),
              InkWell(
                onTap: () async {
                  // Navigator.of(context).push(CustomPageRoute(LoginBasePage()));
                  await WechatUtil().openCustomerServiceChat();
                },
                child: Image.asset(
                  'assets/images/contact.png',
                  fit: BoxFit.fill,
                  width: 30.w,
                  height: 30.w,
                ),

                //
                //
                // Icon(
                //   Icons.headset_mic_outlined,
                //   color: Colors.white,
                //   size: 30.w,
                // )
              ),
            ],
          )),
    );
  }
}
