import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/home/<USER>/parts/coupon_part.dart';
import 'package:user_app/features/home/<USER>/parts/discount_part.dart';
import 'package:user_app/features/home/<USER>/parts/navigation_part.dart';
import 'package:user_app/features/home/<USER>/parts/seckill_part.dart';
import 'package:user_app/features/home/<USER>/parts/special_part.dart';
import 'package:user_app/features/home/<USER>/parts/swiper_part.dart';
import 'package:user_app/features/home/<USER>/widgets/order_ranking_widget.dart';
import 'package:user_app/features/home/<USER>/widgets/tags_widget.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';
import 'package:user_app/features/home/<USER>/sticky_header_delegate.dart';

/// 滚动头部组件
/// Widget to display scrollable header content for the home page
class SliverHeadersWidget {
  /// The home data model containing information to be displayed
  final HomeData? data;

  /// Creates a SliverHeadersWidget
  const SliverHeadersWidget({
    required this.data,
  });

  /// 创建Sliver小部件列表
  List<Widget> build(final BuildContext context, final WidgetRef ref) {
    // 使用select获取位置ID
    final buildingId = ref.watch(
      homeNoticeProvider
          .select((final state) => state.value?.location?.id ?? 0),
    );

    final areaId = ref.watch(
      homeNoticeProvider
          .select((final state) => state.value?.location?.areaId ?? 0),
    );


    List<CouponListItem> couponPin = ref.watch(homeNoticeProvider.select((final state) => state.value?.couponPin ?? []),);
    List<CouponListItem> coupon = ref.watch(homeNoticeProvider.select((final state) => state.value?.coupon ?? []),);

    final screenWidth = MediaQuery.of(context).size.width;
    final List<Widget> headers = [];

    // 添加轮播图
    if ((data?.adverList ?? []).isNotEmpty) {
      headers.add(
        SliverToBoxAdapter(
          child: swiperPart(data!.adverList!, screenWidth,data?.location?.id ?? 0),
        ),
      );
    }

    // 添加导航分类
    if ((data?.categories ?? []).isNotEmpty) {
      headers.add(
        SliverToBoxAdapter(
          child: NavigationPart(
            categories: data!.categories!,
            buildingId: buildingId,
          ),
        ),
      );
    }

    // 优惠券
    if ((couponPin ?? []).isNotEmpty) {
      headers.add(
        SliverToBoxAdapter(
          child: CouponPart(couponPin:couponPin,coupon:coupon),
        ),
      );
    }

    // 添加订单排行榜
    if (data?.orderRankingActivity != null) {
      headers.add(
        SliverToBoxAdapter(
          child: OrderRankingWidget(
            ranking: data!.orderRankingActivity!,
          ),
        ),
      );
    }

    // 添加特价部分
    if ((data?.special ?? []).isNotEmpty) {
      headers.add(
        SliverToBoxAdapter(
          child: SpecialPart(
            special: data!.special,
            buildingId: buildingId,
          ),
        ),
      );
    }

    // 添加折扣部分
    if ((data?.themActivePreferential ?? []).isNotEmpty &&
        data?.themActive != null) {
      final localData = data!; // 局部变量可以被提升
      headers.add(
        SliverToBoxAdapter(
          child: DiscountPart(
            themActivePreferential: localData.themActivePreferential,
            secKillFoods: localData.seckill?.foods,
            themActive: localData.themActive!,
            buildingId: buildingId,
            areaId: areaId,
          ),
        ),
      );
    }

    // 添加秒杀部分
    if ((data?.seckill?.foods ?? []).isNotEmpty && data?.seckill != null) {
      final localData = data!; // 局部变量可以被提升
      headers.add(
        SliverToBoxAdapter(
          child: SeckillPart(
            foods: localData.seckill!.foods!,
            totalSecond: localData.seckill!.remainingSecond ?? 0,
            seckill: localData.seckill!,
            buildingId: buildingId,
          ),
        ),
      );
    }

    // 添加标签部分
    headers.add(
      SliverPersistentHeader(
        pinned: true,
        floating: false,
        delegate: StickyHeaderDelegate(
          child: TagsWidget(data: data),
          height: 54.h,
        ),
      ),
    );

    // 直接返回headers列表，适用于headerSliverBuilder
    return headers;
  }
}
