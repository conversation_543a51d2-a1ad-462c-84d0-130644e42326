import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/skeleton_widget.dart';

/// 首页骨架屏
///
/// 与微信小程序的home.skeleton保持一致的加载效果
class HomeSkeleton extends StatelessWidget {
  const HomeSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeleton(
      children: [
        // 轮播图骨架 - 对应微信小程序的swiper部分
        _buildSwiperSkeleton(),

        // 菜单分类骨架 - 对应微信小程序的fine-cate部分
        _buildCategoryMenuSkeleton(),

        // 标签菜单骨架 - 对应微信小程序的enclosure-menu部分
        _buildTagMenuSkeleton(),

        // 餐厅列表骨架 - 对应微信小程序的enclosure-list部分
        ...List.generate(4, (index) => _buildRestaurantItemSkeleton()),

        // 底部间距
        SizedBox(height: 20.h),
      ],
    );
  }

  /// 轮播图骨架 - 对应微信小程序的swiper部分
  Widget _buildSwiperSkeleton() {
    return Container(
      width: 0.95.sw, // 95%宽度，与微信小程序中的width: 95%对应
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      // 提供明确的高度以避免布局问题
      height: 150.h,
      // 使用装饰而不是子控件来避免使用double.infinity导致的NaN错误
      decoration: BoxDecoration(
        color: const Color(0xFFEEEEEE),
        borderRadius:
            BorderRadius.circular(13.r), // 对应微信小程序的border-radius: 26rpx
      ),
    );
  }

  /// 菜单分类骨架 - 对应微信小程序的fine-cate部分
  Widget _buildCategoryMenuSkeleton() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 15.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: List.generate(
          4,
          (index) => Column(
            children: [
              // 分类图标 - 与微信小程序中的cate-img和cate-icon对应
              Container(
                width: 50.w,
                height: 50.w,
                decoration: BoxDecoration(
                  color: const Color(0xFFEEEEEE),
                  borderRadius: BorderRadius.circular(25.r),
                ),
              ),
              SizedBox(height: 8.h),
              // 分类名称 - 与微信小程序中的cate-name对应
              SkeletonItem(
                width: 70.w,
                height: 16,
                borderRadius: 4,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 标签菜单骨架 - 对应微信小程序的enclosure-menu部分
  Widget _buildTagMenuSkeleton() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      margin: EdgeInsets.only(bottom: 5.h),
      child: Row(
        children: [
          // 标签项 - 与微信小程序中的menu-item对应
          SkeletonItem(
            width: 60.w,
            height: 30,
            margin: EdgeInsets.only(right: 10.w),
            borderRadius: 4,
          ),
          SkeletonItem(
            width: 60.w,
            height: 30,
            margin: EdgeInsets.only(right: 10.w),
            borderRadius: 4,
          ),
          SkeletonItem(
            width: 60.w,
            height: 30,
            borderRadius: 4,
          ),
        ],
      ),
    );
  }

  /// 餐厅列表项骨架 - 对应微信小程序的enclosure-item部分
  Widget _buildRestaurantItemSkeleton() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 餐厅图片骨架 - 对应微信小程序的item-img部分
          SkeletonImage(
            width: 80,
            height: 80,
            borderRadius: 4,
          ),
          SizedBox(width: 10.w),
          // 餐厅信息骨架 - 对应微信小程序的item-data部分
          Expanded(
            child: Container(
              height: 90.h, // 对应微信小程序的height: 180rpx
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 餐厅名称 - 对应微信小程序的item-title部分
                  SkeletonItem(
                    width: 200.w, // 避免使用double.infinity
                    height: 24,
                    margin: EdgeInsets.only(bottom: 4.h),
                    borderRadius: 4,
                  ),
                  // 二级标题 - 对应微信小程序的第二个item-title
                  SkeletonItem(
                    width: 0.8.sw,
                    height: 24,
                    margin: EdgeInsets.only(bottom: 4.h),
                    borderRadius: 4,
                  ),
                  // 月销量 - 对应微信小程序的sales部分
                  SkeletonItem(
                    width: 120.w,
                    height: 16,
                    borderRadius: 4,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
