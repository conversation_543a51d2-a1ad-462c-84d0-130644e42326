import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/data/models/my_order/order_ranking_active.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/dialog/order_ranking_rule_dialog.dart';
import 'package:user_app/routes/paths.dart';
import 'package:user_app/core/providers/core_providers.dart';

/// 首页订单排行榜组件
/// 用于显示排行榜活动信息，有三种状态：
/// 1. 预览：活动未开始时显示预览图片
/// 2. 活动进行中：显示奖品列表和中奖用户
/// 3. 活动已结束：显示中奖用户列表
class OrderRankingWidget extends ConsumerWidget {
  /// 排行榜数据
  final OrderRankingActive ranking;

  /// 点击预览回调
  final VoidCallback? onPreview;

  /// 点击规则回调
  final VoidCallback? onRule;

  /// 点击奖品回调
  final VoidCallback? onPrize;

  /// 构造函数
  const OrderRankingWidget({
    super.key,
    required this.ranking,
    this.onPreview,
    this.onRule,
    this.onPrize,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final s = S.of(context);
    // 检查当前语言
    final lang = ref.watch(languageProvider);
    final isUg = lang == 'ug';

    // 根据不同的活动状态显示不同的UI
    // runningState: 0-预览，1-活动进行中，2-活动已结束
    final int runningState = ranking.runningState ?? 0;

    // 包装容器
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10.w),
      child: _buildContent(context, runningState, s, isUg, ref),
    );
  }

  /// 构建内容
  Widget _buildContent(final BuildContext context, final int runningState,
      final S s, final bool isUg, final WidgetRef ref) {
    // 根据活动状态构建不同的UI
    switch (runningState) {
      case 0:
        // 预览状态
        return _buildPreview(context);
      case 1:
        // 活动进行中
        return _buildActiveState(context, s, isUg, ref);
      case 2:
        // 活动已结束
        return _buildEndState(context, s, isUg, ref);
      default:
        return const SizedBox.shrink();
    }
  }

  /// 预览状态
  Widget _buildPreview(final BuildContext context) {
    return GestureDetector(
      onTap: () =>
          onPreview != null ? onPreview!() : _navigateToPreview(context),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(15.r),
        child: CachedNetworkImage(
          imageUrl: ranking.announceEntranceImage ?? '',
          width: double.infinity,
          fit: BoxFit.fitWidth,
        ),
      ),
    );
  }

  /// 活动进行中状态
  Widget _buildActiveState(final BuildContext context, final S s,
      final bool isUg, final WidgetRef ref) {
    final List<OrderRankingPrize> prizes = ranking.prize ?? [];
    final List<OrderRankingLuckyUser> luckyUsers = ranking.luckUserList ?? [];

    return GestureDetector(
      onTap: () => onPrize != null ? onPrize!() : _navigateToPrize(context),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFFFF5353), Color(0xFFFFA0B7), Color(0xFFFF0000)],
            stops: [0.019, 0.748, 1.103],
            transform: GradientRotation(-41.19 * 3.14159 / 180),
          ),
        ),
        child: Stack(
          children: [
            // 背景图片
            Positioned.fill(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10.r),
                child: CachedNetworkImage(
                  imageUrl:
                      'https://acdn.mulazim.com/wechat_mini/img/orderRanking/start-activity-bg.png',
                  fit: BoxFit.fill,
                ),
              ),
            ),

            // 内容层
            Padding(
              padding: EdgeInsets.all(15.r),
              child: Column(
                children: [
                  // 标题和规则
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        ranking.title ?? '',
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      GestureDetector(
                        onTap: () => onRule != null
                            ? onRule!()
                            : _showRuleDialog(context),
                        child: Row(
                          children: [
                            Text(
                              s.activity_rules,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.white,
                              ),
                            ),
                            Icon(
                              Icons.chevron_right,
                              size: 14.sp,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10.h),

                  // 奖品列表
                  if (prizes.isNotEmpty)
                    Container(
                      height: 135.h,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: EdgeInsets.all(8.r),
                        itemCount: prizes.length,
                        itemBuilder: (final context, final index) {
                          final prize = prizes[index];
                          return Container(
                            width: 75.w,
                            margin: EdgeInsets.symmetric(horizontal: 4.w),
                            child: Column(
                              children: [
                                // 奖品图片
                                Container(
                                  height: 75.h,
                                  width: 75.w,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.r),
                                    image: DecorationImage(
                                      image:
                                          NetworkImage(prize.prizeImage ?? ''),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),

                                // 奖品名称
                                Padding(
                                  padding: EdgeInsets.only(top: 5.h),
                                  child: Text(
                                    prize.prizeName ?? '',
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: const Color(0xFFC0C0C0),
                                    ),
                                  ),
                                ),

                                // 中奖编号
                                Container(
                                  margin: EdgeInsets.only(top: 5.h),
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 6.w, vertical: 2.h),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFFF5151),
                                    borderRadius: BorderRadius.circular(16.5.r),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      CachedNetworkImage(
                                        imageUrl:
                                            'https://acdn.mulazim.com/wechat_mini/img/orderRanking/homeNumber.png',
                                        width: 10.w,
                                        height: 10.h,
                                      ),
                                      SizedBox(width: 3.w),
                                      Text(
                                        prize.luckyUserIndex?.toString() ?? '',
                                        style: TextStyle(
                                          fontSize: 14.sp,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),

                  // 中奖用户列表
                  if (luckyUsers.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: 10.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        textDirection:
                            isUg ? TextDirection.rtl : TextDirection.ltr,
                        children: [
                          Row(
                            textDirection:
                                isUg ? TextDirection.rtl : TextDirection.ltr,
                            children: [
                              Text(
                                s.eleven_lucky_user,
                                style: TextStyle(
                                  fontSize: 17.sp,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              Icon(
                                Icons.chevron_right,
                                size: 17.sp,
                                color: Colors.white,
                              ),
                            ],
                          ),
                          // 使用Stack实现头像重叠效果
                          _buildAvatarStack(luckyUsers, isUg),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建头像栈
  Widget _buildAvatarStack(List<OrderRankingLuckyUser> luckyUsers, bool isUg) {
    return SizedBox(
      width: 180.w, // 增加宽度确保更多按钮完全可见
      height: 25.h,
      child: Stack(
        alignment:
            isUg ? Alignment.centerLeft : Alignment.centerRight, // 根据语言动态调整对齐方式
        children: [
          // 更多按钮始终显示在对应方向的最前端
          Positioned(
            left: isUg ? 0 : null,
            right: isUg ? null : 0, // 根据语言设置位置
            child: Container(
              width: 25.w,
              height: 25.h,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                border: Border.all(
                  color: Colors.white,
                  width: 1.w,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 5.w,
                    height: 5.h,
                    decoration: const BoxDecoration(
                      color: Color(0xFFFF0000),
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: 3.w),
                  Container(
                    width: 5.w,
                    height: 5.h,
                    decoration: const BoxDecoration(
                      color: Color(0xFFFF0000),
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: 3.w),
                  Container(
                    width: 5.w,
                    height: 5.h,
                    decoration: const BoxDecoration(
                      color: Color(0xFFFF0000),
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 头像列表 - 根据语言方向调整叠加方式
          ...(List.generate(
            luckyUsers.length > 6 ? 6 : luckyUsers.length,
            (final index) => index,
          ).reversed.map((final index) => Positioned(
                // 根据语言方向调整位置
                left: isUg ? (index + 1) * 18.w : null,
                right: isUg ? null : (index + 1) * 18.w,
                child: Container(
                  width: 25.w,
                  height: 25.h,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 1.w,
                    ),
                    image: DecorationImage(
                      image: NetworkImage(
                        luckyUsers[index].userImage ?? '',
                      ),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ))),
        ],
      ),
    );
  }

  /// 活动结束状态
  Widget _buildEndState(final BuildContext context, final S s, final bool isUg,
      final WidgetRef ref) {
    final List<OrderRankingLuckyUser> luckyUsers = ranking.luckUserList ?? [];

    return GestureDetector(
      onTap: () => onPrize != null ? onPrize!() : _navigateToPrize(context),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(60.r),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFFFF5353), Color(0xFFFFB8B8), Color(0xFFFF0000)],
            stops: [0.0241, 0.7326, 1.0775],
            transform: GradientRotation(-41.19 * 3.14159 / 180),
          ),
        ),
        child: Stack(
          children: [
            // 背景图片
            Positioned.fill(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(60.r),
                child: CachedNetworkImage(
                  imageUrl:
                      'https://acdn.mulazim.com/wechat_mini/img/orderRanking/end-activity-tag.png',
                  fit: BoxFit.cover,
                ),
              ),
            ),

            // 内容
            Padding(
              padding: EdgeInsets.all(10.r),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                textDirection: isUg ? TextDirection.rtl : TextDirection.ltr,
                children: [
                  Row(
                    textDirection: isUg ? TextDirection.rtl : TextDirection.ltr,
                    children: [
                      Text(
                        s.eleven_lucky_user,
                        style: TextStyle(
                          fontSize: 17.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Icon(
                        Icons.chevron_right,
                        size: 17.sp,
                        color: Colors.white,
                      ),
                    ],
                  ),
                  // 使用Stack实现头像重叠效果
                  _buildAvatarStack(luckyUsers, isUg),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 导航到规则预览页面
  void _navigateToPreview(final BuildContext context) {
    context.push(AppPaths.orderRankingPreviewPage);
  }

  // 导航到奖品页面
  void _navigateToPrize(final BuildContext context) {
    context.push(AppPaths.orderRankingPrizePage);
  }

  // 显示规则对话框
  void _showRuleDialog(final BuildContext context) {
    OrderRankingRuleDialog.show(
      context,
      rule: ranking.rule ?? '',
      onClose: () {
        // 无需特殊处理
      },
    );
  }
}
