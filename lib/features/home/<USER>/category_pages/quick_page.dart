import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/home/<USER>/quick_provider.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';

class QuickPage extends ConsumerStatefulWidget {
  QuickPage({super.key, required this.buildingId, required this.categoryId});
  int buildingId;
  int categoryId;

  @override
  ConsumerState createState() => _QuickPageState();
}

class _QuickPageState extends ConsumerState<QuickPage> {
  List<String> tagStr = [];
  List<String> sortArr = ['auto', 'distance', 'hot'];

  int currentNavigate = 0;

  int currentTag = 0;

  @override
  void initState() {
    // TODO: implement initState
    tagStr = [S.current.comment, S.current.near, S.current.hot];
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((final timeStamp) async {
      ref.read(quickProvider.notifier).fetchQuickData(
            buildingId: widget.buildingId,
            categoryId: widget.categoryId,
            catChildId: widget.categoryId == 30 ? 32 : 0,
          );
    });
  }

  @override
  Widget build(final BuildContext context) {
    SortData sortData = SortData();
    return Scaffold(
      backgroundColor: AppColors.baseBackgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.baseGreenColor,
        foregroundColor: Colors.white,
        centerTitle: true,
        title: Text(
          S.current.app_name,
          style: TextStyle(fontSize: soBigSize, color: Colors.white),
        ),
      ),
      body: ref.watch(quickProvider).when(
        data: (final data) {
          sortData = getSortData(
            data!.restaurantList!.items ?? [],
            data.sortSetting ?? SortSettingQuick(),
          );
          // 遍历childCategories，找到parentId为30且id为32的项的index，并赋值给currentNavigate
          final childCategories = data.childCategories ?? [];
          final targetIndex = childCategories.indexWhere((cat) => cat.parentId == 30 && cat.id == 32);
          if (targetIndex != -1) {
            currentNavigate = targetIndex;
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.w),
                    color: Colors.white,
                  ),
                  margin: EdgeInsets.all(10.w),
                  padding: EdgeInsets.all(10.w),
                  child: GridView.count(
                    shrinkWrap:
                        true, // Allow GridView to take up only the space it needs
                    physics: NeverScrollableScrollPhysics(),
                    crossAxisCount: 4,
                    mainAxisSpacing: 5.w,
                    crossAxisSpacing: 5.w,
                    childAspectRatio: 1,
                    children: List.generate(
                      (data.childCategories ?? []).length,
                      (final index) => _navigateItem(
                        data!.childCategories![index],
                        index,
                      ),
                    ),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.w),
                    color: Colors.white,
                  ),
                  margin:
                      EdgeInsets.only(right: 10.w, left: 10.w, bottom: 10.w),
                  padding: ref.watch(languageProvider) == 'ug'
                      ? EdgeInsets.only(right: 10.w, top: 10.w, bottom: 10.w)
                      : EdgeInsets.only(left: 10.w, top: 10.w, bottom: 10.w),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            S.current.near_market,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: soBigSize,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 16.w,
                      ),
                      Row(
                        children: List.generate(
                          tagStr.length,
                          (final index) => _tagItem(tagStr[index], index),
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  children: List.generate(
                    sortRestaurant(sortData, sortArr[currentTag]).length,
                    (final index) => _restaurantItem(
                      sortRestaurant(
                        sortData,
                        sortArr[currentTag],
                      )[index],
                      index,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
        error: (final error, final stackTrace) {
          // 数据加载失败，显示错误信息
          print(error);
          print(stackTrace);
          return Center(child: Text('address Page Error: $error'));
        },
        loading: () {
          // 正在加载，显示加载指示器
          return Center(
            child: LoadingWidget(),
          );
        },
      ),
    );
  }

  Widget _tagItem(final String tagStr, final int index) {
    return Expanded(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: InkWell(
              onTap: () {
                currentTag = index;
                setState(() {});
              },
              child: Container(
                alignment: Alignment.center,
                height: 40.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6.w),
                  color: currentTag == index
                      ? AppColors.baseGreenColor
                      : AppColors.baseBackgroundColor,
                ),
                child: Text(
                  tagStr,
                  style: TextStyle(
                    color: currentTag == index
                        ? Colors.white
                        : AppColors.textSecondaryColor,
                    fontSize: titleSize,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(
            width: 10.w,
          ),
        ],
      ),
    );
  }

  Widget _navigateItem(final ChildCategories childCategori, final int index) {
    return InkWell(
      onTap: () {
        ref.read(quickProvider.notifier).fetchQuickData(
              buildingId: widget.buildingId,
              categoryId: widget.categoryId,
              catChildId: childCategori.id ?? 0,
            );
        currentNavigate = index;
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.w),
          color: Colors.white,
          boxShadow: [
            if (index == currentNavigate)
              BoxShadow(
                color: Colors.grey.withOpacity(0.5), // 设置阴影颜色和透明度
                spreadRadius: 2, // 阴影扩散半径
                blurRadius: 2, // 阴影模糊半径
                offset: Offset(0, 1), // 阴影偏移量，(x, y)
              ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ClipOval(
              child: Container(
                color: FormatUtil.parseColor(childCategori.rgb ?? '00FF00'),
                width: 46.w,
                height: 46.w,
                child: PrefectImage(
                  imageUrl: childCategori.icon ?? '',
                  width: 22.w,
                  height: 22.w,
                  fit: BoxFit.fill,
                ),
              ),
            ),
            SizedBox(
              height: 6.w,
            ),
            Text(
              childCategori.name ?? '',
              style: TextStyle(fontSize: mainSize),
            ),
          ],
        ),
      ),
    );
  }

  Widget _restaurantItem(final Items item, final int index) {
    // print('index $index item.name ${item.name}');
    //
    // List<MarketTag> allTag = [];
    // for(int i = 0; i < 2; i++){
    //   allTag.add(MarketTag(
    //       type: 1,
    //       color: '#10C35A',
    //       title: 'مۇلازىم يەتكۈزىدۇ',
    //       titleUg: 'مۇلازىم يەتكۈزىدۇ',
    //       titleZh: 'مۇلازىم يەتكۈزىدۇ',
    //       background: '#ffffff',
    //       image: '',
    //       borderColor: '#CFF6E0'));
    // }

    return InkWell(
      onTap: () {
        context.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': item.id ?? 0,
            'buildingId': widget.buildingId,
            'ids': [],
          },
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.w),
          color: Colors.white,
        ),
        margin: EdgeInsets.only(left: 10.w, right: 10.w, bottom: 10.w),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(10.w),
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10.w),
                    // child: Image.asset(
                    //   'assets/images/sec_2.png',
                    //   fit: BoxFit.cover,
                    //   width: 90.w,
                    //   height: 90.w,
                    // ),
                    child: PrefectImage(
                      imageUrl: item.logo ?? '',
                      width: 90.w,
                      height: 90.w,
                      fit: BoxFit.fitWidth,
                    ),
                  ),
                  if (((item.isNew ?? 0) > 0 || (item.hasFoodPre ?? 0) > 0) &&
                      (item.resting ?? 0) == 0)
                    Positioned(
                      bottom: 0,
                      child: Container(
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                            bottomRight: Radius.circular(10.w),
                            bottomLeft: Radius.circular(10.w),
                          ),
                          gradient: LinearGradient(
                            colors: [
                              AppColors.restaurantBgStartColor,
                              AppColors.restaurantBgEndColor,
                            ], // 渐变的颜色列表
                            begin: Alignment.centerRight, // 渐变开始位置
                            end: Alignment.centerLeft, // 渐变结束位置
                          ),
                          border: Border(
                            top: BorderSide(
                              color: Colors.white,
                              width: 1.w,
                            ),
                          ),
                        ),
                        height: 24.w,
                        width: 90.w,
                        child:
                            ((item.isNew ?? 0) > 0 && (item.resting ?? 0) == 0)
                                ? Text(
                                    S.current.is_new,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: secondSize,
                                    ),
                                    textAlign: TextAlign.center,
                                  )
                                : SizedBox(),
                      ),
                    ),
                  (((item.hasFoodPre ?? 0) > 0) &&
                          (item.resting ?? 0) == 0 &&
                          (item.isNew ?? 0) == 0)
                      ? Positioned(
                          bottom: 3.w,
                          right: 0,
                          left: 0,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Image.asset(
                                'assets/images/fire.png',
                                fit: BoxFit.cover,
                                width: 20.w,
                              ),
                              SizedBox(
                                width: 3.w,
                              ),
                              Text(
                                S.current.has_food_pre,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: secondSize,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        )
                      : SizedBox(),
                  (item.resting ?? 0) == 1
                      ? Positioned(
                          child: Container(
                            alignment: Alignment.center,
                            width: 90.w,
                            height: 90.w,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10.w),
                              color: Colors.grey.withOpacity(0.75),
                            ),
                            child: Text(
                              S.current.resting,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: secondSize,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        )
                      : SizedBox(),
                ],
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${item.name}',
                    style: TextStyle(fontSize: mainSize),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(
                    height: 5.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            '${item.starAvg ?? 5}',
                            style: TextStyle(
                              fontSize: secondSize,
                              color: AppColors.baseOrangeColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Icon(
                            Icons.star,
                            color: AppColors.baseOrangeColor,
                            size: mainSize,
                          ),
                          SizedBox(
                            width: 10.w,
                          ),
                          Text(
                            '${S.current.month_order_count}:${item.monthOrderCount ?? ''}',
                            style: TextStyle(
                              fontSize: secondSize,
                              color: AppColors.textSecondColor,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        '${item.distance ?? '0'}km',
                        style: TextStyle(
                          fontSize: secondSize,
                          color: AppColors.textSecondColor,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  // Wrap(
                  //   spacing: 5.w,
                  //   runSpacing: 4.w,
                  //   // mainAxisAlignment: MainAxisAlignment.start,
                  //   children: List.generate(allTag.length, (tagIndex)=>_borderContainer(allTag[tagIndex])),
                  //   // children: [
                  //   //   _borderContainer(baseGreenColor,'مۇلازىم يەتكۈزىدۇ'),
                  //   //   SizedBox(width: 10.w,),
                  //   //   _borderContainer(baseOrangeColor,'ئۆزۈم ئېلۋالاي'),
                  //   // ],
                  // ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: List.generate(
                      (item.takeTag ?? []).length,
                      (final tagIndex) =>
                          _borderContainer(item.takeTag![tagIndex]),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              width: 10.w,
            ),
          ],
        ),
      ),
    );
  }

  Widget _borderContainer(final TakeTag takeTag) {
    return Container(
      margin: EdgeInsets.only(left: 10.w),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Container(
                padding: EdgeInsets.only(left: 5.w, right: 5.w),
                alignment: Alignment.center,
                height: 20.w,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: FormatUtil.parseColor(takeTag.color ?? '00FF00'),
                    width: 0.5.w,
                  ),
                  borderRadius: BorderRadius.circular(5.w),
                  color: Colors.white,
                ),
                child: Text(
                  takeTag.title ?? '',
                  style: TextStyle(
                    color: FormatUtil.parseColor(takeTag.color ?? '00FF00'),
                    fontSize: littleSize,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<Items> sortRestaurant(final SortData sortData, final String sort) {
    List<Items> restaurantList = [];

    switch (sort) {
      case 'auto':
        sortData.discount = descSortByKey(sortData.discount, 'hasFoodPre');
        sortData.business = ascSortByKey(sortData.business, 'weight');
        sortData.resting = ascSortByKey(sortData.resting, 'weight');
        sortData.notTime = ascSortByKey(sortData.notTime, 'weight');
        // "综合" Sort Order: Discount -> User Like -> Not Time -> Resting
        restaurantList = sortData.discount +
            (sortData.newRes) +
            (sortData.userLike) +
            (sortData.business) +
            (sortData.notTime) +
            (sortData.resting);
        print('restaurantList auto ${restaurantList.length}');
        break;

      case 'distance':
        sortData.allBusiness = ascSortByKey(sortData.allBusiness, 'distance');
        sortData.allResting = ascSortByKey(sortData.allResting, 'distance');
        // "距离" Sort Order: All Business -> All Resting
        restaurantList = sortData.allBusiness + sortData.allResting;

        // print('restaurantList distance ${restaurantList.length}');
        break;

      case 'hot':
        sortData.allBusiness =
            descSortByKey(sortData.allBusiness, 'month_order_count');
        sortData.allResting =
            descSortByKey(sortData.allResting, 'month_order_count');
        // "销量" Sort Order: All Business -> All Resting
        restaurantList = sortData.allBusiness + sortData.allResting;
        // print('restaurantList hot ${restaurantList.length}');
        break;
    }

    return restaurantList;
  }

  List<Items> ascSortByKey(final List<Items> array, final String key) {
    try {
      array.sort((final a, final b) {
        var x = a.toJson()[key];
        var y = b.toJson()[key];
        return (x < y) ? -1 : ((x > y) ? 1 : 0);
      });
    } catch (err) {
      print(err);
      return [];
    }
    return array;
  }

  List<Items> descSortByKey(final List<Items> array, final String key) {
    try {
      array.sort((final a, final b) {
        var x = a.toJson()[key];
        var y = b.toJson()[key];
        return (x > y) ? -1 : ((x < y) ? 1 : 0);
      });
    } catch (err) {
      print('err-->$err');
      return [];
    }
    return array;
  }

  List<Items> sortBy(
    final List<Items> array,
    final String key1,
    final String mode1,
    final String key2,
    final String mode2,
  ) {
    array.sort((final a, final b) {
      var x = a.toJson()[key1];
      var y = b.toJson()[key1];
      var x2 = a.toJson()[key2];
      var y2 = b.toJson()[key2];

      // Sort by key1 using mode1
      int i = (mode1 == 'desc')
          ? (x > y ? -1 : ((x < y) ? 1 : 0))
          : (x < y ? -1 : ((x > y) ? 1 : 0));

      if (i == 0) {
        // If key1 is the same, sort by key2 using mode2
        return (mode2 == 'desc')
            ? (x2 > y2 ? -1 : ((x2 < y2) ? 1 : 0))
            : (x2 < y2 ? -1 : ((x2 > y2) ? 1 : 0));
      } else {
        return i;
      }
    });
    return array;
  }
}

class SortData {
  List<Items> discount = [];
  List<Items> newRes = [];
  List<Items> userLike = [];
  List<Items> business = [];
  List<Items> notTime = [];
  List<Items> resting = [];
  List<Items> allBusiness = [];
  List<Items> allResting = [];
  List<Items> hotRes = [];
}

SortData getSortData(
    final List<Items> restaurants, SortSettingQuick orderSetting) {
  // If orderSetting is null or missing specific fields, use default settings
  if (orderSetting == null ||
      orderSetting.searchHotRes == null ||
      orderSetting.discountRes == null ||
      orderSetting.newRes == null ||
      orderSetting.userLikeRes == null) {
    orderSetting = SortSettingQuick(
      newRes: -1,
      discountRes: 5,
      searchHotRes: 3,
      userLikeRes: 3,
    );
  }

  // Initialize SortData
  SortData sortData = SortData();

  for (var i = 0; i < restaurants.length; i++) {
    var state = 0; // 1 for new restaurant, 2 for discount, 3 for resting

    // Determine state
    if (restaurants[i].isNew != null && restaurants[i].isNew! > 0) {
      state = 1;
    }
    if (restaurants[i].hasFoodPre != null && restaurants[i].hasFoodPre! > 0) {
      state = 3;
    }
    if (restaurants[i].resting != null && restaurants[i].resting! == 1) {
      state = 2;
    }

    var item = Items(
      id: restaurants[i].id,
      name: restaurants[i].name,
      logo: restaurants[i].logo,
      starAvg: restaurants[i].starAvg,
      monthOrderCount: restaurants[i].monthOrderCount,
      orderFoodCount: restaurants[i].orderFoodCount,
      hasFoodPre: restaurants[i].hasFoodPre,
      distance: restaurants[i].distance,
      isNew: restaurants[i].isNew,
      state: state,
      resting: restaurants[i].resting,
      weight: restaurants[i].weight,
      takeTag: restaurants[i].takeTag,
    );

    // Classify restaurants based on their state
    if (restaurants[i].resting == 1) {
      if (restaurants[i].state == 1) {
        sortData.notTime.add(item);
      } else {
        sortData.resting.add(item);
      }
    } else {
      if (restaurants[i].hasFoodPre != null && restaurants[i].hasFoodPre! > 0) {
        sortData.discount.add(item);
      } else if (restaurants[i].isNew != null && restaurants[i].isNew! > 0) {
        sortData.newRes.add(item);
      } else {
        sortData.business.add(item);
      }
    }
  }

  // Sort new restaurants by 'isNew'
  sortData.newRes.sort((final a, final b) => a.isNew!.compareTo(b.isNew!));

  // Sort discount restaurants by 'hasFoodPre'
  sortData.discount
      .sort((final a, final b) => b.hasFoodPre!.compareTo(a.hasFoodPre!));

  // Only allow a limited number of new restaurants to be listed after discounts
  if (orderSetting.newRes != -1 &&
      sortData.newRes.length > orderSetting.newRes!) {
    sortData.business.addAll(
      sortData.newRes.sublist(orderSetting.newRes!, sortData.newRes.length),
    );
    sortData.newRes = sortData.newRes.sublist(0, orderSetting.newRes!);
  }

  // Only allow a limited number of discount restaurants to be listed after new restaurants
  if (orderSetting.discountRes != -1 &&
      sortData.discount.length > orderSetting.discountRes!) {
    sortData.business.addAll(
      sortData.discount
          .sublist(orderSetting.discountRes!, sortData.discount.length),
    );
    sortData.discount = sortData.discount.sublist(0, orderSetting.discountRes!);
  }

  // Sort business restaurants by order food count
  sortData.business.sort(
      (final a, final b) => b.orderFoodCount!.compareTo(a.orderFoodCount!));

  // If there are user-liked restaurants, move them to the front
  if (sortData.business.length >= orderSetting.userLikeRes!) {
    sortData.userLike = sortData.business.sublist(0, orderSetting.userLikeRes!);
    sortData.business = sortData.business
        .sublist(orderSetting.userLikeRes!, sortData.business.length);
  }

  // Combine all business restaurants
  sortData.allBusiness = sortData.discount +
      sortData.newRes +
      sortData.userLike +
      sortData.business;

  // Combine all resting restaurants
  sortData.allResting = sortData.notTime + sortData.resting;

  // Sort by sales (month_order_count)
  sortData.hotRes = List.from(sortData.allBusiness)
    ..sort(
        (final a, final b) => b.monthOrderCount!.compareTo(a.monthOrderCount!));

  // Limit hot restaurants if needed
  if (orderSetting.searchHotRes != -1 &&
      sortData.hotRes.length > orderSetting.searchHotRes!) {
    sortData.hotRes = sortData.hotRes.sublist(0, orderSetting.searchHotRes!);
  }

  return sortData;
}
