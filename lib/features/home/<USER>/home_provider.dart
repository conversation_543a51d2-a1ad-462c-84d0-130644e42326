import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/repositories/home/<USER>';

import 'package:user_app/core/providers/core_providers.dart';

///获取首页数据
Future<ApiResult<HomeData>> getHomeInfo(final Ref ref,
    {required final String tagIds,
    required final int page,
    final int? buildingId,
    final int? areaId,
    final String? lat,
    final String? lng}) async {
  //模拟数据
  // String lat = '43.79547';
  // String lng = '87.63356';
  int sort = 1;
  int limit = 30;
  String sortCols = '';

  if (tagIds.contains(',')) {
    sort = 0;
    sortCols = tagIds;
  } else if (int.parse(tagIds) > 10) {
    sort = 0;
    sortCols = tagIds;
  } else if (int.parse(tagIds) < 10) {
    sort = int.parse(tagIds);
    sortCols = '';
  }

  // 构建请求参数
  final Map<String, dynamic> param = {
    'sort': sort,
    'page': page,
    'limit': limit,
    'sort_cols': sortCols,
   
  };

  // 如果有经纬度信息，添加到参数中
  if (buildingId != null && areaId != null) {
    param['building_id'] = buildingId;
    param['area_id'] = areaId;
  }
  // 如果有经纬度信息，添加到参数中
  if (lat != null && lng != null) {
    param['lat'] = lat;
    param['lng'] = lng;
  }
  // 如果lat和lng空的时候不允许buildingId为空
  if (buildingId == 0 || areaId == 0) {
    if ((lat?.isEmpty ?? true) || (lng?.isEmpty ?? true)) {
      param['building_id'] = 1979;
      param['area_id'] = 1;
    }
  }

  log("获取首页数据参数: $param");
  final homeRepository = HomeRepository(apiClient: ref.read(apiClientProvider));
  final homeInfo = await homeRepository.getHomeInfo(param);

  // 计算请求耗时
  return homeInfo;
}

/// 首页数据请求服务提供者
final homeServiceProvider = Provider<HomeService>((final ref) {
  return HomeService(ref: ref);
});

/// 首页数据请求服务
class HomeService {
  final Ref ref;

  HomeService({required this.ref});

  /// 获取首页数据
  Future<ApiResult<HomeData>> fetchHomeData(
      {required final String tagIds,
      required final int page,
      final int? buildingId,
      final int? areaId,
      final String? lat,
      final String? lng}) async {
    try {
      final startTime = DateTime.now();
      log("开始获取首页数据: tagIds=$tagIds, page=$page, buildingId=$buildingId, areaId=$areaId, lat=$lat, lng=$lng");

      final response = await getHomeInfo(ref,
          tagIds: tagIds,
          page: page,
          buildingId: buildingId,
          areaId: areaId,
          lng: lng,
          lat: lat);

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime).inMilliseconds;
      log("首页数据获取成功: ${response.data != null ? '有数据' : '无数据'}, 耗时: ${duration}ms");

      if (response.data != null) {
        log("餐厅列表数量: ${response.data?.restaurantList?.items?.length ?? 0}");
      }

      return response;
    } catch (error, stackTrace) {
      log("获取首页数据失败: $error");
      log("错误堆栈: $stackTrace");
      throw error;
    }
  }

  /// 合并餐厅列表数据
  HomeData? mergeRestaurantList(
      final HomeData? oldData, final HomeData? newData, final int page) {
    if (newData == null) return oldData;

    // 第一页时直接返回新数据
    if (page == 1) {
      return newData;
    }

    // 追加新的餐厅数据到现有数据
    if (oldData != null && oldData.restaurantList != null) {
      final newItems = newData.restaurantList?.items ?? [];

      // 创建一个新的restaurantList以避免直接修改原数据
      final updatedList = RestaurantList(
          perPage: newData.restaurantList?.perPage ??
              oldData.restaurantList!.perPage,
          currentPage: newData.restaurantList?.currentPage ??
              oldData.restaurantList!.currentPage,
          total: newData.restaurantList?.total ?? oldData.restaurantList!.total,
          totalPage: newData.restaurantList?.totalPage ??
              oldData.restaurantList!.totalPage,
          items: [...(oldData.restaurantList!.items ?? []), ...newItems]);

      // 创建一个新的HomeData对象
      return HomeData(
          restaurantList: updatedList,
          discount: oldData.discount,
          seckill: oldData.seckill,
          popupAdver: oldData.popupAdver,
          tags: oldData.tags,
          isCollegeCountShow: oldData.isCollegeCountShow,
          location: oldData.location,
          categories: oldData.categories,
          adverList: oldData.adverList,
          special: oldData.special,
          sortSetting: oldData.sortSetting,
          themActive: oldData.themActive,
          themActivePreferential: oldData.themActivePreferential,
          yunshanfuCouponFlag: oldData.yunshanfuCouponFlag,
          yearSummaryActive: oldData.yearSummaryActive,
          ranking: oldData.ranking); // 添加ranking字段
    } else {
      // 没有旧数据时直接使用新数据
      return newData;
    }
  }

  /// 检查是否可以加载更多
  bool checkCanLoadMore(final HomeData? data) {
    bool canLoadMore = true;
    if (data?.restaurantList?.items != null) {
      if ((data!.restaurantList!.items ?? []).length < 30) {
        log("餐厅数量少于30，无法加载更多");
        canLoadMore = false;
      } else {
        log("餐厅数量达到30，可以加载更多");
        canLoadMore = true;
      }
    }
    return canLoadMore;
  }
}


final selectedTagsProvider = StateProvider.autoDispose<String>((final ref) => '1');

final pageNumProvider = StateProvider.autoDispose<int>((final ref) => 1);

final restaurantListProvider =
    StateProvider.autoDispose<List<RestaurantItems>>((final ref) => []);

final canAddRestaurantDataProvider =
    StateProvider.autoDispose<bool>((final ref) => true);


