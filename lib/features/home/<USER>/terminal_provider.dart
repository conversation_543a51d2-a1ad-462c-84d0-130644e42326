import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/repositories/home/<USER>';

///获取地址列表按定位数据
Future<UpgradeData?> getTerminalInfo(Ref ref,{required int type}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'type': type
  };
  // 秒杀活动数据
  final homeRepository = HomeRepository(apiClient: ref.read(apiClientProvider));
  final terminalInfo = await homeRepository.terminalInfo(param);
  return terminalInfo.data;
}

///地址列表按定位数据提供者类
class TerminalProvider extends StateNotifier<AsyncValue<UpgradeData?>> {
  TerminalProvider(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> fetchTerminalData({required int type}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final response = await getTerminalInfo(ref, type:type );
      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

}

final terminalProvider = StateNotifierProvider.autoDispose<
    TerminalProvider, AsyncValue<UpgradeData?>>(
  (ref) => TerminalProvider(ref),
);


