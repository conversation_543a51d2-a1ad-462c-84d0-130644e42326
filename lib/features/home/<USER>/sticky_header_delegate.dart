// Sticky Header Delegate
import 'package:flutter/material.dart';
import 'package:user_app/core/theme/app_colors.dart';

class StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  StickyHeaderDelegate({required this.child, required this.height});

  @override
  double get minExtent => height;  // 最小高度
  @override
  double get maxExtent => height;  // 最大高度

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: AppColors.baseBackgroundColor,  // 确保背景色不透明
      height: height,  // 设置固定高度，避免 `layoutExtent` 和 `paintExtent` 不一致
      child: child,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}