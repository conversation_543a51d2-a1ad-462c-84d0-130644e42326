import 'package:user_app/data/models/shipper/shipper_tips_history_model.dart';

/// 配送员打赏历史页面状态
class ShipperTipsHistoryState {
  /// 是否加载中
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 打赏历史数据
  final ShipperTipsHistoryModel? tipsHistory;

  /// 是否可以加载更多
  final bool canLoadMore;

  /// 当前页码
  final int currentPage;

  /// 构造函数
  const ShipperTipsHistoryState({
    this.isLoading = false,
    this.error,
    this.tipsHistory,
    this.canLoadMore = true,
    this.currentPage = 1,
  });

  /// 复制方法
  ShipperTipsHistoryState copyWith({
    final bool? isLoading,
    final String? error,
    final ShipperTipsHistoryModel? tipsHistory,
    final bool? canLoadMore,
    final int? currentPage,
  }) {
    return ShipperTipsHistoryState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      tipsHistory: tipsHistory ?? this.tipsHistory,
      canLoadMore: canLoadMore ?? this.canLoadMore,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}
