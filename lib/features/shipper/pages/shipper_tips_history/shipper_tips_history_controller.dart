import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/models/shipper/shipper_tips_history_model.dart';
import 'package:user_app/features/shipper/pages/shipper_tips_history/shipper_tips_history_state.dart';
import 'package:user_app/features/shipper/services/shipper_service.dart';

part 'shipper_tips_history_controller.g.dart';

/// 配送员打赏历史控制器
@riverpod
class ShipperTipsHistoryController extends _$ShipperTipsHistoryController {
  @override
  ShipperTipsHistoryState build(final int shipperId) {
    // 首次加载数据
    Future.microtask(() => loadTipsHistory());
    return const ShipperTipsHistoryState();
  }

  /// 加载打赏历史数据
  ///
  /// 从服务器获取配送员打赏历史信息并更新状态
  Future<void> loadTipsHistory({final bool refresh = false}) async {
    // 如果正在加载或无法加载更多且不是刷新模式，则直接返回
    if (state.isLoading || (!state.canLoadMore && !refresh)) {
      return;
    }

    // 设置加载状态
    state = state.copyWith(
      isLoading: true,
      error: null,
      // 如果是刷新，重置页码为1
      currentPage: refresh ? 1 : state.currentPage,
    );

    try {
      final shipperService = ref.read(shipperServiceProvider);
      final page = refresh ? 1 : state.currentPage;

      // 获取数据
      final result = await shipperService.getShipperTipsHistory(
        shipperId,
        page: page,
        limit: 30,
      );

      if (result.success && result.data != null) {
        // 安全地获取列表
        final currentTipsList =
            state.tipsHistory?.tipsList ?? <ShipperTipItemModel>[];
        // 获取新的数据
        final tipsHistory = result.data;
        // 获取新的列表
        final List<ShipperTipItemModel> newTipsList =
            tipsHistory?.tipsList ?? <ShipperTipItemModel>[];

        // 如果是刷新，直接使用新数据
        if (refresh) {
          state = state.copyWith(
            isLoading: false,
            tipsHistory: tipsHistory,
            currentPage: 2,
            canLoadMore: newTipsList.length == 30,
          );
        } else {
          // 合并原有列表和新列表
          final List<ShipperTipItemModel> mergedTipsList = [
            ...currentTipsList,
            ...newTipsList,
          ];

          // 创建新的打赏历史模型，同时更新合并后的列表
          final updatedTipsHistory = ShipperTipsHistoryModel(
            shipperId: tipsHistory?.shipperId,
            shipperName: tipsHistory?.shipperName,
            shipperAvatar: tipsHistory?.shipperAvatar,
            tipsTotal: tipsHistory?.tipsTotal,
            tipsCount: tipsHistory?.tipsCount,
            tipsList: mergedTipsList,
          );

          state = state.copyWith(
            isLoading: false,
            tipsHistory: updatedTipsHistory,
            currentPage: state.currentPage + 1,
            canLoadMore: newTipsList.length == 30,
          );
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.msg,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 刷新数据
  Future<void> refreshData() async {
    await loadTipsHistory(refresh: true);
  }

  /// 加载更多数据
  Future<void> loadMoreData() async {
    await loadTipsHistory();
  }
}
