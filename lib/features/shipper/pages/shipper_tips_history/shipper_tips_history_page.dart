import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/shipper/pages/shipper_tips_history/shipper_tips_history_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 配送员打赏历史页面
class ShipperTipsHistoryPage extends ConsumerWidget {
  /// 配送员ID
  final int shipperId;

  /// 构造函数
  const ShipperTipsHistoryPage({
    super.key,
    required this.shipperId,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final state = ref.watch(shipperTipsHistoryControllerProvider(shipperId));
    final controller =
        ref.read(shipperTipsHistoryControllerProvider(shipperId).notifier);

    return Scaffold(
      appBar: CustomAppBar(
        title: S.current.shipper_reward_person,
      ),
      body: state.isLoading && state.tipsHistory == null
          ? const Center(child: LoadingWidget())
          : state.tipsHistory == null
              ? Center(
                  child: Text(
                    state.error ?? S.current.no_data,
                    style: TextStyle(fontSize: 16.sp),
                  ),
                )
              : _buildContent(context, state, controller),
    );
  }

  /// 构建页面内容
  Widget _buildContent(
      final BuildContext context, final state, final controller) {
    final tipsHistory = state.tipsHistory!;
    final tipsList = tipsHistory.tipsList ?? [];

    return Column(
      children: [
        // 头部信息
        // _buildHeader(tipsHistory),

        // 列表
        Expanded(
          child: RefreshIndicator(
            onRefresh: () => controller.refreshData(),
            child: tipsList.isEmpty
                ? Center(
                    child: Text(
                      S.current.shipper_no_reward,
                      style: TextStyle(fontSize: 16.sp),
                    ),
                  )
                : NotificationListener<ScrollNotification>(
                    onNotification: (final ScrollNotification scrollInfo) {
                      if (scrollInfo.metrics.pixels ==
                          scrollInfo.metrics.maxScrollExtent) {
                        if (state.canLoadMore && !state.isLoading) {
                          controller.loadMoreData();
                        }
                      }
                      return true;
                    },
                    child: ListView.builder(
                      padding: EdgeInsets.symmetric(vertical: 5.r),
                      itemCount: tipsList.length + (state.canLoadMore ? 1 : 0),
                      itemBuilder: (final context, final index) {
                        // 加载更多的加载中指示器
                        if (index == tipsList.length) {
                          return Center(
                            child: Padding(
                              padding: EdgeInsets.all(10.r),
                              child: state.isLoading
                                  ? const CircularProgressIndicator()
                                  : Text(
                                      S.current.loading_more,
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        color: Colors.grey,
                                      ),
                                    ),
                            ),
                          );
                        }

                        // 打赏项
                        final tipItem = tipsList[index];
                        return _buildTipItem(tipItem);
                      },
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  /// 构建头部信息
  Widget _buildHeader(final tipsHistory) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(15.r),
      color: AppColors.primary.withOpacity(0.1),
      child: Row(
        children: [
          // 配送员头像
          ClipRRect(
            borderRadius: BorderRadius.circular(5.r),
            child: CachedNetworkImage(
              imageUrl: tipsHistory.shipperAvatar ?? '',
              width: 45.w,
              height: 45.w,
              fit: BoxFit.cover,
              placeholder: (final context, final url) => Container(
                color: Colors.grey[200],
                width: 45.w,
                height: 45.w,
              ),
              errorWidget: (final context, final url, final error) => Container(
                width: 45.w,
                height: 45.w,
                color: Colors.grey[200],
                child: Icon(Icons.person, size: 22.sp, color: Colors.grey),
              ),
            ),
          ),

          SizedBox(width: 10.w),

          // 配送员信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  tipsHistory.shipperName ?? '',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 5.h),
                Text(
                  '${S.current.shipper_total}: ¥${tipsHistory.tipsTotal?.toStringAsFixed(2) ?? '0.00'}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.primary,
                  ),
                ),
                SizedBox(height: 5.h),
                Text(
                  '${S.current.shipper_reward_person}: ${tipsHistory.tipsCount ?? 0}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建打赏项
  Widget _buildTipItem(final tipItem) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.r, horizontal: 10.r),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: const Color(0xFFF1F1F8),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 用户信息
          Expanded(
            child: Row(
              children: [
                // 头像
                ClipRRect(
                  borderRadius: BorderRadius.circular(5.r),
                  child: CachedNetworkImage(
                    imageUrl: tipItem.userAvatar ?? '',
                    width: 45.w,
                    height: 45.w,
                    fit: BoxFit.cover,
                    placeholder: (final context, final url) => Container(
                      color: Colors.grey[200],
                      width: 45.w,
                      height: 45.w,
                    ),
                    errorWidget: (final context, final url, final error) =>
                        Container(
                      width: 45.w,
                      height: 45.w,
                      color: Colors.grey[200],
                      child:
                          Icon(Icons.person, size: 22.sp, color: Colors.grey),
                    ),
                  ),
                ),

                SizedBox(width: 5.w),

                // 名称和时间
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        tipItem.userName ?? '',
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        tipItem.createdAt ?? '',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF666666),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 金额
          RichText(
            text: TextSpan(
              style: TextStyle(
                fontSize: 18.sp,
                color: const Color(0xFFFF5151),
                fontWeight: FontWeight.bold,
                fontFamily: AppConstants.mainFont,
              ),
              children: [
                TextSpan(
                  text: '¥',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontFamily: AppConstants.mainFont,
                  ),
                ),
                TextSpan(
                  text: tipItem.amount?.toStringAsFixed(2) ?? '0.00',
                  style: TextStyle(
                    fontFamily: AppConstants.mainFont,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
