// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shipper_tips_history_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$shipperTipsHistoryControllerHash() =>
    r'91c9df93224adf710d60ddcb44b20998617a3d9a';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ShipperTipsHistoryController
    extends BuildlessAutoDisposeNotifier<ShipperTipsHistoryState> {
  late final int shipperId;

  ShipperTipsHistoryState build(
    int shipperId,
  );
}

/// 配送员打赏历史控制器
///
/// Copied from [ShipperTipsHistoryController].
@ProviderFor(ShipperTipsHistoryController)
const shipperTipsHistoryControllerProvider =
    ShipperTipsHistoryControllerFamily();

/// 配送员打赏历史控制器
///
/// Copied from [ShipperTipsHistoryController].
class ShipperTipsHistoryControllerFamily
    extends Family<ShipperTipsHistoryState> {
  /// 配送员打赏历史控制器
  ///
  /// Copied from [ShipperTipsHistoryController].
  const ShipperTipsHistoryControllerFamily();

  /// 配送员打赏历史控制器
  ///
  /// Copied from [ShipperTipsHistoryController].
  ShipperTipsHistoryControllerProvider call(
    int shipperId,
  ) {
    return ShipperTipsHistoryControllerProvider(
      shipperId,
    );
  }

  @override
  ShipperTipsHistoryControllerProvider getProviderOverride(
    covariant ShipperTipsHistoryControllerProvider provider,
  ) {
    return call(
      provider.shipperId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'shipperTipsHistoryControllerProvider';
}

/// 配送员打赏历史控制器
///
/// Copied from [ShipperTipsHistoryController].
class ShipperTipsHistoryControllerProvider
    extends AutoDisposeNotifierProviderImpl<ShipperTipsHistoryController,
        ShipperTipsHistoryState> {
  /// 配送员打赏历史控制器
  ///
  /// Copied from [ShipperTipsHistoryController].
  ShipperTipsHistoryControllerProvider(
    int shipperId,
  ) : this._internal(
          () => ShipperTipsHistoryController()..shipperId = shipperId,
          from: shipperTipsHistoryControllerProvider,
          name: r'shipperTipsHistoryControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$shipperTipsHistoryControllerHash,
          dependencies: ShipperTipsHistoryControllerFamily._dependencies,
          allTransitiveDependencies:
              ShipperTipsHistoryControllerFamily._allTransitiveDependencies,
          shipperId: shipperId,
        );

  ShipperTipsHistoryControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.shipperId,
  }) : super.internal();

  final int shipperId;

  @override
  ShipperTipsHistoryState runNotifierBuild(
    covariant ShipperTipsHistoryController notifier,
  ) {
    return notifier.build(
      shipperId,
    );
  }

  @override
  Override overrideWith(ShipperTipsHistoryController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ShipperTipsHistoryControllerProvider._internal(
        () => create()..shipperId = shipperId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        shipperId: shipperId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<ShipperTipsHistoryController,
      ShipperTipsHistoryState> createElement() {
    return _ShipperTipsHistoryControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ShipperTipsHistoryControllerProvider &&
        other.shipperId == shipperId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, shipperId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ShipperTipsHistoryControllerRef
    on AutoDisposeNotifierProviderRef<ShipperTipsHistoryState> {
  /// The parameter `shipperId` of this provider.
  int get shipperId;
}

class _ShipperTipsHistoryControllerProviderElement
    extends AutoDisposeNotifierProviderElement<ShipperTipsHistoryController,
        ShipperTipsHistoryState> with ShipperTipsHistoryControllerRef {
  _ShipperTipsHistoryControllerProviderElement(super.provider);

  @override
  int get shipperId =>
      (origin as ShipperTipsHistoryControllerProvider).shipperId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
