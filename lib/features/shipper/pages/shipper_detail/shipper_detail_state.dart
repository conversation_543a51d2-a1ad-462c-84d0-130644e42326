import 'package:user_app/data/models/shipper/shipper_detail_model.dart';

/// 配送员详情页面状态
class ShipperDetailState {
  /// 是否正在加载
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 配送员详情
  final ShipperDetailModel? shipperDetail;

  /// 打赏金额
  final String tipAmount;

  /// 构造函数
  const ShipperDetailState({
    this.isLoading = false,
    this.error,
    this.shipperDetail,
    this.tipAmount = '',
  });

  /// 创建副本
  ShipperDetailState copyWith({
    final bool? isLoading,
    final String? error,
    final ShipperDetailModel? shipperDetail,
    final String? tipAmount,
    final bool? showInputAmount,
  }) {
    return ShipperDetailState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      shipperDetail: shipperDetail ?? this.shipperDetail,
      tipAmount: tipAmount ?? this.tipAmount,
    );
  }
}
