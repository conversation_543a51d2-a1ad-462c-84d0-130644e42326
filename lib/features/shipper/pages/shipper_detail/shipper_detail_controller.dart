import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:go_router/go_router.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:user_app/features/shipper/pages/shipper_detail/shipper_detail_state.dart';
import 'package:user_app/features/shipper/pages/shipper_detail/widgets/input_amount_dialog.dart';
import 'package:user_app/features/shipper/services/shipper_service.dart';
import 'package:user_app/main.dart';

part 'shipper_detail_controller.g.dart';

/// 配送员详情控制器
@riverpod
class ShipperDetailController extends _$ShipperDetailController {
  @override
  ShipperDetailState build(final shipperId, final orderId) {
    SchedulerBinding.instance.addPostFrameCallback(
      (final timeStamp) => _loadShipperDetail(),
    );
    ref.listen(isLoggedInProvider, (final previous, final next) {
      if (next) {
        Future.microtask(() => _loadShipperDetail());
      }
    });
    return const ShipperDetailState();
  }

  /// 加载配送员详情数据
  /// 从服务器获取配送员详情信息并更新状态
  Future<void> _loadShipperDetail() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final shipperService = ref.read(shipperServiceProvider);
      final result =
          await shipperService.getShipperDetail(shipperId, orderId: orderId);

      if (result.success && result.data != null) {
        state = state.copyWith(
          isLoading: false,
          shipperDetail: result.data,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.msg,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 显示打赏输入框
  ///
  /// 展示打赏金额输入对话框
  void showInputAmount(final BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (final context) => InputAmountDialog(
        initialAmount: state.tipAmount,
        onConfirm: (final amount) {
          Navigator.of(context).pop();
          submitTips(amount);
        },
      ),
    );
  }

  /// 更新打赏金额
  ///
  /// [amount] 新的打赏金额
  void updateTipAmount(final String amount) {
    state = state.copyWith(tipAmount: amount);
  }

  /// 提交打赏
  ///
  /// [amount] 打赏金额
  /// 提交打赏请求并处理响应
  Future<void> submitTips(final double amount) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final shipperService = ref.read(shipperServiceProvider);

      // 调用Service层的支付处理方法
      final result = await shipperService.processShipperTipsPayment(
        shipperId,
        amount,
        orderId: orderId,
        onPaymentComplete: () {
          // 支付完成后刷新配送员详情数据
          _loadShipperDetail();
        },
      );

      // 处理结果
      if (!result.success) {
        state = state.copyWith(
          isLoading: false,
          error: result.msg,
        );
      } else {
        state = state.copyWith(isLoading: false);
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      BotToast.showText(text: '发起支付失败: ${e.toString()}');
    }
  }

  /// 直接打赏固定金额
  ///
  /// [amount] 固定打赏金额
  Future<void> tipFixedAmount(final double amount) async {
    await submitTips(amount);
  }

  /// 打电话给配送员
  ///
  /// 使用系统电话功能拨打配送员电话
  Future<void> callShipper() async {
    final mobile = state.shipperDetail?.shipperMobile;
    if (mobile != null && mobile.isNotEmpty) {
      final url = 'tel:$mobile';
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url));
      }
    }
  }

  /// 前往打赏记录页面
  ///
  /// 导航到打赏记录页面
  void navigateToTipsHistory(final BuildContext context) {
    // 通过路由导航到打赏记录页面
    context.push('/shipperTipsHistory/$shipperId');
  }
}
