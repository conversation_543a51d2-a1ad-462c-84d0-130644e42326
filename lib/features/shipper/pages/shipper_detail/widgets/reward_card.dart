import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/features/shipper/pages/shipper_detail/shipper_detail_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 打赏卡片
class RewardCard extends ConsumerWidget {
  /// 配送员ID
  final int shipperId;

  /// 订单ID（可选）
  final int? orderId;

  /// 构造函数
  const RewardCard({
    super.key,
    required this.shipperId,
    this.orderId,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 根据是否有orderId使用不同的Provider
    final state =
        ref.watch(shipperDetailControllerProvider(shipperId, orderId));
    final controller =
        ref.read(shipperDetailControllerProvider(shipperId, orderId).notifier);

    final shipperDetail = state.shipperDetail;
    if (shipperDetail == null) return const SizedBox();

    return Card(
      margin: EdgeInsets.only(bottom: 10.r),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(10.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Text(
              S.current.shipper_reward_title1,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),

            SizedBox(height: 5.h),

            // 副标题
            Text(
              S.current.shipper_reward_title2,
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFFa6a7a9),
              ),
            ),

            SizedBox(height: 10.h),

            // 打赏选项
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // 自定义金额
                _buildRewardOption(
                  context,
                  imagePath: 'assets/images/order/redpacket.png',
                  text: S.current.other,
                  onTap: () => controller.showInputAmount(context),
                ),

                // 10元
                _buildRewardOption(
                  context,
                  imagePath: 'https://acdn.mulazim.com/wechat_mini/ziyuan.png',
                  text: '¥10',
                  onTap: () => controller.tipFixedAmount(10),
                ),

                // 5元
                _buildRewardOption(
                  context,
                  imagePath:
                      'https://acdn.mulazim.com/wechat_mini/ziyuan_1.png',
                  text: '¥5',
                  onTap: () => controller.tipFixedAmount(5),
                ),

                // 2元
                _buildRewardOption(
                  context,
                  imagePath:
                      'https://acdn.mulazim.com/wechat_mini/ziyuan_2.png',
                  text: '¥2',
                  onTap: () => controller.tipFixedAmount(2),
                ),
              ],
            ),

            SizedBox(height: 10.h),

            // 打赏记录
            GestureDetector(
              onTap: () => controller.navigateToTipsHistory(context),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 打赏用户头像列表
                  if ((shipperDetail.shipperTips?.length ?? 0) > 0) ...[
                    Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFFe2e2e2),
                        borderRadius: BorderRadius.circular(5.r),
                      ),
                      padding: EdgeInsets.all(2.r),
                      child: Icon(
                        Icons.more_horiz,
                        size: 16.sp,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(width: 5.w),
                    ...List.generate(
                      shipperDetail.shipperTips?.length ?? 0,
                      (final index) => Padding(
                        padding: EdgeInsets.symmetric(horizontal: 1.5.w),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(5.r),
                          child: CachedNetworkImage(
                            imageUrl:
                                shipperDetail.shipperTips?[index].userAvatar ??
                                    '',
                            width: 20.w,
                            height: 20.w,
                            fit: BoxFit.cover,
                            placeholder: (final context, final url) =>
                                Container(
                              color: Colors.grey[200],
                              width: 20.w,
                              height: 20.w,
                            ),
                            errorWidget:
                                (final context, final url, final error) =>
                                    Container(
                              width: 20.w,
                              height: 20.w,
                              color: Colors.grey[200],
                              child: Icon(Icons.person,
                                  size: 10.sp, color: Colors.grey),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],

                  SizedBox(width: 5.w),

                  // 打赏人数
                  Text(
                    shipperDetail.shipperTipsCount == 0
                        ? S.current.shipper_no_reward
                        : '${S.current.shipper_total}${shipperDetail.shipperTipsCount}${S.current.shipper_reward_person}',
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: const Color(0xFFa6a7a9),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建打赏选项
  Widget _buildRewardOption(final BuildContext context,
      {required final String imagePath,
      required final String text,
      final VoidCallback? onTap}) {
    bool isAssetImage = imagePath.startsWith('assets/');

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 70.w,
        padding: EdgeInsets.symmetric(vertical: 15.r),
        margin: EdgeInsets.only(right: 10.r),
        decoration: BoxDecoration(
          color: const Color(0xFFEFF1F6),
          borderRadius: BorderRadius.circular(6.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图片
            isAssetImage
                ? Image.asset(
                    imagePath,
                    width: 40.w,
                    height: 40.w,
                  )
                : CachedNetworkImage(
                    imageUrl: imagePath,
                    width: 40.w,
                    height: 40.w,
                    fit: BoxFit.cover,
                    placeholder: (final context, final url) => Container(
                      color: Colors.grey[200],
                    ),
                    errorWidget: (final context, final url, final error) =>
                        Container(
                      color: Colors.grey[200],
                      child: Icon(Icons.error, size: 20.sp, color: Colors.grey),
                    ),
                  ),

            SizedBox(height: 5.h),

            // 文本
            Text(
              text,
              style: TextStyle(
                fontSize: 15.sp,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
