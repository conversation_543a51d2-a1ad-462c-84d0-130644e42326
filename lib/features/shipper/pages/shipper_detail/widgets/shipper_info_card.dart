import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/shipper/pages/shipper_detail/shipper_detail_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 配送员信息卡片
class ShipperInfoCard extends ConsumerWidget {
  /// 配送员ID
  final int shipperId;

  /// 订单ID（可选）
  final int? orderId;

  /// 构造函数
  const ShipperInfoCard({
    super.key,
    required this.shipperId,
    this.orderId,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 根据是否有orderId使用不同的Provider
    final state =
        ref.watch(shipperDetailControllerProvider(shipperId, orderId));
    final controller =
        ref.read(shipperDetailControllerProvider(shipperId, orderId).notifier);

    final shipperDetail = state.shipperDetail;
    if (shipperDetail == null) return const SizedBox();

    return Card(
      margin: EdgeInsets.only(bottom: 10.r),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(10.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 配送员基本信息
            Row(
              children: [
                // 头像
                ClipRRect(
                  borderRadius: BorderRadius.circular(25.r),
                  child: CachedNetworkImage(
                    imageUrl: shipperDetail.shipperAvatar ?? '',
                    width: 50.w,
                    height: 50.w,
                    fit: BoxFit.cover,
                    placeholder: (final context, final url) => Container(
                      color: Colors.grey[200],
                      width: 50.w,
                      height: 50.w,
                    ),
                    errorWidget: (final context, final url, final error) =>
                        Container(
                      width: 50.w,
                      height: 50.w,
                      color: Colors.grey[200],
                      child:
                          Icon(Icons.person, size: 25.sp, color: Colors.grey),
                    ),
                  ),
                ),

                SizedBox(width: 10.w),

                // 名称和区域
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        shipperDetail.shipperName ?? '',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.all(5.w),
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        height: 20.h, // 40rpx / 2
                        decoration: BoxDecoration(
                          color: const Color(0xFFEAEAEA),
                          borderRadius: BorderRadius.circular(50.r),
                        ),
                        //自适应显示
                        child: IntrinsicWidth(
                          child: Center(
                            child: Text(
                              textAlign: TextAlign.center,
                              shipperDetail.areaName ?? '',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: AppColors.textSecondColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 电话按钮
                IconButton(
                  onPressed: controller.callShipper,
                  icon: Icon(
                    Icons.phone,
                    color: AppColors.primary,
                    size: 30.sp,
                  ),
                ),
              ],
            ),

            // 配送员统计信息
            Container(
              margin: EdgeInsets.only(top: 10.r),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  _buildInfoItem(
                    S.current.shipper_distance,
                    '${shipperDetail.shipperDistance}km',
                  ),
                  _buildInfoItem(
                    S.current.shipper_in_time_percent,
                    '${shipperDetail.shipperOnTimeDeliveryRate}%',
                  ),
                  _buildInfoItem(
                    S.current.shipper_satisfaction_percent,
                    '${shipperDetail.shipperCustomerRate}%',
                  ),
                  _buildInfoItem(
                    S.current.shipper_avg_time,
                    '${shipperDetail.shipperDeliveryAvgTime}${S.current.minute}',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建信息项
  Widget _buildInfoItem(final String title, final String value) {
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF858688),
              fontWeight: FontWeight.w300,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 10.h),
          Text(
            value,
            style: TextStyle(
              fontSize: 17.sp,
              color: Colors.black,
              fontWeight: FontWeight.normal,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
