import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 打赏金额输入对话框
class InputAmountDialog extends StatefulWidget {
  /// 初始金额
  final String initialAmount;

  /// 确认回调
  final void Function(double amount) onConfirm;

  /// 取消回调
  final VoidCallback? onCancel;

  /// 构造函数
  const InputAmountDialog({
    super.key,
    this.initialAmount = '',
    required this.onConfirm,
    this.onCancel,
  });

  @override
  State<InputAmountDialog> createState() => _InputAmountDialogState();
}

class _InputAmountDialogState extends State<InputAmountDialog> {
  late final TextEditingController _controller;
  bool _hasFocus = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialAmount);
    _controller.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return AlertDialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 20.w),
      contentPadding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 0.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.r),
      ),
      content: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 对话框内容
          Container(
            margin: EdgeInsets.symmetric(horizontal: 20.w),
            padding: EdgeInsets.all(20.r),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 输入框
                TextField(
                  controller: _controller,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  autofocus: true,
                  textAlign: TextAlign.center,
                  onTap: () {
                    setState(() {
                      _hasFocus = true;
                    });
                  },
                  onChanged: (final value) {
                    // 过滤非法字符，只允许输入数字和小数点
                    if (value.isNotEmpty &&
                        !RegExp(r'^\d*\.?\d{0,2}$').hasMatch(value)) {
                      _controller.text = widget.initialAmount;
                      _controller.selection = TextSelection.fromPosition(
                        TextPosition(offset: _controller.text.length),
                      );
                    }
                  },
                  decoration: InputDecoration(
                    hintText: '0.00',
                    border: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 10.w,
                      vertical: 10.h,
                    ),
                  ),
                  style: TextStyle(
                    fontSize: 20.sp,
                    color: Colors.black,
                  ),
                  inputFormatters: [
                    // 限制输入格式
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                    LengthLimitingTextInputFormatter(10),
                  ],
                ),

                SizedBox(height: 20.h),

                // 确认按钮
                ElevatedButton(
                  onPressed: _controller.text.isEmpty
                      ? null
                      : () {
                          final amount = double.tryParse(_controller.text) ?? 0;
                          if (amount > 0) {
                            widget.onConfirm(amount);
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    minimumSize: Size(double.infinity, 44.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(50.r),
                    ),
                  ),
                  child: Text(
                    S.current.reward,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
