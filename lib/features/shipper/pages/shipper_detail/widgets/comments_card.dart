import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/star_rating.dart';
import 'package:user_app/data/models/shipper/shipper_detail_model.dart';
import 'package:user_app/features/shipper/pages/shipper_detail/shipper_detail_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 评论卡片
class CommentsCard extends ConsumerWidget {
  /// 配送员ID
  final int shipperId;

  /// 订单ID（可选）
  final int? orderId;

  /// 是否隐藏其他星级评分
  final bool hide2Star;

  /// 构造函数
  const CommentsCard({
    super.key,
    required this.shipperId,
    this.orderId,
    this.hide2Star = false,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 根据是否有orderId使用不同的Provider
    final state =
        ref.watch(shipperDetailControllerProvider(shipperId, orderId));

    final shipperDetail = state.shipperDetail;
    if (shipperDetail == null || (shipperDetail.comments?.isEmpty ?? true)) {
      return const SizedBox();
    }

    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      itemCount: shipperDetail.comments?.length ?? 0,
      itemBuilder: (final context, final index) {
        final comment = shipperDetail.comments![index];
        final bool hideBorder = index == 0 ? true : comment.hideBorder ?? false;

        return _buildCommentItem(comment, hideBorder);
      },
    );
  }

  /// 构建评论项
  Widget _buildCommentItem(final CommentModel comment, final bool hideBorder) {
    return Container(
      padding: EdgeInsets.all(15.r),
      decoration: BoxDecoration(
        border: hideBorder
            ? null
            : Border(
                top: BorderSide(
                  color: const Color(0xFFEFF1F6),
                  width: 0.5,
                ),
              ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // row-item：头像和用户信息（包括评分）
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头像区域
              Container(
                width: 40.w,
                height: 40.w,
                margin: EdgeInsetsDirectional.only(
                  end: 10.w,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.r),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(5.r),
                  child: CachedNetworkImage(
                    imageUrl: comment.userAvatar ?? '',
                    width: 40.w,
                    height: 40.w,
                    fit: BoxFit.cover,
                    placeholder: (final context, final url) => Container(
                      color: Colors.grey[200],
                      width: 40.w,
                      height: 40.w,
                    ),
                    errorWidget: (final context, final url, final error) =>
                        Container(
                      width: 40.w,
                      height: 40.w,
                      color: Colors.grey[200],
                      child:
                          Icon(Icons.person, size: 16.sp, color: Colors.grey),
                    ),
                  ),
                ),
              ),

              // 用户信息和评分区域
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 用户名和时间行
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          constraints: BoxConstraints(maxWidth: 120.w),
                          child: Text(
                            (comment.userName?.isNotEmpty == true) 
                                ? comment.userName! 
                                : '******',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.black,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          comment.createdAt ?? '',
                          style: TextStyle(
                            fontSize: 13.sp,
                            color: const Color(0xFF757575),
                          ),
                          textDirection: TextDirection.ltr,
                        ),
                      ],
                    ),

                    SizedBox(width: 10.w),

                    // 总体评分和食物名称行
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Text(
                              S.current.evaluate_in_general,
                              style: TextStyle(
                                fontSize: 13.5.sp,
                                color: const Color(0xFF424242),
                              ),
                            ),
                            SizedBox(width: 5.w),
                            _buildStarRating(comment.star ?? 5),
                          ],
                        ),
                        if (comment.foodName != null &&
                            comment.foodName!.isNotEmpty)
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 7.5.w, vertical: 5.h),
                            decoration: BoxDecoration(
                              color: const Color(0xFFEFF1F6),
                              borderRadius: BorderRadius.circular(5.r),
                            ),
                            constraints: BoxConstraints(maxWidth: 90.w),
                            child: Text(
                              comment.foodName!,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppColors.primary,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                      ],
                    ),

                    // 味道评分行
                    if (hide2Star &&
                        (comment.star != comment.foodStar ||
                            comment.star != comment.boxStar)) ...[
                      SizedBox(height: 5.h),
                      Row(
                        children: [
                          Text(
                            S.current.evaluate_taste,
                            style: TextStyle(
                              fontSize: 13.5.sp,
                              color: const Color(0xFF424242),
                            ),
                          ),
                          SizedBox(width: 5.w),
                          _buildStarRating(comment.foodStar ?? 5),
                        ],
                      ),

                      // 包装评分行
                      SizedBox(height: 5.h),
                      Row(
                        children: [
                          Text(
                            S.current.evaluate_packing,
                            style: TextStyle(
                              fontSize: 13.5.sp,
                              color: const Color(0xFF424242),
                            ),
                          ),
                          SizedBox(width: 5.w),
                          _buildStarRating(comment.boxStar ?? 5),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          // data-item：评论内容、图片和回复
          Container(
            margin: EdgeInsets.only(top: 5.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 评论内容
                if (comment.text != null && comment.text!.isNotEmpty)
                  Padding(
                    padding: EdgeInsets.only(top: 10.h),
                    child: Text(
                      comment.text!,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: const Color(0xFF303030),
                      ),
                    ),
                  ),

                // 评论图片
                if (comment.images != null && comment.images!.isNotEmpty)
                  _buildImages(comment.images!),

                // 回复
                if (comment.replies != null && comment.replies!.isNotEmpty)
                  _buildReplies(comment.replies!),

                // 评论状态 (审核中/已审核/拒绝审核)
                if (comment.status != null) ...[
                  SizedBox(height: 5.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      _buildStatusTag(comment.status!),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建状态标签
  Widget _buildStatusTag(int status) {
    String text;
    Color color;

    // 根据状态设置文本和颜色
    switch (status) {
      case 0: // 审核中
        text = S.current.comment_reviewing;
        color = Colors.orange;
        break;
      case 1: // 已审核
        text = S.current.comment_reviewed;
        color = AppColors.primary;
        break;
      case 2: // 拒绝审核
        text = S.current.comment_no_reviewing;
        color = Colors.red;
        break;
      default:
        return const SizedBox();
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 3.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12.sp,
          color: color,
        ),
      ),
    );
  }

  /// 构建图片列表
  Widget _buildImages(final List<String> images) {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      child: Wrap(
        spacing: 5.w,
        runSpacing: 5.w,
        children: images.asMap().entries.map((entry) {
          int index = entry.key;
          String image = entry.value;

          double imgWidth;
          double imgHeight;
          BoxFit fitMode;

          if (images.length == 1) {
            // 单张图片
            imgWidth = double.infinity;
            imgHeight = 180.h;
            fitMode = BoxFit.contain;
          } else if (images.length == 2) {
            // 两张图片
            imgWidth = (1.sw - 40.w) / 2;
            imgHeight = 125.h;
            fitMode = BoxFit.cover;
          } else {
            // 三张或更多图片
            imgWidth = (1.sw - 50.w) / 3;
            imgHeight = 97.5.h;
            fitMode = BoxFit.cover;
          }

          return ClipRRect(
            borderRadius: BorderRadius.circular(5.r),
            child: CachedNetworkImage(
              imageUrl: image,
              width: imgWidth,
              height: imgHeight,
              fit: fitMode,
              placeholder: (final context, final url) => Container(
                color: Colors.grey[200],
                width: imgWidth,
                height: imgHeight,
              ),
              errorWidget: (final context, final url, final error) => Container(
                width: imgWidth,
                height: imgHeight,
                color: Colors.grey[200],
                child:
                    Icon(Icons.broken_image, size: 30.sp, color: Colors.grey),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建回复列表
  Widget _buildReplies(final List<ReplyModel> replies) {
    return Container(
      margin: EdgeInsets.only(top: 7.5.h),
      padding: EdgeInsets.all(10.r),
      decoration: BoxDecoration(
        color: const Color(0xFFEFF1F6),
        borderRadius: BorderRadius.circular(5.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: replies.map((final reply) {
          return Container(
            margin: EdgeInsets.only(bottom: 5.h),
            child: RichText(
              textAlign: TextAlign.justify,
              text: TextSpan(
                style: TextStyle(
                  fontSize: 15.sp,
                  color: const Color(0xFF616161),
                  fontFamily: AppConstants.mainFont,
                ),
                children: [
                  TextSpan(
                    text: reply.type == 2
                        ? S.current.reply_mlz
                        : S.current.reply_res,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontFamily: AppConstants.mainFont,
                      color: Color(0xFF515F9C),
                    ),
                  ),
                  TextSpan(
                    text: reply.text ?? '',
                    style: TextStyle(
                      fontFamily: AppConstants.mainFont,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建星级评分
  Widget _buildStarRating(final int rating) {
    return StarRating(
      rating: rating.toDouble(),
      size: 16, // 32rpx / 2
      hideTip: true,
      gap: 2,
    );
  }
}
