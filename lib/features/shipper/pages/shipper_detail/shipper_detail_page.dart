import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/shipper/pages/shipper_detail/shipper_detail_controller.dart';
import 'package:user_app/features/shipper/pages/shipper_detail/widgets/shipper_info_card.dart';
import 'package:user_app/features/shipper/pages/shipper_detail/widgets/comments_card.dart';

/// 配送员详情页面
class ShipperDetailPage extends ConsumerWidget {
  /// 配送员ID
  final int shipperId;

  /// 订单ID（可选）
  final int? orderId;

  /// 构造函数
  const ShipperDetailPage({
    super.key,
    required this.shipperId,
    this.orderId,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 根据是否有orderId使用不同的Provider
    final state =
        ref.watch(shipperDetailControllerProvider(shipperId, orderId));

    return Scaffold(
      appBar: CustomAppBar(
        title: "",
      ),
      body: Stack(
        children: [
          // 背景渐变色
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.center,
                colors: [
                  AppColors.primary,
                  const Color(0xFFEFF1F6),
                ],
                stops: const [0.3, 0.9],
              ),
            ),
          ),

          // 内容
          SafeArea(
            child: state.isLoading && state.shipperDetail == null
                ? const Center(child: LoadingWidget())
                : SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10.r),
                      child: Column(
                        children: [
                          // 配送员信息卡片
                          ShipperInfoCard(
                            shipperId: shipperId,
                            orderId: orderId,
                          ),

                          // // 打赏卡片
                          // RewardCard(
                          //   shipperId: shipperId,
                          //   orderId: orderId,
                          // ),

                          // 评论卡片
                          Card(
                            margin: EdgeInsets.only(bottom: 10.r),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16.r),
                            ),
                            child: CommentsCard(
                              shipperId: shipperId,
                              orderId: orderId,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}
