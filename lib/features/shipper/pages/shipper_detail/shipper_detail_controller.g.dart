// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shipper_detail_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$shipperDetailControllerHash() =>
    r'e106a3f1d2c2275a04a982dd11bcb75e875a7b11';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ShipperDetailController
    extends BuildlessAutoDisposeNotifier<ShipperDetailState> {
  late final dynamic shipperId;
  late final dynamic orderId;

  ShipperDetailState build(
    dynamic shipperId,
    dynamic orderId,
  );
}

/// 配送员详情控制器
///
/// Copied from [ShipperDetailController].
@ProviderFor(ShipperDetailController)
const shipperDetailControllerProvider = ShipperDetailControllerFamily();

/// 配送员详情控制器
///
/// Copied from [ShipperDetailController].
class ShipperDetailControllerFamily extends Family<ShipperDetailState> {
  /// 配送员详情控制器
  ///
  /// Copied from [ShipperDetailController].
  const ShipperDetailControllerFamily();

  /// 配送员详情控制器
  ///
  /// Copied from [ShipperDetailController].
  ShipperDetailControllerProvider call(
    dynamic shipperId,
    dynamic orderId,
  ) {
    return ShipperDetailControllerProvider(
      shipperId,
      orderId,
    );
  }

  @override
  ShipperDetailControllerProvider getProviderOverride(
    covariant ShipperDetailControllerProvider provider,
  ) {
    return call(
      provider.shipperId,
      provider.orderId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'shipperDetailControllerProvider';
}

/// 配送员详情控制器
///
/// Copied from [ShipperDetailController].
class ShipperDetailControllerProvider extends AutoDisposeNotifierProviderImpl<
    ShipperDetailController, ShipperDetailState> {
  /// 配送员详情控制器
  ///
  /// Copied from [ShipperDetailController].
  ShipperDetailControllerProvider(
    dynamic shipperId,
    dynamic orderId,
  ) : this._internal(
          () => ShipperDetailController()
            ..shipperId = shipperId
            ..orderId = orderId,
          from: shipperDetailControllerProvider,
          name: r'shipperDetailControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$shipperDetailControllerHash,
          dependencies: ShipperDetailControllerFamily._dependencies,
          allTransitiveDependencies:
              ShipperDetailControllerFamily._allTransitiveDependencies,
          shipperId: shipperId,
          orderId: orderId,
        );

  ShipperDetailControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.shipperId,
    required this.orderId,
  }) : super.internal();

  final dynamic shipperId;
  final dynamic orderId;

  @override
  ShipperDetailState runNotifierBuild(
    covariant ShipperDetailController notifier,
  ) {
    return notifier.build(
      shipperId,
      orderId,
    );
  }

  @override
  Override overrideWith(ShipperDetailController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ShipperDetailControllerProvider._internal(
        () => create()
          ..shipperId = shipperId
          ..orderId = orderId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        shipperId: shipperId,
        orderId: orderId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<ShipperDetailController,
      ShipperDetailState> createElement() {
    return _ShipperDetailControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ShipperDetailControllerProvider &&
        other.shipperId == shipperId &&
        other.orderId == orderId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, shipperId.hashCode);
    hash = _SystemHash.combine(hash, orderId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ShipperDetailControllerRef
    on AutoDisposeNotifierProviderRef<ShipperDetailState> {
  /// The parameter `shipperId` of this provider.
  dynamic get shipperId;

  /// The parameter `orderId` of this provider.
  dynamic get orderId;
}

class _ShipperDetailControllerProviderElement
    extends AutoDisposeNotifierProviderElement<ShipperDetailController,
        ShipperDetailState> with ShipperDetailControllerRef {
  _ShipperDetailControllerProviderElement(super.provider);

  @override
  dynamic get shipperId =>
      (origin as ShipperDetailControllerProvider).shipperId;
  @override
  dynamic get orderId => (origin as ShipperDetailControllerProvider).orderId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
