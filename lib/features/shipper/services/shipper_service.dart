import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/utils/wechat_util.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/data/models/shipper/shipper_detail_model.dart';
import 'package:user_app/data/models/shipper/shipper_tips_history_model.dart';
import 'package:user_app/data/repositories/shipper/shipper_repository.dart';
import 'package:user_app/generated/l10n.dart';

/// 配送员服务
class ShipperService {
  final ShipperRepository _shipperRepository;

  /// 构造函数
  ShipperService(this._shipperRepository);

  /// 获取配送员详情
  Future<ApiResult<ShipperDetailModel>> getShipperDetail(
    final int shipperId, {
    final int? orderId,
  }) async {
    return await _shipperRepository.getShipperDetail(
      shipperId,
      orderId: orderId,
    );
  }

  /// 获取配送员打赏微信支付参数
  Future<ApiResult<dynamic>> getShipperTipsWechatParams(
    final int shipperId,
    final double amount, {
    final int? orderId,
  }) async {
    try {
      //检查金额是否合法
      if (amount <= 0.2) {
        return ApiResult.error(msg: S.current.invalid_amount);
      }

      return await _shipperRepository.getShipperTipsWechatParams(
        shipperId,
        amount,
        orderId: orderId,
      );
    } catch (e) {
      return ApiResult.error(msg: e.toString());
    }
  }

  /// 处理配送员打赏支付
  /// 获取微信支付参数并处理支付逻辑
  Future<ApiResult<void>> processShipperTipsPayment(
    final int shipperId,
    final double amount, {
    final int? orderId,
    final Function? onPaymentComplete,
  }) async {
    try {
      LoadingDialog().show();

      // 获取微信支付参数
      final wechatParamsResult = await getShipperTipsWechatParams(
        shipperId,
        amount,
        orderId: orderId,
      );

      if (!wechatParamsResult.success || wechatParamsResult.data == null) {
        BotToast.showText(text: wechatParamsResult.msg);
        LoadingDialog().hide();

        return ApiResult.error(
          msg: wechatParamsResult.msg,
        );
      }

      final payParams = wechatParamsResult.data!; // 使用非空断言，因为已经检查过了
      // 从package中提取prepay_id的值
      final packageValue = payParams['package'] as String? ?? '';
      if (packageValue.isNotEmpty && packageValue.startsWith('prepay_id=')) {
        final prepayId = packageValue.substring('prepay_id='.length);
        payParams['prepayId'] = prepayId;
      }
      payParams['partnerId'] =
          payParams['partnerId'] ?? payParams['partner_id'];
      try {
        // 使用WechatUtil的processPayment方法处理支付
        final paymentResult = await WechatUtil().processPayment(
          payParams,
          onPaymentComplete: onPaymentComplete,
        );

        LoadingDialog().hide();

        return paymentResult['success']
            ? ApiResult.success(paymentResult['message'])
            : ApiResult.error(msg: paymentResult['message']);
      } catch (e) {
        BotToast.showText(text: '发起支付失败: ${e.toString()}');
        LoadingDialog().hide();
        return ApiResult.error(msg: e.toString());
      }
    } catch (e) {
      LoadingDialog().hide();
      return ApiResult.error(msg: e.toString());
    }
  }

  /// 获取配送员打赏历史
  Future<ApiResult<ShipperTipsHistoryModel>> getShipperTipsHistory(
    final int shipperId, {
    final int limit = 30,
    final int page = 1,
  }) async {
    try {
      final response = await _shipperRepository.getShipperTipsHistory(
        shipperId,
        limit: limit,
        page: page,
      );

      if (response.data != null) {
        final result = response.data;
        final perPage = result['per_page'] as int? ?? 30;
        final items = result['items'] as List<dynamic>? ?? [];

        // 创建打赏历史模型
        final tipsHistory = ShipperTipsHistoryModel(
          shipperId: result['shipper_id'],
          shipperName: result['shipper_name'],
          shipperAvatar: result['shipper_avatar'],
          tipsTotal: (result['tips_total'] ?? 0).toDouble(),
          tipsCount: result['tips_count'],
          tipsList:
              items.map((item) => ShipperTipItemModel.fromJson(item)).toList(),
        );

        return ApiResult.success(
          tipsHistory,
          msg: 'Successfully loaded tips history',
        );
      } else {
        return ApiResult.error(
          msg: response.msg,
        );
      }
    } catch (e) {
      return ApiResult.error(msg: e.toString());
    }
  }
}

/// 配送员服务提供者
final shipperServiceProvider = Provider<ShipperService>((final ref) {
  return ShipperService(ref.watch(shipperRepositoryProvider));
});
