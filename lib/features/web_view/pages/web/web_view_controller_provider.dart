import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:user_app/features/web_view/pages/web/web_view_state.dart';

part 'web_view_controller_provider.g.dart';

/// Web页面控制器
@riverpod
class WebController extends _$WebController {
  @override
  WebPageState build() {
    // 当provider被销毁时自动清理
    ref.onDispose(() {
      state = state.copyWith(controller: null);
    });
    return const WebPageState();
  }

  /// 初始化WebView
  Future<void> initWebView({
    required final String url,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (_) {
              state = state.copyWith(isLoading: true, error: null);
            },
            onPageFinished: (_) {
              state = state.copyWith(isLoading: false, error: null);
            },
            onWebResourceError: (final error) {
              state = state.copyWith(
                isLoading: false,
                error: '加载失败: ${error.description}',
              );
            },
          ),
        );

      await controller.loadRequest(Uri.parse(url));
      state = state.copyWith(controller: controller);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '初始化失败: $e',
      );
    }
  }

  /// 重新加载
  Future<void> reload() async {
    if (state.controller != null) {
      await state.controller!.reload();
    }
  }

  /// 返回上一页
  Future<bool> goBack() async {
    if (state.controller != null) {
      if (await state.controller!.canGoBack()) {
        await state.controller!.goBack();
        return true;
      }
    }
    return false;
  }

  /// 前进
  Future<void> goForward() async {
    if (state.controller != null) {
      if (await state.controller!.canGoForward()) {
        await state.controller!.goForward();
      }
    }
  }
}
