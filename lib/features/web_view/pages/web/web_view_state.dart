import 'package:webview_flutter/webview_flutter.dart';

/// Web页面状态
class WebPageState {
  /// 构造函数
  const WebPageState({
    this.controller,
    this.isLoading = false,
    this.error,
  });

  /// WebView控制器
  final WebViewController? controller;

  /// 是否正在加载
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 复制状态
  WebPageState copyWith({
    final WebViewController? controller,
    final bool? isLoading,
    final String? error,
  }) {
    return WebPageState(
      controller: controller ?? this.controller,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}
