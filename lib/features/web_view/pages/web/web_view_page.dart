import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/web_view/pages/web/web_view_controller_provider.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:user_app/routes/paths.dart';

/// Web页面
class WebPage extends ConsumerStatefulWidget {
  /// 标题
  final String title;

  /// URL
  final String url;

  /// 构造函数
  const WebPage({
    super.key,
    required this.title,
    required this.url,
  });

  /// 构建路径
  static String buildPath({
    required final String title,
    required final String url,
  }) {
    return '${AppPaths.webViewPage}/${Uri.encodeComponent(title)}/${Uri.encodeComponent(url)}';
  }

  @override
  ConsumerState<WebPage> createState() => _WebPageState();
}

/// Web页面状态
class _WebPageState extends ConsumerState<WebPage> {
  @override
  void initState() {
    super.initState();
    // 初始化WebView
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      if (mounted) {
        ref.read(webControllerProvider.notifier).initWebView(
              url: widget.url,
            );
      }
    });
  }

  @override
  Widget build(final BuildContext context) {
    final state = ref.watch(webControllerProvider);

    return WillPopScope(
      onWillPop: () async {
        final canGoBack =
            await ref.read(webControllerProvider.notifier).goBack();
        return !canGoBack;
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(widget.title),
          centerTitle: true,
          // actions: [
          //   IconButton(
          //     icon: const Icon(Icons.refresh),
          //     onPressed: () {
          //       ref.read(webControllerProvider.notifier).reload();
          //     },
          //   ),
          // ],
        ),
        body: Stack(
          children: [
            if (state.controller != null)
              WebViewWidget(controller: state.controller!),
            if (state.isLoading)
              const Center(
                child: LoadingWidget(),
              ),
            if (state.error != null)
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(state.error!),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        ref.read(webControllerProvider.notifier).reload();
                      },
                      child: const Text('重试'),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
