// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'web_view_controller_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$webControllerHash() => r'a98aab2433689a66c0a9607c4a41eda068842e2d';

/// Web页面控制器
///
/// Copied from [WebController].
@ProviderFor(WebController)
final webControllerProvider =
    AutoDisposeNotifierProvider<WebController, WebPageState>.internal(
  WebController.new,
  name: r'webControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WebController = AutoDisposeNotifier<WebPageState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
