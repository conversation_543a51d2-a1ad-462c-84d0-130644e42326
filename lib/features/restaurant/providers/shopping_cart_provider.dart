import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/core/utils/calculation_utils.dart';
import 'package:user_app/core/utils/decimal_utils.dart';
import 'package:user_app/core/widgets/toast_widget.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/data/models/restaurant/spec_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';
import 'package:user_app/features/restaurant/services/spec_service.dart';
import 'package:user_app/generated/l10n.dart';

part 'shopping_cart_provider.g.dart';

/// 购物车图标的全局Key，用于动画定位
final GlobalKey globalCartIconKey = GlobalKey(debugLabel: 'cart_icon_key');

/// 订单计算工具类
class OrderCalculationConstants {
  /// 配送模式
  static const int deliveryMode = 0;

  /// 自取模式
  static const int pickupMode = 1;
}

/// 购物车项目模型
class SelectFoodItem {
  final int? id;
  final String? foodsName;
  final String? foodsImage;
  final num? foodsPrice; // 普通价格
  final num? prefrentialPrice; // 优惠价格
  final num? oldPrice; // 原价
  final num? lunchBoxFee; // 餐盒费
  final int? lunchBoxAccommodate; // 餐盒容量
  final num? percent; // 利润百分比
  final bool? isSeckill; // 是否秒杀
  final int? seckillMaxCount; // 秒杀最大数量
  final num? seckillPrice; // 秒杀价格
  final int? seckillId; // 秒杀id
  final List<dynamic>? reductionTags; // 满减标签
  int? count;
  final int? seckillActive; // 秒杀活动状态
  final int? specialActive; // 特价活动状态
  final int? multiDiscountId; // 多份打折ID
  final List<MultiDiscountStep>? multiDiscountSteps; // 多份打折步骤
  final num? multiDiscountAdvancePrice; // 多份打折当前价格
  final int? maxOrderCount; // 优惠限购数量

  // ========== 规格相关字段 ==========
  /// 规格唯一标识符 - 用于区分不同规格组合的商品
  final String? specUniqueId;

  /// 规格选择选项信息 - 存储用户选择的规格详情
  final List<Map<String, dynamic>>? specSelectedOptions;

  /// 优惠数量 - 享受优惠的商品数量
  final int? prefrentialCount;

  // ========== 套餐相关字段 ==========
  /// 美食类型 - 2表示套餐
  final int? foodType;

  /// 套餐包含的美食项目
  final List<ComboFoodItem>? comboFoodItems;

  SelectFoodItem({
    this.id,
    this.foodsName,
    this.foodsImage,
    this.foodsPrice,
    this.prefrentialPrice,
    this.oldPrice,
    this.lunchBoxFee = 0,
    this.lunchBoxAccommodate = 1,
    this.percent = 0,
    this.isSeckill = false,
    this.seckillMaxCount = 0,
    this.seckillPrice,
    this.reductionTags,
    this.count = 0,
    this.seckillId,
    this.seckillActive,
    this.specialActive,
    this.multiDiscountId,
    this.multiDiscountSteps,
    this.multiDiscountAdvancePrice,
    this.maxOrderCount = 0,
    // 规格相关字段
    this.specUniqueId,
    this.specSelectedOptions,
    this.prefrentialCount,
    // 套餐相关字段
    this.foodType,
    this.comboFoodItems,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'food_name': foodsName,
      'food_image': foodsImage,
      'food_price': foodsPrice,
      'prefrential_price': prefrentialPrice,
      'old_price': oldPrice,
      'lunch_box_fee': lunchBoxFee,
      'lunch_box_accommodate': lunchBoxAccommodate,
      'percent': percent,
      'is_seckill': isSeckill,
      'seckill_max_count': seckillMaxCount,
      'seckill_price': seckillPrice,
      'seckill_id': seckillId,
      'reduction_tags': reductionTags,
      'count': count,
      'seckill_active': seckillActive,
      'special_active': specialActive,
      'multi_discount_id': multiDiscountId,
      'multi_discount_steps': multiDiscountSteps
          ?.map(
            (final step) => {
              'index': step.index,
              'name': step.name,
              'number': step.number,
              'price': step.price,
              'food_id': step.foodId,
              'advance_price': step.advancePrice,
            },
          )
          .toList(),
      'multi_discount_advance_price': multiDiscountAdvancePrice,
      'max_order_count': maxOrderCount,
    };
  }

  // 转换为与微信小程序兼容的格式
  Map<String, dynamic> toMiniappFormat() {
    return {
      'id': id,
      'price': foodsPrice,
      'old_price': oldPrice,
      'lunch_box_fee': lunchBoxFee,
      'lunch_box_accommodate': lunchBoxAccommodate,
      'prefrential_price': prefrentialPrice,
      'percent': percent,
      'seckill_active': isSeckill == true ? 1 : 0,
      'seckill_max_order_count': seckillMaxCount,
      'seckill_price': seckillPrice,
      'count': count,
      'max_order_count': maxOrderCount ?? 999, // 优惠限购数量
      'reduction_foods_tags': reductionTags,
      'seckill_id': seckillId,
      'special_active': specialActive,
      'multi_discount_id': multiDiscountId,
      'multi_discount_steps': multiDiscountSteps
          ?.map(
            (final step) => {
              'index': step.index,
              'name': step.name,
              'number': step.number,
              'price': step.price,
              'food_id': step.foodId,
              'advance_price': step.advancePrice,
            },
          )
          .toList(),
      'multi_discount_advance_price': multiDiscountAdvancePrice,
    };
  }

  /// 拷贝方法
  SelectFoodItem copyWith({
    final int? id,
    final String? foodsName,
    final String? foodsImage,
    final num? foodsPrice,
    final num? prefrentialPrice,
    final num? oldPrice,
    final num? lunchBoxFee,
    final int? lunchBoxAccommodate,
    final num? percent,
    final bool? isSeckill,
    final int? seckillMaxCount,
    final num? seckillPrice,
    final int? seckillId,
    final List<dynamic>? reductionTags,
    final int? count,
    final int? seckillActive,
    final int? specialActive,
    final int? multiDiscountId,
    final List<MultiDiscountStep>? multiDiscountSteps,
    final num? multiDiscountAdvancePrice,
    final String? specUniqueId,
    final List<Map<String, dynamic>>? specSelectedOptions,
    final int? prefrentialCount,
    final int? foodType,
    final List<ComboFoodItem>? comboFoodItems,
    final int? maxOrderCount,
  }) {
    return SelectFoodItem(
      id: id ?? this.id,
      foodsName: foodsName ?? this.foodsName,
      foodsImage: foodsImage ?? this.foodsImage,
      foodsPrice: foodsPrice ?? this.foodsPrice,
      prefrentialPrice: prefrentialPrice ?? this.prefrentialPrice,
      oldPrice: oldPrice ?? this.oldPrice,
      lunchBoxFee: lunchBoxFee ?? this.lunchBoxFee,
      lunchBoxAccommodate: lunchBoxAccommodate ?? this.lunchBoxAccommodate,
      percent: percent ?? this.percent,
      isSeckill: isSeckill ?? this.isSeckill,
      seckillMaxCount: seckillMaxCount ?? this.seckillMaxCount,
      seckillPrice: seckillPrice ?? this.seckillPrice,
      seckillId: seckillId ?? this.seckillId,
      reductionTags: reductionTags ?? this.reductionTags,
      count: count ?? this.count,
      seckillActive: seckillActive ?? this.seckillActive,
      specialActive: specialActive ?? this.specialActive,
      multiDiscountId: multiDiscountId ?? this.multiDiscountId,
      multiDiscountSteps: multiDiscountSteps ?? this.multiDiscountSteps,
      multiDiscountAdvancePrice:
          multiDiscountAdvancePrice ?? this.multiDiscountAdvancePrice,
      specUniqueId: specUniqueId ?? this.specUniqueId,
      specSelectedOptions: specSelectedOptions ?? this.specSelectedOptions,
      prefrentialCount: prefrentialCount ?? this.prefrentialCount,
      foodType: foodType ?? this.foodType,
      comboFoodItems: comboFoodItems ?? this.comboFoodItems,
      maxOrderCount: maxOrderCount ?? this.maxOrderCount,
    );
  }
}

/// 购物车计算结果模型
class CartCalculationResult {
  /// 商品小计
  final num subtotal;

  /// 餐盒费
  final num lunchBoxFee;

  /// 配送费
  final num shipmentFee;

  /// 满减优惠
  final num reductionDiscount;

  /// 配送费优惠
  final num shipmentDiscount;

  /// 优惠券折扣
  final num couponDiscount;

  /// 总价
  final num totalPrice;

  /// 总数量
  final int totalCount;

  /// 商品种类数量
  final int itemCount;

  /// 是否可以应用满减
  final bool canApplyReduction;

  /// 距离满减还差的金额
  final num missingAmount;

  /// 满减当前阶梯信息
  final Map<String, dynamic>? reductionCurrentStep;

  /// 满减下一个阶梯信息
  final Map<String, dynamic>? reductionNextStep;

  /// 满减当前阶梯提示文本
  final String? reductionCurrentStepText;

  /// 满减下一个阶梯提示文本
  final String? reductionNextStepText;

  /// 配送费减免当前阶梯信息
  final Map<String, dynamic>? shipmentCurrentStep;

  /// 配送费减免下一个阶梯信息
  final Map<String, dynamic>? shipmentNextStep;

  /// 配送费减免当前阶梯提示文本
  final String? shipmentCurrentStepText;

  /// 配送费减免下一个阶梯提示文本
  final String? shipmentNextStepText;

  /// 优惠前的原始总价格
  final num totalOriginalPrice;

  /// 美食原始价格
  final num totalFoodOriginalPrice;

  /// 构造方法
  CartCalculationResult({
    this.subtotal = 0,
    this.lunchBoxFee = 0,
    this.shipmentFee = 0,
    this.reductionDiscount = 0,
    this.shipmentDiscount = 0,
    this.couponDiscount = 0,
    this.totalPrice = 0,
    this.totalCount = 0,
    this.itemCount = 0,
    this.canApplyReduction = false,
    this.missingAmount = 0,
    this.reductionCurrentStep,
    this.reductionNextStep,
    this.reductionCurrentStepText,
    this.reductionNextStepText,
    this.shipmentCurrentStep,
    this.shipmentNextStep,
    this.shipmentCurrentStepText,
    this.shipmentNextStepText,
    this.totalOriginalPrice = 0,
    this.totalFoodOriginalPrice = 0,
  });
}

/// 购物车状态
@riverpod
class ShoppingCart extends _$ShoppingCart {
  @override
  List<SelectFoodItem> build() {
    return [];
  }

  /// 将美食添加到购物车 - 与微信小程序逻辑一致
  /// 每个规格组合作为独立的商品项添加到购物车
  /// 支持最小购买数量逻辑
  void addFoodToCart({required final Food foodItem}) {
    // 对于规格商品，使用id + specUniqueId进行匹配；对于普通商品，使用id匹配
    // 与微信小程序逻辑一致：item.id == foodId && item.spec_unique_id == uniqueId
    int idx = -1;

    if (foodItem.specUniqueId != null && foodItem.specUniqueId!.isNotEmpty) {
      // 规格商品：同时匹配id和specUniqueId - 与微信小程序完全一致
      idx = state.indexWhere(
        (final element) =>
            element.id == foodItem.id &&
            element.specUniqueId == foodItem.specUniqueId,
      );
    } else {
      // 普通商品：只匹配id，且确保不是规格商品 - 与微信小程序逻辑一致
      idx = state.indexWhere(
        (final element) =>
            element.id == foodItem.id &&
            (element.specUniqueId == null || element.specUniqueId!.isEmpty),
      );
    }

    if (idx != -1) {
      // 如果已存在，增加数量 - 与微信小程序逻辑一致
      final updatedList = [...state];
      final originalItem = updatedList[idx];
      final newCount = originalItem.count! + 1;

      // 更新数量
      updatedList[idx].count = newCount;

      // 如果是普通商品，需要更新促销信息和检查限购（秒杀和优惠限购）
      if (foodItem.specUniqueId == null || foodItem.specUniqueId!.isEmpty) {
        // 普通商品：检查限购警告
        _checkLimitPurchaseWarningsForNormalFood(foodItem, newCount);

        // 普通商品：更新秒杀和优惠信息
        updatedList[idx] = _updatePromotionInfoForNormalFood(
          originalItem.copyWith(count: newCount),
          foodItem,
        );
      }

      // 处理多份打折逻辑
      if (foodItem.multiDiscountId != null &&
          foodItem.multiDiscountSteps != null &&
          foodItem.multiDiscountSteps!.isNotEmpty) {
        updatedList[idx] = _updateMultiDiscountSteps(
          updatedList[idx],
          updatedList[idx].count!,
        );
      }

      state = updatedList;
      return;
    }

    // 如果不存在，添加新项目 - 与微信小程序逻辑一致
    // 确定初始添加数量 - 考虑最小购买数量
    final int initialCount =
        (foodItem.minCount != null && foodItem.minCount! > 1)
            ? foodItem.minCount!
            : 1;

    // 如果是普通商品，检查限购警告
    if (foodItem.specUniqueId == null || foodItem.specUniqueId!.isEmpty) {
      _checkLimitPurchaseWarningsForNormalFood(foodItem, initialCount);
    }

    final newItem = SelectFoodItem(
      id: foodItem.id,
      foodsName: foodItem.name,
      foodsImage: foodItem.image,
      foodsPrice: foodItem.price, // 使用Food中已经计算好的价格（可能是规格价格、秒杀价格或优惠价格）
      oldPrice: foodItem.oldPrice ?? foodItem.originPrice, // 使用oldPrice字段
      prefrentialPrice: foodItem.prefrentialPrice,
      lunchBoxFee: foodItem.lunchBoxFee,
      lunchBoxAccommodate: foodItem.lunchBoxAccommodate?.toInt(),
      percent: foodItem.percent,
      isSeckill: foodItem.seckillActive == 1,
      seckillMaxCount: foodItem.seckillMaxOrderCount ?? 0,
      seckillPrice: foodItem.seckillPrice,
      reductionTags: foodItem.reductionFoodsTags,
      count: initialCount, // 使用计算得到的初始数量
      seckillId: foodItem.seckillId,
      seckillActive: foodItem.seckillActive,
      specialActive: foodItem.specialActive,
      multiDiscountId: foodItem.multiDiscountId,
      foodType: foodItem.foodType,
      comboFoodItems: foodItem.comboFoodItems,
      multiDiscountSteps: foodItem.multiDiscountSteps
          ?.map((final step) => step.copyWith())
          .toList(),
      multiDiscountAdvancePrice:
          foodItem.multiDiscountAdvancePrice ?? foodItem.price,
      // 规格相关字段 - 与微信小程序字段一致
      specUniqueId: foodItem.specUniqueId,
      specSelectedOptions: foodItem.specSelectedOptions,
      prefrentialCount: foodItem.prefrentialCount ?? 0,
      maxOrderCount: foodItem.maxOrderCount ?? 0,
    );

    // 处理多份打折逻辑
    final finalItem = foodItem.multiDiscountId != null &&
            foodItem.multiDiscountSteps != null &&
            foodItem.multiDiscountSteps!.isNotEmpty
        ? _updateMultiDiscountSteps(newItem, initialCount)
        : newItem;

    state = [...state, finalItem];
  }

  /// 更新多份打折步骤和价格
  SelectFoodItem _updateMultiDiscountSteps(
    final SelectFoodItem cartItem,
    final int currentCount,
  ) {
    if (cartItem.multiDiscountSteps == null ||
        cartItem.multiDiscountSteps!.isEmpty) {
      return cartItem;
    }

    // 复制步骤列表并更新选中状态
    final updatedSteps = <MultiDiscountStep>[];

    for (int i = 0; i < cartItem.multiDiscountSteps!.length; i++) {
      final originalStep = cartItem.multiDiscountSteps![i];
      // 按照微信小程序逻辑：当前数量大于该步骤数量时，该步骤为已完成状态
      final bool shouldBeChecked = currentCount > (originalStep.number ?? 0);

      // 创建新的步骤实例，更新选中状态
      final updatedStep = originalStep.copyWith(checked: shouldBeChecked);
      updatedSteps.add(updatedStep);
    }

    // 找到当前应该显示的价格（下一个步骤的价格）
    num? newAdvancePrice;

    if (currentCount == 0) {
      // 如果数量为0，显示原价
      newAdvancePrice = cartItem.foodsPrice;
    } else {
      // 找到下一个步骤（当前数量+1对应的步骤）
      final nextStepNumber = currentCount + 1;
      final nextStep = updatedSteps
          .where((final step) => (step.number ?? 0) == nextStepNumber)
          .firstOrNull;

      if (nextStep != null) {
        // 如果找到下一个步骤，使用该步骤的价格
        newAdvancePrice = nextStep.price;
      } else {
        // 如果没有下一个步骤，找到当前数量对应的步骤
        final currentStep = updatedSteps
            .where((final step) => (step.number ?? 0) == currentCount)
            .firstOrNull;

        if (currentStep != null) {
          // 使用当前步骤的价格
          newAdvancePrice = currentStep.price;
        } else {
          // 如果都没找到，使用原价
          newAdvancePrice = cartItem.foodsPrice;
        }
      }
    }

    return SelectFoodItem(
      id: cartItem.id,
      foodsName: cartItem.foodsName,
      foodsImage: cartItem.foodsImage,
      foodsPrice: cartItem.foodsPrice,
      prefrentialPrice: cartItem.prefrentialPrice,
      oldPrice: cartItem.oldPrice,
      lunchBoxFee: cartItem.lunchBoxFee,
      lunchBoxAccommodate: cartItem.lunchBoxAccommodate,
      percent: cartItem.percent,
      isSeckill: cartItem.isSeckill,
      seckillMaxCount: cartItem.seckillMaxCount,
      seckillPrice: cartItem.seckillPrice,
      seckillId: cartItem.seckillId,
      reductionTags: cartItem.reductionTags,
      count: currentCount,
      seckillActive: cartItem.seckillActive,
      specialActive: cartItem.specialActive,
      multiDiscountId: cartItem.multiDiscountId,
      multiDiscountSteps: updatedSteps,
      multiDiscountAdvancePrice: newAdvancePrice,
      // 规格相关字段
      specUniqueId: cartItem.specUniqueId,
      specSelectedOptions: cartItem.specSelectedOptions,
      prefrentialCount: cartItem.prefrentialCount,
      foodType: cartItem.foodType,
      comboFoodItems: cartItem.comboFoodItems,
      maxOrderCount: cartItem.maxOrderCount,
    );
  }

  /// 更新购物车中美食数据 - 适配规格商品
  void updateFoodsData({
    required final List<Food> foodItems,
    final SpecDataResponse? specData,
  }) {
    final updatedList = [...state];

    // 创建规格数据的快速查找映射
    final Map<int, FoodWithSpec> specMap = {};
    if (specData?.data != null) {
      for (final specItem in specData!.data!) {
        if (specItem.foodId != null) {
          specMap[specItem.foodId!] = specItem;
        }
      }
    }

    // 创建Food数据的快速查找映射
    final Map<int, Food> foodMap = {};
    for (final foodItem in foodItems) {
      if (foodItem.id != null) {
        foodMap[foodItem.id!] = foodItem;
      }
    }

    // 遍历购物车中的商品，根据购物车中的信息来匹配更新
    for (int i = 0; i < updatedList.length; i++) {
      final cartItem = updatedList[i];
      final foodItem = foodMap[cartItem.id];

      if (foodItem == null) {
        // 如果在新的食物列表中找不到该商品，跳过（保持原样）
        continue;
      }

      // 保留原有的数量、满减标签和规格信息
      final originalCount = cartItem.count ?? 0;
      final originalReductionTags = cartItem.reductionTags;
      final originalSpecUniqueId = cartItem.specUniqueId;
      final originalSpecSelectedOptions = cartItem.specSelectedOptions;
      final originalPrefrentialCount = cartItem.prefrentialCount;

      // 创建更新后的商品项
      var updatedItem = SelectFoodItem(
        id: foodItem.id,
        foodsName: foodItem.name,
        foodsImage: foodItem.image,
        foodsPrice: foodItem.price,
        oldPrice: foodItem.originPrice,
        prefrentialPrice: foodItem.prefrentialPrice,
        lunchBoxFee: foodItem.lunchBoxFee,
        lunchBoxAccommodate: foodItem.lunchBoxAccommodate?.toInt(),
        percent: foodItem.percent,
        isSeckill: foodItem.seckillActive == 1,
        seckillMaxCount: foodItem.seckillMaxOrderCount,
        seckillPrice: foodItem.seckillPrice,
        reductionTags: originalReductionTags, // 保留原有的reductionTags
        count: originalCount, // 保留原有数量
        seckillId: foodItem.seckillId,
        seckillActive: foodItem.seckillActive,
        specialActive: foodItem.specialActive,
        multiDiscountId: foodItem.multiDiscountId,
        foodType: foodItem.foodType,
        comboFoodItems: foodItem.comboFoodItems,
        multiDiscountSteps: foodItem.multiDiscountSteps
            ?.map((final step) => step.copyWith())
            .toList(),
        multiDiscountAdvancePrice:
            foodItem.multiDiscountAdvancePrice ?? foodItem.price,
        // 规格相关字段 - 保留原有的规格信息
        specUniqueId: originalSpecUniqueId,
        specSelectedOptions: originalSpecSelectedOptions,
        prefrentialCount: originalPrefrentialCount,
        maxOrderCount: foodItem.maxOrderCount ?? 0,
      );

      // 如果该商品有规格数据且购物车中有规格信息，根据规格数据更新价格和活动信息
      final specItem = specMap[foodItem.id];
      if (specItem != null &&
          originalSpecUniqueId != null &&
          originalSpecUniqueId.isNotEmpty &&
          originalSpecSelectedOptions != null &&
          originalSpecSelectedOptions.isNotEmpty) {
        updatedItem = _updateItemWithSpecData(updatedItem, specItem);
      }

      // 处理多份打折逻辑 - 如果有多重折扣且数量大于0，需要更新折扣状态
      if (foodItem.multiDiscountId != null &&
          foodItem.multiDiscountSteps != null &&
          foodItem.multiDiscountSteps!.isNotEmpty &&
          originalCount > 0) {
        updatedItem = _updateMultiDiscountSteps(updatedItem, originalCount);
      }

      updatedList[i] = updatedItem;
    }

    state = updatedList;
  }

  /// 获取商品数量的方法 - 支持规格商品
  int getFoodsCount({required final Food foodItem}) {
    int foodCount = 0;
    int idx = -1;

    if (foodItem.specUniqueId != null && foodItem.specUniqueId!.isNotEmpty) {
      // 规格商品：同时匹配id和specUniqueId
      idx = state.indexWhere(
        (final element) =>
            element.id == foodItem.id &&
            element.specUniqueId == foodItem.specUniqueId,
      );
    } else {
      // 普通商品：只匹配id，且确保不是规格商品
      idx = state.indexWhere(
        (final element) =>
            element.id == foodItem.id &&
            (element.specUniqueId == null || element.specUniqueId!.isEmpty),
      );
    }

    if (idx != -1) {
      foodCount = state[idx].count ?? 0;
    }
    return foodCount;
  }

  // 增加购物车的美食数量 - 支持规格商品和最小购买数量
  void addFoodCount({
    required final int foodId,
    Food? foodItem,
    final String? specUniqueId,
  }) {
    int idx = -1;
    foodItem ??= getFoodItem(foodId: foodId);

    // 优先使用传入的specUniqueId，其次使用foodItem中的specUniqueId
    final targetSpecUniqueId = specUniqueId ?? foodItem?.specUniqueId;

    if (targetSpecUniqueId != null && targetSpecUniqueId.isNotEmpty) {
      // 规格商品：同时匹配id和specUniqueId
      idx = state.indexWhere(
        (final element) =>
            element.id == foodId && element.specUniqueId == targetSpecUniqueId,
      );
    } else {
      // 普通商品：只匹配id，且确保不是规格商品
      idx = state.indexWhere(
        (final element) =>
            element.id == foodId &&
            (element.specUniqueId == null || element.specUniqueId!.isEmpty),
      );
    }

    if (idx != -1) {
      final updatedList = [...state];
      final originalItem = updatedList[idx];
      final currentCount = originalItem.count ?? 0;

      // 确定增加的数量 - 考虑最小购买数量
      final int incrementCount;
      if (currentCount == 0 &&
          foodItem != null &&
          foodItem.minCount != null &&
          foodItem.minCount! > 1) {
        // 如果当前数量为0且有最小购买数量限制，则增加到最小购买数量
        incrementCount = foodItem.minCount!;
      } else {
        // 否则正常增加1
        incrementCount = 1;
      }

      final newCount = currentCount + incrementCount;

      // 更新数量
      updatedList[idx].count = newCount;

      // 如果传入了foodItem，需要更新促销信息和检查限购（普通商品的秒杀和优惠限购）
      if (foodItem != null && targetSpecUniqueId == null) {
        // 普通商品：检查限购警告
        _checkLimitPurchaseWarningsForNormalFood(foodItem, newCount);

        // 普通商品：更新秒杀和优惠信息
        updatedList[idx] = _updatePromotionInfoForNormalFood(
          originalItem.copyWith(count: newCount),
          foodItem,
        );
      }

      // 处理多份打折逻辑
      if (updatedList[idx].multiDiscountId != null &&
          updatedList[idx].multiDiscountSteps != null &&
          updatedList[idx].multiDiscountSteps!.isNotEmpty) {
        updatedList[idx] = _updateMultiDiscountSteps(
          updatedList[idx],
          updatedList[idx].count!,
        );
      }

      state = updatedList;
    }
  }

  /// 减少购物车的美食数量 - 支持规格商品和最小购买数量
  void removeFoodCount({
    required final int foodId,
    Food? foodItem,
    final String? specUniqueId,
  }) {
    int idx = -1;

    foodItem ??= getFoodItem(foodId: foodId);

    // 优先使用传入的specUniqueId，其次使用foodItem中的specUniqueId
    final targetSpecUniqueId = specUniqueId ?? foodItem?.specUniqueId;

    if (targetSpecUniqueId != null && targetSpecUniqueId.isNotEmpty) {
      // 规格商品：同时匹配id和specUniqueId
      idx = state.indexWhere(
        (final element) =>
            element.id == foodId && element.specUniqueId == targetSpecUniqueId,
      );
    } else {
      // 普通商品：只匹配id，且确保不是规格商品
      idx = state.indexWhere(
        (final element) =>
            element.id == foodId &&
            (element.specUniqueId == null || element.specUniqueId!.isEmpty),
      );
    }

    if (idx != -1 && (state[idx].count ?? 0) > 0) {
      final updatedList = [...state];
      final originalItem = updatedList[idx];
      final currentCount = originalItem.count ?? 0;

      // 确定减少的数量 - 考虑最小购买数量
      final int decrementCount;
      final minCount = foodItem?.minCount ?? 1;

      if (currentCount <= minCount) {
        // 如果当前数量等于或小于最小购买数量，则直接减为0
        decrementCount = currentCount;
      } else {
        // 否则正常减少1
        decrementCount = 1;
      }

      final newCount = currentCount - decrementCount;

      // 更新数量
      updatedList[idx].count = newCount;

      // 如果是普通商品且数量大于0，需要更新促销信息（秒杀和优惠限购）
      if (foodItem != null &&
          (targetSpecUniqueId == null || targetSpecUniqueId.isEmpty) &&
          newCount > 0) {
        // 普通商品：更新秒杀和优惠信息
        updatedList[idx] = _updatePromotionInfoForNormalFood(
          originalItem.copyWith(count: newCount),
          foodItem,
        );
      }

      // 处理多份打折逻辑
      if (foodItem != null &&
          foodItem.multiDiscountId != null &&
          foodItem.multiDiscountSteps != null &&
          foodItem.multiDiscountSteps!.isNotEmpty &&
          newCount > 0) {
        updatedList[idx] = _updateMultiDiscountSteps(
          updatedList[idx],
          newCount,
        );
      }

      if (newCount == 0) {
        updatedList.removeAt(idx);
      }

      state = updatedList;
    }
  }

  /// 获取foodItem
  Food? getFoodItem({required final int foodId}) {
    final foodsData = ref.read(restaurantDetailControllerProvider).foodsData;

    return foodsData?.foods?.firstWhereOrNull(
      (final element) => element.id == foodId,
    );
  }

  // 清空购物车
  void clearCardFoods() {
    state = [];
  }

  // 获取购物车中商品的总数量
  int getTotalCount() {
    int count = 0;
    for (var item in state) {
      count += item.count ?? 0;
    }
    return count;
  }

  // 计算购物车总计
  CartCalculationResult calculateCart({
    final FoodsData? foodsData,
    final int deliveryMode = OrderCalculationConstants.deliveryMode,
    final num couponAmount = 0,
  }) {
    // 使用新的计算工具类
    return CalculationUtils.calculateCart(
      state,
      foodsData: foodsData,
      deliveryMode: deliveryMode,
      couponAmount: couponAmount,
    );
  }

  /// 获取满减优惠金额
  String getReductionDiscount({
    final FoodsData? foodsData,
    final int deliveryMode = OrderCalculationConstants.deliveryMode,
  }) {
    print("开始获取满减优惠金额 getReductionDiscount");
    print("配送模式: $deliveryMode");
    print("是否有满减数据: ${foodsData?.market?.steps?.reductionStep != null}");
    print("满减阶梯数量: ${foodsData?.market?.steps?.reductionStep?.length ?? 0}");

    final result =
        calculateCartInfo(foodsData: foodsData, deliveryMode: deliveryMode);
    final discount = result.reductionDiscount;
    print("计算得到的满减优惠金额: $discount");

    return FormatUtil.formatPrice(discount);
  }

  /// 计算购物车信息
  CartCalculationResult calculateCartInfo({
    final FoodsData? foodsData,
    final int deliveryMode = OrderCalculationConstants.deliveryMode,
    final num couponAmount = 0,
  }) {
    print("开始计算购物车信息 calculateCartInfo");
    print("购物车商品数量: ${state.length}");

    final result = CalculationUtils.calculateCart(
      state,
      foodsData: foodsData,
      deliveryMode: deliveryMode,
      couponAmount: couponAmount,
    );

    print(
      "计算结果: subtotal=${result.subtotal}, reductionDiscount=${result.reductionDiscount}, couponDiscount=${result.couponDiscount}",
    );
    return result;
  }

  /// 获取购物车总价（包含配送费、优惠等）
  String getTotalPrice({
    final FoodsData? foodsData,
    final int deliveryMode = OrderCalculationConstants.deliveryMode,
    final num couponAmount = 0,
  }) {
    final result = calculateCartInfo(
      foodsData: foodsData,
      deliveryMode: deliveryMode,
      couponAmount: couponAmount,
    );
    return FormatUtil.formatPrice(result.totalPrice);
  }

  /// 获取配送费
  String getShipmentFee({
    final FoodsData? foodsData,
    final int deliveryMode = OrderCalculationConstants.deliveryMode,
  }) {
    final result =
        calculateCartInfo(foodsData: foodsData, deliveryMode: deliveryMode);
    return FormatUtil.formatPrice(result.shipmentFee);
  }

  /// 获取商品小计（不含配送费和优惠）
  String getSubtotal({
    final FoodsData? foodsData,
    final int deliveryMode = OrderCalculationConstants.deliveryMode,
  }) {
    final result =
        calculateCartInfo(foodsData: foodsData, deliveryMode: deliveryMode);
    return FormatUtil.formatPrice(result.subtotal);
  }

  /// 获取餐盒费
  String getLunchBoxFee({
    final FoodsData? foodsData,
    final int deliveryMode = OrderCalculationConstants.deliveryMode,
  }) {
    final result =
        calculateCartInfo(foodsData: foodsData, deliveryMode: deliveryMode);
    return FormatUtil.formatPrice(result.lunchBoxFee);
  }

  /// 获取配送费优惠金额
  String getShipmentDiscount({
    final FoodsData? foodsData,
    final int deliveryMode = OrderCalculationConstants.deliveryMode,
  }) {
    final result =
        calculateCartInfo(foodsData: foodsData, deliveryMode: deliveryMode);
    return FormatUtil.formatPrice(result.shipmentDiscount);
  }

  /// 获取是否可以使用满减
  bool canApplyReduction({
    final FoodsData? foodsData,
    final int deliveryMode = OrderCalculationConstants.deliveryMode,
  }) {
    final result =
        calculateCartInfo(foodsData: foodsData, deliveryMode: deliveryMode);
    return result.canApplyReduction;
  }

  /// 获取距离满减还差多少金额
  String getMissingAmount({
    final FoodsData? foodsData,
    final int deliveryMode = OrderCalculationConstants.deliveryMode,
  }) {
    final result =
        calculateCartInfo(foodsData: foodsData, deliveryMode: deliveryMode);
    return FormatUtil.formatPrice(result.missingAmount);
  }

  /// 获取实际应付配送费（考虑减免）
  String getActualShipmentFee({
    final FoodsData? foodsData,
    required final int deliveryMode,
  }) {
    try {
      if (foodsData == null) return "0.00";
      final result = CalculationUtils.calculateCart(
        state,
        foodsData: foodsData,
        deliveryMode: deliveryMode,
      );

      // 使用 DecimalUtils 进行精确的减法和舍入
      final double shipmentFee = DecimalUtils.round(result.shipmentFee);
      final double shipmentDiscount =
          DecimalUtils.round(result.shipmentDiscount);
      // 使用 DecimalUtils 执行减法以实现精度
      final double actualFee =
          DecimalUtils.subtract(shipmentFee, shipmentDiscount);

      // 确保非负费用和显示格式
      return FormatUtil.formatPrice(actualFee > 0 ? actualFee : 0.0);
    } catch (e) {
      print("获取实际应付配送费出错: $e");
      return "0.00";
    }
  }

  /// 获取满减相关数值 - 适配美食满减
  Map<String, dynamic>? getReductionValues() {
    try {
      final foodsData = ref.read(restaurantDetailControllerProvider).foodsData;
      if (foodsData == null) {
        return null;
      }

      // 检查是否有满减阶梯数据
      final reductionStepList = foodsData.market?.steps?.reductionStep;
      if (reductionStepList == null || reductionStepList.isEmpty) {
        return null;
      }

      // 判断是否为美食满减 - 根据微信小程序逻辑
      final reductionFoodsId = foodsData.market?.reductionFoodsId ?? [];
      final isFoodsReduction = reductionFoodsId.isNotEmpty;

      // 计算参与满减的金额
      // 如果是美食满减，则计算购物车中有满减标签的商品总价
      // 如果是餐厅满减，则使用所有商品的总价
      double participateAmount = 0;
      if (isFoodsReduction) {
        // 美食满减：只计算有满减标签的商品
        for (final item in state) {
          if (item.reductionTags != null && item.reductionTags!.isNotEmpty) {
            final count = item.count ?? 0;
            if (count > 0) {
              participateAmount += (item.foodsPrice ?? 0) * count;
            }
          }
        }
      } else {
        // 餐厅满减：使用所有商品的总价
        final result = CalculationUtils.calculateCart(
          state,
          foodsData: foodsData,
          deliveryMode: 0,
        );
        participateAmount = DecimalUtils.round(result.subtotal);
      }

      // 使用CalculationUtils重新计算满减阶梯 - 基于我们的participateAmount
      final reductionResult = CalculationUtils.calculateReductionSteps(
        participateAmount,
        reductionStepList,
      );

      final double reductionDiscount =
          DecimalUtils.round(reductionResult['reduceFee'] as num);
      final bool canApplyReduction = reductionResult['isSteps'] as bool;
      final double missingAmount =
          DecimalUtils.round(reductionResult['surplus'] as num);
      final Map<String, dynamic>? currentStep =
          reductionResult['currentStep'] as Map<String, dynamic>?;
      final Map<String, dynamic>? nextStep =
          reductionResult['nextStep'] as Map<String, dynamic>?;

      // 检查是否已经达到最大优惠阶梯
      bool allTiersCompleted = false;
      if (reductionStepList.isNotEmpty) {
        // 找出最大价格的阶梯
        ReductionStep maxTier = reductionStepList[0];
        for (var step in reductionStepList) {
          if (DecimalUtils.round(step.price ?? 0) >
              DecimalUtils.round(maxTier.price ?? 0)) {
            maxTier = step;
          }
        }
        // 如果当前总价已达到或超过最大阶梯价格，且没有下一个阶梯，则所有阶梯已完成
        if (participateAmount >= DecimalUtils.round(maxTier.price ?? 0) &&
            nextStep == null) {
          allTiersCompleted = true;
        }
      }

      // 如果已经满足了当前阶梯（有优惠金额）
      if (canApplyReduction && reductionDiscount > 0) {
        // 如果有下一个阶梯，返回当前减免金额和下一个阶梯的信息
        if (nextStep != null) {
          final double nextStepPrice =
              DecimalUtils.round(nextStep['price'] as num? ?? 0);
          final double nextStepReduce =
              DecimalUtils.round(nextStep['reduce'] as num? ?? 0);
          final double remainingAmount =
              DecimalUtils.subtract(nextStepPrice, participateAmount);

          if (remainingAmount > 0) {
            return {
              'currentDiscount': reductionDiscount,
              'remainingAmount': remainingAmount,
              'nextDiscount': nextStepReduce,
              'isCurrentTierMet': true,
              'hasNextTier': true,
              'allTiersCompleted': allTiersCompleted,
              'isFoodsReduction': isFoodsReduction,
              'participateAmount': participateAmount,
            };
          }
        }
        // 如果没有下一个阶梯，只返回当前减免金额
        return {
          'currentDiscount': reductionDiscount,
          'isCurrentTierMet': true,
          'hasNextTier': false,
          'allTiersCompleted': allTiersCompleted,
          'isFoodsReduction': isFoodsReduction,
          'participateAmount': participateAmount,
        };
      } else {
        // 如果尚未满足当前阶梯，返回还差多少金额
        if (currentStep != null) {
          final double currentStepPrice =
              DecimalUtils.round(currentStep['price'] as num? ?? 0);
          final double currentStepReduce =
              DecimalUtils.round(currentStep['reduce'] as num? ?? 0);

          if (missingAmount > 0) {
            return {
              'remainingAmount': missingAmount,
              'currentDiscount': currentStepReduce,
              'isCurrentTierMet': false,
              'allTiersCompleted': allTiersCompleted,
              'isFoodsReduction': isFoodsReduction,
              'participateAmount': participateAmount,
            };
          }
        }

        // 如果没有满足条件的阶梯，返回第一个阶梯信息
        if (reductionStepList.isNotEmpty) {
          final firstStep = reductionStepList[0];
          final double firstStepPrice =
              DecimalUtils.round(firstStep.price ?? 0);
          final double firstStepReduce =
              DecimalUtils.round(firstStep.reduce ?? 0);
          final double remainingToFirst =
              DecimalUtils.subtract(firstStepPrice, participateAmount);

          return {
            'currentTierPrice': firstStepPrice,
            'currentTierDiscount': firstStepReduce,
            'remainingAmount': remainingToFirst > 0 ? remainingToFirst : 0,
            'isCurrentTierMet': false,
            'allTiersCompleted': allTiersCompleted,
            'isFoodsReduction': isFoodsReduction,
            'participateAmount': participateAmount,
          };
        }
      }

      return null;
    } catch (e) {
      print("获取满减相关数值出错: $e");
      return null;
    }
  }

  /// 获取配送费减免相关数值 - 参考微信小程序calculateShipmentSteps逻辑
  Map<String, dynamic>? getShipmentValues() {
    try {
      final foodsData = ref.read(restaurantDetailControllerProvider).foodsData;
      if (foodsData == null) return null;

      // 检查是否有配送费减免数据
      final shipmentStep = foodsData.market?.steps?.shipmentStep;
      if (shipmentStep == null ||
          DecimalUtils.round(shipmentStep.shipmentReduce ?? 0) <= 0) {
        return null;
      }

      // 计算购物车信息
      final result = CalculationUtils.calculateCart(
        state,
        foodsData: foodsData,
        deliveryMode: 0,
      );

      final double subtotal = DecimalUtils.round(result.subtotal);
      final double minDeliveryPrice =
          DecimalUtils.round(shipmentStep.minDeliveryPrice ?? 0);
      final double shipmentReduce =
          DecimalUtils.round(shipmentStep.shipmentReduce ?? 0);

      // 参考微信小程序逻辑：
      // surplus = Math.max(min_delivery_price - price, 0);
      // if (price >= min_delivery_price) { isSteps = true; }
      final double surplus = subtotal >= minDeliveryPrice
          ? 0
          : DecimalUtils.subtract(minDeliveryPrice, subtotal);
      final bool isCurrentTierMet = subtotal >= minDeliveryPrice;

      if (isCurrentTierMet) {
        // 已满足条件：返回减免后的配送费
        final double shipmentFee = DecimalUtils.round(result.shipmentFee);
        final double actualFee =
            DecimalUtils.subtract(shipmentFee, shipmentReduce);
        return {
          'actualFee': actualFee > 0 ? actualFee : 0.0,
          'isCurrentTierMet': true,
          'isShipment': true,
        };
      } else {
        // 未满足条件：返回还需要多少金额
        return {
          'remainingAmount': surplus,
          'discount': shipmentReduce,
          'currentTierPrice': minDeliveryPrice,
          'currentTierDiscount': shipmentReduce,
          'isCurrentTierMet': false,
          'isShipment': true,
        };
      }
    } catch (e) {
      print("获取配送费减免相关数值出错: $e");
      return null;
    }
  }

  /// 获取下一个满减阶梯提示文本
  String? getNextReductionStepText() {
    try {
      final foodsData = ref.read(restaurantDetailControllerProvider).foodsData;
      if (foodsData == null) {
        return null;
      }
      final result = CalculationUtils.calculateCart(
        state,
        foodsData: foodsData,
        deliveryMode: 0,
      );
      return result.reductionNextStepText;
    } catch (e) {
      print("获取下一个满减阶梯提示文本出错: $e");
      return null;
    }
  }

  /// 获取下一个配送费减免阶梯提示文本
  String? getNextShipmentStepText() {
    try {
      final foodsData = ref.read(restaurantDetailControllerProvider).foodsData;
      if (foodsData == null) {
        return null;
      }
      final result = CalculationUtils.calculateCart(
        state,
        foodsData: foodsData,
        deliveryMode: 0,
      );
      return result.shipmentNextStepText;
    } catch (e) {
      print("获取下一个配送费减免阶梯提示文本出错: $e");
      return null;
    }
  }

  /// 判断配送费是否完全减免
  bool isShipmentFullyReduced() {
    try {
      final foodsData = ref.read(restaurantDetailControllerProvider).foodsData;
      if (foodsData == null) return false;

      final result = CalculationUtils.calculateCart(
        state,
        foodsData: foodsData,
        deliveryMode: 0,
      );
      // 检查是否有配送费减免数据
      final shipmentStep = foodsData.market?.steps?.shipmentStep;
      if (shipmentStep == null ||
          DecimalUtils.round(shipmentStep.shipmentReduce ?? 0) <= 0) {
        return false;
      }

      final double shipmentDiscount =
          DecimalUtils.round(result.shipmentDiscount);
      final double shipmentFee = DecimalUtils.round(result.shipmentFee);

      // 如果配送费减免金额等于或超过配送费，则表示完全减免
      if (shipmentDiscount > 0 && shipmentDiscount >= shipmentFee) {
        return true;
      }

      // 如果已经满足了当前阶梯，并且没有下一个阶梯，也表示完全减免
      if (result.shipmentCurrentStep != null) {
        // 检查是否已经满足了当前阶梯的条件
        final double currentStepPrice = DecimalUtils.round(
          result.shipmentCurrentStep!['price'] as num? ?? 0,
        );
        final double subtotal = DecimalUtils.round(result.subtotal);

        if (currentStepPrice > 0 && subtotal >= currentStepPrice) {
          // Ensure currentStepPrice is valid
          // 检查是否有下一个阶梯
          if (result.shipmentNextStep == null) {
            return true;
          }
        }
      }
      return false;
    } catch (e) {
      print("判断配送费是否完全减免出错: $e");
      return false;
    }
  }

  /// 判断是否有满减活动
  bool hasReductionActivity() {
    try {
      final foodsData = ref.read(restaurantDetailControllerProvider).foodsData;
      if (foodsData == null) {
        return false;
      }

      // 检查是否有满减阶梯数据
      final reductionStepList = foodsData.market?.steps?.reductionStep;
      if (reductionStepList == null || reductionStepList.isEmpty) {
        return false;
      }

      // 检查是否已经获得了满减优惠
      final result = CalculationUtils.calculateCart(
        state,
        foodsData: foodsData,
        deliveryMode: 0,
      );
      if (DecimalUtils.round(result.reductionDiscount) > 0) {
        // 如果已经获得满减优惠，即使是最高阶梯也应该显示
        return true;
      }

      // 检查是否还有未达到的阶梯
      if (state.isNotEmpty) {
        return true; // 只要购物车中有商品且有满减阶梯，就认为有满减活动
      }

      return reductionStepList.isNotEmpty;
    } catch (e) {
      print("判断是否有满减活动出错: $e");
      return false;
    }
  }

  /// 判断是否有配送费减免活动
  bool hasShipmentActivity() {
    try {
      final foodsData = ref.read(restaurantDetailControllerProvider).foodsData;
      if (foodsData == null) {
        return false;
      }

      // 检查是否有配送费减免规则
      final shipmentStep = foodsData.market?.steps?.shipmentStep;

      // 如果有配送费减免规则，且有效，则返回true
      if (shipmentStep != null &&
          shipmentStep.minDeliveryPrice != null &&
          shipmentStep.shipmentReduce != null &&
          DecimalUtils.round(shipmentStep.shipmentReduce!) > 0) {
        return true;
      }

      return false;
    } catch (e) {
      print("判断是否有配送费减免活动出错: $e");
      return false;
    }
  }

  /// 获取配送费优惠提示相关数据
  /// 根据微信小程序逻辑计算profitStatus和profitFee
  /// 只有在没有运费减免和抽奖活动时才显示配送费提示
  Map<String, dynamic>? getProfitValues() {
    try {
      final foodsData = ref.read(restaurantDetailControllerProvider).foodsData;
      if (foodsData == null || state.isEmpty) {
        return null;
      }

      // 检查是否有运费减免或抽奖活动
      final hasShipmentReduction =
          foodsData.market?.hasShipmentReduction ?? false;
      final isLottery = (foodsData.lotteryActive ?? 0) > 0;

      // 只有在没有运费减免和抽奖活动时才显示配送费提示
      if (hasShipmentReduction || isLottery) {
        return null;
      }

      // 获取配送相关信息
      final distribution = foodsData.distribution;
      if (distribution == null) {
        return null;
      }

      final calculation = CalculationUtils.calculateCart(
        state,
        foodsData: foodsData,
        deliveryMode: 0,
      );

      final allProfit =
          (distribution.shipment ?? 0) - (distribution.param ?? 0);
      if (allProfit <= 0 || calculation.subtotal <= 0) {
        return null;
      }

      // 计算profitFee（按微信小程序逻辑）
      double profitFee =
          ((allProfit / calculation.subtotal).ceil() - calculation.subtotal)
              .toDouble();
      profitFee = (profitFee * 100).round() / 100;

      final distributionParam = (distribution.param ?? 0).toDouble();

      // 如果配送费已经等于最低配送费，则不显示提示
      if (calculation.shipmentFee == distributionParam) {
        return null;
      }

      return {
        'profitStatus': calculation.subtotal > 0,
        'profitFee': profitFee,
        'distributionParam': distributionParam,
      };
    } catch (e) {
      print("获取配送费优惠提示相关数据出错: $e");
      return null;
    }
  }

  /// 为普通商品更新促销信息（秒杀和优惠限购）
  SelectFoodItem _updatePromotionInfoForNormalFood(
    final SelectFoodItem cartItem,
    final Food foodItem,
  ) {
    var updatedItem = cartItem;
    final currentCount = cartItem.count ?? 0;

    // 计算各个价格阶段的数量 - 按照微信小程序calculateFoodFee逻辑
    final seckillMaxOrderCount = foodItem.seckillMaxOrderCount ?? 0;
    final maxOrderCount = foodItem.maxOrderCount ?? 0;

    // 秒杀数量：如果有秒杀活动且活跃，计算秒杀部分数量
    final curSecCount =
        (foodItem.seckillId != null && foodItem.seckillActive == 1)
            ? (currentCount <= seckillMaxOrderCount
                ? currentCount
                : seckillMaxOrderCount)
            : 0;

    // 优惠数量：剩余数量中享受优惠的部分
    final remainingAfterSeckill = currentCount - curSecCount;
    final curPreCount =
        (foodItem.prefrentialPrice != null && remainingAfterSeckill > 0)
            ? (remainingAfterSeckill <= maxOrderCount
                ? remainingAfterSeckill
                : maxOrderCount)
            : 0;

    // 原价数量：超出秒杀和优惠限制的部分
    final oldCount = currentCount - curSecCount - curPreCount;

    // 更新秒杀信息
    if (foodItem.seckillId != null &&
        foodItem.seckillId! > 0 &&
        foodItem.seckillActive == 1) {
      updatedItem = updatedItem.copyWith(
        isSeckill: true,
        seckillId: foodItem.seckillId,
        seckillActive: foodItem.seckillActive,
        seckillPrice: foodItem.seckillPrice,
        seckillMaxCount: seckillMaxOrderCount,
      );
    } else {
      updatedItem = updatedItem.copyWith(
        isSeckill: false,
        seckillId: null,
        seckillActive: null,
        seckillPrice: null,
        seckillMaxCount: 0,
      );
    }

    // 更新优惠信息
    if (foodItem.prefrentialPrice != null && foodItem.prefrentialPrice! > 0) {
      updatedItem = updatedItem.copyWith(
        prefrentialPrice: foodItem.prefrentialPrice,
        prefrentialCount: curPreCount, // 使用实际享受优惠的数量
      );
    } else {
      updatedItem = updatedItem.copyWith(
        prefrentialPrice: null,
        prefrentialCount: 0,
      );
    }

    // 更新其他基本信息
    updatedItem = updatedItem.copyWith(
      foodsPrice: foodItem.price,
      oldPrice: foodItem.oldPrice ?? foodItem.originPrice,
      percent: foodItem.percent,
      specialActive: foodItem.specialActive,
      maxOrderCount: maxOrderCount, // 更新优惠限购数量
    );

    debugPrint(
        '普通商品价格计算: 总数量=$currentCount, 秒杀数量=$curSecCount, 优惠数量=$curPreCount, 原价数量=$oldCount');

    return updatedItem;
  }

  /// 检查普通商品的限购警告并显示提示 - 与微信小程序逻辑一致
  void _checkLimitPurchaseWarningsForNormalFood(
      final Food foodItem, final int count) {
    final maxOrderCount = foodItem.maxOrderCount ?? 0;
    final seckillMaxOrderCount = foodItem.seckillMaxOrderCount ?? 0;

    // 秒杀限购次数超购判断 - 与微信小程序逻辑一致
    if (foodItem.seckillId != null && foodItem.seckillActive == 1) {
      // 如果有优惠价且购买数量大于秒杀限购次数
      if (foodItem.prefrentialPrice != null) {
        // 当前购买数量等于秒杀最大限购次数时，触发秒杀提示
        if (seckillMaxOrderCount > 0 && seckillMaxOrderCount + 1 == count) {
          final seckillStatus = {'count': seckillMaxOrderCount, 'type': 1};
          _showSeckillLimitToast(seckillStatus);
        }
        // 当前优惠价购买数量等于最大限购次数时，触发折扣限制提示
        else if (maxOrderCount > 0 &&
            maxOrderCount + seckillMaxOrderCount + 1 == count) {
          _showDiscountExceedLimitToast(maxOrderCount);
        }
      } else {
        // 当前购买数量等于秒杀最大限购次数时，触发秒杀提示
        if (seckillMaxOrderCount > 0 && seckillMaxOrderCount + 1 == count) {
          final seckillStatus = {'count': seckillMaxOrderCount, 'type': 0};
          _showSeckillLimitToast(seckillStatus);
        }
      }
    }

    // 优惠限购次数超购判断
    if ((foodItem.seckillActive == null || foodItem.seckillActive == 0) &&
        maxOrderCount > 0 &&
        maxOrderCount + 1 == count) {
      _showDiscountLimitToast(maxOrderCount);
    }
  }

  /// 显示秒杀限购提示 - 与微信小程序逻辑一致
  void _showSeckillLimitToast(final Map<String, dynamic> detail) {
    try {
      final message = _getSeckillLimitMessage(detail);
      // 使用Future.microtask确保在下一个事件循环中执行，避免同步调用问题
      Future.microtask(() {
        ToastWidget.showRich(message, duration: 3000);
      });
      debugPrint('秒杀限购提示: $message');
    } catch (e) {
      debugPrint('显示秒杀限购提示出错: $e');
    }
  }

  /// 显示优惠限购提示 - 与微信小程序逻辑一致
  void _showDiscountLimitToast(final int maxCount) {
    try {
      final message = S.current.seckill_limit_old_price
          .replaceAll('%s', maxCount.toString());
      Future.microtask(() {
        ToastWidget.showRich(message, duration: 3000);
      });
      debugPrint('优惠限购提示: $message');
    } catch (e) {
      debugPrint('显示优惠限购提示出错: $e');
    }
  }

  /// 显示优惠超限提示
  void _showDiscountExceedLimitToast(final int maxCount) {
    try {
      // 使用国际化文本
      final message = S.current.exceed_discount_num_tips
          .replaceAll('%s', maxCount.toString());
      Future.microtask(() {
        ToastWidget.showRich(message, duration: 3000);
      });
    } catch (e) {
      debugPrint('显示优惠超限提示出错: $e');
    }
  }

  /// 获取秒杀限购消息
  String _getSeckillLimitMessage(final Map<String, dynamic> detail) {
    final int count = detail['count'] ?? 0;
    final int type = detail['type'] ?? 0;

    if (type == 1) {
      // 秒杀限购，超过部分恢复优惠价
      return S.current.seckill_limit_promo_price
          .replaceAll('%s', count.toString());
    } else {
      // 秒杀限购，超过部分恢复原价
      return S.current.seckill_limit_old_price
          .replaceAll('%s', count.toString());
    }
  }

  /// 根据规格数据更新商品信息 - 复用spec_service中的统一计算逻辑
  SelectFoodItem _updateItemWithSpecData(
    final SelectFoodItem item,
    final FoodWithSpec specData,
  ) {
    // 获取spec_service实例
    final specService = ref.read(specServiceProvider);

    // 解析当前选中的规格选项ID
    final selectedOptionIds =
        _parseSpecOptionIds(item.specSelectedOptions ?? []);

    // 计算规格选项的总价格 - 使用spec_service的计算逻辑
    double specsPrice = 0;
    if (specData.specs != null) {
      for (final spec in specData.specs!) {
        if (spec.specOptions != null) {
          for (final option in spec.specOptions!) {
            if (selectedOptionIds.contains(option.id)) {
              specsPrice += option.price ?? 0;
            }
          }
        }
      }
    }

    // 使用spec_service的促销计算方法 - 确保与规格弹窗逻辑一致
    final promotionInfo = specService.calculatePromotionPrice(
      specData,
      selectedOptionIds,
    );

    // 使用spec_service的最终价格计算方法 - 确保与规格弹窗逻辑一致
    final finalPriceInfo = specService.calculateFinalPrice(
      item.count ?? 1,
      specsPrice,
      promotionInfo,
    );

    var updatedItem = item;

    // 处理秒杀活动 - 使用统一的促销信息
    if (promotionInfo.seckillInfo != null) {
      updatedItem = updatedItem.copyWith(
        isSeckill: true,
        seckillId: promotionInfo.seckillInfo!.seckillId,
        seckillActive: promotionInfo.seckillInfo!.seckillActive,
        seckillPrice: promotionInfo.seckillInfo!.price,
        seckillMaxCount: promotionInfo.seckillInfo!.userMaxOrderCount,
      );
    } else {
      // 清除秒杀信息
      updatedItem = updatedItem.copyWith(
        isSeckill: false,
        seckillId: null,
        seckillActive: null,
        seckillPrice: null,
        seckillMaxCount: 0,
      );
    }

    // 处理优惠活动 - 使用统一的促销信息
    if (promotionInfo.prefInfo != null) {
      updatedItem = updatedItem.copyWith(
        prefrentialPrice: promotionInfo.prefInfo!.discountPrice,
      );
    } else {
      // 清除优惠信息
      updatedItem = updatedItem.copyWith(
        prefrentialPrice: null,
      );
    }

    // 处理满减活动 - 使用统一的促销信息
    if (promotionInfo.marketInfo != null) {
      // 保留原有的满减标签
    } else {
      // 没有找到匹配的满减活动，移除满减标签
      updatedItem = updatedItem.copyWith(reductionTags: []);
    }

    // 使用spec_service计算的最终价格和旧价格
    updatedItem = updatedItem.copyWith(
      foodsPrice: finalPriceInfo.finalPrice,
      oldPrice: specsPrice, // 原价为规格组合价格
    );

    return updatedItem;
  }

  /// 解析规格选项ID列表 - 从spec_selected_options中提取spec_option_id
  List<int> _parseSpecOptionIds(
    final List<Map<String, dynamic>> specSelectedOptions,
  ) {
    return specSelectedOptions
        .map((final option) => option['spec_option_id'] as int?)
        .where((final id) => id != null)
        .cast<int>()
        .toList();
  }
}
