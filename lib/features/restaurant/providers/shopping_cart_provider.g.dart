// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shopping_cart_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$shoppingCartHash() => r'ee9a1f344577c4f3474f685743fecdd10199067e';

/// 购物车状态
///
/// Copied from [ShoppingCart].
@ProviderFor(ShoppingCart)
final shoppingCartProvider =
    AutoDisposeNotifierProvider<ShoppingCart, List<SelectFoodItem>>.internal(
  ShoppingCart.new,
  name: r'shoppingCartProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$shoppingCartHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ShoppingCart = AutoDisposeNotifier<List<SelectFoodItem>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
