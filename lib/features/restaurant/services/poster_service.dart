import 'dart:async';

import 'package:bot_toast/bot_toast.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/models/poster/poster_model.dart';
import 'package:user_app/data/repositories/poster/poster_repository.dart';
import 'package:user_app/generated/l10n.dart';

/// 海报服务提供者
final posterServiceProvider = Provider<PosterService>((final ref) {
  return PosterService(ref: ref);
});

/// 海报服务
class PosterService {
  final _dio = Dio();

  /// 构造函数
  PosterService({required this.ref});

  /// 依赖注入
  final Ref ref;

  /// 获取海报列表
  Future<List<PosterModel>> getPosterList(final int type) async {
    try {
      final repository = ref.read(posterRepositoryProvider);
      final result = await repository.getPosterList(type);

      if (result.success) {
        return result.data ?? [];
      } else {
        BotToast.showText(text: result.msg);
        return [];
      }
    } catch (e) {
      BotToast.showText(text: S.current.retry);
      return [];
    }
  }

  /// 生成海报
  Future<String?> generatePoster({
    required final String resId,
    required final int type,
    required final int modelId,
  }) async {
    try {
      final repository = ref.read(posterRepositoryProvider);
      final result = await repository.generatePoster(
        resId: resId,
        type: type,
        modelId: modelId,
      );

      if (result.success) {
        return result.data;
      } else {
        BotToast.showText(text: result.msg);
        return null;
      }
    } catch (e) {
      BotToast.showText(text: S.current.retry);
      return null;
    }
  }

  /// 查询海报状态
  Future<String?> queryPosterStatus(final String jobId) async {
    try {
      final repository = ref.read(posterRepositoryProvider);
      final result = await repository.queryPosterStatus(jobId);

      if (result.success) {
        return result.data;
      } else {
        // BotToast.showText(text: result.message ?? S.current.retry);
        return null;
      }
    } catch (e) {
      // BotToast.showText(text: S.current.retry);
      return null;
    }
  }

  /// 生成并下载海报（综合流程）
  Future<String?> generateAndQueryPoster({
    required final String resId,
    required final int type,
    required final int modelId,
    final VoidCallback? onStart,
    final VoidCallback? onComplete,
    final VoidCallback? onError,
  }) async {
    try {
      // 调用开始回调
      onStart?.call();

      // 生成海报
      final jobId = await generatePoster(
        resId: resId,
        type: type,
        modelId: modelId,
      );

      if (jobId == null) {
        onError?.call();
        return null;
      }

      // 查询海报状态（轮询）
      String? posterUrl;
      int retryCount = 0;
      const maxRetries = 10; // 最多尝试10次

      while (posterUrl == null && retryCount < maxRetries) {
        posterUrl = await queryPosterStatus(jobId);
        if (posterUrl == null) {
          retryCount++;
          await Future.delayed(const Duration(seconds: 1));
        }
      }

      if (posterUrl == null) {
        onError?.call();
        return null;
      }

      // 调用完成回调
      onComplete?.call();

      return posterUrl;
    } catch (e) {
      onError?.call();
      return null;
    }
  }
}
