import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/data/models/restaurant/spec_model.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/data/repositories/restaurant/spec_repository.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/spec_modal_state.dart';

/// 规格服务类
class SpecService {
  final SpecRepository _specRepository;

  /// g z
  SpecService({
    required final SpecRepository specRepository,
  }) : _specRepository = specRepository;

  /// 获取美食规格数据
  /// [foodId] 美食ID
  Future<FoodWithSpec?> getFoodSpecData(final int foodId) async {
    try {
      final result = await _specRepository.getFoodSpecData([foodId]);

      if (result.success && result.data != null && result.data!.data != null) {
        // 返回第一个美食的规格数据
        final foods = result.data!.data!;
        if (foods.isNotEmpty) {
          return foods.first;
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 计算选定规格的总价格
  /// [basePrice] 基础价格
  /// [specGroups] 规格组列表
  double calculateTotalPrice({
    required final double basePrice,
    required final List<SpecGroup> specGroups,
  }) {
    double totalSpecPrice = 0.0;

    for (final group in specGroups) {
      if (group.specOptions != null) {
        for (final option in group.specOptions!) {
          if (option.isSelected == true) {
            totalSpecPrice += option.price ?? 0.0;
          }
        }
      }
    }

    return basePrice + totalSpecPrice;
  }

  /// 获取已选规格的显示文本
  /// [specGroups] 规格组列表
  String getSelectedSpecsText(final List<SpecGroup> specGroups) {
    List<String> selectedTexts = [];

    for (final group in specGroups) {
      if (group.specOptions != null) {
        for (final option in group.specOptions!) {
          if (option.isSelected == true) {
            selectedTexts.add(option.name ?? '');
          }
        }
      }
    }

    return selectedTexts.join('，');
  }

  /// 检查规格组是否需要选择
  /// [group] 规格组
  bool isGroupRequired(final SpecGroup group) {
    if (group.required == true) {
      // 检查是否有已选中的选项
      if (group.specOptions != null) {
        return !group.specOptions!
            .any((final option) => option.isSelected == true);
      }
      return true;
    }
    return false;
  }

  /// 获取规格组的已选选项
  /// [group] 规格组
  List<SpecOption> getSelectedOptions(final SpecGroup group) {
    if (group.specOptions == null) return [];

    return group.specOptions!
        .where((final option) => option.isSelected == true)
        .toList();
  }

  /// 计算促销价格 - 通用方法，供规格弹窗和购物车使用
  /// 与微信小程序_calculatePromotionPrice逻辑一致
  ({SeckillInfo? seckillInfo, PrefInfo? prefInfo, MarketInfo? marketInfo})
      calculatePromotionPrice(
    final FoodWithSpec? specData,
    final List<int> selectedOptionIds,
  ) {
    if (specData == null) {
      return (seckillInfo: null, prefInfo: null, marketInfo: null);
    }

    // 改为使用null而不是undefined，与微信小程序保持一致
    SeckillInfo? seckillInfo;
    PrefInfo? prefInfo;
    MarketInfo? marketInfo;

    // 检查秒杀活动 - 与微信小程序逻辑一致
    if (specData.seckill != null && specData.seckill!.isNotEmpty) {
      // 查找匹配的秒杀活动 - 与微信小程序逻辑一致
      for (final seckillItem in specData.seckill!) {
        final isMatch =
            isOptionIdsMatch(selectedOptionIds, seckillItem.optionIds);

        if (seckillItem.seckillActive == 1 && isMatch) {
          seckillInfo = SeckillInfo(
            seckillId: seckillItem.seckillId!,
            seckillActive: seckillItem.seckillActive!,
            price: seckillItem.price!,
            userMaxOrderCount: seckillItem.userMaxOrderCount ?? 0,
          );
          break; // 找到匹配的秒杀活动就退出循环
        }
      }
    }

    // 检查优惠活动 - 与微信小程序逻辑一致
    if (specData.pref != null && specData.pref!.isNotEmpty) {
      // 查找匹配的优惠活动
      for (final prefItem in specData.pref!) {
        final isMatch = isOptionIdsMatch(selectedOptionIds, prefItem.optionIds);

        if (isMatch) {
          prefInfo = PrefInfo(
            preferentialId: prefItem.preferentialId!,
            discountPrice: prefItem.discountPrice!,
            maxOrderCount: prefItem.maxOrderCount ?? 0,
          );
          break; // 找到匹配的优惠活动就退出循环
        }
      }
    }

    // 检查营销活动 - 与微信小程序逻辑一致
    if (specData.market != null && specData.market!.isNotEmpty) {
      // 查找匹配的满减活动
      for (final marketItem in specData.market!) {
        final isMatch =
            isOptionIdsMatch(selectedOptionIds, marketItem.optionIds);

        if (isMatch) {
          marketInfo = MarketInfo(
            steps: marketItem.steps,
          );
          break; // 找到匹配的满减活动就退出循环
        }
      }
    }

    return (
      seckillInfo: seckillInfo,
      prefInfo: prefInfo,
      marketInfo: marketInfo
    );
  }

  /// 计算最终价格 - 通用方法，供规格弹窗和购物车使用
  /// 与微信小程序_calculateFinalPrice逻辑完全一致
  /// [count] 数量
  /// [basePrice] 基础价格（规格价格总和）
  /// [promoInfo] 促销信息
  /// 返回最终价格、促销数量和当前价格
  ({double finalPrice, int promotionCount, double currentPrice})
      calculateFinalPrice(
    final int count,
    final double basePrice,
    final ({
      SeckillInfo? seckillInfo,
      PrefInfo? prefInfo,
      MarketInfo? marketInfo
    }) promoInfo,
  ) {
    int promotionCount = 0;
    double finalPrice = basePrice;
    double currentPrice = basePrice;

    // 处理秒杀价格 - 与微信小程序逻辑完全一致
    if (promoInfo.seckillInfo != null) {
      // 如果只有秒杀的话别的活动没有的话显示秒杀价格
      if (promoInfo.prefInfo == null) {
        finalPrice = promoInfo.seckillInfo!.price;
        // 如果超出秒杀数量，显示原价
        if (count > promoInfo.seckillInfo!.userMaxOrderCount) {
          currentPrice = basePrice;
        } else {
          currentPrice = promoInfo.seckillInfo!.price;
        }
      } else {
        final max = promoInfo.seckillInfo!.userMaxOrderCount;
        if (max == 0 || count <= max) {
          finalPrice = promoInfo.seckillInfo!.price;
          currentPrice = promoInfo.seckillInfo!.price;
        } else if (promoInfo.prefInfo != null) {
          // 超出秒杀数量，使用优惠价格
          final prefMax = promoInfo.prefInfo!.maxOrderCount;
          promotionCount =
              prefMax > 0 ? min(count - max, prefMax) : count - max;

          // 如果优惠数量大于0，则显示优惠价格
          if (promotionCount > 0) {
            finalPrice = promoInfo.prefInfo!.discountPrice;
            currentPrice = promoInfo.prefInfo!.discountPrice;
          }
          // 如果超出优惠数量，显示原价
          if (count > max + prefMax) {
            currentPrice = basePrice;
          }
        }
      }
    } else if (promoInfo.prefInfo != null) {
      // 处理优惠价格
      final prefMax = promoInfo.prefInfo!.maxOrderCount;
      // 计算优惠数量：如果超过最大优惠数量，则只计算最大优惠数量
      promotionCount = min(count, prefMax);

      finalPrice = promoInfo.prefInfo!.discountPrice;
      // 如果优惠数量大于等于优惠最大数量，则显示原价
      if (count > prefMax) {
        currentPrice = basePrice;
      } else {
        currentPrice = promoInfo.prefInfo!.discountPrice;
      }
    }

    return (
      finalPrice: finalPrice,
      promotionCount: promotionCount,
      currentPrice: currentPrice
    );
  }

  /// 检查选项ID是否匹配 - 通用方法
  /// 与微信小程序_isOptionIdsMatch逻辑一致
  bool isOptionIdsMatch(
      final List<int> selectedOptionIds, final List<int>? targetOptionIds) {
    if (targetOptionIds == null || targetOptionIds.isEmpty) {
      return false;
    }
    if (selectedOptionIds.isEmpty) {
      return false;
    }

    // 与微信小程序逻辑完全一致：
    // return Array.isArray(targetOptionIds) &&
    //        Array.isArray(selectedOptionIds) &&
    //        targetOptionIds.every((id) => selectedOptionIds.includes(Number(id)))

    // 检查目标option_ids中的每个ID是否都包含在选中的option_ids中
    return targetOptionIds
        .every((final targetId) => selectedOptionIds.contains(targetId));
  }

  /// 格式化价格 - 通用方法
  /// 与微信小程序_formatPrice逻辑一致
  double formatPrice(final double val) {
    return (((val) * 100).round()) / 100;
  }

  /// 生成规格唯一标识符 - 通用方法
  /// 与微信小程序逻辑一致：`${foodId}_${selectedOptionIds.join('_')}`
  String generateSpecUniqueId(
      final int foodId, final List<int> selectedOptionIds) {
    return '${foodId}_${selectedOptionIds.join('_')}';
  }

  /// 构建规格选择选项信息 - 通用方法
  /// 与微信小程序spec_selected_options格式一致
  List<Map<String, dynamic>> buildSpecSelectedOptions(
    final List<SelectedSpecOption> selectedOptions,
  ) {
    return selectedOptions
        .map((final opt) => {
              'spec_type_id': opt.specTypeId,
              'spec_option_id': opt.specOptionId,
              'name': opt.name,
              'price': opt.price,
            })
        .toList();
  }

  /// 构建规格商品项目 - 通用方法，供规格弹窗和购物车使用
  /// 与微信小程序_buildSpecFoodItem逻辑一致
  Food buildSpecFoodItem({
    required final Food foodItem,
    required final List<int> selectedOptionIds,
    required final List<SelectedSpecOption> selectedOptions,
    required final double additionalPrice,
    required final int count,
    required final ({
      SeckillInfo? seckillInfo,
      PrefInfo? prefInfo,
      MarketInfo? marketInfo
    }) promoInfo,
    // 新增参数：用于验证匹配的规格数据
    final FoodWithSpec? specData,
  }) {
    // 生成唯一ID - 与微信小程序逻辑一致
    final specUniqueId = generateSpecUniqueId(foodItem.id!, selectedOptionIds);

    // 构建规格选择选项信息 - 与微信小程序逻辑一致
    final specSelectedOptions = buildSpecSelectedOptions(selectedOptions);

    // 计算规格组合的总价格 - 用作 oldPrice
    final specCombinationPrice = formatPrice(additionalPrice);

    // 基础项目信息 - 与微信小程序逻辑一致
    // 微信小程序中：price: this._formatPrice(originalPrice) - 用规格价格覆盖foodItem.price
    Food specFoodItem = foodItem.copyWith(
      price: specCombinationPrice, // 使用规格价格覆盖foodItem原价
      minCount: foodItem.minCount ?? 1,
      // 设置规格相关字段
      specUniqueId: specUniqueId,
      specSelectedOptions: specSelectedOptions,
      originPrice: specCombinationPrice,
      oldPrice: specCombinationPrice, // 设置为规格组合的总价格
    );

    // 验证并添加秒杀信息 - 与微信小程序逻辑一致
    // 只有当选中的option_ids与秒杀活动的option_ids匹配时才设置秒杀信息
    bool hasValidSeckill = false;
    if (promoInfo.seckillInfo != null && specData?.seckill != null) {
      // 查找匹配的秒杀活动
      final matchedSeckill = specData!.seckill!.firstWhere(
        (final seckillItem) =>
            seckillItem.seckillActive == 1 &&
            isOptionIdsMatch(selectedOptionIds, seckillItem.optionIds),
        orElse: () => SeckillActivity(),
      );

      if (matchedSeckill.seckillId != null) {
        hasValidSeckill = true;
        specFoodItem = specFoodItem.copyWith(
          seckillId: matchedSeckill.seckillId,
          seckillActive: matchedSeckill.seckillActive,
          seckillPrice: matchedSeckill.price,
          seckillMaxOrderCount: matchedSeckill.userMaxOrderCount,
          price: matchedSeckill.price, // 秒杀价格覆盖规格价格
          oldPrice: specCombinationPrice, // 秒杀时原价为规格组合价格
          originPrice: specCombinationPrice,
        );
      }
    }

    // 如果没有有效的秒杀信息，清除秒杀信息 - 与微信小程序逻辑一致
    if (!hasValidSeckill) {
      specFoodItem = specFoodItem.copyWith(
        seckillId: null,
        seckillActive: null,
        seckillPrice: null,
        seckillMaxOrderCount: 0,
      );
    }

    // 验证并添加优惠信息 - 与微信小程序逻辑一致
    // 只有当选中的option_ids与优惠活动的option_ids匹配时才设置优惠信息
    bool hasValidPref = false;
    if (promoInfo.prefInfo != null && specData?.pref != null) {
      // 查找匹配的优惠活动
      final matchedPref = specData!.pref!.firstWhere(
        (final prefItem) => isOptionIdsMatch(selectedOptionIds, prefItem.optionIds),
        orElse: () => PrefActivity(),
      );

      if (matchedPref.preferentialId != null) {
        hasValidPref = true;
        specFoodItem = specFoodItem.copyWith(
          maxOrderCount: matchedPref.maxOrderCount,
          prefrentialId: matchedPref.preferentialId,
          prefrentialPrice: matchedPref.discountPrice,
          prefrentialCount: 0, // 初始优惠数量为0，会在购物车中计算
        );

        // 没有有效秒杀信息，但有优惠价格 - 与微信小程序逻辑一致
        if (!hasValidSeckill) {
          specFoodItem = specFoodItem.copyWith(
            price: matchedPref.discountPrice, // 优惠价格覆盖规格价格
            oldPrice: specCombinationPrice, // 优惠时原价为规格组合价格
            originPrice: specCombinationPrice,
          );
        }
      }
    }

    // 如果没有有效的优惠信息，清除优惠信息 - 与微信小程序逻辑一致
    if (!hasValidPref) {
      specFoodItem = specFoodItem.copyWith(
        prefrentialId: null,
        prefrentialPrice: null, // 现在可以正确设置为null
        maxOrderCount: 0,
        prefrentialCount: null,
      );
    }

    // 验证并处理营销信息 - 与微信小程序逻辑一致
    // 只有当选中的option_ids与满减活动的option_ids匹配时才保留满减标签
    bool hasValidMarket = false;
    if (promoInfo.marketInfo != null && specData?.market != null) {
      // 查找匹配的满减活动
      final matchedMarket = specData!.market!.firstWhere(
        (final marketItem) =>
            isOptionIdsMatch(selectedOptionIds, marketItem.optionIds),
        orElse: () => MarketActivity(),
      );

      if (matchedMarket.steps != null) {
        hasValidMarket = true;
        // 保留满减标签
        specFoodItem = specFoodItem.copyWith(
          reductionFoodsTags: foodItem.reductionFoodsTags,
        );
      }
    }

    // 微信小程序中：if (!promoInfo.marketInfo) { delete baseItem.reduction_foods_tags; }
    if (!hasValidMarket) {
      // 清除满减标签
      specFoodItem = specFoodItem.copyWith(reductionFoodsTags: []);
    }

    return specFoodItem;
  }

  /// 处理再来一单规格商品 - 新增方法
  /// 与微信小程序getSpecInfoForOrderAgain逻辑一致
  Future<Food?> processComeAgainSpecFood({
    required final Food food,
    required final String? specUniqueId,
  }) async {
    try {
      // 获取美食规格数据
      final specData = await getFoodSpecData(food.id!);
      if (specData == null) {
        return null;
      }

      // 如果没有规格信息，返回null
      if (specUniqueId == null || specUniqueId.isEmpty) {
        return null;
      }

      // 解析规格唯一标识符获取选项ID
      final selectedOptionIds = _parseSpecUniqueId(specUniqueId);
      if (selectedOptionIds.isEmpty) {
        return null;
      }

      // 从API获取的规格数据中根据selectedOptionIds构建规格选项
      final selectedOptions = _buildSpecOptionsFromIds(
        specData.specs ?? [],
        selectedOptionIds,
      );

      if (selectedOptions.isEmpty) {
        return null;
      }

      // 计算规格附加价格， 不包括美食的原始价格，只有规格的价格相加
      final additionalPrice = selectedOptions.fold<double>(
        0,
        (final sum, final option) => sum + option.price,
      );

      // 计算促销信息
      final promoInfo = calculatePromotionPrice(specData, selectedOptionIds);

      // 构建规格商品
      final specFood = buildSpecFoodItem(
        foodItem: food,
        selectedOptionIds: selectedOptionIds,
        selectedOptions: selectedOptions,
        additionalPrice: additionalPrice,
        count: 1,
        promoInfo: promoInfo,
        specData: specData,
      );

      return specFood;
    } catch (e) {
      return null;
    }
  }

  /// 从API规格数据中根据选项ID构建SelectedSpecOption列表
  List<SelectedSpecOption> _buildSpecOptionsFromIds(
    final List<SpecGroup> specGroups,
    final List<int> selectedOptionIds,
  ) {
    final selectedOptions = <SelectedSpecOption>[];

    for (final group in specGroups) {
      if (group.specOptions == null) continue;

      // 在当前组中查找匹配的选项
      for (final option in group.specOptions!) {
        if (selectedOptionIds.contains(option.id) && option.state == 1) {
          selectedOptions.add(SelectedSpecOption(
            specTypeId: group.id ?? 0,
            specOptionId: option.id ?? 0,
            name: option.name ?? '',
            price: option.price?.toDouble() ?? 0.0,
          ));
          break; // 每个组只选择一个选项
        }
      }
    }

    // 如果选中的选项数量与规格组数量不匹配，使用默认选项
    if (selectedOptions.length != specGroups.length) {
      return _getDefaultSelectedOptions(specGroups);
    }

    return selectedOptions;
  }

  /// 获取默认选中的规格选项
  List<SelectedSpecOption> _getDefaultSelectedOptions(
      final List<SpecGroup> specGroups) {
    final defaultOptions = <SelectedSpecOption>[];

    for (final group in specGroups) {
      if (group.specOptions == null || group.specOptions!.isEmpty) continue;

      // 查找第一个有效的选项
      final defaultOption = group.specOptions!.firstWhere(
        (final opt) => opt.state == 1,
        orElse: () => group.specOptions!.first,
      );

      if (defaultOption.id != null) {
        defaultOptions.add(SelectedSpecOption(
          specTypeId: group.id ?? 0,
          specOptionId: defaultOption.id ?? 0,
          name: defaultOption.name ?? '',
          price: defaultOption.price?.toDouble() ?? 0.0,
        ));
      }
    }

    return defaultOptions;
  }

  /// 解析规格唯一标识符获取选项ID - 与微信小程序parseSpecUniqueId逻辑一致
  List<int> _parseSpecUniqueId(final String specUniqueId) {
    final parts = specUniqueId.split('_');
    if (parts.length <= 1) return [];

    // 第一个部分是食品ID，从第二个部分开始是选项ID
    return parts
        .skip(1)
        .map((final id) => int.tryParse(id))
        .where((final id) => id != null)
        .cast<int>()
        .toList();
  }
}

/// 规格服务提供者
final specServiceProvider = Provider<SpecService>((final ref) {
  return SpecService(
    specRepository: ref.watch(specRepositoryProvider),
  );
});
