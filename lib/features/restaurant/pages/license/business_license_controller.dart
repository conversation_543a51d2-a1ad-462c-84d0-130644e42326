import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/widgets/toast_widget.dart';
import 'package:user_app/features/restaurant/pages/license/business_license_state.dart';
import 'package:user_app/features/restaurant/services/license_service.dart';
import 'package:user_app/generated/l10n.dart';

part 'business_license_controller.g.dart';

/// 营业执照页面控制器
@riverpod
class LicenseController extends _$LicenseController {
  @override
  LicenseState build() {
    return const LicenseState();
  }

  /// 初始化页面，获取验证码
  /// [restaurantId] 餐厅ID
  Future<void> initPage(final int restaurantId) async {
    state = state.copyWith(
      restaurantId: restaurantId,
    );

    await _checkVerificationStatus();
  }

  /// 检查验证状态并获取验证码
  Future<void> _checkVerificationStatus() async {
    if (state.restaurantId == 0) return;

    final licenseService = LicenseService(ref.read(apiClientProvider));
    final result = await licenseService.getLicenseCaptcha(state.restaurantId);

    if (result.success && result.data != null) {
      final data = result.data!;
      final allUrls = data.license.map((final item) => item.bigImage).toList();

      state = state.copyWith(
        captchaImage: data.captcha,
        inputCode: '',
        licenseData: data.license,
        allUrls: allUrls,
        error: data.error,
      );

      // 如果验证码为空且成功，则直接进入查看模式
      if (data.captcha.isEmpty && data.success) {
        state = state.copyWith(isVerified: true);
      }
    } else {
      state = state.copyWith(
        error: result.msg,
      );

      // 如果是404错误，可能需要返回上一页
      if (result.status == 404) {
        ToastWidget.showRich(result.msg);
      } else {
        ToastWidget.showRich(result.msg);
      }
    }
  }

  /// 更新输入的验证码
  /// [code] 验证码
  void updateInputCode(final String code) {
    state = state.copyWith(inputCode: code);
  }

  /// 验证输入的验证码
  Future<void> verifyCode() async {
    if (state.inputCode.isEmpty) {
      ToastWidget.showRich(S.current.captcha_required);
      return;
    }

    if (state.captchaImage.isEmpty) {
      ToastWidget.showRich(S.current.captcha_load_failed);
      return;
    }

    final licenseService = LicenseService(ref.read(apiClientProvider));
    final result = await licenseService.verifyCaptcha(
      state.restaurantId,
      state.inputCode,
    );

    if (result.success && result.data != null) {
      final data = result.data!;
      final allUrls = data.license.map((final item) => item.bigImage).toList();

      state = state.copyWith(
        captchaImage: data.captcha,
        inputCode: '',
        licenseData: data.license,
        allUrls: allUrls,
      );

      // 如果验证码为空且成功，则验证成功
      if (data.captcha.isEmpty && data.success) {
        state = state.copyWith(isVerified: true);
      }
    } else {
      state = state.copyWith(
        error: result.msg,
      );
    }
  }

  /// 刷新验证码
  Future<void> refreshCaptcha() async {
    await _checkVerificationStatus();
  }
}
