import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/restaurant/pages/license/business_license_controller.dart';
import 'package:user_app/features/restaurant/pages/license/widgets/captcha_input_widget.dart';
import 'package:user_app/features/restaurant/pages/license/widgets/captcha_image_widget.dart';
import 'package:user_app/features/restaurant/pages/license/widgets/verify_button_widget.dart';

/// 营业执照验证组件
class LicenseVerificationWidget extends ConsumerWidget {
  const LicenseVerificationWidget({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Padding(
        padding: EdgeInsets.fromLTRB(15.w, 200.h, 15.w, 20.h),
        child: Column(
          children: [
            // 输入框和验证码同一行
            Row(
              children: [
                // 验证码显示
                Expanded(
                  flex: 3,
                  child: Consumer(
                    builder: (final context, final ref, final child) {
                      final captchaImage = ref.watch(
                        licenseControllerProvider
                            .select((final state) => state.captchaImage),
                      );
                      final error = ref.watch(
                        licenseControllerProvider
                            .select((final state) => state.error),
                      );

                      return CaptchaImageWidget(
                        captchaImage: captchaImage,
                        error: error,
                        onRefresh: () => ref
                            .read(licenseControllerProvider.notifier)
                            .refreshCaptcha(),
                      );
                    },
                  ),
                ),

                SizedBox(width: 10.w),

                // 输入框
                Expanded(
                  flex: 4,
                  child: Consumer(
                    builder: (final context, final ref, final child) {
                      final inputCode = ref.watch(
                        licenseControllerProvider
                            .select((final state) => state.inputCode),
                      );

                      return CaptchaInputWidget(
                        inputCode: inputCode,
                        onChanged: (final code) => ref
                            .read(licenseControllerProvider.notifier)
                            .updateInputCode(code),
                      );
                    },
                  ),
                ),
              ],
            ),

            SizedBox(height: 20.h),

            // 验证按钮
            Consumer(
              builder: (final context, final ref, final child) {
                final inputCode = ref.watch(
                  licenseControllerProvider
                      .select((final state) => state.inputCode),
                );

                return VerifyButtonWidget(
                  enabled: inputCode.isNotEmpty,
                  onPressed: () =>
                      ref.read(licenseControllerProvider.notifier).verifyCode(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
