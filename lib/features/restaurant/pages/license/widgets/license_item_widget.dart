import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/restaurant/pages/license/business_license_state.dart';
import 'package:user_app/generated/l10n.dart';

/// 营业执照项目组件
class LicenseItemWidget extends StatelessWidget {
  final LicenseItem licenseItem;
  final VoidCallback onTap;

  const LicenseItemWidget({
    super.key,
    required this.licenseItem,
    required this.onTap,
  });

  @override
  Widget build(final BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.w),
        border: Border.all(color: const Color(0xFFF5F5F5), width: 1.w),
      ),
      child: Padding(
        padding: EdgeInsets.all(5.w),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(6.w),
          ),
          child: GestureDetector(
            onTap: onTap,
            child: Image.network(
              licenseItem.image,
              fit: BoxFit.contain,
              width: double.infinity,
              height: 200.h,
              errorBuilder: (final context, final error, final stackTrace) {
                return Container(
                  height: 200.h,
                  color: Colors.grey[200],
                  child: Center(
                    child: Text(
                      S.current.no_business_license,
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                );
              },
              loadingBuilder:
                  (final context, final child, final loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  height: 200.h,
                  color: Colors.grey[100],
                  child: const Center(child: LoadingWidget()),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
