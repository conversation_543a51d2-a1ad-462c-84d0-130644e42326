import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 验证码图片组件
class CaptchaImageWidget extends StatelessWidget {
  final String captchaImage;
  final String? error;
  final VoidCallback onRefresh;

  const CaptchaImageWidget({
    super.key,
    required this.captchaImage,
    this.error,
    required this.onRefresh,
  });

  @override
  Widget build(final BuildContext context) {
    return GestureDetector(
      onTap: onRefresh,
      child: Container(
        height: 50.h,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: const Color(0xFFE0E0E0), width: 1.w),
          borderRadius: BorderRadius.circular(5.w),
        ),
        child: captchaImage.isNotEmpty
            ? ClipRRect(
                borderRadius: BorderRadius.circular(3.w),
                child: Image.memory(
                  base64Decode(captchaImage),
                  fit: BoxFit.cover,
                ),
              )
            : Center(
                child: Text(
                  error ?? '',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 11.sp,
                  ),
                ),
              ),
      ),
    );
  }
}
