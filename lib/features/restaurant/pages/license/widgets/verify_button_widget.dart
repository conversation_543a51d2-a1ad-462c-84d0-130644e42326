import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 验证按钮组件
class VerifyButtonWidget extends StatelessWidget {
  final bool enabled;
  final VoidCallback onPressed;

  const VerifyButtonWidget({
    super.key,
    required this.enabled,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50.h,
      child: ElevatedButton(
        onPressed: enabled ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.baseGreenColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5.w),
          ),
          disabledBackgroundColor: Colors.grey[300],
        ),
        child: Text(
          S.current.verify_captcha,
          style: TextStyle(fontSize: 18.sp),
        ),
      ),
    );
  }
}
