/// 营业执照页面状态
class LicenseState {
  /// 构造函数
  const LicenseState({
    this.isVerified = false,
    this.captchaImage = '',
    this.inputCode = '',
    this.licenseData = const [],
    this.allUrls = const [],
    this.restaurantId = 0,
    this.error,
  });

  /// 是否已验证通过
  final bool isVerified;

  /// 验证码图片base64
  final String captchaImage;

  /// 用户输入的验证码
  final String inputCode;

  /// 营业执照数据
  final List<LicenseItem> licenseData;

  /// 所有图片URL
  final List<String> allUrls;

  /// 餐厅ID
  final int restaurantId;

  /// 错误信息
  final String? error;

  /// 拷贝方法
  LicenseState copyWith({
    final bool? isVerified,
    final String? captchaImage,
    final String? inputCode,
    final List<LicenseItem>? licenseData,
    final List<String>? allUrls,
    final int? restaurantId,
    final String? error,
  }) {
    return LicenseState(
      isVerified: isVerified ?? this.isVerified,
      captchaImage: captchaImage ?? this.captchaImage,
      inputCode: inputCode ?? this.inputCode,
      licenseData: licenseData ?? this.licenseData,
      allUrls: allUrls ?? this.allUrls,
      restaurantId: restaurantId ?? this.restaurantId,
      error: error ?? this.error,
    );
  }
}

/// 营业执照项目数据模型
class LicenseItem {
  const LicenseItem({
    required this.image,
    required this.bigImage,
  });

  /// 小图URL
  final String image;

  /// 大图URL
  final String bigImage;

  /// 从JSON创建对象
  factory LicenseItem.fromJson(final Map<String, dynamic> json) {
    return LicenseItem(
      image: json['image'] ?? '',
      bigImage: json['big_image'] ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'image': image,
      'big_image': bigImage,
    };
  }
}

/// 验证码接口响应数据模型
class LicenseCaptchaResponse {
  const LicenseCaptchaResponse({
    required this.success,
    this.captcha = '',
    this.license = const [],
    this.error,
  });

  /// 是否成功
  final bool success;

  /// 验证码图片base64
  final String captcha;

  /// 营业执照列表
  final List<LicenseItem> license;

  /// 错误信息
  final String? error;

  /// 从JSON创建对象
  factory LicenseCaptchaResponse.fromJson(final Map<String, dynamic> json) {
    return LicenseCaptchaResponse(
      success: json['success'] ?? false,
      captcha: json['captcha'] ?? '',
      license: (json['license'] as List<dynamic>?)
              ?.map(
                  (final e) => LicenseItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      error: json['error'],
    );
  }
}
