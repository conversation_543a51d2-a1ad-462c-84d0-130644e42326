import 'dart:developer';
import 'dart:typed_data';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/data/models/restaurant/restaurant_home_model.dart';
import 'package:user_app/data/repositories/restaurant/restaurant_repository.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_state.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/features/restaurant/models/come_again_item_model.dart';
import 'package:user_app/features/restaurant/services/spec_service.dart';

import 'package:user_app/core/utils/wechat_util.dart';
import 'package:user_app/core/utils/image_util.dart';
import 'package:user_app/generated/l10n.dart';

part 'restaurant_detail_controller.g.dart';

/// 餐厅详情控制器
@riverpod
class RestaurantDetailController extends _$RestaurantDetailController {
  late RestaurantRepository _restaurantRepository;
  List<ComeAgainItemModel> _pendingComeAgainItems = [];

  @override
  RestaurantDetailState build() {
    _restaurantRepository = RestaurantRepository(
      apiClient: ref.read(apiClientProvider),
    );

    return RestaurantDetailState.loading();
  }

  /// 初始化数据
  Future<void> init({
    required final int restaurantId,
    required int buildingId,
    required final List<dynamic> ids,
    final List<dynamic> comeAgainItems = const [],
  }) async {
    state = RestaurantDetailState.loading();

    // 如果buildingId为0，则使用当前定位的buildingId
    final currentBuildingId =
        ref.read(homeNoticeProvider).value?.location?.id ?? 0;
    if (buildingId == 0 || buildingId != currentBuildingId) {
      buildingId = currentBuildingId;
    }

    // 记录开始加载时间
    final startTime = DateTime.now();

    print('ids --> $ids');

    // 清空购物车
    ref.read(shoppingCartProvider.notifier).clearCardFoods();

    try {
      // 并行加载数据，提高效率
      final results = await Future.wait([
        loadRestaurantHome(restaurantId: restaurantId, buildingId: buildingId),
        loadFoodsList(
          restaurantId: restaurantId,
          buildingId: buildingId,
          ids: ids,
        ),
      ]);

      // 计算已经过去的时间
      final loadingDuration =
          DateTime.now().difference(startTime).inMilliseconds;

      // 确保骨架屏至少显示400毫秒，避免闪烁
      if (loadingDuration < 200) {
        await Future.delayed(Duration(milliseconds: 200 - loadingDuration));
      }

      // 更新状态
      final restaurantData = results[0] as RestaurantHomeData;
      final foodsData = results[1] as FoodsData;

      state = RestaurantDetailState.loaded(
        restaurantData: restaurantData,
        foodsData: foodsData,
        buildingId: buildingId,
      );
    } catch (e) {
      log('初始化餐厅详情数据失败: $e');
      state = RestaurantDetailState.error(e.toString());
    }
    // 更新上一步的foodIds
    state = state.updateFromFoodIds(ids);
    Future.delayed(const Duration(seconds: 3), () {
      state = state.updateFromFoodIds([]);
    });

    // 存储待处理的再来一单商品
    if (comeAgainItems.isNotEmpty) {
      _pendingComeAgainItems = comeAgainItems
          .map(
            (final item) =>
                ComeAgainItemModel.fromJson(item as Map<String, dynamic>),
          )
          .toList();
      log('存储待处理的再来一单商品: ${_pendingComeAgainItems.length}个');
    }
  }

  /// 处理待处理的再来一单商品
  Future<void> processPendingComeAgainItems() async {
    if (_pendingComeAgainItems.isEmpty || state.foodsData == null) return;

    try {
      final foods = state.foodsData!.foods ?? [];
      final cartNotifier = ref.read(shoppingCartProvider.notifier);
      final specService = ref.read(specServiceProvider);

      log('开始处理待处理的再来一单商品: ${_pendingComeAgainItems.length}个');

      for (final comeAgainItem in _pendingComeAgainItems) {
        // 根据商品ID查找对应的food - 使用where避免异常
        final matchingFoods = foods
            .where(
              (final food) => food.id == comeAgainItem.id && food.state == 1,
            )
            .toList();

        if (matchingFoods.isEmpty) {
          log('跳过未找到的商品: ${comeAgainItem.id}');
          continue;
        }

        final food = matchingFoods.first;

        log('找到商品: ${food.id}, 名称: ${food.name}, 类型: ${comeAgainItem.foodType}');

        // 根据商品类型处理
        if (comeAgainItem.foodType == 1 && comeAgainItem.specUniqueId != null) {
          // 规格商品：使用spec_service处理规格商品
          log('开始处理规格商品: ${food.id}, 规格ID: ${comeAgainItem.specUniqueId}');

          // 使用spec_service处理规格商品，获取实时规格信息和价格
          final specFood = await specService.processComeAgainSpecFood(
            food: food,
            specUniqueId: comeAgainItem.specUniqueId,
          );

          if (specFood != null) {
            log('规格商品处理成功: ${food.id}, 最终价格: ${specFood.price}');

            // 计算循环次数：考虑minCount的影响
            // 第一次添加会根据minCount添加，后续添加只增加1个
            final minCount = specFood.minCount ?? 1;
            final targetCount = comeAgainItem.count;

            // 如果目标数量小于等于最小购买数量，只需要添加一次
            if (targetCount <= minCount) {
              try {
                cartNotifier.addFoodToCart(foodItem: specFood);
                log('规格商品添加完成: ${food.id}, 添加1次，实际数量: $targetCount');
              } catch (e) {
                log('添加规格商品失败: ${food.id}, 错误: $e');
              }
            } else {
              // 第一次添加minCount个，剩余的逐个添加
              final remainingCount = targetCount - minCount;
              try {
                // 第一次添加（会添加minCount个）
                cartNotifier.addFoodToCart(foodItem: specFood);
                log('规格商品第一次添加: ${food.id}, 添加了${minCount}个');

                // 剩余的逐个添加
                for (int i = 0; i < remainingCount; i++) {
                  cartNotifier.addFoodToCart(foodItem: specFood);
                  log('规格商品额外添加第${i + 1}次: ${food.id}');
                }

                log('规格商品添加完成: ${food.id}, 总计: ${1 + remainingCount}次添加，实际数量: $targetCount');
              } catch (e) {
                log('添加规格商品失败: ${food.id}, 错误: $e');
              }
            }
          } else {
            log('规格商品处理失败，跳过: ${food.id}');
          }
        } else {
          // 普通商品：直接添加到购物车
          log('准备添加普通商品: ${food.id}, 数量: ${comeAgainItem.count}');
          final newFood = food.copyWith(
            minCount: 1,
          );

          // 计算循环次数：考虑minCount的影响
          final minCount = newFood.minCount ?? 1;
          final targetCount = comeAgainItem.count;

          // 如果目标数量小于等于最小购买数量，只需要添加一次
          if (targetCount <= minCount) {
            try {
              cartNotifier.addFoodToCart(foodItem: newFood);
              log('普通商品添加完成: ${newFood.id}, 添加1次，实际数量: $targetCount');
            } catch (e) {
              log('添加普通商品失败: ${newFood.id}, 错误: $e');
            }
          } else {
            // 第一次添加minCount个，剩余的逐个添加
            final remainingCount = targetCount - minCount;
            try {
              // 第一次添加（会添加minCount个）
              cartNotifier.addFoodToCart(foodItem: newFood);
              log('普通商品第一次添加: ${newFood.id}, 添加了${minCount}个');

              // 剩余的逐个添加
              for (int i = 0; i < remainingCount; i++) {
                cartNotifier.addFoodToCart(foodItem: newFood);
                log('普通商品额外添加第${i + 1}次: ${newFood.id}');
              }

              log('普通商品添加完成: ${newFood.id}, 总计: ${1 + remainingCount}次添加，实际数量: $targetCount');
            } catch (e) {
              log('添加普通商品失败: ${newFood.id}, 错误: $e');
            }
          }
        }
      }

      log('再来一单处理完成，共处理${_pendingComeAgainItems.length}个商品');
    } catch (e) {
      log('处理再来一单失败: $e');
      BotToast.showText(text: '添加商品到购物车失败: $e');
    } finally {
      // 清空待处理列表
      _pendingComeAgainItems.clear();
    }
  }

  /// 加载餐厅基础信息
  Future<RestaurantHomeData> loadRestaurantHome({
    required final int restaurantId,
    required final int buildingId,
  }) async {
    final param = {"restaurant_id": restaurantId, "building_id": buildingId};

    log('getRestaurantHome接收到的参数: $param');

    final response = await _restaurantRepository.getRestaurantHome(param);

    if (response.status != 200) {
      BotToast.showText(text: response.msg);
      throw response.msg;
    }

    if (response.data == null) {
      return RestaurantHomeData();
    }

    return response.data!;
  }

  /// 加载食品列表数据
  Future<FoodsData> loadFoodsList({
    required final int restaurantId,
    required final int buildingId,
    required final List<dynamic> ids,
    final int? typeId,
    final String? keyword,
    final int page = 1,
  }) async {
    final param = {
      "restaurant_id": restaurantId,
      "building_id": buildingId,
      if (page > 1) "page": page,
      if (typeId != null) "type": typeId,
      if (keyword != null && keyword.isNotEmpty) "kw": keyword,
    };
    if (ids.isNotEmpty) param['ids[0]'] = ids[0];

    log('getFoodsList接收到的参数: $param');

    final response = await _restaurantRepository.getFoodsList(param);

    if (response.status != 200) {
      BotToast.showText(text: response.msg);
      throw response.msg;
    }

    if (response.data == null) {
      return FoodsData();
    }

    // 检查是否有秒杀商品
    final hasSeckill =
        response.data!.foods?.any((final food) => food.seckillActive == 1) ??
            false;

    // 创建默认的分类列表，包含"全部"或"秒杀专区"
    final List<FoodType> updatedFoodTypes = [];

    // 添加"全部"或"秒杀专区"分类
    updatedFoodTypes.add(
      FoodType(
        id: 0,
        type: 0,
        licenseType: 0,
        name: hasSeckill ? S.current.sec_kill : S.current.classify_all,
        shake: 0,
      ),
    );

    // 添加API返回的其他分类
    if (response.data!.foodType != null) {
      updatedFoodTypes.addAll(response.data!.foodType!);
    }

    // 处理满减活动标签 - 与微信小程序formattedFoodsInfo逻辑一致
    final updatedFoods = _processReductionFoodsTags(
      response.data!.foods ?? [],
      response.data!.market,
    );

    // 更新foodType
    return response.data!.copyWith(
      foodType: updatedFoodTypes,
      foods: updatedFoods,
    );
  }

  /// 处理满减活动标签 - 与微信小程序formattedFoodsInfo逻辑一致
  /// 检查美食ID是否在reduction_foods_id中，如果在则添加满减标签
  List<Food> _processReductionFoodsTags(
    final List<Food> foods,
    final Market? market,
  ) {
    final reductionFoodsId = market?.reductionFoodsId ?? [];
    final reductionTags = market?.tags?.reductionTags ?? [];

    // 如果没有满减活动或没有满减标签，直接返回原始食品列表
    if (reductionFoodsId.isEmpty || reductionTags.isEmpty) {
      return foods;
    }

    // 处理每个美食
    return foods.map((final food) {
      // 检查美食ID是否在满减活动列表中 - 与微信小程序逻辑一致
      // if (reduction_foods_id.includes(foods[i].id.toString()))
      if (reductionFoodsId.contains(food.id.toString())) {
        // 添加满减活动标签 - 与微信小程序逻辑一致
        // item['reduction_foods_tags'] = market.tags.reduction_tags;
        return food.copyWith(
          reductionFoodsTags:
              reductionTags.map((final tag) => tag.toJson()).toList(),
        );
      }
      return food;
    }).toList();
  }

  /// 收藏或取消收藏餐厅
  Future<void> toggleFavorite() async {
    if (state.isLoading || state.restaurantData == null) return;

    final restaurantId = state.restaurantData!.id;
    if (restaurantId == null) return;

    final isFavorite = state.isFavorite;
    final url = isFavorite
        ? '/v1/user/remove-store-collect'
        : '/v1/user/add-store-collect';
    final params =
        isFavorite ? {'store_id[0]': restaurantId} : {'store_id': restaurantId};

    try {
      final response = await ref.read(apiClientProvider).post(
            url,
            data: params,
            fromJson: (final response, final data) => response,
          );

      if (response.status == 401) {
        // 用户未登录，显示登录提示
        BotToast.showText(text: S.current.please_login);
        return;
      }

      if (response.status == 200) {
        // 更新状态 - 只更新isFavorite状态，不更新整个restaurantData
        state = state.copyWith(
          isFavorite: !isFavorite,
        );

        // 同时更新restaurantData中的isFavorite值以保持一致性
        if (state.restaurantData != null) {
          state.restaurantData!.isFavorite = !isFavorite ? 1 : 0;
        }

        // 显示提示信息
        BotToast.showText(text: isFavorite ? S.current.un_like_done : S.current.like_done);
      } else {
        // 显示错误信息
        BotToast.showText(text: response.msg ?? '操作失败');
      }
    } catch (e) {
      log('收藏/取消收藏餐厅失败: $e');
      BotToast.showText(text: '操作失败');
    }
  }

  /// 分享餐厅
  Future<void> shareRestaurant() async {
    if (state.isLoading || state.restaurantData == null) return;

    final restaurant = state.restaurantData!;
    final restaurantId = restaurant.id;
    if (restaurantId == null) return;

    try {
      // 获取餐厅Logo图片数据
      final logoUrl = restaurant.logo;
      final Uint8List? thumbData = logoUrl != null && logoUrl.isNotEmpty
          ? await ImageUtil.getImageFromUrl(logoUrl)
          : null;

      if (thumbData == null) {
        BotToast.showText(text: S.current.about_share_failed);
        return;
      }

      // 构建分享路径
      final path = "pages/index/index?resId=$restaurantId&langId=1";

      // 分享标题使用餐厅名称
      final title = restaurant.name ?? S.current.app_name;

      // 执行分享
      final result = await WechatUtil().shareMiniProgram(
        path: path,
        title: title,
        description: S.current.app_description,
        thumbData: thumbData,
      );

      if (result) {
        BotToast.showText(text: S.current.about_share_success);
      } else {
        BotToast.showText(text: S.current.about_share_failed);
      }
    } catch (e) {
      log('分享餐厅失败: $e');
      BotToast.showText(text: S.current.about_share_failed);
    }
  }

  /// 搜索或筛选食品列表
  Future<void> searchOrFilterFoods({final String? keyword}) async {
    if (state.isLoading || state.restaurantData == null) return;

    final restaurantId = state.restaurantData!.id ?? 0;
    final buildingId = state.buildingId;

    // 检查当前选中的分类是否是资质证书类型
    final selectedIndex = state.selectedCategoryIndex;
    final foodTypes = state.foodsData?.foodType ?? [];
    if (selectedIndex >= 0 && selectedIndex < foodTypes.length) {
      final selectedCategory = foodTypes[selectedIndex];
      if (selectedCategory.licenseType == 1) {
        // 如果是资质证书类型，直接返回，不进行网络请求
        state = state.copyWith(isFoodsLoading: false);
        return;
      }
    }

    // 记录当前"全部/秒杀专区"的显示状态
    bool wasShowingSecKill = false;
    if (foodTypes.isNotEmpty && foodTypes[0].type == 0) {
      wasShowingSecKill = foodTypes[0].name == S.current.sec_kill;
    }

    // 设置为食品列表加载状态，而不是整页加载
    state = state.copyWith(isFoodsLoading: true);

    try {
      final response = await loadFoodsList(
        restaurantId: restaurantId,
        buildingId: buildingId,
        typeId: state.selectedTypeId,
        keyword: keyword,
        page: state.page,
        ids: [],
      );

      // 判断是否还有更多数据
      final bool hasMoreData = response.page != null &&
          response.totalPage != null &&
          response.page! < response.totalPage!;

      // 检查返回的菜品数量，如果为空或数量为0，也认为没有更多数据了
      final bool hasNewFood =
          response.foods != null && response.foods!.isNotEmpty;
      final bool noMoreData = !hasMoreData || !hasNewFood;

      // 修复第一个分类名称（全部/秒杀专区）的显示问题
      List<FoodType>? updatedFoodTypes;
      if (response.foodType != null && response.foodType!.isNotEmpty) {
        // 创建更新后的食品类型列表
        updatedFoodTypes = [];

        // 处理第一个标签（保持原状态）
        if (response.foodType![0].type == 0) {
          // 如果第一个标签是"全部"类型，根据之前的状态决定是显示"全部"还是"秒杀专区"
          final firstCategory = response.foodType![0];
          updatedFoodTypes.add(
            FoodType(
              id: firstCategory.id,
              type: firstCategory.type,
              licenseType: firstCategory.licenseType,
              name: wasShowingSecKill
                  ? S.current.sec_kill
                  : S.current.classify_all,
              shake: firstCategory.shake,
              license: firstCategory.license,
              nameUg: firstCategory.nameUg,
              nameZh: firstCategory.nameZh,
            ),
          );

          // 添加其余分类
          updatedFoodTypes.addAll(response.foodType!.sublist(1));
        } else {
          updatedFoodTypes = response.foodType;
        }
      }

      // 如果是第一页，直接替换数据，否则合并数据
      if (state.page == 1) {
        state = state.copyWith(
          isFoodsLoading: false,
          foods: response.foods,
          foodsPage: response.page,
          foodsTotalPage: response.totalPage,
          foodsTotal: response.total,
          foodsData: state.foodsData?.copyWith(
            foodType: updatedFoodTypes ?? response.foodType,
            foods: response.foods,
            page: response.page,
            totalPage: response.totalPage,
            total: response.total,
          ),
          hasMoreData: hasMoreData && hasNewFood,
        );
      } else {
        // 如果没有新数据，不更新现有数据，只更新加载状态和hasMoreData
        if (!hasNewFood) {
          state = state.copyWith(
            isFoodsLoading: false,
            hasMoreData: false,
          );
          return;
        }

        // 合并现有数据和新加载的数据
        final currentFoods = state.foods ?? [];
        final newFoods = response.foods ?? [];
        final mergedFoods = [...currentFoods, ...newFoods];

        state = state.copyWith(
          isFoodsLoading: false,
          foods: mergedFoods,
          foodsPage: response.page,
          foodsTotalPage: response.totalPage,
          foodsTotal: response.total,
          foodsData: state.foodsData?.copyWith(
            foods: mergedFoods,
            page: response.page,
            totalPage: response.totalPage,
            total: response.total,
          ),
          hasMoreData: hasMoreData,
        );
      }
    } catch (e) {
      log('搜索或筛选食品列表失败: $e');
      state = state.copyWith(
        isFoodsLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 显示食品列表
  void showFoodsList() {
    state = state.showFoodsList();
  }

  /// 隐藏食品列表
  void hideFoodsList() {
    state = state.hideFoodsList();
  }

  /// 清空购物车并隐藏食品列表
  void clearCartAndHideFoodsList() {
    state = state.hideFoodsList();
    Future.delayed(const Duration(milliseconds: 300), () {
      ref.read(shoppingCartProvider.notifier).clearCardFoods();
    });
  }

  /// 同时更新分类和清空搜索关键词的组合方法，避免二次请求
  Future<void> updateCategoryAndResetSearch(
    final int index, {
    final int? typeId,
  }) async {
    // 获取分类ID
    int? selectedTypeId = typeId;
    if (typeId == null &&
        state.foodsData?.foodType != null &&
        index < state.foodsData!.foodType!.length) {
      selectedTypeId = state.foodsData!.foodType![index].type;
    }
    state = state.updateSelectedCategory(index, selectedTypeId);
    // 只执行一次请求
    await searchOrFilterFoods();

  }

  /// 更新选中的分类索引和ID
  void updateSelectedCategory(final int index, {final int? typeId}) async {
    // 获取分类ID
    int? selectedTypeId = typeId;
    if (typeId == null &&
        state.foodsData?.foodType != null &&
        index < state.foodsData!.foodType!.length) {
      selectedTypeId = state.foodsData!.foodType![index].type;
    }

    // 更新状态
    state = state.updateSelectedCategory(index, selectedTypeId);

    // 重新加载食品列表
    await searchOrFilterFoods();
  }

  /// 更新搜索关键词
  void updateSearchKeyword(final String keyword) async {
    // state = state.updateSearchKeyword(keyword);

    // 重新加载食品列表
    await searchOrFilterFoods(keyword: keyword);
  }

  /// 加载下一页
  Future<void> loadNextPage() async {
    // 如果没有更多数据，直接返回
    if (!state.hasMoreData || state.isFoodsLoading) {
      return;
    }

    state = state.nextPage();
    await searchOrFilterFoods();
  }

  /// 重置筛选条件
  Future<void> resetFilters() async {
    state = state.resetFilters();
    await searchOrFilterFoods();
  }

  /// 刷新数据
  Future<void> refresh({
    required final int restaurantId,
    required final int buildingId,
    required final List<dynamic> ids,
    final List<dynamic> comeAgainItems = const [],
  }) async {
    await init(
      restaurantId: restaurantId,
      buildingId: buildingId,
      ids: ids,
      comeAgainItems: [],
    );
  }

  /// 更新 foodsData - 用于特价食品详情组件
  void updateFoodsData(FoodsData foodsData) {
    state = state.copyWith(
      isLoading: false,
      foodsData: foodsData,
      foods: foodsData.foods,
      // 同时更新相关的配置数据
      distribution: foodsData.distribution,
      canMulazimTake: foodsData.canMulazimTake,
      canSelfTake: foodsData.canSelfTake,
      isResting: foodsData.isResting,
      limit: foodsData.limit,
      lotteryActive: foodsData.lotteryActive,
      lotteryMinOrderPrice: foodsData.lotteryMinOrderPrice,
      lowestPercent: foodsData.lowestPercent,
      market: foodsData.market,
      notSameLocation: foodsData.notSameLocation,
      notSameLocationMsg: foodsData.notSameLocationMsg,
      notice: foodsData.notice,
      restaurantType: foodsData.restaurantType,
      isOpen: foodsData.isOpen,
      reductionSteps: foodsData.reductionSteps,
      tags: foodsData.tags,
      takeTime: foodsData.takeTime,
    );
  }
}
