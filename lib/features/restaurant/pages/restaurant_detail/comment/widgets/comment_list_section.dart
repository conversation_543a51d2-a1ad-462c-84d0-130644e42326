import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/custom_refresh_indicator.dart';
import 'package:user_app/core/widgets/empty_view.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/comment/restaurant_comment_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/comment/widgets/comment_item_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/comment/widgets/comment_star_section.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/comment/widgets/comment_types_section.dart';
import 'package:user_app/generated/l10n.dart';

/// 评论列表区域组件
class CommentListSection extends ConsumerStatefulWidget {
  final int restaurantId;
  final int? foodId;
  final Future<void> Function() loadMoreComments;
  final bool showStar;

  const CommentListSection({
    super.key,
    required this.restaurantId,
    this.foodId,
    required this.loadMoreComments,
    required this.showStar,
  });

  @override
  ConsumerState<CommentListSection> createState() => _CommentListSectionState();
}

class _CommentListSectionState extends ConsumerState<CommentListSection> {
  @override
  void initState() {
    super.initState();
    debugPrint("CommentListSection初始化");
  }

  @override
  void didUpdateWidget(final CommentListSection oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 只有当餐厅ID或食品ID改变时才重新加载
    if (oldWidget.restaurantId != widget.restaurantId ||
        oldWidget.foodId != widget.foodId) {
      debugPrint("餐厅ID或食品ID改变，重新加载评论");
      // 延迟一下再加载，避免状态冲突
      Future.microtask(() {
        if (mounted) {
          _loadComments();
        }
      });
    }
  }

  // 加载评论数据
  Future<void> _loadComments() async {
    // 检查组件是否仍然挂载
    if (!mounted) return;

    // 获取当前控制器状态
    final controller = ref.read(restaurantCommentControllerProvider.notifier);
    final currentState = ref.read(restaurantCommentControllerProvider);
    final selectedType = currentState.selectedType;

    debugPrint(
      "正在加载评论数据，类型: $selectedType, 餐厅ID: ${widget.restaurantId}, 食品ID: ${widget.foodId}",
    );

    // 调用控制器加载数据
    await controller.getCommentList(
      restaurantId: widget.restaurantId,
      foodId: widget.foodId,
      type: selectedType,
      isRefresh: true,
    );

    debugPrint("评论数据加载请求已发送");
  }

  @override
  Widget build(final BuildContext context) {
    // 使用Consumer监听评论数据状态
    return Consumer(
      builder: (final context, final ref, final _) {
        // 使用controller中的hasMoreComments方法判断是否有更多数据
        final controller =
            ref.read(restaurantCommentControllerProvider.notifier);
        final hasMoreData = controller.hasMoreComments();

        // 使用CustomRefreshIndicator实现下拉刷新和加载更多
        return CustomRefreshIndicator(
          onRefresh: _loadComments,
          enablePullDown: false,
          enablePullUp: true,
          hasMoreData: hasMoreData,
          onLoading: widget.loadMoreComments,
          child: CustomScrollView(
            slivers: [
              SliverOverlapInjector(
                handle:
                    NestedScrollView.sliverOverlapAbsorberHandleFor(context),
              ),
              // 评分信息
              if (widget.showStar)
                const SliverToBoxAdapter(
                  child: CommentStarSection(showStar: true),
                ),

              // 评论类型筛选
              SliverToBoxAdapter(
                child: CommentTypesSection(
                  restaurantId: widget.restaurantId,
                  foodId: widget.foodId,
                ),
              ),

              // 评论列表内容 - 修改为直接使用SliverBuilder
              _buildCommentListSection(ref),

              // 底部间距
              SliverToBoxAdapter(
                child: SizedBox(height: 20.h),
              ),
            ],
          ),
        );
      },
    );
  }

  // 构建评论列表部分
  Widget _buildCommentListSection(final WidgetRef ref) {
    // 监听评论数据状态
    final isLoading = ref.watch(
      restaurantCommentControllerProvider.select(
        (final state) => state.isLoading,
      ),
    );

    final hasError = ref.watch(
      restaurantCommentControllerProvider.select(
        (final state) => state.hasError,
      ),
    );

    final errorMessage = ref.watch(
      restaurantCommentControllerProvider.select(
        (final state) => state.errorMessage,
      ),
    );

    final commentItems = ref.watch(
      restaurantCommentControllerProvider.select(
        (final state) => state.commentData?.items,
      ),
    );

    // 加载状态
    if (isLoading && (commentItems == null || commentItems.isEmpty)) {
      return SliverToBoxAdapter(
        child: Container(
          height: 200.h,
          alignment: Alignment.center,
          child: const CircularProgressIndicator(),
        ),
      );
    }

    // 错误状态
    if (hasError) {
      return SliverToBoxAdapter(
        child: EmptyView(
          message: !kReleaseMode ? errorMessage : S.current.no_data,
          icon: Image.asset(
            "assets/images/no_comment.png",
            width: 80.w,
            height: 80.h,
          ),
          onRetry: _loadComments,
        ),
      );
    }

    // 空数据状态
    if (commentItems == null || commentItems.isEmpty) {
      return SliverToBoxAdapter(
        child: _buildEmptyComment(),
      );
    }

    // 评论列表 - 直接显示评论项，不处理加载更多（由CustomRefreshIndicator处理）
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (final context, final index) {
          if (index < commentItems.length) {
            return CommentItemWidget(comment: commentItems[index]);
          }

          // 额外项用于底部间距
          return SizedBox(height: 10.h);
        },
        childCount: commentItems.length + 1, // +1 为底部间距
      ),
    );
  }

  // 构建空评论提示
  Widget _buildEmptyComment() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 50.h),
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            "assets/images/no_comment.png",
            width: 80.w,
            height: 80.h,
          ),
          SizedBox(height: 15.h),
          Text(
            S.current.comment_no_data,
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF9E9E9E),
            ),
          ),
        ],
      ),
    );
  }
}
