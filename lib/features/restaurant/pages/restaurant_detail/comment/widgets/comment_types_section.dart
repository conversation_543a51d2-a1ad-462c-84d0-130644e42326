import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/restaurant/commnent_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/comment/restaurant_comment_controller.dart';

/// 评论类型筛选部分组件
class CommentTypesSection extends ConsumerWidget {
  final int restaurantId;
  final int? foodId;

  const CommentTypesSection({
    super.key,
    required this.restaurantId,
    this.foodId,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 直接从 RestaurantCommentController 读取评论类型列表和选中类型
    final types = ref.watch(
      restaurantCommentControllerProvider.select(
        (final state) => state.types,
      ),
    );

    // 类型数据为空时显示空布局
    if (types == null || types.isEmpty) {
      return const SizedBox.shrink();
    }

    return _buildTypesList(context, ref, types);
  }

  // 构建类型列表UI
  Widget _buildTypesList(final BuildContext context, final WidgetRef ref,
      final List<CommnentType> types) {
    return Container(
      width: double.infinity,
      padding:
          EdgeInsets.only(left: 10.w, right: 10.w, top: 10.h, bottom: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: const Color(0xFFEFF1F6),
            width: 0.5,
          ),
        ),
      ),
      child: Wrap(
        spacing: 10.w,
        runSpacing: 10.h,
        children: types.map((final type) {
          final typeValue = type.type ?? 0;

          // 读取当前控制器状态中的选中类型
          final selectedType = ref.watch(
            restaurantCommentControllerProvider.select(
              (final state) => state.selectedType,
            ),
          );
          final isSelected = typeValue == selectedType;

          return InkWell(
            onTap: () {
              ref
                  .read(restaurantCommentControllerProvider.notifier)
                  .changeCommentType(
                    restaurantId: restaurantId,
                    foodId: foodId,
                    type: typeValue,
                  );
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 10.w,
                vertical: 8.h,
              ),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.primary
                    : AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(25.r),
              ),
              child: Text(
                "${type.name}(${type.count})",
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: isSelected ? Colors.white : AppColors.primary,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
