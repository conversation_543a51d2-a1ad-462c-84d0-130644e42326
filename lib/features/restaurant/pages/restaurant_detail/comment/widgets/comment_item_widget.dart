import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/image_viewer.dart';
import 'package:user_app/core/widgets/star_rating.dart';
import 'package:user_app/data/models/restaurant/commnent_list_model.dart';
import 'package:user_app/generated/l10n.dart';

/// 评论项组件
class CommentItemWidget extends StatelessWidget {
  final CommentItems comment;
  final bool isFirst;
  final bool isLast;

  const CommentItemWidget({
    super.key,
    required this.comment,
    this.isFirst = false,
    this.isLast = false,
  });

  @override
  Widget build(final BuildContext context) {
    final isUg = Localizations.localeOf(context).languageCode == 'en';

    return Container(
      padding: EdgeInsets.all(15.r),
      decoration: BoxDecoration(
        color: Colors.white,
        border: !isLast
            ? Border(
                bottom: BorderSide(
                  color: const Color(0xFFEFF1F6),
                  width: 0.5,
                ),
              )
            : null,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户头像
          Container(
            width: 45.w,
            height: 45.w,
            margin: EdgeInsetsDirectional.only(end: 15.w, top: 2.h),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(5.r),
              child: CachedNetworkImage(
                imageUrl: comment.userAvatar ?? '',
                width: 45.w,
                height: 45.w,
                fit: BoxFit.cover,
                placeholder: (final context, final url) => Container(
                  color: Colors.grey[200],
                  width: 45.w,
                  height: 45.w,
                ),
                errorWidget: (final context, final url, final error) =>
                    Container(
                  width: 45.w,
                  height: 45.w,
                  color: Colors.grey[200],
                  child: Icon(
                    Icons.person,
                    size: 25.sp,
                    color: Colors.grey,
                  ),
                ),
              ),
            ),
          ),

          // 右侧内容区域
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 用户信息
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 用户名和时间
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          comment.userName?.isNotEmpty == true
                              ? comment.userName!
                              : '******',
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.black,
                          ),
                        ),
                        Text(
                          comment.createdAt ?? '',
                          style: TextStyle(
                            fontSize: 13.sp,
                            color: const Color(0xFF757575),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 8.h),

                    // 总体评分
                    Row(
                      children: [
                        Text(
                          S.current.evaluate_in_general,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: const Color(0xFF424242),
                          ),
                        ),
                        SizedBox(width: 5.w),
                        StarRating(
                          rating: (comment.star ?? 5).toDouble(),
                          size: 16,
                          hideTip: true,
                          gap: 2,
                        ),

                        const Spacer(),

                        // 食品名称标签
                        if (comment.foodName != null &&
                            comment.foodName!.isNotEmpty)
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFFEFF1F6),
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                            child: Text(
                              isUg
                                  ? comment.foodName!.length > 14
                                      ? '${comment.foodName!.substring(0, 14)}...'
                                      : comment.foodName!
                                  : comment.foodName!.length > 10
                                      ? '${comment.foodName!.substring(0, 10)}...'
                                      : comment.foodName!,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppColors.primary,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                      ],
                    ),

                    // 味道评分 (仅在与总体评分不同时显示)
                    if (comment.foodStar != null &&
                        comment.foodStar != comment.star) ...[
                      SizedBox(height: 5.h),
                      Row(
                        children: [
                          Text(
                            S.current.evaluate_taste,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: const Color(0xFF424242),
                            ),
                          ),
                          SizedBox(width: 5.w),
                          StarRating(
                            rating: (comment.foodStar ?? 5).toDouble(),
                            size: 16,
                            hideTip: true,
                            gap: 2,
                          ),
                        ],
                      ),
                    ],

                    // 包装评分 (仅在与总体评分不同时显示)
                    if (comment.boxStar != null &&
                        comment.boxStar != comment.star) ...[
                      SizedBox(height: 5.h),
                      Row(
                        children: [
                          Text(
                            S.current.evaluate_packing,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: const Color(0xFF424242),
                            ),
                          ),
                          SizedBox(width: 5.w),
                          StarRating(
                            rating: (comment.boxStar ?? 5).toDouble(),
                            size: 16,
                            hideTip: true,
                            gap: 2,
                          ),
                        ],
                      ),
                    ],
                  ],
                ),

                // 评论文本
                if (comment.text != null && comment.text!.isNotEmpty)
                  Padding(
                    padding: EdgeInsets.only(top: 12.h),
                    child: Text(
                      comment.text!,
                      style: TextStyle(
                        fontSize: 15.sp,
                        color: const Color(0xFF303030),
                      ),
                    ),
                  ),

                // 评论图片
                if (comment.images != null && comment.images!.isNotEmpty)
                  _buildCommentImages(context, comment.images!),

                // 商家回复
                if (comment.replies != null && comment.replies!.isNotEmpty)
                  _buildReplies(comment.replies!),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建评论图片
  Widget _buildCommentImages(
      final BuildContext context, final List<dynamic> images) {
    // 将动态列表转换为字符串列表
    final List<String> imageUrls = images
        .map((final img) => img.toString())
        .where((final url) => url.isNotEmpty)
        .toList();

    if (imageUrls.isEmpty) return const SizedBox.shrink();

    // 计算图片尺寸
    final screenWidth = MediaQuery.of(context).size.width;
    final containerWidth = screenWidth - 90.w; // 总宽度减去头像、边距等
    final gap = 8.w; // 图片间的间距

    // 根据图片数量决定布局
    double imgWidth;
    double imgHeight;
    BoxFit imgFit = BoxFit.cover;

    if (imageUrls.length == 1) {
      // 单张图片占满整行，高度为宽度的一半
      imgWidth = containerWidth;
      imgHeight = containerWidth * 0.5;
      imgFit = BoxFit.fitWidth;
    } else if (imageUrls.length == 2) {
      // 两张图片时，平分宽度
      imgWidth = (containerWidth - gap) / 2;
      imgHeight = imgWidth * 0.75; // 3:4的高宽比
    } else {
      // 三张及以上图片，每行最多3张
      imgWidth = (containerWidth - gap * 2) / 3; // 考虑两个间隙
      imgHeight = imgWidth * 0.75; // 3:4的高宽比
    }

    return Container(
      margin: EdgeInsets.only(top: 10.h),
      width: containerWidth,
      child: Wrap(
        spacing: gap,
        runSpacing: 8.h,
        children: imageUrls.asMap().entries.map((final entry) {
          final index = entry.key;
          final url = entry.value;

          return GestureDetector(
            onTap: () => showImageViewer(context,
                imageUrls: imageUrls, initialIndex: index),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4.r),
              child: CachedNetworkImage(
                imageUrl: url,
                width: imgWidth,
                height: imgHeight,
                fit: imgFit,
                placeholder: (final context, final url) => Container(
                  color: Colors.grey[200],
                  width: imgWidth,
                  height: imgHeight,
                ),
                errorWidget: (final context, final url, final error) =>
                    Container(
                  width: imgWidth,
                  height: imgHeight,
                  color: Colors.grey[200],
                  child: Icon(
                    Icons.image_not_supported,
                    size: 24.sp,
                    color: Colors.grey,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建回复列表
  Widget _buildReplies(final List<dynamic> replies) {
    if (replies.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: EdgeInsets.only(top: 10.h),
      padding: EdgeInsets.all(10.r),
      decoration: BoxDecoration(
        color: const Color(0xFFF0F0F0),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: List.generate(replies.length, (final index) {
          // 提取回复数据
          final reply = replies[index];
          final int replyType = reply["type"] is int ? reply["type"] : 1;
          final String replyText = reply["text"] is String ? reply["text"] : "";

          return Padding(
            padding:
                EdgeInsets.only(bottom: index < replies.length - 1 ? 8.h : 0),
            child: RichText(
              text: TextSpan(
                style: TextStyle(
                  fontFamily: AppConstants.mainFont,
                  fontSize: 14.sp,
                  color: const Color(0xFF616161),
                ),
                children: [
                  TextSpan(
                    text: replyType == 2
                        ? S.current.reply_mlz
                        : S.current.reply_res,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontFamily: AppConstants.mainFont,
                      color: AppColors.primary,
                      fontSize: 14.sp,
                    ),
                  ),
                  TextSpan(
                    text: "：$replyText",
                    style: TextStyle(
                      fontFamily: AppConstants.mainFont,
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }
}
