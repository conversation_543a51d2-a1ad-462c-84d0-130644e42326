// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'restaurant_comment_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$restaurantRepositoryHash() =>
    r'12fd2f6ee97dd1c20aae16e58e7791156210cc98';

/// 餐厅评论 Repository Provider
///
/// Copied from [restaurantRepository].
@ProviderFor(restaurantRepository)
final restaurantRepositoryProvider =
    AutoDisposeProvider<RestaurantRepository>.internal(
  restaurantRepository,
  name: r'restaurantRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$restaurantRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RestaurantRepositoryRef = AutoDisposeProviderRef<RestaurantRepository>;
String _$restaurantCommentControllerHash() =>
    r'905c73d8e8fd33e6c146311158b0361ab66df08f';

/// 餐厅评论控制器
///
/// Copied from [RestaurantCommentController].
@ProviderFor(RestaurantCommentController)
final restaurantCommentControllerProvider = AutoDisposeNotifierProvider<
    RestaurantCommentController, RestaurantCommentState>.internal(
  RestaurantCommentController.new,
  name: r'restaurantCommentControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$restaurantCommentControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RestaurantCommentController
    = AutoDisposeNotifier<RestaurantCommentState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
