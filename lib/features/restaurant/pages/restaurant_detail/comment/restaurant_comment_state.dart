import 'package:user_app/data/models/restaurant/commnent_list_model.dart';

/// 餐厅评论列表状态
class RestaurantCommentState {
  /// 构造函数
  const RestaurantCommentState({
    this.isLoading = false,
    this.isLoadingMore = false,
    this.hasError = false,
    this.errorMessage = '',
    this.commentData,
    this.currentPage = 1,
    this.selectedType = 1,
    this.canLoadMore = true,
    this.isFirstLoad = true,
    this.star,
    this.types,
  });

  /// 是否正在加载
  final bool isLoading;

  /// 是否正在加载更多
  final bool isLoadingMore;

  /// 是否有错误
  final bool hasError;

  /// 错误信息
  final String errorMessage;

  /// 评论数据
  final CommentListData? commentData;

  /// 当前页码
  final int currentPage;

  /// 当前选中的评论类型
  final int selectedType;

  /// 是否可以加载更多
  final bool canLoadMore;

  /// 是否是首次加载
  final bool isFirstLoad;

  /// 评论星级数据（单独存储，避免列表刷新时丢失）
  final CommentStar? star;

  /// 评论类型列表（单独存储，避免列表刷新时丢失）
  final List<CommnentType>? types;

  /// 创建新状态
  RestaurantCommentState copyWith({
    bool? isLoading,
    bool? isLoadingMore,
    bool? hasError,
    String? errorMessage,
    CommentListData? commentData,
    int? currentPage,
    int? selectedType,
    bool? canLoadMore,
    bool? isFirstLoad,
    CommentStar? star,
    List<CommnentType>? types,
  }) {
    return RestaurantCommentState(
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasError: hasError ?? this.hasError,
      errorMessage: errorMessage ?? this.errorMessage,
      commentData: commentData ?? this.commentData,
      currentPage: currentPage ?? this.currentPage,
      selectedType: selectedType ?? this.selectedType,
      canLoadMore: canLoadMore ?? this.canLoadMore,
      isFirstLoad: isFirstLoad ?? this.isFirstLoad,
      star: star ?? this.star,
      types: types ?? this.types,
    );
  }
}
