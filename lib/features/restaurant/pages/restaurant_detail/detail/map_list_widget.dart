import 'dart:io';
import 'dart:math';
import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:user_app/core/config/config.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/dialogs/animate_confirm_send_dialog.dart';
import 'package:user_app/generated/l10n.dart';

/// 地图导航选择弹窗组件
/// @param context 构建上下文
/// @param title 弹窗标题
/// @param startLatitude 起点纬度
/// @param startLongitude 起点经度
/// @param endLatitude 终点纬度
/// @param endLongitude 终点经度
Widget mapCallWidget(final context,
    {required final String title,
    required final num startLatitude,
    required final num startLongitude,
    required final num endLatitude,
    required final num endLongitude}) {
  return SafeArea(
    child: Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(10.w),
          topLeft: Radius.circular(10.w),
        ),
      ),
      width: double.infinity,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          Container(
            margin: EdgeInsets.symmetric(horizontal: 15.w, vertical: 12.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(title, style: TextStyle(fontSize: 18.sp)),
                InkWell(
                  child: Padding(
                      padding: EdgeInsets.all(5.w),
                      child: const Icon(Icons.close)),
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                )
              ],
            ),
          ),
          SingleChildScrollView(
            child: Container(
              child: Column(
                children: [
                  // 高德地图导航选项
                  Column(
                    children: [
                      ListTile(
                          onTap: () async {
                            _launchMapApp(
                              Platform.isIOS
                                  ? 'iosamap://route/plan/?sourceApplication=Mulazim&slat=$startLatitude&slon=$startLongitude&sname=我的位置&dlat=$endLatitude&dlon=$endLongitude&dname=终点&dev=0&t=2'
                                  : 'amapuri://route/plan/?sourceApplication=Mulazim&slat=$startLatitude&slon=$startLongitude&sname=我的位置&dlat=$endLatitude&dlon=$endLongitude&dname=终点&dev=0&t=2',
                              Platform.isIOS
                                  ? 'iosamap://'
                                  : 'com.autonavi.minimap',
                              S.current.gaode_navigation,
                              context,
                            );
                            Navigator.of(context).pop();
                          },
                          title: Text(S.current.gaode_navigation,
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16.sp)),
                          subtitle: Text(S.current.gaode_navigation_remark,
                              style: TextStyle(fontSize: 14.sp)),
                          trailing: Image.asset(
                            "assets/images/gaode_map.png",
                            fit: BoxFit.fitHeight,
                            width: 42.w,
                            height: 42.w,
                          )),
                      Divider(color: AppColors.grayColor),
                    ],
                  ),
                  // 百度地图导航选项
                  Column(
                    children: [
                      ListTile(
                          onTap: () async {
                            // 坐标系转换：高德坐标转百度坐标
                            List<double> start = gcj02ToBd09(
                                double.parse(startLatitude.toString()),
                                double.parse(startLongitude.toString()));
                            List<double> end = gcj02ToBd09(
                                double.parse(endLatitude.toString()),
                                double.parse(endLongitude.toString()));
                            _launchMapApp(
                              'baidumap://map/direction?origin=name:我的位置|latlng:${start[0]},${start[1]}&destination=name:目的地|latlng:${end[0]},${end[1]}&mode=walking',
                              Platform.isIOS
                                  ? 'baidumap://'
                                  : 'com.baidu.BaiduMap',
                              S.current.baidu_navigation,
                              context,
                            );
                            Navigator.of(context).pop();
                          },
                          title: Text(S.current.baidu_navigation,
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16.sp)),
                          subtitle: Text(S.current.baidu_navigation_remark,
                              style: TextStyle(fontSize: 14.sp)),
                          trailing: Image.asset(
                            "assets/images/baidu_map.png",
                            fit: BoxFit.fitHeight,
                            width: 42.w,
                            height: 42.w,
                          )),
                      Divider(color: AppColors.grayColor),
                    ],
                  ),
                  // 腾讯地图导航选项
                  Column(
                    children: [
                      ListTile(
                          onTap: () async {
                            _launchMapApp(
                              'qqmap://map/routeplan?type=walk&from=我的位置&fromcoord=$startLatitude,$startLongitude&to=目的地&tocoord=$endLatitude,$endLongitude&policy=1',
                              Platform.isIOS ? 'qqmap://' : 'com.tencent.map',
                              S.current.tengxun_navigation,
                              context,
                            );
                            Navigator.of(context).pop();
                          },
                          title: Text(S.current.tengxun_navigation,
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16.sp)),
                          subtitle: Text(S.current.tengxun_navigation_remark,
                              style: TextStyle(fontSize: 14.sp)),
                          trailing: Image.asset(
                            "assets/images/tengxun_map.png",
                            fit: BoxFit.fitHeight,
                            width: 42.w,
                            height: 42.w,
                          )),
                      SizedBox(
                        height: 20.w,
                      ),
                    ],
                  )
                ],
              ),
            ),
          )
        ],
      ),
    ),
  );
}

/// 坐标系转换相关常量
const double pi = 3.14159265358979324;
const double x_pi = pi * 3000.0 / 180.0;

/// 高德坐标系(GCJ-02)转百度坐标系(BD-09)
/// @param lat 纬度
/// @param lon 经度
/// @returns [纬度, 经度]
List<double> gcj02ToBd09(final double lat, final double lon) {
  double z = sqrt(lon * lon + lat * lat) + 0.00002 * sin(lat * x_pi);
  double theta = atan2(lat, lon) + 0.000003 * cos(lon * x_pi);
  double bdLon = z * cos(theta) + 0.0065;
  double bdLat = z * sin(theta) + 0.006;
  return [bdLat, bdLon];
}

/// 启动URL
Future<void> _launchURL(final String url) async {
  try {
    Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      throw 'Could not launch $url';
    }
  } catch (e) {
    throw 'Could not launch $url: $e';
  }
}


final _methodChannel = const MethodChannel('com.almas.dinner');
/// 启动地图应用
/// @param url 地图应用URL
/// @param packageName 应用包名
/// @param appName 应用名称
/// @param context 构建上下文
void _launchMapApp(final String url, final String packageName, final String appName, final BuildContext context) async {
  try {
    bool appIsInstalled = await _methodChannel.invokeMethod<dynamic>("isAppInstalled",{"packageName": packageName});
    if (appIsInstalled) {
      try {
        Uri uri = Uri.parse(url);
        bool launched = await launchUrl(uri, mode: LaunchMode.externalApplication);
        if (!launched) {
          _showInstallDialog(context, appName, packageName);
        }
      } catch (launchError) {
        _showInstallDialog(context, appName, packageName);
      }
    } else {
      _showInstallDialog(context, appName, packageName);
    }
  } catch (e) {
    // 如果检测失败，直接尝试启动
    try {
      Uri uri = Uri.parse(url);
      bool launched = await launchUrl(uri, mode: LaunchMode.externalApplication);
      if (!launched) {
        _showInstallDialog(context, appName, packageName);
      }
    } catch (launchError) {
      _showInstallDialog(context, appName, packageName);
    }
  }
}

/// 显示安装地图应用提示对话框
void _showInstallDialog(final BuildContext context, final String appName,
    final String packageName) {
  showGeneralDialog(
    transitionDuration: const Duration(milliseconds: 100),
    transitionBuilder: (final BuildContext context,
        final Animation<double> animation,
        final Animation<double> secondaryAnimation,
        final Widget child) {
      return ScaleTransition(scale: animation, child: child);
    },
    context: context,
    pageBuilder: (final BuildContext context, final Animation<double> animation,
        final Animation<double> secondaryAnimation) {
      return ConfirmSendDialog(
        content: '$appName${S.current.do_you_install}',
        screenWidth: MediaQuery.of(context).size.width,
      );
    },
  ).then((final value) async {
    if (value != null) {
      String confirmState = value.toString() ?? '';
      if (confirmState == 'yes') {
        // 根据平台打开不同的应用商店
        if (Platform.isIOS) {
          switch (packageName) {
            case "iosamap://":
              lunchAppStore(context, Config.mapMpdel.amap);
              break;
            case "baidumap://":
              lunchAppStore(context, Config.mapMpdel.baiduMap);
              break;
            case "qqmap://":
              lunchAppStore(context, Config.mapMpdel.qqMap);
              break;
          }
        } else {
          // _launchURL('https://app.mi.com/details?id=$packageName');
          _launchURL('market://details?id=$packageName');
        }
      }
    }
  });
}

/// 启动App Store
/// @param context 构建上下文
/// @param url App Store URL
void lunchAppStore(final context, final String url) async {
  Uri appUrl = Uri.parse(url);
  if (await canLaunchUrl(appUrl)) {
    launchUrl(appUrl, mode: LaunchMode.platformDefault);
  } else {
    print("无法启动地图应用");
    BotToast.showText(text: "无法启动应用");
  }
}
