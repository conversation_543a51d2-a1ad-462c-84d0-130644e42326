import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/generated/l10n.dart';

/// 套餐美食列表组件
/// 用于显示套餐包含的美食项目列表
class ComboFoodListWidget extends ConsumerWidget {
  /// 套餐美食项目列表
  final List<ComboFoodItem> comboFoodItems;

  /// 最大显示数量，默认10个
  final int maxDisplayCount;

  /// 是否显示背景色，默认true
  final bool showBackground;

  /// 构造函数
  const ComboFoodListWidget({
    super.key,
    required this.comboFoodItems,
    this.maxDisplayCount = 10,
    this.showBackground = true,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    if (comboFoodItems.isEmpty) {
      return const SizedBox.shrink();
    }

    // 限制显示数量
    final displayItems = comboFoodItems.length > maxDisplayCount
        ? comboFoodItems.take(maxDisplayCount).toList()
        : comboFoodItems;

    return Container(
      margin: EdgeInsets.only(bottom: 12.w),
      padding: EdgeInsets.all(10.w),
      decoration: showBackground
          ? BoxDecoration(
              borderRadius: BorderRadius.circular(10.w),
            )
          : null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 套餐美食垂直列表
          ...List.generate(
            displayItems.length,
            (final index) {
              final comboFoodItem = displayItems[index];
              return Container(
                margin: EdgeInsets.only(
                  bottom: index == displayItems.length - 1 ? 0 : 8.w,
                ),
                child: _buildComboFoodItemCard(comboFoodItem, ref),
              );
            },
          ),
        ],
      ),
    );
  }

  /// 构建单个套餐美食卡片
  Widget _buildComboFoodItemCard(
    final ComboFoodItem comboFoodItem,
    final WidgetRef ref,
  ) {
    return Column(
      children: [
        // 主要内容：水平布局（图片在左，信息在右）
        Row(
          children: [
            // 美食图片
            Container(
              width: 55.w, // 110rpx / 2
              height: 55.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.w),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8.w),
                child: PrefectImage(
                  imageUrl: comboFoodItem.restaurantFood?.image ?? '',
                  fit: BoxFit.cover,
                  width: 55.w,
                  height: 55.w,
                ),
              ),
            ),
            SizedBox(width: 8.w), // 16rpx / 2
            // 美食内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 名称和价格行
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 美食名称
                      Expanded(
                        child: Text(
                          comboFoodItem.restaurantFood?.name ?? '',
                          style: TextStyle(
                            fontSize: 16.sp, // 32rpx / 2
                            color: Color(0xff333333),
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      // 价格
                      Text(
                        '¥${FormatUtil.formatAmount(comboFoodItem.restaurantFood?.price ?? 0)}',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Color(0xff333333),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 5.w), // 10rpx / 2
                  // 数量信息
                  Row(
                    children: [
                      Text(
                        S.current.count,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Color(0xff8D8C8C),
                        ),
                      ),
                      SizedBox(width: 7.5.w), // 15rpx / 2
                      Text(
                        '${comboFoodItem.count}x',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Color(0xff8D8C8C),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        // 规格信息（如果有）
        if (_getSpecOptionsName(comboFoodItem).isNotEmpty)
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(top: 5.w), // 10rpx / 2
            padding: EdgeInsets.all(4.w), // 8rpx / 2
            decoration: BoxDecoration(
              color: Color(0xffEFF1F6),
              borderRadius: BorderRadius.circular(5.w), // 10rpx / 2
            ),
            child: Text(
              _getSpecOptionsName(comboFoodItem),
              style: TextStyle(
                fontSize: 14.sp, // 28rpx / 2
                color: Color(0xff8D8C8C),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
      ],
    );
  }

  /// 获取规格选项名称
  String _getSpecOptionsName(final ComboFoodItem comboFoodItem) {
    if (comboFoodItem.selectedSpec?.specOptions != null) {
      return comboFoodItem.selectedSpec!.specOptions!
          .map((final option) => option.name ?? '')
          .where((final name) => name.isNotEmpty)
          .join("｜");
    }
    return '';
  }
}
