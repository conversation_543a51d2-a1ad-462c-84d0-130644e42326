import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 食品数量显示组件
class FoodCountWidget extends StatelessWidget {
  final int foodCount;

  const FoodCountWidget({
    super.key,
    required this.foodCount,
  });

  @override
  Widget build(BuildContext context) {
    return foodCount > 0
        ? Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w),
            alignment: Alignment.center,
            child: Text(
              foodCount.toString(),
              style: TextStyle(fontSize: 16.sp),
            ),
          )
        : const SizedBox.shrink();
  }
}
