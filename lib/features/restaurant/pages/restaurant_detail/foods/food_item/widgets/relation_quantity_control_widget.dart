import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/cart_animation_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/common_widgets.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/food_count_widget.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';

/// 关联食品的数量控制组件（确保不会被标记为直接添加）
class RelationQuantityControlWidget extends ConsumerWidget {
  final int foodCount;
  final int? foodId;
  final Food? food;
  final Function(int, int?, Food?, [BuildContext?])? onRelationAddFood;
  final Function(int?)? onRemoveFood;

  const RelationQuantityControlWidget({
    super.key,
    required this.foodCount,
    this.foodId,
    this.food,
    this.onRelationAddFood,
    this.onRemoveFood,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final isUg = Localizations.localeOf(context).languageCode == 'en';

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 5.w),
      child: Directionality(
        textDirection: isUg ? TextDirection.ltr : TextDirection.rtl,
        child: Container(
          alignment: Alignment.center,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 加号按钮
              addRemoveIcon(
                type: CartType.add,
                onTap: (final context) {
                  _playAddToCartAnimation(context);
                  onRelationAddFood?.call(foodCount, foodId, food, context);
                },
              ),
              // 商品数量显示
              FoodCountWidget(foodCount: foodCount),
              // 减号按钮
              if (foodCount > 0)
                addRemoveIcon(
                  type: CartType.remove,
                  onTap: (final _) => onRemoveFood?.call(foodId),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 播放加入购物车动画
  void _playAddToCartAnimation(final BuildContext? btnContext) {
    if (btnContext == null) return;

    // 获取加号按钮的位置
    final RenderBox? renderBox = btnContext.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final Offset startPosition = renderBox.localToGlobal(
      Offset(renderBox.size.width / 2, renderBox.size.height / 2),
    );

    // 获取购物车图标的位置
    final Offset endPosition = getWidgetGlobalPosition(globalCartIconKey);
    if (endPosition == Offset.zero) return;

    // 获取按钮的颜色
    const Color buttonColor = Colors.red;

    // 启动动画
    CartAnimationController.startAnimation(
      btnContext,
      startPosition,
      Offset(endPosition.dx + 21.w, endPosition.dy + 21.h), // 调整到购物车图标中心
      buttonColor,
    );
  }
}
