import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/generated/l10n.dart';

/// 销量和数量信息组件
class SalesQuantityInfoWidget extends StatelessWidget {
  final Food? foodItem;

  const SalesQuantityInfoWidget({
    super.key,
    required this.foodItem,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          '${S.current.sales_count} :${foodItem?.monthOrderCount}',
          style: TextStyle(
            fontSize: 13.sp,
            color: AppColors.textSecondaryColor,
          ),
        ),
        SizedBox(width: 10.w),

        /// 显示优惠数量
        if ((foodItem?.seckillActive == null || foodItem?.seckillActive == 0) &&
            (foodItem?.maxOrderCount ?? 0) > 0)
          Text(
            '${S.current.discount_max_count} :${foodItem?.maxOrderCount ?? 0}',
            style: TextStyle(
              fontSize: 13.sp,
              color: AppColors.textSecondaryColor,
            ),
          ),

        /// 显示秒杀数量
        if ((foodItem?.seckillMaxOrderCount ?? 0) > 0 &&
            (foodItem?.seckillActive == 1))
          Text(
            '${S.current.seckill_total_count} :${foodItem?.seckillMaxOrderCount}',
            style: TextStyle(
              fontSize: 13.sp,
              color: AppColors.textSecondaryColor,
            ),
          ),
      ],
    );
  }
}
