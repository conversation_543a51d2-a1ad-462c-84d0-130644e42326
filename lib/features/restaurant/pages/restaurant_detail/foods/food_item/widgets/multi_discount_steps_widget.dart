import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/generated/l10n.dart';

/// 多份打折步骤显示组件
class MultiDiscountStepsWidget extends ConsumerWidget {
  final Food? foodItem;
  final int foodCount;

  const MultiDiscountStepsWidget({
    super.key,
    required this.foodItem,
    required this.foodCount,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 检查是否有多份打折且有步骤
    if (foodItem?.multiDiscountId == null ||
        foodItem?.multiDiscountSteps == null ||
        foodItem!.multiDiscountSteps!.isEmpty) {
      return SizedBox.shrink();
    }

    // 从购物车获取实时的步骤状态
    final cartItems = ref.watch(shoppingCartProvider);
    final cartItem = cartItems.firstWhere(
      (final item) => item.id == foodItem!.id,
      orElse: () => SelectFoodItem(count: 0),
    );

    // 使用购物车中的步骤数据，如果没有则使用原始数据
    final steps = cartItem.multiDiscountSteps ?? foodItem!.multiDiscountSteps!;
    final currentAdvancePrice = cartItem.multiDiscountAdvancePrice ??
        foodItem!.multiDiscountAdvancePrice;

    final textDirection = Directionality.of(context);
    final bool isRTL = textDirection == TextDirection.rtl;

    // 过滤显示的步骤
    // 1. 跳过第一个步骤（index 0）
    // 2. 如果isShow为false，则不显示
    // 3. 如果最后一份价格跟原价一样，则不显示
    final visibleSteps = steps
        .asMap()
        .entries
        .where((final entry) {
          final index = entry.key;
          final step = entry.value;

          // 跳过第一个步骤（index 0）
          if (index == 0) return false;

          // 如果是价格跟原价一样，则不显示
          if (step.price == foodItem?.price) {
            return false;
          }

          return true;
        })
        .map((final entry) => entry.value)
        .toList();

    if (visibleSteps.isEmpty) {
      return SizedBox.shrink();
    }

    return Container(
      margin:
          EdgeInsets.only(left: 7.5.w, right: 7.5.w, top: 10.h, bottom: 10.h),
      child: Column(
        children:
            _buildStepRows(visibleSteps, isRTL, currentAdvancePrice, foodCount),
      ),
    );
  }

  /// 构建步骤行列表，每行显示两列
  List<Widget> _buildStepRows(
    final List<MultiDiscountStep> steps,
    final bool isRTL,
    final num? currentAdvancePrice,
    final int currentCount,
  ) {
    List<Widget> rows = [];
    for (int i = 0; i < steps.length; i += 2) {
      List<Widget> rowChildren = [];

      // 第一列
      if (i < steps.length) {
        rowChildren.add(
          Expanded(
            child: _buildStepItem(
              steps[i],
              isRTL,
              currentAdvancePrice,
              currentCount,
            ),
          ),
        );
      }

      // 第二列
      if (i + 1 < steps.length) {
        rowChildren.add(
          Expanded(
            child: _buildStepItem(
              steps[i + 1],
              isRTL,
              currentAdvancePrice,
              currentCount,
            ),
          ),
        );
      } else {
        // 如果只有一个元素，添加空白占位
        rowChildren.add(Expanded(child: SizedBox()));
      }

      rows.add(Row(children: rowChildren));

      // 添加行间距（除了最后一行）
      if (i + 2 < steps.length) {
        rows.add(SizedBox(height: 4.h));
      }
    }
    return rows;
  }

  /// 构建单个步骤项
  Widget _buildStepItem(
    final MultiDiscountStep step,
    final bool isRTL,
    final num? currentAdvancePrice,
    final int currentCount,
  ) {
    // 按照微信小程序的逻辑判断步骤状态
    // 1. 已完成步骤：当前数量大于该步骤数量时，显示为已完成（灰色）
    final bool isCompleted = currentCount >= (step.number ?? 0);

    // 2. 当前激活步骤：下一个即将达到的步骤（橙色）
    // 当前显示价格等于该步骤价格，且该步骤未完成时，为当前激活步骤
    final bool isCurrentStep = !isCompleted &&
        step.price == currentAdvancePrice &&
        currentCount < (step.number ?? 0);

    // 确定步骤的颜色状态
    Color stepColor;
    Color borderColor;
    if (isCurrentStep) {
      // 当前激活步骤（下一个即将达到的步骤）：橙色
      stepColor = Color(0xFFFF7430);
      borderColor = Color(0xFFFF7430);
    } else if (isCompleted) {
      // 已完成步骤：灰色
      stepColor = Color(0xFFA39791);
      borderColor = Color(0xFFA39791);
    } else {
      // 未达到步骤：默认颜色
      stepColor = AppColors.textPrimaryColor;
      borderColor = AppColors.textPrimaryColor;
    }

    // 获取步骤名称文案
    String stepName = _getStepName(step.number ?? 0);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 步骤数字圆圈
          Container(
            width: 14.w,
            height: 14.h,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: borderColor, width: 1),
            ),
            child: Center(
              child: Text(
                '${step.number ?? 0}',
                style: TextStyle(
                  fontSize: 10.sp,
                  color: stepColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          SizedBox(width: 1.w),
          // 步骤名称
          Container(
            padding: EdgeInsets.only(right: 4.w),
            child: Text(
              stepName,
              style: TextStyle(
                fontSize: 14.sp,
                color: (isCompleted ? stepColor : AppColors.textPrimaryColor),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 10.w),

          // 价格
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${step.price ?? 0}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Color(0xFFFF7430),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '¥',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Color(0xFFFF7430),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 根据步骤数字获取对应的文案
  String _getStepName(final int number) {
    switch (number) {
      case 1:
        return S.current.multi_discount_step_1;
      case 2:
        return S.current.multi_discount_step_2;
      case 3:
        return S.current.multi_discount_step_3;
      case 4:
        return S.current.multi_discount_step_4;
      case 5:
        return S.current.multi_discount_step_5;
      default:
        return '第${number}份';
    }
  }
}
