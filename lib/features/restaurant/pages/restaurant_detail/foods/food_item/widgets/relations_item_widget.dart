import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/food_image_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/relation_header_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/relation_quantity_control_widget.dart';

/// 关联商品项组件
class RelationsItemWidget extends ConsumerWidget {
  final Food relations;
  final VoidCallback? onHideRelations;
  final Function(Food)? onGetItemCount;
  final Function(int, int?, Food?, [BuildContext?])? onRelationAddFood;
  final Function(int?)? onRemoveFood;

  const RelationsItemWidget({
    super.key,
    required this.relations,
    this.onHideRelations,
    this.onGetItemCount,
    this.onRelationAddFood,
    this.onRemoveFood,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final foodCount = onGetItemCount?.call(relations) ?? 0;

    return Container(
      padding: EdgeInsets.all(5.w),
      child: Column(
        children: [
          RelationHeaderWidget(onHideRelations: onHideRelations),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.r),
            ),
            margin: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
            child: Row(
              children: [
                FoodImageWidget(imageUrl: relations.image ?? ""),
                SizedBox(width: 10.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        relations.name ?? "",
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 15.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // 关联商品也使用统一的价格显示方式
                          RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: '¥',
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red,
                                  ),
                                ),
                                TextSpan(
                                  text: _getCurrentPrice(relations),
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          RelationQuantityControlWidget(
                            foodCount: foodCount,
                            foodId: relations.id,
                            food: relations,
                            onRelationAddFood: onRelationAddFood,
                            onRemoveFood: onRemoveFood,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 获取当前显示价格
  String _getCurrentPrice(final Food food) {
    // 优先使用优惠价格
    if (food.prefrentialPrice != null &&
        food.maxOrderCount != null &&
        food.maxOrderCount! > 0) {
      return food.prefrentialPrice.toString();
    }

    // 默认显示原价
    return food.price?.toString() ?? '0';
  }
}
