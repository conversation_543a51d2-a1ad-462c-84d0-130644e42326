import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/generated/l10n.dart';

/// 关联商品头部组件
class RelationHeaderWidget extends StatelessWidget {
  final VoidCallback? onHideRelations;

  const RelationHeaderWidget({
    super.key,
    this.onHideRelations,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 5.w),
      child: Row(
        children: [
          InkWell(
            onTap: onHideRelations,
            child: Icon(
              Icons.cancel_outlined,
              color: Colors.white,
              size: 28.w,
            ),
          ),
          SizedBox(width: 5.w),
          Text(
            S.current.may_be_you_like,
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
