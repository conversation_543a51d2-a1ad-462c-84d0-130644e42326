import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/relations_item_widget.dart';

/// 关联商品容器组件
class RelationsContainerWidget extends ConsumerWidget {
  final bool relationsExists;
  final bool showRelations;
  final List<Food>? relations;
  final Animation<double> slideAnimation;
  final Animation<Offset> showAnimation;
  final Animation<Offset> hideAnimation;
  final VoidCallback? onHideRelations;
  final Function(Food)? onGetItemCount;
  final Function(int, int?, Food?, [BuildContext?])? onRelationAddFood;
  final Function(int?)? onRemoveFood;

  const RelationsContainerWidget({
    super.key,
    required this.relationsExists,
    required this.showRelations,
    this.relations,
    required this.slideAnimation,
    required this.showAnimation,
    required this.hideAnimation,
    this.onHideRelations,
    this.onGetItemCount,
    this.onRelationAddFood,
    this.onRemoveFood,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final Widget relationsContent = relationsExists
        ? Column(
            children: relations!
                .map(
                  (final relations) => RelationsItemWidget(
                    relations: relations,
                    onGetItemCount: onGetItemCount,
                    onHideRelations: onHideRelations,
                    onRelationAddFood: onRelationAddFood,
                    onRemoveFood: onRemoveFood,
                  ),
                )
                .toList(),
          )
        : SizedBox.shrink();

    return AnimatedSize(
      duration: Duration(milliseconds: 250),
      curve: Curves.easeOut,
      child: Container(
        height: showRelations && relationsExists ? null : 0,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(),
        child: FadeTransition(
          opacity: slideAnimation,
          child: AnimatedSwitcher(
            duration: Duration(milliseconds: 250),
            child: showRelations
                ? SlideTransition(
                    position: showAnimation,
                    child: relationsContent,
                  )
                : SlideTransition(
                    position: hideAnimation,
                    child: relationsContent,
                  ),
          ),
        ),
      ),
    );
  }
}
