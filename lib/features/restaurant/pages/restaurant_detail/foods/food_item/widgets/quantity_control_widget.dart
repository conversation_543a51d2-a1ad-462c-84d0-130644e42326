import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/cart_animation_widget.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/min_count_button_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/common_widgets.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/food_count_widget.dart';

/// 数量控制组件
class QuantityControlWidget extends ConsumerWidget {
  final int foodCount;
  final int? foodId;
  final Food? food;
  final bool relationsExists;
  final bool? isResting;
  final Function(int, int?, Food?, bool, [BuildContext?])? onAdd;
  final Function(int?)? onRemove;
  final Function(Food)? onAddMinCount;
  final Function(Food)? onSpecSelect; // 规格选择回调

  const QuantityControlWidget({
    super.key,
    required this.foodCount,
    this.foodId,
    this.food,
    this.relationsExists = false,
    this.isResting,
    this.onAdd,
    this.onRemove,
    this.onAddMinCount,
    this.onSpecSelect,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 检查商品是否已售馨
    final bool isSoldOut = food?.state == 2;
    // 检查餐厅是否休息中
    final bool isRestingState = isResting ?? false;

    // 如果已售馨或餐厅休息中，显示已售馨文字
    if (isSoldOut || isRestingState) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 3.h),
        child: Text(
          S.current.sell_done,
          style: TextStyle(
            fontSize: 15.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.redColor,
          ),
        ),
      );
    }

    // 根据food_type显示不同的控件
    final int foodType = food?.foodType ?? 0;

    // 规格美食(food_type == 1)显示规格选择按钮
    if (foodType == 1) {
      return _buildSpecButton(context);
    }

    // 套餐美食(food_type == 2)显示套餐按钮
    if (foodType == 2) {
      return _buildComboButton(context);
    }

    // 普通美食(food_type == 0)显示普通的加减按钮
    return _buildNormalQuantityControl(context);
  }

  /// 构建规格选择按钮
  Widget _buildSpecButton(final BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: GestureDetector(
        onTap: () => onSpecSelect?.call(food!),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Container(
              margin: EdgeInsets.symmetric(horizontal: 5.w),
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Text(
                S.current.spec_btn,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            // 如果有数量，显示数量
            if (foodCount > 0)
              Positioned(
                top: -7.h,
                child: Container(
                  width: 15.w,
                  height: 15.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: AppColors.redColor,
                    borderRadius: BorderRadius.circular(15.r),
                  ),
                  child: Text(
                    foodCount.toString(),
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 构建套餐按钮
  Widget _buildComboButton(final BuildContext context) {
    if (foodCount == 0) {
      return GestureDetector(
        onTap: () {
          _playAddToCartAnimation(context);
          onAdd?.call(foodCount, foodId, food, relationsExists, context);
        },
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 3.h),
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Text(
            S.current.comboTitle, // 需要添加这个国际化字符串
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      );
    } else {
      return _buildQuantityControls(context);
    }
  }

  /// 构建普通数量控制
  Widget _buildNormalQuantityControl(final BuildContext context) {
    // 如果有最小购买限制且当前数量为0，显示最小购买数量按钮
    if (food != null &&
        food!.minCount != null &&
        food!.minCount! > 1 &&
        foodCount == 0) {
      return MinCountButtonWidget(
        food: food,
        foodCount: foodCount,
        isResting: isResting,
        onTap: () => onAddMinCount?.call(food!),
      );
    }

    final isUg = Localizations.localeOf(context).languageCode == 'en';

    // 普通数量控制
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 5.w),
      child: Directionality(
        textDirection: isUg ? TextDirection.ltr : TextDirection.rtl,
        child: Container(
          alignment: Alignment.center,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 加号按钮
              addRemoveIcon(
                type: CartType.add,
                onTap: (final context) {
                  _playAddToCartAnimation(context);
                  onAdd?.call(
                      foodCount, foodId, food, relationsExists, context);
                },
              ),
              // 商品数量显示
              FoodCountWidget(foodCount: foodCount),
              // 减号按钮
              if (foodCount > 0)
                addRemoveIcon(
                  type: CartType.remove,
                  onTap: (final _) => onRemove?.call(foodId),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建数量控制按钮（用于套餐）
  Widget _buildQuantityControls(final BuildContext context) {
    final isUg = Localizations.localeOf(context).languageCode == 'en';

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 5.w),
      child: Directionality(
        textDirection: isUg ? TextDirection.ltr : TextDirection.rtl,
        child: Container(
          alignment: Alignment.center,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 加号按钮
              addRemoveIcon(
                type: CartType.add,
                onTap: (final context) {
                  _playAddToCartAnimation(context);
                  onAdd?.call(
                      foodCount, foodId, food, relationsExists, context);
                },
              ),
              // 商品数量显示
              FoodCountWidget(foodCount: foodCount),
              // 减号按钮
              if (foodCount > 0)
                addRemoveIcon(
                  type: CartType.remove,
                  onTap: (final _) => onRemove?.call(foodId),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 播放加入购物车动画
  void _playAddToCartAnimation(final BuildContext? btnContext) {
    if (btnContext == null) return;

    // 获取加号按钮的位置
    final RenderBox? renderBox = btnContext.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final Offset startPosition = renderBox.localToGlobal(
      Offset(renderBox.size.width / 2, renderBox.size.height / 2),
    );

    // 获取购物车图标的位置
    final Offset endPosition = getWidgetGlobalPosition(globalCartIconKey);
    if (endPosition == Offset.zero) return;

    // 获取按钮的颜色
    const Color buttonColor = Colors.red;

    // 启动动画
    CartAnimationController.startAnimation(
      btnContext,
      startPosition,
      Offset(endPosition.dx + 21.w, endPosition.dy + 21.h), // 调整到购物车图标中心
      buttonColor,
    );
  }
}
