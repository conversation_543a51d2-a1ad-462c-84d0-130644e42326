import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:async';

import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/multi_discount_steps_widget.dart';
import 'package:user_app/features/restaurant/providers/food_detail_provider.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/food_detail_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/spec_modal_widget.dart';

// 导入组件化文件
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/index.dart';

/// 食品扩展类，为Food类添加UI需要的属性
extension FoodExtension on Food {
  /// 是否高亮显示
  bool get highLight => false;

  /// 满减活动标签
  List<dynamic>? get reductionFoodsTags => null;

  /// 满减活动状态
  int? get reductionActive => null;

  /// 来自父级的满减活动值
  int? get reductionActiveValueFromParent => null;

  /// 商品数量
  int? get count => null;
}

/// 存储用户直接点击添加按钮添加的美食ID
class DirectlyAddedFoods {
  /// 使用静态集合存储直接添加的美食ID
  static Set<int> directlyAddedIds = <int>{};

  /// 添加一个直接添加的美食ID
  static void add(final int? foodId) {
    if (foodId != null) {
      directlyAddedIds.add(foodId);
    }
  }

  /// 检查一个美食ID是否是直接添加的
  static bool contains(final int? foodId) {
    return foodId != null && directlyAddedIds.contains(foodId);
  }

  /// 当美食数量变为0时移除ID
  static void remove(final int? foodId) {
    if (foodId != null) {
      directlyAddedIds.remove(foodId);
    }
  }
}

/// 餐厅美食（商品）列表项
/// style: 1 - 餐厅样式，2 - 超市便利样式，3 - 便利店样式
class RestaurantFoodsItem extends ConsumerStatefulWidget {
  /// 餐厅页面样式
  final int? style;

  /// 餐厅美食数据
  final Food? foodItem;

  /// 关联的美食
  final List<Food>? relations;

  ///餐厅id
  final int? restaurantId;

  ///是否休息
  final bool? isResting;

  /// 从食品ID列表
  final List<dynamic>? fromFoodIds;

  /// 构造方法
  const RestaurantFoodsItem({
    super.key,
    required this.style,
    required this.foodItem,
    required this.relations,
    required this.restaurantId,
    required this.isResting,
    this.fromFoodIds,
  });

  @override
  ConsumerState<RestaurantFoodsItem> createState() =>
      _RestaurantFoodsItemState();
}

class _RestaurantFoodsItemState extends ConsumerState<RestaurantFoodsItem>
    with SingleTickerProviderStateMixin {
  // 状态变量
  bool _showRelations = false;
  bool _manuallyHidden = false;
  int? _lastFoodCount;

  // 倒计时相关变量
  String _hour = '00';
  String _minute = '00';
  String _second = '00';
  int? _seckillTimeValue; // 可变的秒杀倒计时值

  // 动画相关
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<Offset> _showAnimation;
  late Animation<Offset> _hideAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    // 初始化秒杀时间（如果商品是秒杀商品且未开始）
    _initSeckillTime();
  }

  /// 初始化所有动画
  void _initAnimations() {
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 250),
    );

    _slideAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );

    _showAnimation = Tween<Offset>(
      begin: Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ),
    );

    _hideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(0, -0.3),
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
  }

  /// 初始化秒杀时间
  void _initSeckillTime() {
    final foodItem = widget.foodItem;
    // 检查是否是秒杀商品且未开始
    if (foodItem != null &&
        foodItem.seckillId != null &&
        foodItem.seckillId! > 0 &&
        foodItem.seckillActive != null &&
        foodItem.seckillActive == 0 &&
        foodItem.seckillTime != null &&
        foodItem.seckillTime! > 0) {
      // 初始化秒杀倒计时值
      _seckillTimeValue = foodItem.seckillTime;

      // 格式化秒杀时间
      _formatSeckillTime(_seckillTimeValue!);
    }
  }

  /// 格式化秒杀时间
  void _formatSeckillTime(final int seconds) {
    if (mounted) {
      setState(() {
        int h = seconds ~/ 3600;
        int m = (seconds % 3600) ~/ 60;
        int s = seconds % 60;

        _hour = h.toString().padLeft(2, '0');
        _minute = m.toString().padLeft(2, '0');
        _second = s.toString().padLeft(2, '0');
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 显示关联商品，带动画效果
  void _showRelationsWithAnimation() {
    setState(() {
      _showRelations = true;
      _manuallyHidden = false;
    });
    _animationController.forward(from: 0.0);
  }

  /// 隐藏关联商品
  void _hideRelations() {
    setState(() {
      _showRelations = false;
      _manuallyHidden = true;
    });
  }

  /// 获取购物车中主商品的数量
  int _getFoodCount() {
    return _getItemCount(widget.foodItem);
  }

  /// 获取购物车中指定商品的数量
  /// 对于规格商品，需要将同一个美食的所有不同规格组合的数量相加
  /// 与微信小程序逻辑一致：遍历购物车列表，如果商品id与当前商品id相同，则累加数量
  int _getItemCount(final Food? food) {
    if (food == null) return 0;
    final cartItems = ref.watch(shoppingCartProvider);

    try {
      // 对于规格商品（food_type == 1），需要累加所有相同id的商品数量
      // 对于普通商品，直接查找匹配的商品
      if (food.foodType == 1) {
        return cartItems
            .where((final item) => item.id == food.id)
            .fold(0, (final sum, final item) => sum + (item.count ?? 0));
      } else {
        // 普通商品：直接查找匹配的商品
        final item = cartItems.firstWhere(
          (final item) => item.id == food.id,
          orElse: () => SelectFoodItem(count: 0),
        );
        return item.count ?? 0;
      }
    } catch (e) {
      debugPrint('Error in _getItemCount: $e');
      return 0;
    }
  }

  @override
  Widget build(final BuildContext context) {
    final foodCount = _getFoodCount();
    final relationsExists =
        widget.relations != null && widget.relations!.isNotEmpty;

    _handleFoodCountChange(foodCount);
    _handleRelationsVisibility(foodCount, relationsExists);

    // 判断是否有多粉折扣
    final bool hasMultiDiscount = widget.foodItem?.multiDiscountId != null &&
        (widget.foodItem?.multiDiscountSteps?.isNotEmpty ?? false);

    // 分开构建各个部分，不共用容器装饰
    return Container(
      decoration: _getContainerDecoration(hasMultiDiscount),
      child: Column(
        children: [
          MainFoodItemWidget(
            style: widget.style,
            foodItem: widget.foodItem,
            foodCount: foodCount,
            relationsExists: relationsExists,
            fromFoodIds: widget.fromFoodIds,
            hour: _hour,
            minute: _minute,
            second: _second,
            isResting: widget.isResting,
            onTap: () => _openFoodDetail(),
            onAdd: _handleAddFood,
            onRemove: _handleRemoveFood,
            onAddMinCount: _handleAddMinCountFood,
            onSpecSelect: _handleSpecSelect,
          ),
          // 关联商品 - 带单独的背景
          if (!hasMultiDiscount)
            RelationsContainerWidget(
              relationsExists: relationsExists,
              showRelations: _showRelations,
              relations: widget.relations,
              slideAnimation: _slideAnimation,
              showAnimation: _showAnimation,
              hideAnimation: _hideAnimation,
              onHideRelations: _hideRelations,
              onGetItemCount: _getItemCount,
              onRelationAddFood: _handleRelationAddFood,
              onRemoveFood: _handleRemoveFood,
            ),
          // 多份打折步骤显示 - 集成到主容器内部
          if (hasMultiDiscount)
            MultiDiscountStepsWidget(
              foodItem: widget.foodItem,
              foodCount: foodCount,
            ),
        ],
      ),
    );
  }

  /// 根据样式获取容器装饰
  BoxDecoration _getContainerDecoration(final bool hasMultiDiscount) {
    if (hasMultiDiscount) {
      return BoxDecoration(
        borderRadius: BorderRadius.circular(11.r),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xffFFD9C7),
            Color(0xffFAFBFF),
          ],
        ),
      );
    } else {
      return BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xff27CC5A),
            Color(0xff27CC5A),
            Colors.white,
          ],
        ),
      );
    }
  }

  /// 处理食品数量变化
  void _handleFoodCountChange(final int foodCount) {
    if (_lastFoodCount != null && _lastFoodCount! > 0 && foodCount == 0) {
      if (_showRelations) {
        Future.delayed(Duration.zero, () {
          if (mounted) {
            _hideRelations();
            DirectlyAddedFoods.remove(widget.foodItem?.id);
          }
        });
      }
    }
    _lastFoodCount = foodCount;
  }

  /// 处理关联食品显示逻辑
  void _handleRelationsVisibility(
    final int foodCount,
    final bool relationsExists,
  ) {
    // 只有当食品数量大于0、有关联食品、且是由用户直接点击主食品添加按钮添加的食品才展开关联食品
    if (foodCount > 0 &&
        relationsExists &&
        DirectlyAddedFoods.contains(widget.foodItem?.id)) {
      if (!_showRelations && !_manuallyHidden) {
        Future.delayed(Duration.zero, () {
          if (mounted) {
            _showRelationsWithAnimation();
          }
        });
      }
    } else if (foodCount == 0) {
      _manuallyHidden = false;
    }
  }

  /// 打开食品详情
  void _openFoodDetail() async {
    ref
        .read(foodDetailProvider.notifier)
        .fetchFoodDetailData(foodId: widget.foodItem!.id ?? 0);
    ref.read(restaurantCommentListProvider.notifier).fetchRestaurantCommentList(
          page: 1,
          id: widget.restaurantId ?? 0,
          type: 1,
          foodId: widget.foodItem!.id ?? 0,
        );
    await showFoodDetailWidget(
        context,
        widget.restaurantId ?? 0,
        widget.foodItem!.monthOrderCount ?? 0,
        widget.foodItem!.seckillMaxOrderCount ?? 0,
        widget.foodItem!.foodQuantityTypeValUg ?? '',
        widget.foodItem!.foodQuantityTypeValZh ?? '',
        widget.foodItem!.seckillId ?? 0,
        widget.foodItem!.maxOrderCount ?? 0,
        widget.isResting ?? false,
        widget.foodItem!);
  }

  Future<String> showFoodDetailWidget(
      final BuildContext context,
      final int restaurantId,
      final int monthOrderCount,
      final int seckillMaxOrderCount,
      final String foodQuantityTypeValUg,
      final String foodQuantityTypeValZh,
      final int seckillId,
      final int maxOrderCount,
      final bool isResting,
      final Food? foodItem) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      useSafeArea: true,
      builder: (final BuildContext bottomPanelContext) {
        return FoodDetailWidget(
          restaurantId: restaurantId,
          monthOrderCount: monthOrderCount,
          seckillMaxOrderCount: seckillMaxOrderCount,
          foodQuantityTypeValUg: foodQuantityTypeValUg,
          foodQuantityTypeValZh: foodQuantityTypeValZh,
          seckillId: seckillId,
          maxOrderCount: maxOrderCount,
          isResting: isResting,
          food: foodItem,
        );
      },
    ).then((final value) {
      return value ?? '';
    });
  }

  /// 处理关联食品添加逻辑（不记录为直接添加）
  void _handleRelationAddFood(
      final int foodCount, final int? foodId, final Food? food,
      [final BuildContext? btnContext]) {
    if (foodCount == 0) {
      if (food != null) {
        // 添加到购物车（不标记为直接添加）
        ref.read(shoppingCartProvider.notifier).addFoodToCart(
              foodItem: food,
            );
      }
    } else {
      // 增加数量
      ref.read(shoppingCartProvider.notifier).addFoodCount(
            foodId: foodId ?? 0,
            foodItem: food,
          );
    }
  }

  /// 处理减少食品逻辑（包装方法，保持向后兼容）
  void _handleRemoveFood(final int? foodId) {
    _handleRemoveFoodWithItem(foodId, widget.foodItem);
  }

  /// 处理减少食品逻辑（实际实现）
  void _handleRemoveFoodWithItem(final int? foodId, final Food? food) {
    ref.read(shoppingCartProvider.notifier).removeFoodCount(
          foodId: foodId ?? 0,
          foodItem: food,
        );
  }

  /// 处理最小购买数量添加
  void _handleAddMinCountFood(final Food food) {
    // 由于addFoodToCart已经处理了最小购买数量逻辑，直接调用一次即可
    ref.read(shoppingCartProvider.notifier).addFoodToCart(
          foodItem: food,
        );

    // 如果是主食品，标记为直接添加
    if (food.id == widget.foodItem?.id) {
      DirectlyAddedFoods.add(food.id);
    }
  }

  /// 处理规格选择
  void _handleSpecSelect(final Food food) async {
    // 显示规格选择弹窗
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (final BuildContext context) {
        return SpecModalWidget(
          foodItem: food, // 直接使用原始foodItem
          restaurantId: widget.restaurantId,
          isResting: widget.isResting,
          onClose: () {
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  /// 处理添加食品逻辑
  void _handleAddFood(final int foodCount, final int? foodId, final Food? food,
      final bool relationsExists,
      [final BuildContext? btnContext]) {
    if (foodCount == 0) {
      if (food != null) {
        // 只有当添加的是主食品（不是关联食品）时，才记录为直接添加
        if (food.id == widget.foodItem?.id) {
          DirectlyAddedFoods.add(foodId);
        }

        // 添加到购物车
        ref.read(shoppingCartProvider.notifier).addFoodToCart(
              foodItem: food,
            );

        // 如果有关联商品则显示，但只在添加主食品时
        if (relationsExists && food.id == widget.foodItem?.id) {
          _showRelationsWithAnimation();
        }
      }
    } else {
      // 增加数量
      ref.read(shoppingCartProvider.notifier).addFoodCount(
            foodId: foodId ?? 0,
            foodItem: food,
          );
    }
  }
}
