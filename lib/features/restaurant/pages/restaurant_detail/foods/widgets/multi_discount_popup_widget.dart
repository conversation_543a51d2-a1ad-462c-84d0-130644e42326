import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/generated/l10n.dart';

/// 多重折扣步骤弹窗组件
///
/// 使用示例：
/// ```dart
/// // 在需要显示多重折扣弹窗的地方调用
/// await MultiDiscountPopupWidget.show(
///   context,
///   food: selectFoodItem, // 商品信息，包含多重折扣步骤
///   position: tapPosition, // 点击位置，用于定位弹窗
/// );
/// ```
///
/// 功能特点：
/// - 自动过滤显示步骤（第一个步骤必显示，价格相同的步骤会跳过）
/// - 根据当前商品数量判断步骤完成状态
/// - 支持国际化显示步骤名称
/// - 响应式设计，适配不同屏幕尺寸
class MultiDiscountPopupWidget {
  /// 显示多重折扣步骤弹窗
  static Future<void> show(
    final BuildContext context, {
    required final SelectFoodItem food,
    required final Offset position,
  }) async {
    // 检查是否有多重折扣步骤
    if (food.multiDiscountSteps == null || food.multiDiscountSteps!.isEmpty) {
      return;
    }

    // 过滤可显示的步骤
    final visibleSteps = _filterVisibleSteps(food);

    if (visibleSteps.isEmpty) {
      return;
    }

    // 获取当前商品数量
    final currentCount = food.count ?? 0;

    final result = await showMenu<String>(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx - 50.w, // 向左偏移一些，让弹窗在问号图标上方显示
        position.dy - 120.h, // 向上偏移，显示在问号上方
        position.dx + 50.w,
        position.dy,
      ),
      color: Colors.transparent,
      elevation: 0,
      items: [
        PopupMenuItem<String>(
          value: 'steps',
          padding: EdgeInsets.zero,
          child: Container(
            decoration: BoxDecoration(
              color: Color(0xFFF3F4F6),
              borderRadius: BorderRadius.circular(11.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.16),
                  blurRadius: 15,
                  spreadRadius: 5,
                  offset: Offset(1, 1),
                ),
              ],
            ),
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 7.5.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: buildStepItems(visibleSteps, currentCount),
            ),
          ),
        ),
      ],
    );

    if (result != null) {
      // 处理选择结果（生产环境中应该移除或使用日志系统）
      // print('Selected: $result');
    }
  }

  /// 过滤可显示的步骤
  static List<MultiDiscountStep> _filterVisibleSteps(
    final SelectFoodItem food,
  ) {
    return food.multiDiscountSteps!
        .asMap()
        .entries
        .where((final entry) {
          final index = entry.key;
          final step = entry.value;

          // 第一个步骤必须显示，不受价格条件限制
          if (index == 0) {
            return true;
          }

          // 其他步骤：如果价格与商品原价相同则跳过
          if (step.price == food.foodsPrice) {
            return false;
          }

          return true;
        })
        .map((final entry) => entry.value)
        .toList();
  }

  /// 构建步骤项列表
  ///
  /// [steps] 步骤列表
  /// [currentCount] 当前数量
  /// [showOnlyToCount] 是否只显示到当前数量的步骤
  /// [forceGrayColor] 是否强制使用灰色
  /// [showConnectingLines] 是否显示连接线
  /// [itemHeight] 每个步骤项的高度
  static List<Widget> buildStepItems(
    final List<MultiDiscountStep> steps,
    final int currentCount, {
    final bool showOnlyToCount = false,
    final bool forceGrayColor = false,
    final bool showConnectingLines = true,
    final double? itemHeight,
  }) {
    List<Widget> stepWidgets = [];

    // 如果需要限制显示步骤，则过滤步骤
    List<MultiDiscountStep> displaySteps = steps;
    if (showOnlyToCount && currentCount > 0) {
      displaySteps = steps
          .where((final step) => (step.number ?? 0) <= currentCount)
          .toList();
    }

    final height = itemHeight ?? 20.h;

    for (int i = 0; i < displaySteps.length; i++) {
      final step = displaySteps[i];

      stepWidgets.add(
        SizedBox(
          height: height,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center, // 圆圈与文字居中对齐
            children: [
              // 左侧序号圆圈和虚线 - 固定宽度
              SizedBox(
                width: 12.w, // 对应微信小程序的24rpx
                height: height,
                child: Stack(
                  children: [
                    // 上部虚线（第一个步骤不显示，且需要显示连接线时）
                    if (i > 0 && showConnectingLines)
                      Positioned(
                        left: 6.w - 0.5.w, // 圆圈中心位置减去线宽的一半
                        top: 1.h, // 稍微往下，不从最顶部开始
                        child: SizedBox(
                          width: 1.w,
                          height: height * 0.45, // 动态计算虚线高度
                          child: CustomPaint(
                            painter: DashedLinePainter(
                              color: Color(0xFFC4C4C4),
                              strokeWidth: 1.w,
                              dashLength: 2.h,
                              gapLength: 2.h,
                            ),
                          ),
                        ),
                      ),
                    // 下部虚线（最后一个步骤不显示，且需要显示连接线时）
                    if (i < displaySteps.length - 1 && showConnectingLines)
                      Positioned(
                        left: 6.w - 0.5.w, // 圆圈中心位置减去线宽的一半
                        top: height * 0.5, // 从圆圈中心开始
                        child: SizedBox(
                          width: 1.w,
                          height: height * 0.45, // 动态计算虚线高度
                          child: CustomPaint(
                            painter: DashedLinePainter(
                              color: Color(0xFFC4C4C4),
                              strokeWidth: 1.w,
                              dashLength: 2.h,
                              gapLength: 2.h,
                            ),
                          ),
                        ),
                      ),
                    // 圆圈 - 居中显示（放在最后，确保在虚线上层）
                    Positioned(
                      left: 6.w - 4.w, // 圆圈中心位置减去圆圈半径
                      top: height * 0.5 - 4.w, // 动态计算垂直居中位置
                      child: Container(
                        width: 8.w,
                        height: 8.w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: showConnectingLines
                              ? Color(0xFFF3F4F6)
                              : null, // 只有显示连接线时才需要背景色
                          border: Border.all(
                            color: forceGrayColor
                                ? Color(0xFFC4C4C4)
                                : (_isStepCompleted(step, currentCount)
                                    ? AppColors.primary
                                    : Color(0xFFC4C4C4)),
                            width: 1,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 5.w), // 对应微信小程序的gap: 10rpx
              // 右侧内容区域
              showConnectingLines
                  ? Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment
                            .spaceBetween, // 对应微信小程序的justify-content: space-between
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // 步骤名称
                          Text(
                            "${_getStepName(step.number ?? 0)}：",
                            style: TextStyle(
                              fontSize: 13.sp, // 对应微信小程序的26rpx
                              fontWeight: forceGrayColor
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              color: forceGrayColor
                                  ? Color(0xFF8D8C8C)
                                  : (_isStepCompleted(step, currentCount)
                                      ? Color(0xFF000000)
                                      : Color(0xFF8D8C8C)),
                            ),
                          ),
                          // 步骤价格
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                '${step.price}',
                                style: TextStyle(
                                  fontSize: 13.sp,
                                  fontWeight: forceGrayColor
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                  color: forceGrayColor
                                      ? Color(0xFF8D8C8C)
                                      : (_isStepCompleted(step, currentCount)
                                          ? Color(0xFF000000)
                                          : Color(0xFF8D8C8C)),
                                ),
                              ),
                              Text(
                                '¥',
                                style: TextStyle(
                                  fontSize: 11.sp, // 对应微信小程序的22rpx
                                  fontWeight: FontWeight.bold,

                                  color: forceGrayColor
                                      ? Color(0xFF8D8C8C)
                                      : (_isStepCompleted(step, currentCount)
                                          ? Color(0xFF000000)
                                          : Color(0xFF8D8C8C)),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    )
                  : Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 步骤名称
                        Text(
                          "${_getStepName(step.number ?? 0)}：",
                          style: TextStyle(
                            fontSize: 13.sp, // 对应微信小程序的26rpx
                            fontWeight: forceGrayColor
                                ? FontWeight.bold
                                : FontWeight.normal,
                            color: forceGrayColor
                                ? Color(0xFF8D8C8C)
                                : (_isStepCompleted(step, currentCount)
                                    ? Color(0xFF000000)
                                    : Color(0xFF8D8C8C)),
                          ),
                        ),
                        // 步骤价格
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '${step.price}',
                              style: TextStyle(
                                fontSize: 13.sp,
                                fontWeight: forceGrayColor
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                color: forceGrayColor
                                    ? Color(0xFF8D8C8C)
                                    : (_isStepCompleted(step, currentCount)
                                        ? Color(0xFF000000)
                                        : Color(0xFF8D8C8C)),
                              ),
                            ),
                            Text(
                              '¥',
                              style: TextStyle(
                                fontSize: 11.sp, // 对应微信小程序的22rpx
                                fontWeight: forceGrayColor
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                color: forceGrayColor
                                    ? Color(0xFF8D8C8C)
                                    : (_isStepCompleted(step, currentCount)
                                        ? Color(0xFF000000)
                                        : Color(0xFF8D8C8C)),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
            ],
          ),
        ),
      );
    }

    return stepWidgets;
  }

  /// 判断步骤是否已完成
  /// 根据微信小程序逻辑：当前数量大于等于该步骤数量时，该步骤为已完成状态
  static bool _isStepCompleted(
    final MultiDiscountStep step,
    final int currentCount,
  ) {
    return currentCount >= (step.number ?? 0);
  }

  /// 根据步骤数字获取对应的文案
  static String _getStepName(final int number) {
    switch (number) {
      case 1:
        return S.current.multi_discount_step_1;
      case 2:
        return S.current.multi_discount_step_2;
      case 3:
        return S.current.multi_discount_step_3;
      case 4:
        return S.current.multi_discount_step_4;
      case 5:
        return S.current.multi_discount_step_5;
      default:
        return '第$number份';
    }
  }
}

/// 虚线绘制器
class DashedLinePainter extends CustomPainter {
  /// 线条颜色
  final Color color;

  /// 线条宽度
  final double strokeWidth;

  /// 虚线段长度
  final double dashLength;

  /// 虚线间隔长度
  final double gapLength;

  /// 构造函数
  DashedLinePainter({
    required this.color,
    required this.strokeWidth,
    required this.dashLength,
    required this.gapLength,
  });

  @override
  void paint(final Canvas canvas, final Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    double startY = 0;
    final endY = size.height;

    while (startY < endY) {
      canvas.drawLine(
        Offset(size.width / 2, startY),
        Offset(size.width / 2, (startY + dashLength).clamp(0, endY)),
        paint,
      );
      startY += dashLength + gapLength;
    }
  }

  @override
  bool shouldRepaint(covariant final CustomPainter oldDelegate) => false;
}
