import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/icon_font.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 搜索框状态控制器，用于从外部控制搜索框
class SearchBarController {
  /// 清空搜索内容并隐藏键盘
  void Function()? clearAndDismiss;

  /// 只清空搜索框文本，不触发API请求
  void Function()? clearSilently;
}

/// 通用的餐厅搜索栏组件
/// 用于在不同餐厅样式中复用搜索功能
class RestaurantSearchBar extends ConsumerStatefulWidget {
  /// 静态的GlobalKey，用于检测点击区域
  static final GlobalKey searchBarGlobalKey = GlobalKey();

  /// 是否显示圆角边框
  final bool showBorder;

  /// 是否使用灰色背景
  final bool useGreyBackground;

  /// 边距设置
  final EdgeInsetsGeometry? margin;

  /// 外部控制器
  final SearchBarController? controller;

  /// 样式
  final int style;

  /// 构造函数
  const RestaurantSearchBar({
    super.key,
    this.showBorder = true,
    this.useGreyBackground = false,
    this.margin,
    this.controller,
    this.style = 0,
  });

  @override
  ConsumerState<RestaurantSearchBar> createState() =>
      _RestaurantSearchBarState();
}

class _RestaurantSearchBarState extends ConsumerState<RestaurantSearchBar> {
  /// 搜索文本控制器
  late TextEditingController _searchController;

  /// 当前搜索文本
  late String _searchText;

  /// 输入框焦点控制
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _searchText = '';
    _searchController = TextEditingController();

    // 延迟注册外部控制方法，确保context初始化完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.controller != null) {
        widget.controller!.clearAndDismiss = _clearAndDismiss;
        widget.controller!.clearSilently = _clearSilently;
        debugPrint('SearchBar: 控制器方法设置成功');
      }
    });
  }

  /// 只清空搜索框文本，不触发API请求
  void _clearSilently() {
    try {
      _searchController.clear();
      _searchText = '';
      // 确保context可用
      if (context.mounted) {
        FocusScope.of(context).unfocus();
      }
      // 输出调试信息
      debugPrint('SearchBar: clearSilently执行成功');
    } catch (e) {
      debugPrint('SearchBar: clearSilently执行失败: $e');
    }
  }

  /// 清空搜索并隐藏键盘
  void _clearAndDismiss() {
    try {
      _searchController.clear();
      _searchText = '';
      // 更新搜索关键词
      ref
          .read(restaurantDetailControllerProvider.notifier)
          .updateSearchKeyword('');
      // 确保context可用
      if (context.mounted) {
        FocusScope.of(context).unfocus();
      }
      // 输出调试信息
      debugPrint('SearchBar: clearAndDismiss执行成功');
    } catch (e) {
      debugPrint('SearchBar: clearAndDismiss执行失败: $e');
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 获取控制器
    final controller = ref.read(restaurantDetailControllerProvider.notifier);

    // // 使用select只监听searchKeyword字段
    // final searchKeyword = ref.watch(restaurantDetailControllerProvider
    //     .select((state) => state.searchKeyword));

    // // 如果状态中的搜索关键词变化，更新输入框
    // if (searchKeyword != _searchText &&
    //     searchKeyword != _searchController.text) {
    //   _searchController.text = searchKeyword;
    //   _searchText = searchKeyword;
    // }

    return Container(
      key: RestaurantSearchBar.searchBarGlobalKey, // 使用静态GlobalKey
      margin: widget.margin ??
          EdgeInsets.only(
            left: 8.w,
            right: 8.w,
            top: 8.h,
            bottom: 8.h,
          ),
      height: 36.h, // 72rpx ÷ 2 = 36.h
      decoration: BoxDecoration(
        color:
            widget.useGreyBackground ? const Color(0xFFF5F5F5) : Colors.white,
        borderRadius: BorderRadius.circular(9.r), // 18rpx ÷ 2 = 9.r
        border: widget.showBorder
            ? Border.all(color: Colors.grey.withOpacity(0.2), width: 0.5)
            : null,
      ),
      // 使整个搜索栏可点击，点击时聚焦到输入框
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            _focusNode.requestFocus();
          },
          borderRadius: BorderRadius.circular(9.r),
          child: Row(
            children: [
              // 搜索图标，使用更大的内边距
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 7.5.w),
                child: Icon(
                  IconFont.search,
                  size: 18.sp,
                  color: const Color(0xFF8D8C8C), // 与小程序一致的颜色
                ),
              ),
              // 输入框
              Expanded(
                child: TextField(
                  controller: _searchController,
                  focusNode: _focusNode,
                  decoration: InputDecoration(
                    hintText: S.current.input_food_search,
                    hintStyle: TextStyle(
                      fontSize: 13.sp, // 26rpx ÷ 2 = 13.sp
                      color: const Color(0xFF8D8C8C),
                    ),
                    border: InputBorder.none,
                    isDense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                  style: TextStyle(
                    fontSize: 14.sp,
                  ),
                  onChanged: (final value) {
                    // 直接更新变量，无需setState
                    _searchText = value;
                    // 直接使用controller处理搜索
                    controller.updateSearchKeyword(value);
                  },
                  // 添加onSubmitted回调，在提交搜索时隐藏键盘
                  onSubmitted: (final value) {
                    // 隐藏键盘
                    FocusScope.of(context).unfocus();
                  },
                ),
              ),
              // 清除按钮，增加点击区域
              if (_searchText.isNotEmpty)
                GestureDetector(
                  onTap: () {
                    _searchController.clear();
                    // 直接更新变量，无需setState
                    _searchText = '';
                    // 直接使用controller处理清除操作
                    controller.updateSearchKeyword('');
                    // 清除后保持输入框焦点
                    _focusNode.requestFocus();
                    // 隐藏键盘
                    FocusScope.of(context).unfocus();
                  },
                  // 增大点击区域
                  child: Container(
                    width: 36.w,
                    height: 36.h,
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.close,
                      size: 16.sp,
                      color: const Color(0xFF8D8C8C),
                    ),
                  ),
                )
              else
                // 当没有文本时，保持右侧留白
                SizedBox(width: 7.5.w),
            ],
          ),
        ),
      ),
    );
  }
}
