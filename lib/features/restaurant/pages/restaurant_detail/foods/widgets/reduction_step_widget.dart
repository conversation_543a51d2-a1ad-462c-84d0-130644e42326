import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/utils/format_util.dart';

/// 满减步骤组件
/// 参考微信小程序的 reductionStep 组件
class ReductionStepWidget extends StatelessWidget {
  /// 满减阶梯列表
  final List<Map<String, dynamic>> stepList;

  /// 还差金额
  final double butSmall;

  /// 状态 (1: 未达到, 2: 达到但有下一级, 3: 达到最高级)
  final int state;

  /// 已减金额
  final double reduce;

  /// 未来可减金额
  final double futureReduce;

  /// 活动状态 (1: 活动美食, 2: 普通)
  final int resState;

  /// 构造函数
  const ReductionStepWidget({
    super.key,
    required this.stepList,
    this.butSmall = 0,
    this.state = 0,
    this.reduce = 0,
    this.futureReduce = 0,
    this.resState = 2,
  });

  @override
  Widget build(final BuildContext context) {
    if (stepList.isEmpty) {
      return const SizedBox.shrink();
    }

    final isUg = Localizations.localeOf(context).languageCode == 'en';

    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFFFEBEE),
        border: Border(
          left: BorderSide(color: const Color(0xFFFFDADA), width: 1.w),
          right: BorderSide(color: const Color(0xFFFFDADA), width: 1.w),
        ),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(10.r),
          bottomRight: Radius.circular(10.r),
        ),
      ),
      margin: EdgeInsets.symmetric(horizontal: 28.w),
      child: Container(
        width: 1.sw,
        padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          textDirection: isUg ? TextDirection.rtl : TextDirection.ltr,
          children: [
            Expanded(
              child: isUg ? _buildUgContent() : _buildZhContent(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建维语内容
  Widget _buildUgContent() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 5.w),
      child: _buildUgState(),
    );
  }

  /// 构建中文内容
  Widget _buildZhContent() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w),
      child: Text.rich(
        TextSpan(children: _buildZhState()),
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
        style: _getTextStyle(),
      ),
    );
  }

  /// 构建维语状态内容
  Widget _buildUgState() {
    List<TextSpan> spans = [];

    switch (state) {
      case 1:
        spans = _buildUgSpansForCase1();
        break;
      case 2:
        spans = _buildUgSpansForCase2();
        break;
      case 3:
        spans = _buildUgSpansForCase3();
        break;
      default:
        return const SizedBox.shrink();
    }

    return Text.rich(
      TextSpan(children: spans),
      textAlign: TextAlign.center,
      textDirection: TextDirection.rtl,
      style: _getTextStyle(),
    );
  }

  /// 构建维语状态1的TextSpan列表
  List<TextSpan> _buildUgSpansForCase1() {
    List<TextSpan> spans = [];

    if (resState == 1) {
      spans.add(TextSpan(text: ' ئېتىبار تاماقتىن ', style: _getTextStyle()));
    }
    if (butSmall != (stepList.isNotEmpty ? stepList[0]['price'] ?? 0 : 0)) {
      spans.add(TextSpan(text: ' يەنە ', style: _getTextStyle()));
    }
    spans.add(
      TextSpan(
        text: FormatUtil.formatPrice(butSmall),
        style: _getNumberStyle(),
      ),
    );
    spans.add(
      TextSpan(
        text: 'يۈەنلىك سېتىۋالسىڭىز تاماق پۇلىدىن',
        style: _getTextStyle(),
      ),
    );
    spans.add(
      TextSpan(
        text: FormatUtil.formatPrice(reduce),
        style: _getNumberStyle(),
      ),
    );
    spans.add(TextSpan(text: 'يۈەن ئېتىبار قىلىنىدۇ', style: _getTextStyle()));

    return spans;
  }

  /// 构建维语状态2的TextSpan列表
  List<TextSpan> _buildUgSpansForCase2() {
    List<TextSpan> spans = [];

    spans.add(TextSpan(text: ' تاماق پۇلىدىن ', style: _getTextStyle()));
    spans.add(
      TextSpan(
        text: FormatUtil.formatPrice(reduce),
        style: _getNumberStyle(),
      ),
    );
    spans.add(TextSpan(text: ' يۈەن ئېتىبار قىلىندى ', style: _getTextStyle()));
    spans.add(TextSpan(text: '،', style: _getTextStyle()));
    if (resState == 1) {
      spans.add(TextSpan(text: ' ئېتىبار تاماقتىن ', style: _getTextStyle()));
    }
    spans.add(TextSpan(text: ' يەنە ', style: _getTextStyle()));
    spans.add(
      TextSpan(
        text: FormatUtil.formatPrice(butSmall),
        style: _getNumberStyle(),
      ),
    );
    spans.add(
      TextSpan(
        text: 'يۈەنلىك سېتىۋالسىڭىز جەمئي',
        style: _getTextStyle(),
      ),
    );
    spans.add(
      TextSpan(
        text: FormatUtil.formatPrice(futureReduce),
        style: _getNumberStyle(),
      ),
    );
    spans.add(TextSpan(text: 'يۈەن ئېتىبار قىلىنىدۇ', style: _getTextStyle()));

    return spans;
  }

  /// 构建维语状态3的TextSpan列表
  List<TextSpan> _buildUgSpansForCase3() {
    List<TextSpan> spans = [];

    spans.add(TextSpan(text: 'تاماق پۇلىدىن', style: _getTextStyle()));
    spans.add(
      TextSpan(
        text: FormatUtil.formatPrice(reduce),
        style: _getNumberStyle(),
      ),
    );
    spans.add(TextSpan(text: 'يۈەن ئېتىبار قىلىندى', style: _getTextStyle()));

    return spans;
  }

  /// 构建中文状态内容
  List<TextSpan> _buildZhState() {
    switch (state) {
      case 1:
        final firstStepPrice =
            stepList.isNotEmpty ? stepList[0]['price'] ?? 0 : 0;
        List<TextSpan> spans = [];

        if (resState != 1 && butSmall != firstStepPrice) {
          spans.add(TextSpan(text: ' 再', style: _getTextStyle()));
        }
        if (butSmall == firstStepPrice) {
          spans.add(
            TextSpan(
              text: resState == 1 ? '活动美食总额' : '美食总额',
              style: _getTextStyle(),
            ),
          );
        }
        if (resState == 1 && butSmall != firstStepPrice) {
          spans.add(TextSpan(text: '活动美食再买', style: _getTextStyle()));
        }
        if (resState != 1) {
          spans.add(
            TextSpan(
              text: butSmall != firstStepPrice ? '买' : '满',
              style: _getTextStyle(),
            ),
          );
        }
        spans.add(
          TextSpan(
            text: FormatUtil.formatPrice(butSmall),
            style: _getNumberStyle(),
          ),
        );
        if (butSmall == firstStepPrice) {
          spans.add(TextSpan(text: '元立减', style: _getTextStyle()));
        }
        if (butSmall != firstStepPrice) {
          spans.add(TextSpan(text: '元,美食总额立减', style: _getTextStyle()));
        }
        spans.add(
          TextSpan(
            text: FormatUtil.formatPrice(reduce),
            style: _getNumberStyle(),
          ),
        );
        spans.add(TextSpan(text: '元', style: _getTextStyle()));

        return spans;
      case 2:
        return [
          TextSpan(text: '美食总额已减', style: _getTextStyle()),
          TextSpan(
            text: FormatUtil.formatPrice(reduce),
            style: _getNumberStyle(),
          ),
          TextSpan(text: '元', style: _getTextStyle()),
          if (resState == 1) TextSpan(text: '活动美食', style: _getTextStyle()),
          TextSpan(text: '再买', style: _getTextStyle()),
          TextSpan(
            text: FormatUtil.formatPrice(butSmall),
            style: _getNumberStyle(),
          ),
          TextSpan(text: '元,可享受共', style: _getTextStyle()),
          TextSpan(
            text: FormatUtil.formatPrice(futureReduce),
            style: _getNumberStyle(),
          ),
          TextSpan(text: '元优惠', style: _getTextStyle()),
        ];
      case 3:
        return [
          TextSpan(text: '美食总额已减', style: _getTextStyle()),
          TextSpan(
            text: FormatUtil.formatPrice(reduce),
            style: _getNumberStyle(),
          ),
          TextSpan(text: '元', style: _getTextStyle()),
        ];
      default:
        return [];
    }
  }

  /// 获取普通文本样式
  TextStyle _getTextStyle() {
    return TextStyle(
      fontSize: 16.sp,
      color: Colors.black,
    );
  }

  /// 获取数字文本样式
  TextStyle _getNumberStyle() {
    return TextStyle(
      fontSize: 17.sp,
      color: const Color(0xFFF44336),
      fontWeight: FontWeight.w600,
    );
  }
}
