import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';

/// 餐厅详情页骨架屏
/// 完全复制微信小程序的骨架屏样式和动画效果
class RestaurantDetailSkeleton extends ConsumerWidget {
  /// 构造函数
  const RestaurantDetailSkeleton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isRtl = ref.watch(languageProvider) == 'ug';

    return Scaffold(
      backgroundColor: const Color(0xFFE0E0E0),
      body: Stack(
        children: [
          // 主要内容区域
          Column(
            children: [
              // 导航栏区域
              _buildNavigationBar(isRtl),

              // 餐厅信息栏
              _buildRestaurantNavbar(isRtl),

              // 主要内容容器
              Expanded(
                child: _buildMainContainer(isRtl),
              ),
            ],
          ),

          // 骨架屏动画遮罩层
          // Positioned.fill(
          //   child: SkeletonAnimation(),
          // ),
        ],
      ),
    );
  }

  /// 构建导航栏区域
  Widget _buildNavigationBar(bool isRtl) {
    return Container(
      height: 80.h, // 包含状态栏的高度
      color: Colors.transparent,
      child: Stack(
        children: [
          // 状态栏区域
          // Container(
          //   height: 45.h,
          //   // color: const Color(0xFFE0E0E0),
          // ),

          // 导航按钮区域
          // Positioned(
          //   top: 52.h,
          //   left: isRtl ? null : 15.w,
          //   right: isRtl ? 15.w : null,
          //   child: Row(
          //     mainAxisSize: MainAxisSize.min,
          //     children: [
          //       // 返回按钮
          //       Container(
          //         width: 32.w,
          //         height: 32.w,
          //         decoration: BoxDecoration(
          //           color: const Color(0xFFEFEFEF),
          //           borderRadius: BorderRadius.circular(16.w),
          //         ),
          //       ),
          //       SizedBox(width: 10.w),
          //       // 首页按钮
          //       Container(
          //         width: 32.w,
          //         height: 32.w,
          //         decoration: BoxDecoration(
          //           color: const Color(0xFFEAEAEA),
          //           borderRadius: BorderRadius.circular(4.w),
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
        ],
      ),
    );
  }

  /// 构建餐厅信息导航栏
  Widget _buildRestaurantNavbar(bool isRtl) {
    return Container(
      color: const Color(0xFFE0E0E0),
      child: Column(
        children: [
          // 餐厅详情区域
          Container(
            height: 120.h,
            margin: EdgeInsets.symmetric(horizontal: 15.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.w),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.06),
                  blurRadius: 10.w,
                  spreadRadius: 2.w,
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.all(15.w),
              child: Row(
                children: [
                  // 餐厅图片
                  Container(
                    width: 60.w,
                    height: 60.w,
                    decoration: BoxDecoration(
                      color: const Color(0xFFEAEAEA),
                      borderRadius: BorderRadius.circular(8.w),
                    ),
                  ),
                  SizedBox(width: 15.w),

                  // 餐厅信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 餐厅名称
                        Container(
                          width: 150.w,
                          height: 20.h,
                          decoration: BoxDecoration(
                            color: const Color(0xFFEEEEEE),
                            borderRadius: BorderRadius.circular(4.w),
                          ),
                        ),
                        SizedBox(height: 8.h),

                        // 评分和订单信息
                        Row(
                          children: [
                            Container(
                              width: 80.w,
                              height: 16.h,
                              decoration: BoxDecoration(
                                color: const Color(0xFFEEEEEE),
                                borderRadius: BorderRadius.circular(4.w),
                              ),
                            ),
                            SizedBox(width: 15.w),
                            Container(
                              width: 60.w,
                              height: 16.h,
                              decoration: BoxDecoration(
                                color: const Color(0xFFEEEEEE),
                                borderRadius: BorderRadius.circular(4.w),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 6.h),

                        // 距离信息
                        Container(
                          width: 100.w,
                          height: 16.h,
                          decoration: BoxDecoration(
                            color: const Color(0xFFEEEEEE),
                            borderRadius: BorderRadius.circular(4.w),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Tab导航栏
          Container(
            height: 40.h,
            color: Colors.white,
            margin: EdgeInsets.symmetric(vertical: 10.h),
            child: Row(
              children: [
                _buildTabItem("菜单", true, isRtl),
                _buildTabItem("评价", false, isRtl),
                _buildTabItem("商家", false, isRtl),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建Tab项目
  Widget _buildTabItem(String text, bool isActive, bool isRtl) {
    return Expanded(
      child: Container(
        height: 50.h,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          border: isActive
              ? Border(
                  bottom: BorderSide(
                    color: const Color(0xFFEBEBEB),
                    width: 2.w,
                  ),
                )
              : null,
        ),
        child: Container(
          width: 60.w,
          height: 18.h,
          decoration: BoxDecoration(
            color: const Color(0xFFEEEEEE),
            borderRadius: BorderRadius.circular(4.w),
          ),
        ),
      ),
    );
  }

  /// 构建主要内容容器
  Widget _buildMainContainer(bool isRtl) {
    return Container(
      color: const Color(0xFFEFF1F6),
      child: Row(
        children: [
          // 左侧分类菜单
          _buildCategoryMenu(isRtl),

          // 右侧美食列表
          Expanded(
            child: _buildFoodsList(isRtl),
          ),
        ],
      ),
    );
  }

  /// 构建分类菜单
  Widget _buildCategoryMenu(bool isRtl) {
    return Container(
      width: 90.w,
      color: const Color(0xFFEFF1F6),
      child: ListView.builder(
        padding: EdgeInsets.zero,
        itemCount: 12,
        itemBuilder: (context, index) {
          return Container(
            height: 45.h,
            margin: EdgeInsets.only(
              top: 7.5.h,
              left: isRtl ? 0 : 5.w,
              right: isRtl ? 5.w : 0,
            ),
            decoration: BoxDecoration(
              color: const Color(0xFFEAEAEA),
              borderRadius: BorderRadius.only(
                topLeft: isRtl ? Radius.zero : Radius.circular(5.w),
                bottomLeft: isRtl ? Radius.zero : Radius.circular(5.w),
                topRight: isRtl ? Radius.circular(5.w) : Radius.zero,
                bottomRight: isRtl ? Radius.circular(5.w) : Radius.zero,
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建美食列表
  Widget _buildFoodsList(bool isRtl) {
    return Container(
      child: Column(
        children: [
          // 搜索栏
          _buildSearchBar(isRtl),

          // 美食列表
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: 6,
              itemBuilder: (context, index) {
                return _buildFoodItem(isRtl);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar(bool isRtl) {
    return Container(
      height: 30.h,
      margin: EdgeInsets.symmetric(horizontal: 5.w, vertical: 3.5.h),
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(5.h),
      ),
      child: Row(
        children: [
          SizedBox(width: 15.w),
          Container(
            width: 16.w,
            height: 16.w,
            decoration: BoxDecoration(
              color: const Color(0xFFEFEFEF),
              borderRadius: BorderRadius.circular(8.w),
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
            child: Container(
              height: 16.h,
              decoration: BoxDecoration(
                color: const Color(0xFFEFEFEF),
                borderRadius: BorderRadius.circular(4.w),
              ),
            ),
          ),
          SizedBox(width: 15.w),
        ],
      ),
    );
  }

  /// 构建美食项目
  Widget _buildFoodItem(bool isRtl) {
    return Container(
      padding: EdgeInsets.all(7.5.w),
      margin: EdgeInsets.all(3.5.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(5.h),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
        children: [
          // 美食图片
          Container(
            width: 90.w,
            height: 90.w,
            decoration: BoxDecoration(
              color: const Color(0xFFEAEAEA),
              borderRadius: BorderRadius.circular(6.w),
            ),
          ),
          SizedBox(width: 15.w),

          // 美食信息
          Expanded(
            child: Column(
              crossAxisAlignment:
                  isRtl ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                // 美食名称
                Container(
                  width: 150.w,
                  height: 20.h,
                  decoration: BoxDecoration(
                    color: const Color(0xFFEEEEEE),
                    borderRadius: BorderRadius.circular(4.w),
                  ),
                ),
                SizedBox(height: 10.h),

                // 销量信息
                Container(
                  width: 100.w,
                  height: 16.h,
                  decoration: BoxDecoration(
                    color: const Color(0xFFEEEEEE),
                    borderRadius: BorderRadius.circular(4.w),
                  ),
                ),
                SizedBox(height: 15.h),

                // 价格和操作区域
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
                  children: [
                    // 价格
                    Row(
                      children: [
                        Container(
                          width: 12.w,
                          height: 16.h,
                          decoration: BoxDecoration(
                            color: const Color(0xFFEEEEEE),
                            borderRadius: BorderRadius.circular(4.w),
                          ),
                        ),
                        SizedBox(width: 4.w),
                        Container(
                          width: 40.w,
                          height: 20.h,
                          decoration: BoxDecoration(
                            color: const Color(0xFFEEEEEE),
                            borderRadius: BorderRadius.circular(4.w),
                          ),
                        ),
                      ],
                    ),

                    // 添加按钮
                    Container(
                      width: 28.w,
                      height: 28.w,
                      decoration: BoxDecoration(
                        color: const Color(0xFFEFEFEF),
                        borderRadius: BorderRadius.circular(14.w),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 骨架屏动画组件
class SkeletonAnimation extends StatefulWidget {
  @override
  _SkeletonAnimationState createState() => _SkeletonAnimationState();
}

class _SkeletonAnimationState extends State<SkeletonAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                Colors.transparent,
                Colors.white.withOpacity(0.3),
                Colors.transparent,
              ],
              stops: [
                (_animation.value - 0.3).clamp(0.0, 1.0),
                _animation.value.clamp(0.0, 1.0),
                (_animation.value + 0.3).clamp(0.0, 1.0),
              ],
            ),
          ),
        );
      },
    );
  }
}
