import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/custom_refresh_indicator.dart';
import 'package:user_app/core/widgets/empty_view.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/restaurant_foods_item.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/widgets/restaurant_search_bar.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/styles/widgets/restaurant_countdown_widget.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/routes/paths.dart';

/// 超市便利样式的商品列表组件
/// 采用左侧分类列表+右侧商品垂直列表的布局
class RestaurantFoodsListWidget extends ConsumerStatefulWidget {
  /// 构造函数
  const RestaurantFoodsListWidget({
    super.key,
  });

  @override
  ConsumerState<RestaurantFoodsListWidget> createState() =>
      _RestaurantFoodsListWidgetState();
}

class _RestaurantFoodsListWidgetState
    extends ConsumerState<RestaurantFoodsListWidget> {
  /// 分类列表滚动控制器
  late ScrollController _categoriesController;

  /// 搜索框控制器
  final SearchBarController _searchBarController = SearchBarController();

  @override
  void initState() {
    super.initState();
    _categoriesController = ScrollController();

    // 滚动到当前选择的分类位置
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      if (_categoriesController.hasClients) {
        _scrollToSelectedCategory();
      }
    });
  }

  /// 滚动到当前选择的分类
  void _scrollToSelectedCategory() {
    // 获取当前选中的分类索引
    final selectedCategoryIndex =
        ref.read(restaurantDetailControllerProvider).selectedCategoryIndex;

    // 从controller获取食品分类数据
    final foodTypes =
        ref.read(restaurantDetailControllerProvider).foodsData?.foodType ?? [];

    if (foodTypes.isEmpty || selectedCategoryIndex >= foodTypes.length) {
      return;
    }

    // 计算滚动位置 - 根据小程序的样式，每个菜单项高度为70rpx (35.h)
    final itemHeight = 35.h;
    final offset = selectedCategoryIndex * itemHeight;

    // 滚动到指定位置
    if (_categoriesController.hasClients) {
      _categoriesController.animateTo(
        offset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void dispose() {
    _categoriesController.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    // 使用select只监听需要的状态字段
    final selectedCategoryIndex = ref.watch(
      restaurantDetailControllerProvider
          .select((final state) => state.selectedCategoryIndex),
    );

    // final searchKeyword = ref.watch(
    //   restaurantDetailControllerProvider
    //       .select((final state) => state.searchKeyword),
    // );

    // final isFoodsLoading = ref.watch(
    //   restaurantDetailControllerProvider
    //       .select((final state) => state.isFoodsLoading),
    // );

    // 监听购物车数据以便在购物车变化时更新UI
    ref.watch(shoppingCartProvider);

    // 从controller获取数据
    final foodsData = ref.watch(
      restaurantDetailControllerProvider
          .select((final state) => state.foodsData),
    );

    final restaurantId = ref.watch(
      restaurantDetailControllerProvider
          .select((final state) => state.restaurantData?.id ?? 0),
    );

    // 获取控制器
    final controller = ref.read(restaurantDetailControllerProvider.notifier);

    final foodTypes = foodsData?.foodType ?? [];

    if (foodTypes.isEmpty) {
      return EmptyView(
        message: S.current.search_nothing_food,
      );
    }

    // 获取当前选择的分类
    final selectedCategory =
        foodTypes.isNotEmpty && selectedCategoryIndex < foodTypes.length
            ? foodTypes[selectedCategoryIndex]
            : null;

    // 该分类下的商品列表
    final foodsList = foodsData?.foods ?? [];

    // 检查是否有秒杀商品
    final bool hasSeckill =
        foodsList.any((final food) => food.seckillActive == 1);

    // 检查当前分类是否是秒杀专区
    bool isSecKill = selectedCategory?.type == 0 &&
        (selectedCategory?.name == S.current.sec_kill || hasSeckill);
    // 如果当前分类不是秒杀专区，但有商品是秒杀商品，也显示倒计时
    if (!isSecKill) {
      isSecKill = foodsList.any((final food) => food.seckillActive == 1);
    }

    return Container(
      color: Colors.white, // 背景色 #EFF1F6 与小程序一致
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左侧分类列表 - 宽度与小程序保持一致 105.w (210rpx÷2)
          Container(
            width: 90.w,
            decoration: const BoxDecoration(
              color: Color(0xFFEFF1F6),
            ),
            child: CustomScrollView(
              controller: _categoriesController,
              slivers: [
                SliverOverlapInjector(
                  handle:
                      NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                ),
                SliverList.builder(
                  itemCount: foodTypes.length,
                  itemBuilder: (final context, final index) {
                    final category = foodTypes[index];
                    final isSelected = index == selectedCategoryIndex;

                    // 完全按照小程序的样式实现
                    return GestureDetector(
                      onTap: () {
                        if (category.licenseType == 1) {
                          // 跳转营业执照页面
                          context.push(
                            AppPaths.businessLicensePage,
                            extra: {
                              'restaurant_id': restaurantId,
                            },
                          );
                          return;
                        }

                        // 使用组合方法一次性清空搜索并更新分类，避免二次请求
                        controller.updateCategoryAndResetSearch(
                          index,
                          typeId: category.type,
                        );

                        // 如果搜索框有清空方法，使用静默清空方法更新UI状态
                        if (_searchBarController.clearSilently != null) {
                          _searchBarController.clearSilently!();
                        }
                      },
                      child: Container(
                        height: 40.h, // 70rpx ÷ 2 = 35.h
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Colors.white
                              : const Color(0xFFEFF1F6),
                          border: index < foodTypes.length - 1
                              ? Border(
                                  bottom: BorderSide(
                                    color: Colors.grey.withOpacity(0.1),
                                    width: 0.5,
                                  ),
                                )
                              : null,
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: 7.w,
                        ), // 14rpx ÷ 2 = 7.w
                        child: Text(
                          category.name ?? '',
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: isSelected
                                ? AppColors.primary
                                : AppColors.textPrimaryColor,
                            fontSize: 14.sp, // 28rpx ÷ 2 = 14.sp
                            fontWeight:
                                isSelected ? FontWeight.bold : FontWeight.w500,
                          ),
                        ),
                      ),
                    );
                  },
                ),
                SliverOverlapInjector(
                  handle:
                      NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                ),
              ],
            ),
          ),

          // 右侧内容区域（搜索框 + 倒计时 + 商品列表）
          Expanded(
            child: _buildFoodsListView(
              foodTypes: foodTypes,
              foodsList: foodsList,
              isSecKill: isSecKill,
              restaurantId: restaurantId,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建商品垂直列表
  Widget _buildFoodsList(
    final List<Food> foodsList,
    final int restaurantId,
    final bool isResting,
    final List<dynamic>? fromFoodIds,
  ) {
    return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      sliver: SliverList.builder(
        key: const ValueKey<String>("foods_list"),
        itemCount: foodsList.length + 1,
        itemBuilder: (final context, final index) {
          if (index == foodsList.length) {
            return Consumer(
              builder: (final context, final ref, final child) {
                final hasMoreData = ref.watch(
                  restaurantDetailControllerProvider
                      .select((final state) => state.hasMoreData),
                );
                if (hasMoreData) {
                  return const SizedBox.shrink();
                }

                return Container(
                  height: 80.h,
                  alignment: Alignment.topCenter,
                  child: Text(S.current.no_more_data),
                );
              },
            );
          }

          final food = foodsList[index];
          return Padding(
            padding: EdgeInsets.only(bottom: 15.h),
            child: RestaurantFoodsItem(
              style: 2, // 使用超市便利样式
              foodItem: food,
              relations: food.relations,
              restaurantId: restaurantId,
              isResting: isResting,
              fromFoodIds: fromFoodIds,
            ),
          );
        },
      ),
    );
  }

  Widget _buildFoodsListView({
    required final List<FoodType> foodTypes,
    required final List<Food> foodsList,
    required final bool isSecKill,
    required final int restaurantId,
  }) {
    return Consumer(
      builder: (final context, final ref, final child) {
        // 获取选中的类别索引和搜索关键词用于key
        final selectedCategoryIndex = ref.watch(
          restaurantDetailControllerProvider
              .select((final state) => state.selectedCategoryIndex),
        );
        final isResting = ref.watch(
          restaurantDetailControllerProvider
              .select((final state) => state.restaurantData?.isResting),
        );
        // 获取控制器
        final controller =
            ref.read(restaurantDetailControllerProvider.notifier);
        final fromFoodIds = ref.watch(
          restaurantDetailControllerProvider
              .select((final state) => state.fromFoodIds),
        );
        return CustomRefreshIndicator(
          onRefresh: () async {},
          enablePullDown: false,
          enablePullUp: true,
          footerHeight: 150.h,
          hideNoMore: true,
          hasMoreData: ref.watch(
            restaurantDetailControllerProvider
                .select((final state) => state.hasMoreData),
          ),
          onLoading: () async {
            await controller.loadNextPage();
          },
          child: CustomScrollView(
            // controller: _productsController,
            key: ValueKey('$selectedCategoryIndex'),
            slivers: [
              SliverOverlapInjector(
                handle:
                    NestedScrollView.sliverOverlapAbsorberHandleFor(context),
              ),
              // 显示搜索框
              SliverToBoxAdapter(
                child: RestaurantSearchBar(
                  controller: _searchBarController,
                  useGreyBackground: true,
                ),
              ),
              // 倒计时组件，只在有秒杀活动的时候显示
              if (isSecKill)
                SliverToBoxAdapter(
                  child: RestaurantCountdownWidget(
                    bottom: 8.h,
                    foods: foodsList,
                  ),
                ),
              _buildFoodsList(
                  foodsList, restaurantId, isResting ?? false, fromFoodIds),
            ],
          ),
        );
      },
    );
  }
}
