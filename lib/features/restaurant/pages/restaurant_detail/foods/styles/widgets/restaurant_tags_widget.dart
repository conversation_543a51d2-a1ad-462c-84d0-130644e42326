import 'dart:developer' as dev;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/common_widgets.dart';

/// 餐厅/超市/便利店优惠信息标签组件
/// 用于三种样式中显示优惠信息横向滚动区域
class RestaurantTagsWidget extends ConsumerWidget {
  /// 构造函数
  const RestaurantTagsWidget({
    super.key,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 直接从状态获取数据
    final market = ref.watch(
      restaurantDetailControllerProvider.select((final state) => state.market),
    );

    final tags = market?.tags;

    var padding = EdgeInsets.only(top: 8.h);

    if ((tags?.shipmentReductionTags?.isEmpty ?? true) &&
        (tags?.reductionTags?.isEmpty ?? true) &&
        (tags?.multiDiscountTags?.isEmpty ?? true)) {
      padding = EdgeInsets.only(top: 0.h);
    }

    return Container(
      padding: padding,
      color: Colors.white,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // 显示配送相关的优惠标签
            ...tags?.shipmentReductionTags?.map(
                  (final tag) => Row(
                    children: [
                      buildShipmentReductionText(
                        tag: tag,
                        onTap: () {
                          dev.log(tag.title ?? "");
                          showShipmentReductionDialog(
                            tags?.shipmentReductionTags,
                            context,
                            ref,
                          );
                        },
                      ),
                      SizedBox(width: 5.w),
                    ],
                  ),
                ) ??
                [],

            // 显示满减相关的优惠标签
            ...tags?.multiDiscountTags?.map(
                  (final tag) => Row(
                    children: [
                      buildReductionText(
                        text: tag.title ?? "",
                        image: tag.image,
                        onTap: () {
                          showReductionBottomSheet(
                            context: context,
                            tags: tags,
                          );
                        },
                      ),
                      SizedBox(width: 5.w),
                    ],
                  ),
                ) ??
                [],

            // 显示优惠标签
            ...tags?.reductionTags?.map(
                  (final tag) => Row(
                    children: [
                      buildReductionText(
                        text: tag.title ?? "",
                        onTap: () {
                          showReductionBottomSheet(
                            context: context,
                            tags: tags,
                          );
                        },
                      ),
                      SizedBox(width: 5.w),
                    ],
                  ),
                ) ??
                [],
          ],
        ),
      ),
    );
  }
}
