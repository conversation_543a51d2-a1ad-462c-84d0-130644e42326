import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/routes/paths.dart';

/// 餐厅详情页操作按钮组件
/// 用于显示收藏、二维码和分享按钮
class RestaurantActionButtons extends StatelessWidget {
  /// 收藏按钮回调
  final VoidCallback? onLike;

  /// 分享按钮回调
  final VoidCallback? onShare;

  /// 二维码按钮回调
  final Function(String)? onQrCode;

  /// 餐厅ID
  final String? restaurantId;

  /// 是否使用暗色主题（白色图标）
  final bool isDarkTheme;

  /// 按钮尺寸
  final double buttonSize;

  /// 按钮图标尺寸
  final double iconSize;

  /// 按钮之间的间距
  final double spacing;

  /// 按钮排列方向
  final Axis direction;

  /// 是否已收藏
  final bool isFavorite;

  /// 构造函数
  const RestaurantActionButtons({
    super.key,
    this.onLike,
    this.onShare,
    this.onQrCode,
    this.restaurantId,
    this.isDarkTheme = true,
    this.buttonSize = 25,
    this.iconSize = 25,
    this.spacing = 10,
    this.direction = Axis.vertical,
    this.isFavorite = false,
  });

  /// 创建水平布局的操作按钮（用于AppBar的actions）
  factory RestaurantActionButtons.horizontal({
    final Key? key,
    final VoidCallback? onLike,
    final VoidCallback? onShare,
    final Function(String)? onQrCode,
    final String? restaurantId,
    final bool isDarkTheme = true,
    final double buttonSize = 25,
    final double iconSize = 25,
    final double spacing = 10,
    final bool isFavorite = false,
  }) {
    return RestaurantActionButtons(
      key: key,
      onLike: onLike,
      onShare: onShare,
      onQrCode: onQrCode,
      restaurantId: restaurantId,
      isDarkTheme: isDarkTheme,
      buttonSize: buttonSize,
      iconSize: iconSize,
      spacing: spacing,
      direction: Axis.horizontal,
      isFavorite: isFavorite,
    );
  }

  /// 创建垂直布局的操作按钮（用于内容区域）
  factory RestaurantActionButtons.vertical({
    final Key? key,
    final VoidCallback? onLike,
    final VoidCallback? onShare,
    final Function(String)? onQrCode,
    final String? restaurantId,
    final bool isDarkTheme = true,
    final double buttonSize = 25,
    final double iconSize = 25,
    final double spacing = 10,
    final bool isFavorite = false,
  }) {
    return RestaurantActionButtons(
      key: key,
      onLike: onLike,
      onShare: onShare,
      onQrCode: onQrCode,
      restaurantId: restaurantId,
      isDarkTheme: isDarkTheme,
      buttonSize: buttonSize,
      iconSize: iconSize,
      spacing: spacing,
      direction: Axis.vertical,
      isFavorite: isFavorite,
    );
  }

  @override
  Widget build(final BuildContext context) {
    // 按钮整体尺寸
    final double btnSize = buttonSize.w;

    // 构建图片按钮
    Widget buildImageButton({
      required final String imagePath,
      required final VoidCallback? onPressed,
    }) {
      return InkWell(
        onTap: onPressed,
        child: SizedBox(
          width: btnSize,
          height: btnSize,
          child: Image.asset(
            imagePath,
            width: iconSize.w,
            height: iconSize.h,
            fit: BoxFit.contain,
          ),
        ),
      );
    }

    // 间距组件
    Widget spacer = direction == Axis.vertical
        ? SizedBox(height: spacing.h)
        : SizedBox(width: spacing.w);

    final List<Widget> children = [
      // 收藏按钮 - 使用小程序中的图标
      buildImageButton(
        imagePath: isFavorite
            ? 'assets/images/restaurant/like2_fill.png'
            : 'assets/images/restaurant/like2.png',
        onPressed: onLike,
      ),
      spacer,
      // 二维码按钮 - 继续使用二维码图标
      buildImageButton(
        imagePath: 'assets/images/restaurant/share-pyq2.png',
        onPressed: restaurantId != null
            ? () {
                if (onQrCode != null) {
                  onQrCode!(restaurantId!);
                } else {
                  context.push(
                    AppPaths.posterPage,
                    extra: {
                      "resId": restaurantId,
                      "type": 1,
                    },
                  );
                }
              }
            : null,
      ),
      spacer,
      // 分享按钮 - 使用小程序中的图标
      buildImageButton(
        imagePath: 'assets/images/restaurant/share-hy2.png',
        onPressed: onShare,
      ),
    ];

    // 根据方向返回不同的布局
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 5.w),
      child: direction == Axis.vertical
          ? Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: children,
            )
          : Row(
              children: children,
            ),
    );
  }
}
