import 'dart:ui';

import 'package:flutter/material.dart';

/// 餐厅背景组件
///
/// 用于显示餐厅背景图片，并添加高斯模糊和渐变遮罩效果
/// 这个组件可以在不同餐厅样式中复用
class RestaurantBackgroundWidget extends StatelessWidget {
  /// 背景图片URL
  final String imageUrl;

  /// 背景区域高度
  final double height;

  /// 图片缩放比例
  final double scale;

  /// 模糊效果强度
  final double blurSigma;

  /// 渐变遮罩顶部不透明度
  final double gradientTopOpacity;

  /// 渐变遮罩底部不透明度
  final double gradientBottomOpacity;

  /// 是否显示错误处理UI
  final bool showErrorContainer;

  /// 错误处理UI的颜色
  final Color errorColor;

  /// 构造函数
  const RestaurantBackgroundWidget({
    super.key,
    required this.imageUrl,
    required this.height,
    this.scale = 1.5,
    this.blurSigma = 10.0,
    this.gradientTopOpacity = 0.5,
    this.gradientBottomOpacity = 0.7,
    this.showErrorContainer = true,
    this.errorColor = const Color(0xFFE0E0E0),
  });

  @override
  Widget build(final BuildContext context) {
    return SizedBox(
      height: height,
      width: double.infinity,
      child: Stack(
        children: [
          // 背景图片和虚化效果
          Positioned.fill(
            child: Stack(
              children: [
                // 放大背景图片以填充空间
                Image.network(
                  imageUrl,
                  width: double.infinity,
                  height: height,
                  fit: BoxFit.cover,
                  errorBuilder: (final _, final __, final ___) =>
                      Container(color: errorColor),
                ),
                // 添加高斯模糊效果和半透明遮罩
                Positioned.fill(
                  child: ClipRect(
                    child: BackdropFilter(
                      filter: ImageFilter.blur(
                        sigmaX: blurSigma,
                        sigmaY: blurSigma,
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withOpacity(gradientTopOpacity),
                              Colors.black.withOpacity(gradientBottomOpacity),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
