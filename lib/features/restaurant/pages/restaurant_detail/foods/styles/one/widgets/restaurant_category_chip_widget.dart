import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/theme/app_colors.dart';

/// 餐厅菜品分类选项组件
///
/// 用于展示餐厅菜品分类的选项，如"热销"、"主食"、"套餐"等
/// 具有选中和未选中两种状态，选中时高亮显示
/// 实现了点击选择功能
class RestaurantCategoryChipWidget extends ConsumerWidget {
  /// 分类选项显示的文本
  final String text;

  /// 是否选中状态
  final bool isSelected;

  /// 点击回调函数
  final VoidCallback onTap;

  /// 构造函数
  ///
  /// @param key Widget的key
  /// @param text 分类选项显示的文本
  /// @param isSelected 选项是否处于选中状态
  /// @param onTap 点击回调函数
  const RestaurantCategoryChipWidget({
    super.key,
    required this.text,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return GestureDetector(
      // 点击时触发回调
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: 4.w,
        ),
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.h),
        // 装饰效果：选中时背景为主题色，未选中时为白色
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.white,
          borderRadius: BorderRadius.circular(8.r),
        ),
        // 文本样式：选中时文字为白色，未选中时为主文本颜色
        child: Text(
          text,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
            color: isSelected ? Colors.white : AppColors.textPrimaryColor,
          ),
        ),
      ),
    );
  }
}
