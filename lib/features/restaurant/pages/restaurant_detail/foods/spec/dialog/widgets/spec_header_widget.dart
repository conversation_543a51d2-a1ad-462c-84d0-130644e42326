import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';

/// 规格弹窗头部组件
class SpecHeaderWidget extends StatelessWidget {
  /// 商品信息
  final Food? foodItem;

  const SpecHeaderWidget({
    super.key,
    this.foodItem,
  });

  @override
  Widget build(final BuildContext context) {
    return Container(
      padding: EdgeInsets.all(10.w),
      child: Row(
        children: [
          Expanded(
            child: Text(
              foodItem?.name ?? '',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.start,
            ),
          ),
        ],
      ),
    );
  }
}
