import 'package:user_app/data/models/restaurant/spec_model.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';

/// 规格选择弹窗状态
class SpecModalState {
  /// 构造函数
  const SpecModalState({
    this.isLoading = false,
    this.errorMessage,
    this.foodItem,
    this.specGroups = const [],
    this.selectedSpecs = const {},
    this.selectedOptions = const [],
    this.basePrice = 0.0,
    this.additionalPrice = 0.0,
    this.originalPrice = 0.0,
    this.currentPrice = 0.0,
    this.totalPrice = 0.0,
    this.count = 0,
    this.minCount = 1,
    this.show = false,
    this.seckillInfo,
    this.prefInfo,
    this.marketInfo,
    this.hasPreferential = false,
    this.hasSeckill = false,
    this.maxOrderCount = 0,
    this.seckillMaxOrderCount = 0,
  });

  /// 是否加载中
  final bool isLoading;

  /// 错误信息
  final String? errorMessage;

  /// 食品项目信息 - 保存当前操作的食品
  final Food? foodItem;

  /// 规格组列表
  final List<SpecGroup> specGroups;

  /// 选中的规格 Map<groupId, optionId>
  final Map<int, int> selectedSpecs;

  /// 选中的规格选项信息
  final List<SelectedSpecOption> selectedOptions;

  /// 基础价格
  final double basePrice;

  /// 规格附加价格
  final double additionalPrice;

  /// 原价（规格附加价格）- 与微信小程序originalPrice对应
  final double originalPrice;

  /// 当前显示价格
  final double currentPrice;

  /// 总价格
  final double totalPrice;

  /// 数量
  final int count;

  /// 最小购买数量
  final int minCount;

  /// 是否显示弹窗 - 与微信小程序show对应
  final bool show;

  /// 秒杀信息
  final SeckillInfo? seckillInfo;

  /// 优惠信息
  final PrefInfo? prefInfo;

  /// 营销信息
  final MarketInfo? marketInfo;

  /// 是否有优惠
  final bool hasPreferential;

  /// 是否有秒杀
  final bool hasSeckill;

  /// 最大订购数量
  final int maxOrderCount;

  /// 秒杀最大订购数量
  final int seckillMaxOrderCount;

  /// 拷贝方法
  SpecModalState copyWith({
    bool? isLoading,
    String? errorMessage,
    Food? foodItem,
    List<SpecGroup>? specGroups,
    Map<int, int>? selectedSpecs,
    List<SelectedSpecOption>? selectedOptions,
    double? basePrice,
    double? additionalPrice,
    double? originalPrice,
    double? currentPrice,
    double? totalPrice,
    int? count,
    int? minCount,
    bool? show,
    SeckillInfo? seckillInfo,
    PrefInfo? prefInfo,
    MarketInfo? marketInfo,
    bool? hasPreferential,
    bool? hasSeckill,
    int? maxOrderCount,
    int? seckillMaxOrderCount,
  }) {
    return SpecModalState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      foodItem: foodItem ?? this.foodItem,
      specGroups: specGroups ?? this.specGroups,
      selectedSpecs: selectedSpecs ?? this.selectedSpecs,
      selectedOptions: selectedOptions ?? this.selectedOptions,
      basePrice: basePrice ?? this.basePrice,
      additionalPrice: additionalPrice ?? this.additionalPrice,
      originalPrice: originalPrice ?? this.originalPrice,
      currentPrice: currentPrice ?? this.currentPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      count: count ?? this.count,
      minCount: minCount ?? this.minCount,
      show: show ?? this.show,
      seckillInfo: seckillInfo,
      prefInfo: prefInfo,
      marketInfo: marketInfo,
      hasPreferential: hasPreferential ?? this.hasPreferential,
      hasSeckill: hasSeckill ?? this.hasSeckill,
      maxOrderCount: maxOrderCount ?? this.maxOrderCount,
      seckillMaxOrderCount: seckillMaxOrderCount ?? this.seckillMaxOrderCount,
    );
  }
}

/// 选中的规格选项信息
class SelectedSpecOption {
  final String name;
  final double price;
  final int specTypeId;
  final int specOptionId;

  const SelectedSpecOption({
    required this.name,
    required this.price,
    required this.specTypeId,
    required this.specOptionId,
  });
}

/// 秒杀信息
class SeckillInfo {
  final int seckillId;
  final int seckillActive;
  final double price;
  final int userMaxOrderCount;

  const SeckillInfo({
    required this.seckillId,
    required this.seckillActive,
    required this.price,
    required this.userMaxOrderCount,
  });
}

/// 优惠信息
class PrefInfo {
  final int preferentialId;
  final double discountPrice;
  final int maxOrderCount;

  const PrefInfo({
    required this.preferentialId,
    required this.discountPrice,
    required this.maxOrderCount,
  });
}

/// 营销信息
class MarketInfo {
  final List<dynamic>? steps;

  const MarketInfo({
    required this.steps,
  });
}
