import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/spec_modal_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/spec_modal_state.dart';
import 'package:user_app/generated/l10n.dart';

/// 已选规格显示组件
class SelectedSpecsWidget extends ConsumerWidget {
  /// 构造函数
  const SelectedSpecsWidget({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 只监听已选规格选项
    final selectedOptions = ref.watch(
      specModalControllerProvider
          .select((final state) => state.selectedOptions),
    );

    if (selectedOptions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(bottom: 11.h), // 22rpx / 2
      child: Row(
        children: [
          Text(
            '${S.current.spec_selected}:',
            style: TextStyle(
              fontSize: 14.sp, // 28rpx / 2
              color: Color(0xFF8D8C8C),
            ),
          ),
          SizedBox(width: 4.w), // 8rpx / 2
          Expanded(
            child: RichText(
              text: TextSpan(
                children: _buildSelectedOptionsSpans(selectedOptions),
                style: TextStyle(
                  fontSize: 14.sp, // 28rpx / 2
                  color: Colors.black,
                  fontFamily: AppConstants.mainFont,
                ),
              ),
              textAlign: TextAlign.start,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建已选规格选项的富文本spans
  List<TextSpan> _buildSelectedOptionsSpans(
    final List<SelectedSpecOption> selectedOptions,
  ) {
    if (selectedOptions.isEmpty) return [];

    List<TextSpan> spans = [];
    for (int i = 0; i < selectedOptions.length; i++) {
      // 添加选项名称
      spans.add(
        TextSpan(
          text: selectedOptions[i].name,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
        ),
      );

      // 添加分隔符（除了最后一个选项）
      if (i < selectedOptions.length - 1) {
        spans.add(
          TextSpan(
            text: ' | ',
            style: TextStyle(
              fontSize: 14.sp,
              color: Color(0xFFD4D7DC),
            ),
          ),
        );
      }
    }
    return spans;
  }
}
