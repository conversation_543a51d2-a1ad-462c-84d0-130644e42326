import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/generated/l10n.dart';

/// 优惠提示文本组件
class DiscountPromptText extends StatelessWidget {
  /// 优惠信息
  final Map<String, dynamic>? values;

  /// 是否显示配送费相关文本
  final bool isShipment;

  /// 文本样式
  final TextStyle? textStyle;

  /// 加粗文本样式
  final TextStyle? boldTextStyle;

  /// 字体
  final String? fontFamily;

  /// 当前语言
  final String? lang;

  /// 构造方法
  const DiscountPromptText({
    super.key,
    required this.values,
    this.isShipment = false,
    this.textStyle,
    this.boldTextStyle,
    this.fontFamily,
    required this.lang,
  });

  @override
  Widget build(final BuildContext context) {
    if (values == null) return const SizedBox.shrink();

    // 默认样式
    final defaultTextStyle = textStyle ??
        TextStyle(
          fontSize: 16.sp,
          color: Colors.red,
          fontFamily: AppConstants.mainFont,
          height: 1.5,
        );

    final defaultBoldTextStyle = boldTextStyle ??
        TextStyle(
          fontSize: 14.sp,
          color: Colors.red,
          fontWeight: FontWeight.bold,
          fontFamily: AppConstants.mainFont,
        );

    // 如果所有阶梯都已完成（达到最大优惠），直接显示无下一阶梯的文本
    if (values!['allTiersCompleted'] == true && !isShipment) {
      return _buildReductionWithoutNextTierText(
          defaultTextStyle, defaultBoldTextStyle);
    }

    // 如果已经满足了当前阶梯
    if (values!['isCurrentTierMet'] == true) {
      if (isShipment) {
        // 配送费减免已满足
        return _buildShipmentMetText(defaultTextStyle, defaultBoldTextStyle);
      } else {
        // 满减优惠已满足
        if (values!['hasNextTier'] == true) {
          // 有下一个阶梯
          return _buildReductionWithNextTierText(
              defaultTextStyle, defaultBoldTextStyle);
        } else {
          // 没有下一个阶梯
          return _buildReductionWithoutNextTierText(
              defaultTextStyle, defaultBoldTextStyle);
        }
      }
    } else {
      // 如果尚未满足当前阶梯
      if (values!['remainingAmount'] != null) {
        return _buildRemainingAmountText(
            defaultTextStyle, defaultBoldTextStyle);
      } else if (values!['currentTierPrice'] != null) {
        // 默认显示当前阶梯信息
        return _buildCurrentTierText(defaultTextStyle, defaultBoldTextStyle);
      }
    }

    // 确保总是返回可见内容，不会消失
    if (values!['currentDiscount'] != null) {
      return _buildReductionWithoutNextTierText(
          defaultTextStyle, defaultBoldTextStyle);
    }

    return const SizedBox.shrink();
  }

  /// 构建配送费减免已满足的文本
  Widget _buildShipmentMetText(
      final TextStyle textStyle, final TextStyle boldTextStyle) {
    return RichText(
      textDirection: lang == "ug" ? TextDirection.rtl : TextDirection.ltr,
      text: TextSpan(
        style: TextStyle(
          fontSize: 16.sp,
          color: Colors.red,
          fontFamily: AppConstants.mainFont,
        ),
        children: [
          TextSpan(
            text: S.current.discount_after_price,
            style: TextStyle(
              fontFamily: AppConstants.mainFont,
            ),
          ),
          TextSpan(
            text: " ${FormatUtil.formatPrice(values!['actualFee'])} ",
            style: boldTextStyle,
          ),
          TextSpan(
            text: S.current.yuan,
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.red,
              fontFamily: AppConstants.mainFont,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建满减优惠已满足且有下一个阶梯的文本
  Widget _buildReductionWithNextTierText(
      final TextStyle textStyle, final TextStyle boldTextStyle) {
    if (lang == "ug") {
      return Expanded(
        child: RichText(
          textDirection: lang == "ug" ? TextDirection.rtl : TextDirection.ltr,
          textAlign: TextAlign.center,
          text: TextSpan(
            children: [
              TextSpan(
                text: "تاماق پۇلىدىن ",
                style: textStyle,
              ),
              TextSpan(
                text: FormatUtil.formatPrice(values!['currentDiscount']),
                style: boldTextStyle,
              ),
              TextSpan(
                text: " يۈەن ئېتىبار قىلىندى،",
                style: textStyle,
              ),
              TextSpan(
                text: " يەنە ",
                style: textStyle,
              ),
              TextSpan(
                text: FormatUtil.formatPrice(values!['remainingAmount']),
                style: boldTextStyle,
              ),
              TextSpan(
                text: " يۈەنلىك سېتىۋالسىڭىز جەمئى ",
                style: textStyle,
              ),
              TextSpan(
                text: FormatUtil.formatPrice(values!['nextDiscount']),
                style: boldTextStyle,
              ),
              TextSpan(
                text: " ئېتىبار قىلىندۇ  ",
                style: textStyle,
              ),
            ],
          ),
        ),
      );
    } else {
      return RichText(
        textDirection: lang == "ug" ? TextDirection.rtl : TextDirection.ltr,
        text: TextSpan(
          children: [
            TextSpan(
              text: "已减",
              style: textStyle,
            ),
            TextSpan(
              text: FormatUtil.formatPrice(values!['currentDiscount']),
              style: boldTextStyle,
            ),
            TextSpan(
              text: "元、再买",
              style: textStyle,
            ),
            TextSpan(
              text: FormatUtil.formatPrice(values!['remainingAmount']),
              style: boldTextStyle,
            ),
            TextSpan(
              text: "元、可享受",
              style: textStyle,
            ),
            TextSpan(
              text: FormatUtil.formatPrice(values!['nextDiscount']),
              style: boldTextStyle,
            ),
            TextSpan(
              text: "元优惠",
              style: textStyle,
            ),
          ],
        ),
      );
    }
  }

  /// 构建满减优惠已满足但没有下一个阶梯的文本
  Widget _buildReductionWithoutNextTierText(
      final TextStyle textStyle, final TextStyle boldTextStyle) {
    // 根据语言选择显示文本
    if (lang == "ug") {
      return RichText(
        textDirection: lang == "ug" ? TextDirection.rtl : TextDirection.ltr,
        text: TextSpan(
          children: [
            TextSpan(
              text: "تاماق پۇلىدىن ",
              style: textStyle,
            ),
            TextSpan(
              text: FormatUtil.formatPrice(values!['currentDiscount']),
              style: boldTextStyle,
            ),
            TextSpan(
              text: " يۈەن ئېتىبار قىلىندى ",
              style: textStyle,
            ),
          ],
        ),
      );
    } else {
      return RichText(
        textDirection: lang == "ug" ? TextDirection.rtl : TextDirection.ltr,
        text: TextSpan(
          children: [
            TextSpan(
              text: "美食总额已减",
              style: textStyle,
            ),
            TextSpan(
              text: FormatUtil.formatPrice(values!['currentDiscount']),
              style: boldTextStyle,
            ),
            TextSpan(
              text: "元",
              style: textStyle,
            ),
          ],
        ),
      );
    }
  }

  /// 构建尚未满足当前阶梯但有剩余金额的文本
  Widget _buildRemainingAmountText(
      final TextStyle textStyle, final TextStyle boldTextStyle) {
    if (lang == "ug") {
      final discountText = isShipment ? "كىرا" : "تاماق";
      return RichText(
        textDirection: lang == "ug" ? TextDirection.rtl : TextDirection.ltr,
        text: TextSpan(
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.red,
            fontFamily: "UkijTuzTom",
            height: 1.5,
          ),
          children: [
            TextSpan(
              text: "يەنە",
              style: textStyle,
            ),
            TextSpan(
              text: " ${FormatUtil.formatPrice(values!['remainingAmount'])} ",
              style: boldTextStyle,
            ),
            TextSpan(
              text: "يۈئەنلىك سېتۋالسىڭىز $discountText پۇلىدىن",
              style: textStyle,
            ),
            TextSpan(
              text:
                  " ${FormatUtil.formatPrice(values!['currentDiscount'] ?? values!['discount'])} ",
              style: boldTextStyle,
            ),
            TextSpan(
              // text: isShipment ? "元配送费" : "元",
              text: "يۈەن ئېتىبار قىلىندۇ",
              style: textStyle,
            ),
          ],
        ),
      );
    } else {
      return RichText(
        textDirection: lang == "ug" ? TextDirection.rtl : TextDirection.ltr,
        text: TextSpan(
          children: [
            TextSpan(
              text: "再买",
              style: textStyle,
            ),
            TextSpan(
              text: FormatUtil.formatPrice(values!['remainingAmount']),
              style: boldTextStyle,
            ),
            TextSpan(
              text: "元立减",
              style: textStyle,
            ),
            TextSpan(
              text: FormatUtil.formatPrice(
                  values!['currentDiscount'] ?? values!['discount']),
              style: boldTextStyle,
            ),
            TextSpan(
              text: isShipment ? "元配送费" : "元",
              style: textStyle,
            ),
          ],
        ),
      );
    }
  }

  /// 构建默认显示当前阶梯信息的文本
  Widget _buildCurrentTierText(
      final TextStyle textStyle, final TextStyle boldTextStyle) {
    final discountText = isShipment ? "كىرا" : "تاماق";
    if (lang == "ug") {
      return RichText(
        textDirection: lang == "ug" ? TextDirection.rtl : TextDirection.ltr,
        text: TextSpan(
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.red,
            fontFamily: "UkijTuzTom",
            height: 1.5,
          ),
          children: [
            TextSpan(
              text: FormatUtil.formatPrice(values!['currentTierPrice']),
              style: boldTextStyle,
            ),
            TextSpan(
              text: "يۈئەنلىك سېتۋالسىڭىز $discountText پۇلىدىن",
              style: textStyle,
            ),
            TextSpan(
              text: FormatUtil.formatPrice(values!['currentTierDiscount']),
              style: boldTextStyle,
            ),
            TextSpan(
              text: "يۈەن ئېتىبار قىلىندۇ",
              style: textStyle,
            ),
          ],
        ),
      );
    } else {
      return RichText(
        textDirection: lang == "ug" ? TextDirection.rtl : TextDirection.ltr,
        text: TextSpan(
          children: [
            TextSpan(
              text: "满",
              style: textStyle,
            ),
            TextSpan(
              text: FormatUtil.formatPrice(values!['currentTierPrice']),
              style: boldTextStyle,
            ),
            TextSpan(
              text: "元立减",
              style: textStyle,
            ),
            TextSpan(
              text: FormatUtil.formatPrice(values!['currentTierDiscount']),
              style: boldTextStyle,
            ),
            TextSpan(
              text: isShipment ? "元配送费" : "元",
              style: textStyle,
            ),
          ],
        ),
      );
    }
  }
}
