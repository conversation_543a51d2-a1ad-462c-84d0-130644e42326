import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/generated/l10n.dart';

/// 购物车加减按钮
///
/// [type] 类型
/// [onTap] 点击回调
Widget addRemoveIcon({
  required final CartType type,
  required final Function(BuildContext) onTap,
}) {
  return Builder(
    builder: (final BuildContext context) {
      return InkWell(
        onTap: () => onTap(context),
        child: Icon(
          type == CartType.add
              ? Icons.add_circle_outline
              : Icons.remove_circle_outline,
          color: type == CartType.add ? AppColors.primary : Colors.red,
          size: 30.sp,
        ),
      );
    },
  );
}

/// 购物车加减按钮
///
enum CartType {
  /// 加
  add,

  /// 减
  remove,
}

/// 满减活动标签
///
/// [text] 标签文本
/// [onTap] 点击回调
Widget buildReductionText({
  required final String text,
  final String? image,
  required final VoidCallback onTap,
}) {
  return InkWell(
    onTap: () => onTap(),
    child: Container(
      margin: EdgeInsets.symmetric(horizontal: 0.w),
      child: Stack(
        clipBehavior: Clip.none, // 防止子组件被裁剪
        children: [
          // 右侧图标
          Positioned(
            right: 0,
            bottom: 0,
            top: 0,
            child: SizedBox(
              height: 24.w,
              width: 32.w,
              child: image == null
                  ? Image.asset(
                      "assets/images/restaurant/res_home_price_discount.png",
                      fit: BoxFit.cover,
                    )
                  : image.startsWith("assets")
                      ? Image.asset(
                          image,
                          fit: BoxFit.cover,
                        )
                      : CachedNetworkImage(
                          imageUrl: image,
                          fit: BoxFit.cover,
                        ),
            ),
          ),
          // 文本内容容器 - 调整为内容自适应宽度
          Container(
            margin: EdgeInsets.only(right: 26.w, top: 1.h, bottom: 1.h),
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: Color(0xffFF4F27), width: 0.5),
                bottom: BorderSide(color: Color(0xffFF4F27), width: 0.5),
                left: BorderSide(color: Color(0xffFF4F27), width: 0.5),
              ),
              borderRadius: BorderRadius.circular(3.r),
            ),
            child: Text(
              text,
              style: TextStyle(color: Color(0xffFF4F27), fontSize: 13.sp),
              // 确保文本不截断
              softWrap: true,
              overflow: TextOverflow.visible,
            ),
          ),
        ],
      ),
    ),
  );
}

/// 减配配送费活动标签
///
/// [text] 标签文本
/// [onTap] 点击回调
Widget buildShipmentReductionText({
  required final Tag tag,
  required final VoidCallback onTap,
}) {
  // 解析tag的颜色字段，如果为空则使用默认颜色
  final backgroundColor = tag.background != null && tag.background!.isNotEmpty
      ? Color(int.parse(tag.background!.replaceFirst('#', '0xff')))
      : Colors.white;

  final borderColor = tag.borderColor != null && tag.borderColor!.isNotEmpty
      ? Color(int.parse(tag.borderColor!.replaceFirst('#', '0xff')))
      : Color(0xff10C35A);

  final textColor = tag.color != null && tag.color!.isNotEmpty
      ? Color(int.parse(tag.color!.replaceFirst('#', '0xff')))
      : Color(0xff10C35A);

  return InkWell(
    onTap: () => onTap(),
    child: Container(
      margin: EdgeInsets.symmetric(horizontal: 0.w),
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border.all(
          color: borderColor,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(7.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min, // 关键修改：让 Row 根据内容自适应宽度
        children: [
          // 右侧图标
          CachedNetworkImage(
            fit: BoxFit.fitHeight,
            imageUrl: tag.image ?? "",
            height: 20.h,
          ),
          // 文本内容容器 - 调整为内容自适应宽度
          Flexible(
            child: Text(
              tag.title ?? "",
              style: TextStyle(color: textColor, fontSize: 13.sp),
              softWrap: true,
              overflow: TextOverflow.visible,
            ),
          ),
          SizedBox(width: 4.w), // 文本和图标之间的间距
          Icon(
            Icons.help_outline,
            size: 16.sp,
            color: textColor,
          ),
          SizedBox(width: 4.w),
        ],
      ),
    ),
  );
}

/// 满减活动弹窗
///
/// [tags] 满减活动标签列表
/// [context] 上下文
void showShipmentReductionDialog(
  final List<Tag>? tags,
  final BuildContext context,
  final WidgetRef ref,
) {
  final lang = ref.read(languageProvider);
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (final context) => Material(
      color: Colors.transparent,
      child: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          padding: EdgeInsets.all(5.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15.w),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Directionality(
                textDirection:
                    lang == 'ug' ? TextDirection.rtl : TextDirection.ltr,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(width: 50.w),
                    Text(
                      S.current.shipment_discount,
                      style: TextStyle(
                          fontSize: titleSize,
                          color: AppColors.textPrimaryColor,
                          fontWeight: FontWeight.bold),
                    ),
                    _closeIcon(context: context),
                  ],
                ),
              ),
              SizedBox(height: 10.h),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                child: Column(
                  mainAxisAlignment:  MainAxisAlignment.start,
                  crossAxisAlignment:  CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: EdgeInsets.only(bottom: 10.h),
                      child: Text(
                        S.current.order_amount_some_yuan(
                          FormatUtil.formatAmount(
                            tags?.first.minDeliveryPrice ?? 0,
                          ),
                        ),
                        textAlign: TextAlign.right,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.textPrimaryColor,
                        ),
                      ),
                    ),
                    ...tags?.mapIndexed((final index, final tag) {
                          final distanceEnd = tag.distanceEnd;
                          final price = tag.price;
                          final isLastStep = distanceEnd == 999000;
                          final getKm = FormatUtil.formatDistance(
                            isLastStep
                                ? (tags[tags.length - 2].distanceEnd ?? 0)
                                : (tag.distanceEnd ?? 0),
                          );

                          final kmText = lang == "ug" ? "${getKm}km" : getKm;

                          if (isLastStep) {
                            return _buildShipmentReductionTextContent(
                                text: S.current.out_km_discount,
                                kmText: kmText,
                                price: price ?? 0,
                                lang: lang);
                          } else {
                            return _buildShipmentReductionTextContent(
                                text: S.current.in_km_discount,
                                kmText: kmText,
                                price: price ?? 0,
                                lang: lang);
                          }
                        }) ??
                        [],
                  ],
                ),
              ),
              SizedBox(height: 5.h),
              buildGotItButton(
                context: context,
                onTap: () => Navigator.of(context).pop(),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

/// 构建减配送费活动对话框文字
///
/// [text] 语言包文字
/// [kmText] 距离文字
/// [price] 价格
Widget _buildShipmentReductionTextContent({
  required final Function text,
  required final String kmText,
  required final num price,
  required final String lang,
}) {
  return Directionality(
    textDirection: lang == 'ug' ? TextDirection.rtl : TextDirection.ltr,
    child: Container(
      margin: EdgeInsets.only(bottom: 10.h),
      child: Row(
        children: [
          Text(
            kmText,
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textPrimaryColor,
            ),
          ),
          Text(
            text(FormatUtil.formatAmount(price)),
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textPrimaryColor,
            ),
          ),
        ],
      ),
    ),
  );
}

/// 满减活动弹窗底部按钮
///
/// [context] 上下文
/// [onTap] 点击回调
Widget buildGotItButton({
  required final BuildContext context,
  required final VoidCallback onTap,
}) {
  return InkWell(
    onTap: onTap,
    child: Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(vertical: 10.w, horizontal: 30.w),
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: BorderRadius.circular(45),
      ),
      child: Text(
        S.current.got_it,
        style: TextStyle(fontSize: 16.sp, color: Colors.white),
      ),
    ),
  );
}

/// 对话框关闭按钮
///
/// [context] 上下文
Widget _closeIcon({required final BuildContext context}) {
  return Container(
    width: 50.w,
    padding: const EdgeInsets.all(8.0),
    child: InkWell(
      onTap: () => Navigator.of(context).pop(),
      child: Icon(
        Icons.close,
        size: 20.sp,
        color: AppColors.textSecondColor,
      ),
    ),
  );
}

/// 活动优惠标签弹窗
///
/// [context] 上下文
/// [tags] 活动标签列表
Future showReductionBottomSheet({
  required final BuildContext context,
  required final Tags? tags,
}) {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(15.r)),
    ),
    builder: (final ctx) {
      return Container(
        padding: EdgeInsets.all(10.w),
        width: double.infinity,
        // 限制最大高度，确保不会占满整个屏幕
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min, // 根据内容自适应高度
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 固定的标题行
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.current.discount,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
                _closeIcon(context: context),
              ],
            ),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: double.infinity,
                      child: Wrap(
                        spacing: 10.w, // 横向间距
                        runSpacing: 5.h, // 纵向行间距
                        alignment: WrapAlignment.start,
                        children: [
                          // 处理满减标签
                          if (tags?.reductionTags != null &&
                              tags!.reductionTags!.isNotEmpty)
                            ...tags.reductionTags!.map(
                              (final tag) => buildReductionText(
                                text: tag.title ?? "",
                                onTap: () {},
                              ),
                            ),

                          // 处理配送费减免标签
                          if (tags?.shipmentReductionTags != null &&
                              tags!.shipmentReductionTags!.isNotEmpty)
                            ...tags.shipmentReductionTags!.map(
                              (final tag) => buildShipmentReductionText(
                                tag: tag,
                                onTap: () {},
                              ),
                            ),

                          // 处理配送费减免标签
                          if (tags?.multiDiscountTags != null &&
                              tags!.multiDiscountTags!.isNotEmpty)
                            ...tags.multiDiscountTags!.map(
                              (final tag) => buildReductionText(
                                text: tag.title ?? "",
                                image: tag.image,
                                onTap: () {},
                              ),
                            ),
                        ],
                      ),
                    ),
                    SizedBox(height: 16.h), // 底部留出空间
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    },
  );
}
