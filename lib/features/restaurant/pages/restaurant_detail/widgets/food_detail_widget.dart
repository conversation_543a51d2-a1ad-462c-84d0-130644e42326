import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';

import 'package:user_app/core/utils/image_util.dart';
import 'package:user_app/core/utils/wechat_util.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/core/widgets/image_viewer.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/widgets/prefect_image_width.dart';
import 'package:user_app/core/widgets/star_rating.dart';
import 'package:user_app/data/models/restaurant/food_detail_model.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/data/models/restaurant/restaurant_comment_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/old_price_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/price_quantity_row_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/widgets/restaurant_bottom_bar_widget.dart';
import 'package:user_app/features/restaurant/providers/food_detail_provider.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/restaurant_foods_item.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/quantity_control_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/combo_food_list_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/spec_modal_widget.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';

/// 食品详情页组件
/// 用于显示食品的详细信息，包括图片、名称、价格、描述、评论等
class FoodDetailWidget extends ConsumerStatefulWidget {
  /// 构造函数
  /// @param monthOrderCount 月销量
  /// @param seckillMaxOrderCount 秒杀限购数量
  /// @param foodQuantityTypeValUg 食品数量类型值（乌兹别克语）
  /// @param foodQuantityTypeValZh 食品数量类型值（中文）
  /// @param seckillId 秒杀活动ID
  FoodDetailWidget({
    super.key,
    required this.monthOrderCount,
    required this.seckillMaxOrderCount,
    required this.foodQuantityTypeValUg,
    required this.foodQuantityTypeValZh,
    required this.seckillId,
    required this.maxOrderCount,
    required this.restaurantId,
    required this.isResting,
    required this.food,
  });
  int monthOrderCount;
  int seckillMaxOrderCount;
  String foodQuantityTypeValUg;
  String foodQuantityTypeValZh;
  int seckillId;
  int maxOrderCount;
  int restaurantId;
  bool isResting;
  Food? food;

  @override
  ConsumerState createState() => _FoodDetailWidgetState();
}

class _FoodDetailWidgetState extends ConsumerState<FoodDetailWidget> {
  int page = 1;
  int type = 1;
  int id = 0;
  int isFavorite = 0;

  @override
  Widget build(final BuildContext context) {
    print('restaurantId : ${widget.restaurantId}');

    return SafeArea(
      child: Container(
        height: 700.w,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.w),
        ),
        child: ref.watch(foodDetailProvider).when(
          data: (final data) {
            return _body(context, data);
          },
          error: (final error, final stackTrace) {
            // 数据加载失败，显示错误信息
            return Center(child: Text('address Page Error: $error'));
          },
          loading: () {
            // 正在加载，显示加载指示器
            return Center(
              child: LoadingWidget(),
            );
          },
        ),
      ),
    );
  }

  Widget _body(final BuildContext context, final FoodDetailData? data) {
    id = data?.id ?? 0;
    isFavorite = data?.isFavorite ?? 0;
    final foodCount = _getFoodCount(data!);
    return Stack(
      children: [
        Column(
          children: [
            Container(
              padding: EdgeInsets.symmetric(vertical: 15.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(20.w),
                  topLeft: Radius.circular(20.w),
                ),
              ),
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.max,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.w),
                      child: Icon(
                        Icons.clear,
                        color: AppColors.textSecondColor,
                        size: 26.sp,
                      ),
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      InkWell(
                        onTap: () async {
                          bool favoriteChange = false;
                          if (isFavorite == 0) {
                            favoriteChange = await ref
                                .read(foodCollectProvider.notifier)
                                .foodAddCollect(foodId: id);
                          } else {
                            favoriteChange = await ref
                                .read(foodCollectProvider.notifier)
                                .foodRemoveCollect(foodId: id);
                          }
                          if (favoriteChange) {
                            ref
                                .read(foodDetailProvider.notifier)
                                .fetchFoodDetailData(foodId: id);
                          }
                          setState(() {});
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 10.w,
                            vertical: 8.w,
                          ),
                          child: Image.asset(
                            isFavorite == 1
                                ? 'assets/images/restaurant/like2_fill.png'
                                : 'assets/images/restaurant/like2.png',
                            color: isFavorite == 0
                                ? AppColors.textPrimaryColor
                                : null,
                            width: 30.w,
                          ),
                        ),
                      ),
                      if ((widget.seckillId == 0 && widget.maxOrderCount > 0) ||
                          (widget.seckillId > 0))
                        InkWell(
                          onTap: () {
                            context.push(
                              AppPaths.posterPage,
                              extra: {
                                "resId": id.toString(),
                                "type": 5,
                              },
                            );
                          },
                          child: Row(
                            children: [
                              SizedBox(
                                width: 15.w,
                              ),
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 10.w,
                                  vertical: 8.w,
                                ),
                                child: SvgPicture.asset(
                                  "assets/images/share/share-pyq.svg",
                                  width: 30.w,
                                  height: 30.w,
                                ),
                              ),
                            ],
                          ),
                        ),
                      SizedBox(
                        width: 15.w,
                      ),
                      InkWell(
                        onTap: () async {
                          await shareFood();
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 10.w,
                            vertical: 8.w,
                          ),
                          child: SvgPicture.asset(
                            "assets/images/share/share-hy.svg",
                            width: 30.w,
                            height: 30.w,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Expanded(
              child: NotificationListener<ScrollNotification>(
                onNotification: (final ScrollNotification notification) {
                  if (notification is ScrollUpdateNotification) {
                    // 判断是否滚动到底部
                    if (notification.metrics.pixels ==
                        notification.metrics.maxScrollExtent) {
                      if (ref.watch(canCommentDataProvider)) {
                        _onScrollToBottom();
                      }
                    }
                  }
                  return true;
                },
                child: SingleChildScrollView(
                  child: Container(
                    padding:
                        EdgeInsets.only(right: 15.w, left: 15.w, bottom: 15.w),
                    child: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            showImageViewer(
                              context,
                              imageUrls: [data?.image ?? ''],
                              heroTagPrefix: 'chat_image',
                            );
                          },
                          child: Container(
                            height: 380.w,
                            color: Colors.white,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(10.w),
                              child: PrefectImageWidth(
                                imageUrl: data?.image ?? '',
                                fit: BoxFit.cover,
                                width: MediaQuery.of(context).size.width,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 12.w,
                        ),
                        Container(
                          alignment: ref.watch(languageProvider) == 'ug'
                              ? Alignment.centerRight
                              : Alignment.centerLeft,
                          child: Text(
                            data?.name ?? '',
                            style: TextStyle(
                                color: Colors.black, fontSize: soBigSize),
                          ),
                        ),
                        SizedBox(
                          height: 12.w,
                        ),
                        Container(
                          child: Row(
                            children: [
                              Row(
                                children: [
                                  Text(
                                    '${S.current.sales_count}:',
                                    style: TextStyle(
                                      color: AppColors.textPrimaryColor,
                                      fontSize: mainSize,
                                    ),
                                  ),
                                  Text(
                                    '${widget.monthOrderCount}',
                                    style: TextStyle(
                                      color: AppColors.textPrimaryColor,
                                      fontSize: mainSize,
                                    ),
                                  ),
                                ],
                              ),
                              if (widget.seckillMaxOrderCount != 0)
                                Row(
                                  children: [
                                    SizedBox(
                                      width: 10.w,
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                          '${S.current.sec_kill_limit}:',
                                          style: TextStyle(
                                            color: AppColors.textPrimaryColor,
                                            fontSize: mainSize,
                                          ),
                                        ),
                                        Text(
                                          '${widget.seckillMaxOrderCount}',
                                          style: TextStyle(
                                            color: AppColors.textPrimaryColor,
                                            fontSize: mainSize,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              SizedBox(
                                width: 10.w,
                              ),
                              Text(
                                ref.watch(languageProvider) == 'ug'
                                    ? widget.foodQuantityTypeValUg
                                    : widget.foodQuantityTypeValZh,
                                style: TextStyle(
                                  color: AppColors.textPrimaryColor,
                                  fontSize: mainSize,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 8.w,
                        ),
                        Container(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  // 当前价格显示
                                  RichText(
                                    text: TextSpan(
                                      children: [
                                        TextSpan(
                                          text: '¥',
                                          style: TextStyle(
                                            fontSize: mainSize,
                                            fontWeight: FontWeight.bold,
                                            fontFamily: AppConstants.numberFont,
                                            color: AppColors.primary,
                                          ),
                                        ),
                                        TextSpan(
                                          text: PriceQuantityRowWidget
                                              .getCurrentPrice(
                                                  widget.food, ref),
                                          style: TextStyle(
                                            fontSize: 28.sp,
                                            fontFamily: AppConstants.numberFont,
                                            fontWeight: FontWeight.bold,
                                            color: AppColors.primary,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(width: 10.w),
                                  // 显示原价（按小程序的条件显示原价）
                                  OldPriceWidget(
                                    food: widget.food,
                                    fontSize: 20.sp,
                                  ),
                                ],
                              ),
                              QuantityControlWidget(
                                foodCount: foodCount,
                                foodId: widget.food?.id,
                                food: widget.food,
                                relationsExists: false,
                                isResting: widget.isResting,
                                onAdd: _handleAddFood,
                                onRemove: _handleRemoveFood,
                                onAddMinCount: _handleAddMinCountFood,
                                onSpecSelect: (final food) => _handleSpecSelect(
                                    food, widget.isResting ?? false),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 8.w,
                        ),
                        // 套餐美食列表
                        if (widget.food?.foodType == 2 &&
                            widget.food?.comboFoodItems != null &&
                            widget.food!.comboFoodItems!.isNotEmpty)
                          ComboFoodListWidget(
                            comboFoodItems: widget.food!.comboFoodItems!,
                            maxDisplayCount: 10,
                            showBackground: true,
                          ),
                        Container(
                          alignment: ref.watch(languageProvider) == 'ug'
                              ? Alignment.centerRight
                              : Alignment.centerLeft,
                          child: Text(
                            data?.description ?? '',
                            style: TextStyle(
                              color: AppColors.textSecondColor,
                              fontSize: mainSize,
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 12.w,
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6.w),
                            color: AppColors.baseBackgroundColor,
                          ),
                          height: 45.w,
                          child: Row(
                            children: [
                              SizedBox(
                                width: 5.w,
                              ),
                              Icon(
                                Icons.volume_up,
                                color: Colors.brown,
                              ),
                              SizedBox(
                                width: 5.w,
                              ),
                              Text(
                                S.current.food_have_to_eat,
                                style: TextStyle(
                                  color: Colors.brown,
                                  fontSize: mainSize,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 12.w,
                        ),
                        Container(
                          alignment: ref.watch(languageProvider) == 'ug'
                              ? Alignment.centerRight
                              : Alignment.centerLeft,
                          child: Text(
                            S.current.comments,
                            style: TextStyle(
                                color: Colors.black, fontSize: soBigSize),
                          ),
                        ),
                        SizedBox(
                          height: 12.w,
                        ),
                        Container(
                          alignment: ref.watch(languageProvider) == 'ug'
                              ? Alignment.centerRight
                              : Alignment.centerLeft,
                          child: Wrap(
                            spacing: 10.w,
                            runSpacing: 10.w,
                            children: List.generate(
                              (ref
                                          .watch(restaurantCommentListProvider)
                                          .value
                                          ?.type ??
                                      [])
                                  .length,
                              (final index) => _tabWidget(
                                index,
                                ref
                                    .watch(restaurantCommentListProvider)
                                    .value!
                                    .type![index],
                                data,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 12.w,
                        ),
                        if ((ref.watch(commentItemsProvider)).isNotEmpty)
                          ...List.generate(
                            (ref.watch(commentItemsProvider)).length,
                            (final index) => _item(
                              context,
                              ref.watch(commentItemsProvider)[index],
                            ),
                          ),
                        if ((ref.watch(commentItemsProvider)).isEmpty)
                          Container(
                            padding: EdgeInsets.only(top: 15.w),
                            height: 100.w,
                            child: Text(
                              S.current.comment_no_data,
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: titleSize,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(
              height: 20.w,
            ),
          ],
        ),
        widget.isResting
            ? Positioned(
                bottom: 0.w,
                right: 0.w,
                left: 0.w,
                child: RestaurantBottomBarWidget(
                  onCloseModal: () {
                    Navigator.of(context).pop();
                  },
                ),
              )
            : Positioned(
                bottom: 8.w,
                right: 10.w,
                left: 10.w,
                child: RestaurantBottomBarWidget(
                  onCloseModal: () {
                    Navigator.of(context).pop();
                  },
                ),
              )
      ],
    );
  }

  /// 获取购物车中主商品的数量
  int _getFoodCount(final FoodDetailData foodItem) {
    return _getItemCount(foodItem);
  }

  /// 获取购物车中指定商品的数量
  int _getItemCount(final FoodDetailData? food) {
    if (food == null) return 0;
    final cartItems = ref.watch(shoppingCartProvider);
    try {
      final item = cartItems.firstWhere(
        (final item) => item.id == food.id,
        orElse: () => SelectFoodItem(count: 0),
      );
      return item.count ?? 0;
    } catch (e) {
      debugPrint('Error in _getItemCount: $e');
      return 0;
    }
  }

  /// 处理最小购买数量添加
  void _handleAddMinCountFood(final Food food) {
    // 一次性添加最小数量的食品
    for (int i = 0; i < (food.minCount ?? 1); i++) {
      ref.read(shoppingCartProvider.notifier).addFoodToCart(
            foodItem: food,
          );
    }

    // 如果是主食品，标记为直接添加
    DirectlyAddedFoods.add(food.id);
  }

  /// 处理规格选择
  void _handleSpecSelect(final Food food, final bool isResting) async {
    // 显示规格选择弹窗
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (final BuildContext context) {
        return SpecModalWidget(
          foodItem: food,
          restaurantId: widget.restaurantId,
          isResting: isResting,
          onClose: () {
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  /// 处理减少食品逻辑
  void _handleRemoveFood(final int? foodId) {
    ref.read(shoppingCartProvider.notifier).removeFoodCount(
          foodId: foodId ?? 0,
        );
  }

  /// 处理添加食品逻辑
  void _handleAddFood(
    final int foodCount,
    final int? foodId,
    final Food? food,
    final bool relationsExists, [
    final BuildContext? btnContext,
  ]) {
    if (foodCount == 0) {
      if (food != null) {
        // 只有当添加的是主食品（不是关联食品）时，才记录为直接添加
        // if (food.id == widget.foodItem?.id) {
        DirectlyAddedFoods.add(foodId);
        // }

        // 添加到购物车
        ref.read(shoppingCartProvider.notifier).addFoodToCart(
              foodItem: food,
            );

        // 执行加入购物车动画
        // _playAddToCartAnimation(btnContext);

        // 如果有关联商品则显示，但只在添加主食品时
        // if (relationsExists && food.id == widget.foodItem?.id) {
        //   _showRelationsWithAnimation();
        // }
      }
    } else {
      // 增加数量
      ref.read(shoppingCartProvider.notifier).addFoodCount(
            foodId: foodId ?? 0,
          );

      // 执行加入购物车动画
      // _playAddToCartAnimation(btnContext);
    }
  }

  // 方法在滚动到底部时触发
  Future<void> _onScrollToBottom() async {
    page = page + 1;
    ref.read(restaurantCommentListProvider.notifier).fetchRestaurantCommentList(
          page: page,
          id: widget.restaurantId,
          type: type,
          foodId: id,
        );
    print("滚动到底部了！----");
  }

  int currentTab = 0;
  Widget _tabWidget(final int index, final Type type, final data) {
    return InkWell(
      onTap: () {
        print('index $index, widget.restaurantId ${widget.restaurantId}');

        currentTab = index;
        page = 1;
        this.type = type.type ?? 0;
        ref
            .read(restaurantCommentListProvider.notifier)
            .fetchRestaurantCommentList(
              page: page,
              id: widget.restaurantId,
              type: this.type,
              foodId: id,
            );
        // ref.watch(canCommentDataProvider.notifier).state = true;
        setState(() {});
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.w),
          color: index == currentTab
              ? AppColors.baseGreenColor
              : AppColors.baseBackgroundColor,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${type.name}',
              style: TextStyle(
                fontSize:
                    ref.read(languageProvider) == 'ug' ? mainSize : titleSize,
                color: index == currentTab
                    ? AppColors.baseBackgroundColor
                    : AppColors.baseGreenColor,
                fontWeight: index == currentTab ? FontWeight.bold : null,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.start,
            ),
            SizedBox(
              width: 4.w,
            ),
            Text(
              '(${type.count})',
              style: TextStyle(
                fontSize: secondSize,
                color: index == currentTab
                    ? AppColors.baseBackgroundColor
                    : AppColors.baseGreenColor,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.start,
            ),
          ],
        ),
      ),
    );
  }

  Widget _item(final BuildContext context, final Items item) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 12.w),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(color: Colors.grey.shade100, width: 1.0),
            ),
          ),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(10.w),
                child: PrefectImage(
                  imageUrl: item.userAvatar ?? '',
                  fit: BoxFit.cover,
                  width: 52.w,
                  height: 52.w,
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${(item.userName == null || item.userName == '') ? '******' : item.userName}',
                          style: TextStyle(
                            fontSize: mainSize,
                            color: Colors.black,
                          ),
                        ),
                        Text(
                          item.createdAt ?? '',
                          style: TextStyle(
                            fontSize: secondSize,
                            color: AppColors.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 5.w,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          S.current.evaluate_in_general,
                          style: TextStyle(
                            fontSize: secondSize,
                            color: Colors.black,
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            StarRating(
                              rating: double.parse('${item.star ?? 5}'),
                              size: 18.sp, // 32rpx / 2
                              gap: 4.0, // 设置星星间隔
                              hideTip: true,
                            ),
                            SizedBox(
                              width: 15.w,
                            ),
                            Container(
                              width: 111.w,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(6.w),
                                color: AppColors.baseBackgroundColor,
                              ),
                              padding: EdgeInsets.symmetric(
                                horizontal: 6.w,
                                vertical: 3.w,
                              ),
                              child: Text(
                                '${item.foodName}',
                                style: TextStyle(
                                  fontSize: mainSize,
                                  color: AppColors.baseGreenColor,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.start,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    if ((item.star ?? 5) < 5)
                      Column(
                        children: [
                          SizedBox(
                            height: 5.w,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                S.current.evaluate_taste,
                                style: TextStyle(
                                  fontSize: secondSize,
                                  color: Colors.black,
                                ),
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  StarRating(
                                    rating:
                                        double.parse('${item.foodStar ?? 5}'),
                                    size: 18.sp, // 32rpx / 2
                                    gap: 4.0, // 设置星星间隔
                                    hideTip: true,
                                  ),
                                  SizedBox(
                                    width: 15.w,
                                  ),
                                  Container(
                                    width: 111.w,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    if ((item.star ?? 5) < 5)
                      Column(
                        children: [
                          SizedBox(
                            height: 5.w,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                S.current.evaluate_packing,
                                style: TextStyle(
                                  fontSize: secondSize,
                                  color: Colors.black,
                                ),
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  StarRating(
                                    rating:
                                        double.parse('${item.boxStar ?? 5}'),
                                    size: 18.sp, // 32rpx / 2
                                    gap: 4.0, // 设置星星间隔
                                    hideTip: true,
                                  ),
                                  SizedBox(
                                    width: 15.w,
                                  ),
                                  Container(
                                    width: 111.w,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
        if (item.text != null && item.text != '')
          Container(
            alignment: ref.watch(languageProvider) == 'ug'
                ? Alignment.centerRight
                : Alignment.centerLeft,
            child: Text(
              '${item.text}',
              style: TextStyle(fontSize: mainSize, color: Colors.black),
            ),
          ),
        if ((item.images ?? []).isNotEmpty)
          Column(
            children: List.generate(
              (item.images ?? []).length,
              (final imageIndex) => Column(
                children: [
                  SizedBox(
                    height: 12.w,
                  ),
                  InkWell(
                    onTap: () {
                      showImageViewer(
                        context,
                        imageUrls: [item.images![imageIndex]],
                        heroTagPrefix: 'chat_image',
                      );
                    },
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(10.w),
                      child: PrefectImage(
                        imageUrl: item.images![imageIndex],
                        fit: BoxFit.cover,
                        width: MediaQuery.of(context).size.width,
                        height: 190.w,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        Column(
          children: List.generate(
            (item.replies ?? []).length,
            (final repIndex) => Column(
              children: [
                SizedBox(
                  height: 12.w,
                ),
                Container(
                  alignment: ref.watch(languageProvider) == 'ug'
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  padding: EdgeInsets.symmetric(
                    horizontal: 10.w,
                    vertical: 10.w,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6.w),
                    color: AppColors.baseBackgroundColor,
                  ),
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: S.current.reply_res,
                          style: TextStyle(
                            color: Colors.indigo,
                            fontSize: mainSize,
                            fontFamily: AppConstants.mainFont,
                            height: 1.2,
                          ),
                        ),
                        TextSpan(
                          text: '${item.replies?[repIndex].text}',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: mainSize,
                            fontFamily: AppConstants.mainFont,
                            height: 1.2,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 分享美食
  Future<void> shareFood() async {
    LoadingDialog().show();

    // 获取当前语言ID
    final languageId = ref.watch(languageProvider) == 'zh' ? 2 : 1;

    // 根据小程序分享路径构建参数
    // path="/pages/index/index?resId=" + app.globalData.appMerchantId + "&langId=" + app.globalData.langId + "&foodsId=" + foods.id
    final sharePath =
        "pages/index/index?resId=${widget.restaurantId}&langId=$languageId&foodsId=${widget.food?.id ?? id}";

    final result = await WechatUtil().shareMiniProgram(
      thumbData: await ImageUtil.getImageFromUrl(widget.food?.image ?? ''),
      path: sharePath,
      title:
          "${widget.food?.name ?? ''} ${S.current.share_price}${widget.food?.price ?? 0}",
      description: widget.food?.name ?? '',
    );

    if (result) {
      BotToast.showText(text: S.current.about_share_success);
    } else {
      BotToast.showText(text: S.current.about_share_failed);
    }
    LoadingDialog().hide();
  }
}
