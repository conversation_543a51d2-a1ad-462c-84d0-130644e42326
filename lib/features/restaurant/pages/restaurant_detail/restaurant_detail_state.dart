import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/data/models/restaurant/restaurant_home_model.dart';

/// 餐厅详情页状态
class RestaurantDetailState {
  /// 是否加载中（只用于整个页面初始加载）
  final bool isLoading;

  /// 上一步的foodIds
  final List<dynamic>? fromFoodIds;

  /// 食品列表是否加载中（用于筛选、搜索时局部加载状态）
  final bool isFoodsLoading;

  /// 错误信息
  final String? error;

  /// 是否显示食品列表
  final bool isShowFoodsList;

  /// 餐厅数据
  final RestaurantHomeData? restaurantData;

  /// 食品列表数据
  final FoodsData? foodsData;

  /// 食品列表（与配置分离）
  final List<Food>? foods;

  /// 食品分页数据
  final int? foodsPage;
  final int? foodsTotalPage;
  final int? foodsTotal;

  /// 食品配置相关数据
  final int? canMulazimTake;
  final int? canSelfTake;
  final Distribution? distribution;
  final int? isResting;
  final int? limit;
  final int? lotteryActive;
  final num? lotteryMinOrderPrice;
  final num? lowestPercent;
  final Market? market;
  final int? notSameLocation;
  final NotSameLocationMsg? notSameLocationMsg;
  final Notice? notice;
  final int? restaurantType;
  final int? isOpen;
  final List<ReductionStep>? reductionSteps;
  final Tags? tags;
  final List<String>? takeTime;

  /// 当前选中的分类索引
  final int selectedCategoryIndex;

  /// 当前选中的分类ID
  final int? selectedTypeId;

  /// 当前页码
  final int page;

  /// 建筑ID
  final int buildingId;

  /// 是否还有更多数据
  final bool hasMoreData;

  /// 餐厅是否已收藏
  final bool isFavorite;

  /// 构造函数
  const RestaurantDetailState({
    this.isLoading = true,
    this.isFoodsLoading = false,
    this.error,
    this.isShowFoodsList = false,
    this.restaurantData,
    this.foodsData,
    this.foods,
    this.foodsPage,
    this.foodsTotalPage,
    this.foodsTotal,
    this.canMulazimTake,
    this.canSelfTake,
    this.distribution,
    this.isResting,
    this.limit,
    this.lotteryActive,
    this.lotteryMinOrderPrice,
    this.lowestPercent,
    this.market,
    this.notSameLocation,
    this.notSameLocationMsg,
    this.notice,
    this.restaurantType,
    this.isOpen,
    this.reductionSteps,
    this.tags,
    this.takeTime,
    this.selectedCategoryIndex = 0,
    this.selectedTypeId,
    this.page = 1,
    this.buildingId = 1979, // 默认值
    this.hasMoreData = true,
    this.isFavorite = false,
    this.fromFoodIds,
  });

  /// 创建加载中状态
  factory RestaurantDetailState.loading() {
    return const RestaurantDetailState(
      isLoading: true,
      isFoodsLoading: false,
    );
  }

  /// 创建错误状态
  factory RestaurantDetailState.error(String errorMsg) {
    return RestaurantDetailState(
      isLoading: false,
      isFoodsLoading: false,
      error: errorMsg,
    );
  }

  /// 创建数据加载完成状态
  factory RestaurantDetailState.loaded({
    required RestaurantHomeData restaurantData,
    required FoodsData foodsData,
    int buildingId = 1979,
    List<dynamic>? fromFoodIds,
  }) {
    // 判断是否还有更多数据
    final bool hasMore = foodsData.page != null &&
        foodsData.totalPage != null &&
        foodsData.page! < foodsData.totalPage!;

    return RestaurantDetailState(
      isLoading: false,
      isFoodsLoading: false,
      restaurantData: restaurantData,
      foodsData: foodsData,
      foods: foodsData.foods,
      foodsPage: foodsData.page,
      foodsTotalPage: foodsData.totalPage,
      foodsTotal: foodsData.total,
      // 存储配置相关数据
      canMulazimTake: foodsData.canMulazimTake,
      canSelfTake: foodsData.canSelfTake,
      distribution: foodsData.distribution,
      isResting: foodsData.isResting,
      limit: foodsData.limit,
      lotteryActive: foodsData.lotteryActive,
      lotteryMinOrderPrice: foodsData.lotteryMinOrderPrice,
      lowestPercent: foodsData.lowestPercent,
      market: foodsData.market,
      notSameLocation: foodsData.notSameLocation,
      notSameLocationMsg: foodsData.notSameLocationMsg,
      notice: foodsData.notice,
      restaurantType: foodsData.restaurantType,
      isOpen: foodsData.isOpen,
      reductionSteps: foodsData.reductionSteps,
      tags: foodsData.tags,
      takeTime: foodsData.takeTime,
      buildingId: buildingId,
      hasMoreData: hasMore,
      isFavorite: restaurantData.isFavorite == 1,
      fromFoodIds: fromFoodIds,
    );
  }

  /// 拷贝方法
  RestaurantDetailState copyWith({
    bool? isLoading,
    bool? isFoodsLoading,
    String? error,
    bool? isShowFoodsList,
    RestaurantHomeData? restaurantData,
    FoodsData? foodsData,
    List<Food>? foods,
    int? foodsPage,
    int? foodsTotalPage,
    int? foodsTotal,
    int? canMulazimTake,
    int? canSelfTake,
    Distribution? distribution,
    int? isResting,
    int? limit,
    int? lotteryActive,
    num? lotteryMinOrderPrice,
    num? lowestPercent,
    Market? market,
    int? notSameLocation,
    NotSameLocationMsg? notSameLocationMsg,
    Notice? notice,
    int? restaurantType,
    int? isOpen,
    List<ReductionStep>? reductionSteps,
    Tags? tags,
    List<String>? takeTime,
    int? selectedCategoryIndex,
    int? selectedTypeId,
    int? page,
    int? buildingId,
    bool? hasMoreData,
    bool? isFavorite,
    List<dynamic>? fromFoodIds,
  }) {
    return RestaurantDetailState(
      isLoading: isLoading ?? this.isLoading,
      isFoodsLoading: isFoodsLoading ?? this.isFoodsLoading,
      error: error ?? this.error,
      isShowFoodsList: isShowFoodsList ?? this.isShowFoodsList,
      restaurantData: restaurantData ?? this.restaurantData,
      foodsData: foodsData ?? this.foodsData,
      foods: foods ?? this.foods,
      foodsPage: foodsPage ?? this.foodsPage,
      foodsTotalPage: foodsTotalPage ?? this.foodsTotalPage,
      foodsTotal: foodsTotal ?? this.foodsTotal,
      // 复制配置相关数据
      canMulazimTake: canMulazimTake ?? this.canMulazimTake,
      canSelfTake: canSelfTake ?? this.canSelfTake,
      distribution: distribution ?? this.distribution,
      isResting: isResting ?? this.isResting,
      limit: limit ?? this.limit,
      lotteryActive: lotteryActive ?? this.lotteryActive,
      lotteryMinOrderPrice: lotteryMinOrderPrice ?? this.lotteryMinOrderPrice,
      lowestPercent: lowestPercent ?? this.lowestPercent,
      market: market ?? this.market,
      notSameLocation: notSameLocation ?? this.notSameLocation,
      notSameLocationMsg: notSameLocationMsg ?? this.notSameLocationMsg,
      notice: notice ?? this.notice,
      restaurantType: restaurantType ?? this.restaurantType,
      isOpen: isOpen ?? this.isOpen,
      reductionSteps: reductionSteps ?? this.reductionSteps,
      tags: tags ?? this.tags,
      takeTime: takeTime ?? this.takeTime,
      selectedCategoryIndex:
          selectedCategoryIndex ?? this.selectedCategoryIndex,
      selectedTypeId: selectedTypeId ?? this.selectedTypeId,
      page: page ?? this.page,
      buildingId: buildingId ?? this.buildingId,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      isFavorite: isFavorite ?? this.isFavorite,
      fromFoodIds: fromFoodIds ?? this.fromFoodIds,
    );
  }

  /// 显示食品列表
  RestaurantDetailState showFoodsList() {
    return copyWith(isShowFoodsList: true);
  }

  /// 隐藏食品列表
  RestaurantDetailState hideFoodsList() {
    return copyWith(isShowFoodsList: false);
  }

  /// 更新选中的分类索引
  RestaurantDetailState updateSelectedCategory(int index, int? typeId) {
    return copyWith(
      selectedCategoryIndex: index,
      selectedTypeId: typeId,
      // 切换分类时重置页码和hasMoreData
      page: 1,
      hasMoreData: true,
    );
  }

  /// 更新上一步的foodIds
  RestaurantDetailState updateFromFoodIds(List<dynamic> ids) {
    return copyWith(fromFoodIds: ids);
  }

  /// 加载下一页
  RestaurantDetailState nextPage() {
    return copyWith(page: page + 1);
  }

  /// 重置筛选条件
  RestaurantDetailState resetFilters() {
    return copyWith(
      selectedCategoryIndex: 0,
      selectedTypeId: null,
      page: 1,
      hasMoreData: true,
    );
  }

  /// 更新建筑ID
  RestaurantDetailState updateBuildingId(int newBuildingId) {
    return copyWith(
      buildingId: newBuildingId,
      // 更改位置时重置其他参数
      page: 1,
      selectedCategoryIndex: 0,
      selectedTypeId: null,
      hasMoreData: true,
    );
  }
}
