import 'dart:developer';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/widgets/index.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/widgets/restaurant_bottom_bar_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/styles/one/restaurant_style_one.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/styles/two/restaurant_style_two.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/styles/three/restaurant_style_three.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/widgets/restaurant_search_bar.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/widgets/cart_food_list_widget.dart';
import 'package:user_app/features/activity/pages/user_ranking/widgets/ranking_card_widget.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/widgets/restaurant_detail_skeleton.dart';
import 'package:user_app/routes/paths.dart';

/// 餐厅详情页
class RestaurantDetailPage extends ConsumerStatefulWidget {
  /// 餐厅ID
  final int restaurantId;

  /// 建筑id
  final int buildingId;

  final List<dynamic> ids;

  /// "再来一单"商品列表
  final List<dynamic> comeAgainItems;

  /// 构造函数
  const RestaurantDetailPage({
    super.key,
    required this.restaurantId,
    required this.buildingId,
    required this.ids,
    this.comeAgainItems = const [],
  });

  @override
  ConsumerState<RestaurantDetailPage> createState() =>
      _RestaurantDetailPageState();
}

class _RestaurantDetailPageState extends ConsumerState<RestaurantDetailPage>
    with RouteAware {
  bool isShowKeyboard = false;
  late final ScrollController _scrollController;
  bool _isScrolling = false; // 添加滚动状态标记
  Timer? _scrollTimer; // 添加定时器来处理滚动停止检测
  bool _isManuallyHidden = false; // 手动隐藏状态

  @override
  void initState() {
    super.initState();

    // 初始化 ScrollController
    _scrollController = ScrollController();

    // 添加滚动监听
    _scrollController.addListener(_onScrollChanged);

    // 在初始化时加载数据
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      ref.read(restaurantDetailControllerProvider.notifier).init(
            restaurantId: widget.restaurantId,
            buildingId: widget.buildingId,
            ids: widget.ids,
            comeAgainItems: widget.comeAgainItems,
          );
    });
  }

  /// 滚动监听回调
  void _onScrollChanged() {
    // 取消之前的定时器
    _scrollTimer?.cancel();

    // 设置滚动状态为true
    if (!_isScrolling) {
      setState(() {
        _isScrolling = true;
      });
    }

    // 设置定时器，500ms后如果没有新的滚动事件，则认为停止滚动
    _scrollTimer = Timer(Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _isScrolling = false;
        });
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 注册路由观察者
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      AppContext().routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    // 清理定时器
    _scrollTimer?.cancel();
    // 释放 ScrollController
    _scrollController.dispose();
    // 取消注册路由观察者
    AppContext().routeObserver.unsubscribe(this);
    super.dispose();
  }

  /// 检查点击位置是否在搜索框区域内
  bool _isClickInSearchBar(PointerDownEvent details) {
    try {
      // 使用静态GlobalKey查找搜索框
      final RenderBox? searchBarBox = RestaurantSearchBar
          .searchBarGlobalKey.currentContext
          ?.findRenderObject() as RenderBox?;
      if (searchBarBox == null) return false;

      // 获取搜索框在屏幕上的位置和大小
      final searchBarPosition = searchBarBox.localToGlobal(Offset.zero);
      final searchBarSize = searchBarBox.size;

      // 检查点击位置是否在搜索框区域内
      final isInSearchBar = details.position.dx >= searchBarPosition.dx &&
          details.position.dx <= searchBarPosition.dx + searchBarSize.width &&
          details.position.dy >= searchBarPosition.dy &&
          details.position.dy <= searchBarPosition.dy + searchBarSize.height;

      debugPrint('搜索框位置: $searchBarPosition, 大小: $searchBarSize');
      debugPrint('点击位置: ${details.position}');
      debugPrint('是否在搜索框内: $isInSearchBar');

      return isInSearchBar;
    } catch (e) {
      debugPrint('检查搜索框点击位置失败: $e');
      return false;
    }
  }

  /// 当从其他页面返回到当前页面时触发 - 类似微信小程序的onShow
  @override
  void didPopNext() {
    super.didPopNext();

    // 从其他页面返回到餐厅详情页时，更新美食列表数据
    // 这里主要是为了刷新商品的价格、库存、促销状态等信息
    final controller = ref.read(restaurantDetailControllerProvider.notifier);

    // 刷新美食列表数据
    controller.searchOrFilterFoods();

    debugPrint('餐厅详情页: 从其他页面返回，已刷新美食列表数据');
  }

  @override
  Widget build(final BuildContext context) {
    return Focus(
      onFocusChange: (final hasFocus) {
        isShowKeyboard = hasFocus;
      },
      onKeyEvent: (final node, final event) {
        if (event.logicalKey == LogicalKeyboardKey.enter ||
            event.logicalKey == LogicalKeyboardKey.escape) {
          FocusScope.of(context).unfocus();
        }
        return KeyEventResult.handled;
      },
      child: Listener(
        onPointerDown: (final details) {
          log("点击了屏幕, $isShowKeyboard");
          if (isShowKeyboard) {
            // 检查点击位置是否在搜索框区域内
            final isInSearchBar = _isClickInSearchBar(details);

            // 如果点击位置不在搜索框区域内，才隐藏键盘
            if (!isInSearchBar) {
              FocusScope.of(context).unfocus();
            }
          }
        },
        child: SafeArea(
          top: false,
          bottom: false,
          child: Scaffold(
            bottomSheet: const RestaurantCartFoodList(),
            body: Stack(
              children: [
                Consumer(
                  builder: (final context, final ref, final child) {
                    final isLoading = ref.watch(
                      restaurantDetailControllerProvider
                          .select((final state) => state.isLoading),
                    );
                    final error = ref.watch(
                      restaurantDetailControllerProvider
                          .select((final state) => state.error),
                    );
                    final isShowFoodsList = ref.watch(
                      restaurantDetailControllerProvider
                          .select((final state) => state.isShowFoodsList),
                    );

                    final controller =
                        ref.read(restaurantDetailControllerProvider.notifier);

                    // 延迟处理再来一单商品，避免在build期间修改provider状态
                    if (!isLoading) {
                      WidgetsBinding.instance.addPostFrameCallback((final _) {
                        controller.processPendingComeAgainItems();
                      });
                    }

                    return Skeleton.overlay(
                      isLoading: isLoading,
                      skeleton: const RestaurantDetailSkeleton(),
                      child: Stack(
                        children: [
                          if (error != null)
                            // 显示错误视图
                            EmptyView(
                              message: error,
                              retryMessage: S.current.retry,
                              onRetry: () => controller.refresh(
                                restaurantId: widget.restaurantId,
                                buildingId: widget.buildingId,
                                ids: widget.ids,
                                comeAgainItems: widget.comeAgainItems,
                              ),
                            )
                          else if (!isLoading)
                            // 显示餐厅详情内容 - 根据类型使用不同样式
                            RestaurantStyleSelector(),

                          // 显示食品列表遮罩层
                          if (isShowFoodsList)
                            Positioned.fill(
                              child: Container(
                                color: Colors.black.withAlpha(100),
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),
                // 排行榜活动入口卡片
                _buildRankingCard(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建排行榜活动入口卡片
  Widget _buildRankingCard() {
    final restaurantData = ref.watch(restaurantDetailControllerProvider
        .select((final state) => state.restaurantData));
    final isLoading = ref.watch(restaurantDetailControllerProvider
        .select((final state) => state.isLoading));

    // 安全地检查ranking数组
    final rankingList = restaurantData?.ranking;
    bool isShowRankingCard = rankingList != null && rankingList.isNotEmpty;

    // 如果正在初始加载且没有数据，隐藏卡片
    if (isLoading && restaurantData == null) {
      return const SizedBox.shrink();
    }

    // 如果数组为空或不存在，返回空容器
    if (!isShowRankingCard) {
      return const SizedBox.shrink();
    }

    final isUg = ref.watch(languageProvider) == 'ug';
    final padding = _isScrolling || _isManuallyHidden ? -90.w : 12.w;
    return AnimatedPositioned(
      duration: Duration(milliseconds: 400),
      curve: Curves.easeInOut,
      bottom: MediaQuery.of(context).size.height * 0.2,
      right: isUg ? null : padding,
      left: isUg ? padding : null, // 滚动时或手动隐藏时隐藏到左边，留更多显示部分
      child: RankingCardWidget(
        rankingList: rankingList,
        onTap: (extraData) {
          context.push(
            AppPaths.userRankingPage,
            extra: extraData,
          );
        },
        onClose: () {
          setState(() {
            _isManuallyHidden = true;
          });
        },
        onShow: () {
          setState(() {
            _isManuallyHidden = false;
          });
        },
        isHidden: _isManuallyHidden,
      ),
    );
  }
}

/// 餐厅分类选择器组件，根据餐厅类型选择对应的样式展示
class RestaurantStyleSelector extends ConsumerWidget {
  /// 构造函数
  const RestaurantStyleSelector({
    super.key,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 直接读取餐厅类型，餐厅类型不会频繁改变，适合用select监听
    final restaurantType = ref.watch(
      restaurantDetailControllerProvider
          .select((final state) => state.restaurantType ?? 1),
    );

    // 根据餐厅类型返回不同的样式组件
    switch (restaurantType) {
      case 1: // 餐厅类型
        return RestaurantStyleOne();
      case 2: // 超市便利类型
        return const RestaurantStyleTwo();
      case 3: // 便利店类型
        return const RestaurantStyleThree();
      default: // 默认使用餐厅样式
        return const RestaurantStyleOne();
    }
  }
}

/// 餐厅购物车食品列表组件
class RestaurantCartFoodList extends ConsumerWidget {
  const RestaurantCartFoodList({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final isShowFoodsList = ref.watch(
      restaurantDetailControllerProvider
          .select((final state) => state.isShowFoodsList),
    );

    if (!isShowFoodsList) return const SizedBox.shrink();

    final controller = ref.read(restaurantDetailControllerProvider.notifier);

    // 获取Scaffold的body的底部到 屏幕底部的距离

    return AnimatedContainer(
      curve: Curves.easeInSine,
      duration: const Duration(milliseconds: 300),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CartFoodListWidget(
            onClose: () => controller.hideFoodsList(),
            onClearAndClose: () => controller.clearCartAndHideFoodsList(),
          ),
          Container(
            margin: EdgeInsets.only(right: 12.w, left: 12.w, bottom: 12.w),
            child: RestaurantBottomBarWidget(),
          ),
        ],
      ),
    );
  }
}
