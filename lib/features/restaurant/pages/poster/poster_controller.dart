import 'dart:async';

import 'package:bot_toast/bot_toast.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/utils/gallery_util.dart';
import 'package:user_app/core/utils/image_util.dart';
import 'package:user_app/core/utils/wechat_util.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/features/restaurant/pages/poster/poster_state.dart';
import 'package:user_app/features/restaurant/services/poster_service.dart';
import 'package:user_app/generated/l10n.dart';

part 'poster_controller.g.dart';

/// 海报页面控制器
@riverpod
class PosterController extends _$PosterController {
  @override
  PosterState build() {
    // 在控制器被销毁
    ref.onDispose(() {});

    return const PosterState();
  }

  /// 加载海报列表
  Future<void> loadPosterList(final int type) async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true);

    try {
      final service = ref.read(posterServiceProvider);
      final posters = await service.getPosterList(type);

      state = state.copyWith(
        isLoading: false,
        posters: posters,
        currentIndex: 0,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 更新当前选中的海报索引
  void updateCurrentIndex(final int index) {
    state = state.copyWith(currentIndex: index);
  }

  /// 关闭分享菜单
  void closeShareMenu() {
    state = state.copyWith(
      showShareMenu: false,
    );
  }

  /// 保存图片到相册
  Future<void> saveImageToPhotosAlbum(final String resId) async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true);

    try {
      // 显示加载提示
      LoadingDialog().show();

      // 获取当前选中的海报
      final selectedPoster = state.posters[state.currentIndex];

      // 生成海报
      final service = ref.read(posterServiceProvider);
      final posterUrl = await service.generateAndQueryPoster(
        resId: resId,
        type: selectedPoster.typeId,
        modelId: selectedPoster.id,
        onComplete: () {
         
        },
        onError: () {
          // 查询出错时隐藏加载提示
          BotToast.showText(text: S.current.retry);
          LoadingDialog().hide();
        },
      );

      if (posterUrl != null) {
        state = state.copyWith(
          isLoading: false,
          shareUrl: posterUrl,
          showShareMenu: true,
          cachedImageData: await ImageUtil.getImageFromUrl(posterUrl),
        );
      } else {
        state = state.copyWith(isLoading: false);
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    } finally {
      LoadingDialog().hide();
    }
  }

  /// 下载海报
  Future<void> downloadPoster() async {
    if (state.shareUrl.isEmpty) return;

    try {
      // 使用GalleryUtil直接保存图片到相册，无需通过service
      if (state.cachedImageData != null && state.cachedImageData!.isNotEmpty) {
        final success = await GalleryUtil.saveBytesToGallery(
          imageData: state.cachedImageData!,
        );
        if (success) {
          // 保存成功后关闭分享菜单
          closeShareMenu();
        }
      } else {
        final success = await GalleryUtil.saveImageToGallery(
          imageUrl: state.shareUrl,
        );
        if (success) {
          // 保存成功后关闭分享菜单
          closeShareMenu();
        }
      }
    } catch (e) {
      BotToast.showText(text: e.toString());
    }
  }

  /// 分享海报到微信群/好友
  Future<void> sharePoster() async {
    try {
      LoadingDialog().show();
      final wechatUtil = WechatUtil();
      final success = await wechatUtil.shareMiniProgram(
        path: "pages/poster/index",
        title: S.current.app_name,
        description: S.current.app_description,
        thumbData: state.cachedImageData ?? await ImageUtil.getImageFromUrl(state.shareUrl),
      );
      if (success) {
        BotToast.showText(text: S.current.about_share_success);
      } else {
        BotToast.showText(text: S.current.about_share_failed);
      }
    } catch (e) {
      BotToast.showText(text: e.toString());
    } finally {
      LoadingDialog().hide();
    }
  }
}
