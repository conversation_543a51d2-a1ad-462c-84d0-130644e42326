import 'dart:typed_data';

import 'package:user_app/data/models/poster/poster_model.dart';

/// 海报页面状态
class PosterState {
  /// 构造函数
  const PosterState({
    this.isLoading = false,
    this.error,
    this.posters = const [],
    this.currentIndex = 0,
    this.shareUrl = '',
    this.showShareMenu = false,
    this.cachedImageData,
  });

  /// 是否加载中
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 海报列表
  final List<PosterModel> posters;

  /// 当前选中的海报索引
  final int currentIndex;

  /// 分享链接
  final String shareUrl;

  /// 是否显示分享菜单
  final bool showShareMenu;

  /// 缓存的图片数据
  final Uint8List? cachedImageData;

  /// 复制状态
  PosterState copyWith({
    bool? isLoading,
    String? error,
    List<PosterModel>? posters,
    int? currentIndex,
    String? shareUrl,
    bool? showShareMenu,
    Uint8List? cachedImageData,
  }) {
    return PosterState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      posters: posters ?? this.posters,
      currentIndex: currentIndex ?? this.currentIndex,
      shareUrl: shareUrl ?? this.shareUrl,
      showShareMenu: showShareMenu ?? this.showShareMenu,
      cachedImageData: cachedImageData ?? this.cachedImageData,
    );
  }
}
