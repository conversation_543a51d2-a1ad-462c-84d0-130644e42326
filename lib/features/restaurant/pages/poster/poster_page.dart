import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/restaurant/pages/poster/poster_controller.dart';
import 'package:user_app/features/restaurant/pages/poster/widgets/poster_carousel.dart';
import 'package:user_app/features/restaurant/pages/poster/widgets/save_button.dart';
import 'package:user_app/features/restaurant/pages/poster/widgets/share_menu.dart';
import 'package:user_app/generated/l10n.dart';

/// 海报页面
class PosterPage extends ConsumerStatefulWidget {
  /// 构造函数
  const PosterPage({
    super.key,
    required this.resId,
    required this.type,
  });

  /// 餐厅ID
  final String resId;

  /// 海报类型
  final int type;

  @override
  ConsumerState<PosterPage> createState() => _PosterPageState();
}

class _PosterPageState extends ConsumerState<PosterPage> {
  @override
  void initState() {
    super.initState();
    // 初始化时加载海报列表
    Future.microtask(() {
      ref.read(posterControllerProvider.notifier).loadPosterList(widget.type);
    });
  }

  @override
  Widget build(final BuildContext context) {
    final isLoading = ref.watch(
        posterControllerProvider.select((final state) => state.isLoading));
    final posters = ref
        .watch(posterControllerProvider.select((final state) => state.posters));

    return Scaffold(
      backgroundColor: const Color(0xFFEFF1F6),
      appBar: CustomAppBar(
        title: S.of(context).poster_page_title,
      ),
      // 主页面内容
      body: Stack(
        children: [
          // 主要内容
          Column(
            children: [
              // 海报轮播
              Expanded(
                child: isLoading && posters.isEmpty
                    ? const Center(child: LoadingWidget(isFullScreen: false,))
                    : const PosterCarousel(),
              ),

              // 保存按钮
              SaveButton(resId: widget.resId),

              SizedBox(height: 30.h), // 底部间距
            ],
          ),

          // 半透明遮罩层 - 只在显示分享菜单时出现
          Consumer(
            builder: (final context, final ref, final child) {
              final showShareMenu = ref.watch(posterControllerProvider
                  .select((final state) => state.showShareMenu));
              return showShareMenu
                  ? GestureDetector(
                      onTap: ref
                          .read(posterControllerProvider.notifier)
                          .closeShareMenu,
                      child: Container(
                        width: double.infinity,
                        height: double.infinity,
                        color: Colors.black.withOpacity(0.5),
                      ),
                    )
                  : const SizedBox.shrink();
            },
          ),
        ],
      ),

      // 底部菜单
      bottomSheet: Consumer(
        builder: (final context, final ref, final child) {
          final showShareMenu = ref.watch(posterControllerProvider
              .select((final state) => state.showShareMenu));
          return showShareMenu ? const ShareMenu() : const SizedBox.shrink();
        },
      ),
    );
  }
}
