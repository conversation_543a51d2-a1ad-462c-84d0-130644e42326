// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$posterControllerHash() => r'9bd2798408c50069d9868de651b3fc91e5885371';

/// 海报页面控制器
///
/// Copied from [PosterController].
@ProviderFor(PosterController)
final posterControllerProvider =
    AutoDisposeNotifierProvider<PosterController, PosterState>.internal(
  PosterController.new,
  name: r'posterControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$posterControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PosterController = AutoDisposeNotifier<PosterState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
