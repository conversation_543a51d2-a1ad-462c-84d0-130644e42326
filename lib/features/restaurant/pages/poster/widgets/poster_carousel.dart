import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/data/models/poster/poster_model.dart';
import 'package:user_app/features/restaurant/pages/poster/poster_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 海报轮播组件
class PosterCarousel extends ConsumerWidget {
  /// 构造函数
  const PosterCarousel({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final posters =
        ref.watch(posterControllerProvider.select((state) => state.posters));
    final currentIndex = ref
        .watch(posterControllerProvider.select((state) => state.currentIndex));
    final controller = ref.read(posterControllerProvider.notifier);

    return Directionality(
      textDirection: TextDirection.ltr,
      child: Container(
        height: 76.h * 10, // 76vh -> 约76%的屏幕高度
        padding: EdgeInsets.only(top: 30.h),
        child: PageView.builder(
          onPageChanged: controller.updateCurrentIndex,
          itemCount: posters.length,
          controller: PageController(
            viewportFraction: 0.75, // 视窗占比，使两侧卡片稍微露出一点
            initialPage: currentIndex,
          ),
          itemBuilder: (final context, final index) {
            final poster = posters[index];
            final isActive = currentIndex == index;
      
            return TweenAnimationBuilder(
              tween: Tween<double>(
                  begin: isActive ? 0.8 : 1.0, end: isActive ? 1.0 : 0.8),
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              builder: (context, double value, child) {
                return Center(
                  child: Transform(
                    transform: Matrix4.identity()..scale(value),
                    alignment: Alignment.center,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(23.r), // 46rpx / 2
                        boxShadow: [
                          if (isActive)
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 2),
                            ),
                        ],
                      ),
                      height: poster.height > 520
                          ? double.infinity
                          : poster.height / 1.1,
                      width: 0.9.sw, // 90%的屏幕宽度
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(23.r),
                        child: Stack(
                          children: [
                            // 海报图片
                            _buildPosterImage(poster),
      
                            // 提示文本（仅在激活状态显示）
                            AnimatedOpacity(
                              opacity: isActive ? 1.0 : 0.0,
                              duration: const Duration(milliseconds: 300),
                              child: _buildTipsOverlay(context, isActive),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  /// 构建海报图片
  Widget _buildPosterImage(final PosterModel poster) {
    if (poster.height > 520) {
      return CachedNetworkImage(
        imageUrl: poster.cover,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        errorWidget: (final context, final url, final error) =>
            Icon(Icons.error, size: 50.r),
      );
    } else {
      return CachedNetworkImage(
        imageUrl: poster.cover,
        fit: BoxFit.cover,
        width: double.infinity,
        placeholder: (final context, final url) =>
            const Center(child: LoadingWidget()),
        errorWidget: (final context, final url, final error) =>
            Icon(Icons.error, size: 50.r),
      );
    }
  }

  /// 构建提示文本覆盖层
  Widget _buildTipsOverlay(final BuildContext context, final bool isActive) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 10.h),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.6),
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(23.r),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              S.of(context).swapir_tpis1,
              style: TextStyle(
                color: Colors.white,
                fontSize: 15.sp,
              ),
            ),
            SizedBox(height: 7.h),
            Text(
              S.of(context).swapir_tpis2,
              style: TextStyle(
                color: Colors.white,
                fontSize: 15.sp,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
