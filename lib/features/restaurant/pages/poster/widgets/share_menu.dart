import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:user_app/features/restaurant/pages/poster/poster_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 分享菜单组件
class ShareMenu extends ConsumerWidget {
  /// 构造函数
  const ShareMenu({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final shareUrl = ref.watch(
        posterControllerProvider.select((final state) => state.shareUrl));
    final controller = ref.read(posterControllerProvider.notifier);
    final isUyghur = Localizations.localeOf(context).languageCode == 'en';

    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        color: Color(0xFFEFF1F6),
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        textDirection: isUyghur ? TextDirection.rtl : TextDirection.ltr,
        children: [
          // 分享给好友按钮
          _buildMenuButton(
            context: context,
            isUyghur: isUyghur,
            iconPath: 'assets/images/share/share-hy.svg',
            title: S.of(context).shareMenu_hy,
            onTap: () {
              // 分享功能
              controller.sharePoster();
            },
          ),

          Divider(height: 1.h, color: Colors.grey[200]),

          // 保存到相册按钮
          _buildMenuButton(
            context: context,
            isUyghur: isUyghur,
            iconPath: 'assets/images/share/share-pyq.svg',
            title: S.of(context).shareMenu_pyq,
            onTap: controller.downloadPoster,
          ),

          SizedBox(height: 13.h), // 26rpx / 2

          // 取消按钮
          InkWell(
            onTap: controller.closeShareMenu,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.only(
                top: 15.h, // 30rpx / 2
                bottom: MediaQuery.of(context).padding.bottom > 0
                    ? MediaQuery.of(context).padding.bottom
                    : 17.5.h,
              ),
              alignment: Alignment.center,
              color: Colors.white,
              child: Text(
                S.of(context).back,
                style: TextStyle(
                  fontSize: 17.sp, // 34rpx / 2
                  color: Colors.red,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建菜单按钮
  Widget _buildMenuButton({
    required final BuildContext context,
    required final bool isUyghur,
    required final String iconPath,
    required final String title,
    required final VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 22.5.h), // 45rpx / 2
        alignment: isUyghur ? Alignment.centerRight : Alignment.centerLeft,
        color: Colors.white,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 25.w),
          child: Row(
            textDirection: isUyghur ? TextDirection.rtl : TextDirection.ltr,
            children: [
              if (iconPath.toLowerCase().endsWith('.svg'))
                SvgPicture.asset(
                  iconPath,
                  width: 25.w,
                  height: 25.w,
                  cacheColorFilter: true,
                )
              else
                Image.asset(
                  iconPath,
                  width: 25.w, // 50rpx / 2
                  height: 25.w,
                ),
              SizedBox(width: isUyghur ? 15.w : 25.w),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18.sp,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
