import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/restaurant/pages/poster/poster_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 保存按钮组件
class SaveButton extends ConsumerWidget {
  /// 构造函数
  const SaveButton({
    required this.resId,
    super.key,
  });

  /// 餐厅ID
  final String resId;

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final controller = ref.read(posterControllerProvider.notifier);

    return GestureDetector(
      onTap: () => controller.saveImageToPhotosAlbum(resId),
      child: Container(
        width: 150.w, // 300rpx / 2
        padding: EdgeInsets.symmetric(vertical: 12.5.h), // 25rpx / 2
        margin: EdgeInsets.only(top: 50.h), // 100rpx / 2
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.primary,
              const Color.fromARGB(146, 21, 196, 91),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(50.r), // 50rpx / 2
        ),
        alignment: Alignment.center,
        child: Text(
          S.of(context).save,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16.sp,
          ),
        ),
      ),
    );
  }
}
