import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/icon_font.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/data/models/order/history_address_model.dart';
import 'package:user_app/features/order/providers/order_provider.dart';

import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/address/providers/address_provider.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/core/widgets/dash_painter.dart';
import 'package:user_app/routes/index.dart';

/// 用户地址列表
class AddressList extends ConsumerStatefulWidget {
  /// 餐厅编号
  final int restaurantId;

  /// 建筑编号
  final int homeBuildingId;

  /// 点击地址后的会掉函数
  final Function(HistoryAddressData) onAddressSelected;

  /// 构造函数
  const AddressList(this.restaurantId, this.homeBuildingId,
      {super.key, required this.onAddressSelected});

  @override
  ConsumerState<AddressList> createState() => _AddressListState();
}

class _AddressListState extends ConsumerState<AddressList> {
  // 缓存参数，避免重复请求
  Map<String, dynamic>? _params;

  @override
  void initState() {
    super.initState();
    // 初始化参数
    _params = {
      "limit": 100,
      "restaurant_id": widget.restaurantId,
      "home_building_id": widget.homeBuildingId,
    };
  }

  @override
  Widget build(final BuildContext context) {
    // 使用缓存的参数，避免每次build时创建新对象
    final addressListAsync = ref.watch(addressProvider(_params));

    return Column(
      children: [
        Expanded(
          child: addressListAsync.when(
            data: (final addressList) {
              // 如果地址列表为空，则显示空状态
              if (addressList == null || addressList.isEmpty) {
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 36.w),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () {
                            // 点击地址后，调用回调函数
                            context.push(AppPaths.addAddressPage);
                          },
                          child: SizedBox(
                            width: 100.w,
                            height: 100.w,
                            child: DashedContainer(
                              width: 100.w,
                              height: 100.w,
                              dashColor: const Color(0xFFDDDDDD),
                              strokeWidth: 3.w,
                              borderRadius: 20.r,
                              child: Icon(
                                Icons.add,
                                size: 50.w,
                                color: const Color(0xFFCCCCCC),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 15.h),
                        Text(
                          S.current.my_addr_not,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 19.sp,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }

              // 将地址分为两组：可选择的地址和不可选择的地址
              final selectableAddresses = addressList
                      ?.where((final addr) => addr.state == 1)
                      .toList() ??
                  [];
              final unselectableAddresses = addressList
                      ?.where(
                          (final addr) => addr.state == 0 || addr.state == null)
                      .toList() ??
                  [];

              // 获取当前默认地址ID
              int currendId = ref.read(submitOrderInfoProvider
                  .select((final v) => v.valueOrNull?.address?.id ?? 0));
              // 如果当前地址不为空，则使用当前地址ID
              if (ref.read(currentAddressProvider).id != null) {
                currendId = ref.read(currentAddressProvider).id ?? 0;
              }

              return ListView(
                children: [
                  // 可选择的地址列表
                  ...selectableAddresses.asMap().entries.map((final entry) {
                    final index = entry.key;
                    final address = entry.value;
                    final isLast = index == (selectableAddresses.length - 1);
                    final isSelected = address.id == currendId;

                    return _buildAddressItem(
                      address: address,
                      isLast: isLast,
                      isSelected: isSelected,
                      isSelectable: true,
                    );
                  }),

                  // 在可选择地址列表的最后添加分隔线
                  if (selectableAddresses.isNotEmpty)
                    Container(
                      height: 1,
                      color: AppColors.dividerColor,
                    ),

                  // 如果存在不可选择的地址，显示标题和不可选择的地址列表
                  if (unselectableAddresses.isNotEmpty) ...[
                    SizedBox(height: 16.h),
                    Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                      child: Row(
                        children: [
                          SizedBox(
                            width: 36.w,
                            child: Icon(
                              Icons.location_off_outlined,
                              color: Color(0xff727272),
                            ),
                          ),
                          Text(
                            S.current.not_range,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                              color: Color(0xff727272),
                            ),
                          )
                        ],
                      ),
                    ),

                    /// 不可选择的地址列表
                    ...unselectableAddresses.asMap().entries.map((entry) {
                      final index = entry.key;
                      final address = entry.value;
                      final isLast =
                          index == (unselectableAddresses.length - 1);

                      return _buildAddressItem(
                        address: address,
                        isLast: isLast,
                        isSelected: false,
                        isSelectable: false,
                      );
                    }),
                  ],
                ],
              );
            },
            error: (final error, final stack) => Text(error.toString()),
            loading: () => LoadingWidget(),
          ),
        ),
        SafeArea(
          child: GestureDetector(
            onTap: () => context.push(AppPaths.addAddressPage),
            child: Container(
              margin: EdgeInsets.only(
                  left: 16.w, right: 16.w, top: 5.h, bottom: 16.h),
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
              alignment: Alignment.center,
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(45.r),
              ),
              child: Text(
                S.current.add_address,
                style: TextStyle(color: Colors.white, fontSize: 16.sp),
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget _buildAddressItem({
    required final HistoryAddressData address,
    required final bool isLast,
    required final bool isSelected,
    required final bool isSelectable,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: isSelected
            ? AppColors.primary.withAlpha(20)
            : (isSelectable ? Colors.white : Colors.grey.shade50),
        borderRadius: BorderRadius.circular(10.r),
        border: Border(
          bottom: isLast
              ? BorderSide.none
              : BorderSide(color: AppColors.dividerColor),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 36.w,
                child: isSelected && isSelectable
                    ? Icon(
                        IconFont.duihao,
                        color: AppColors.primary,
                        size: 20.sp,
                      )
                    : (!isSelectable
                        ? Icon(Icons.do_not_disturb,
                            color: Colors.grey.shade400, size: 20.sp)
                        : Container()),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: isSelectable
                      ? () {
                          widget.onAddressSelected(address);
                        }
                      : null,
                  child: Container(
                    color: Colors.transparent,
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${address.buildingName}${address.address}",
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: isSelectable
                                ? AppColors.textPrimaryColor
                                : Colors.grey.shade500,
                          ),
                        ),
                        Text(
                          "${address.name} ${address.tel}",
                          style: TextStyle(
                            fontSize: 15.sp,
                            color: isSelectable
                                ? AppColors.textSecondaryColor
                                : Colors.grey.shade400,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              if (isSelectable)
                GestureDetector(
                  onTap: () {
                    context.push(
                      AppPaths.addAddressPage,
                      extra: {
                        "buildingName": address.buildingName,
                        "buildingNameZh": address.buildingNameZh ??
                            address.buildingName ??
                            '',
                        "buildingId": address.id,
                        "address": address.address,
                        "name": address.name,
                        "tel": address.tel,
                        "addressId": address.id,
                      },
                    );
                  },
                  child: Icon(
                    IconFont.bianji,
                    color: AppColors.textHintColor,
                    size: 23.sp,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
