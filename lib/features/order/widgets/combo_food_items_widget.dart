import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';

/// 订单页面套餐详情列表组件
/// 用于显示购物车中套餐包含的美食项目列表
class ComboFoodItemsWidget extends ConsumerWidget {
  /// 套餐美食项目列表
  final List<dynamic>? comboItems;

  /// 构造函数
  const ComboFoodItemsWidget({
    super.key,
    required this.comboItems,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    if (comboItems == null || comboItems!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.only(top: 8.h),
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: comboItems!.map((final comboItem) {
          return Container(
            margin: EdgeInsets.only(bottom: 8.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // 套餐项目图片
                    ClipRRect(
                      borderRadius: BorderRadius.circular(4.r),
                      child: CachedNetworkImage(
                        imageUrl: _getImageUrl(comboItem),
                        width: 40.w,
                        height: 40.w,
                        fit: BoxFit.cover,
                        placeholder: (final context, final url) => Container(
                          width: 40.w,
                          height: 40.w,
                          color: Colors.grey.shade200,
                        ),
                        errorWidget: (final context, final url, final error) =>
                            Container(
                          width: 40.w,
                          height: 40.w,
                          color: Colors.grey.shade200,
                          child: Icon(
                            Icons.fastfood,
                            color: Colors.grey.shade400,
                            size: 16.sp,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    // 套餐项目信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getName(comboItem),
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: AppColors.textPrimaryColor,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            "${_getCount(comboItem)}×",
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: AppColors.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // 套餐项目数量和价格
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          "¥${FormatUtil.formatAmount(_getPrice(comboItem))}",
                          textDirection: TextDirection.ltr,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: AppColors.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                // 规格信息（如果有）
                if (_getSpecOptionsName(comboItem).isNotEmpty)
                  Container(
                    width: double.infinity,
                    margin: EdgeInsets.only(top: 5.w), // 10rpx / 2
                    padding: EdgeInsets.all(4.w), // 8rpx / 2
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(5.w), // 10rpx / 2
                    ),
                    child: Text(
                      _getSpecOptionsName(comboItem),
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondaryColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 获取图片URL - 兼容不同数据结构
  String _getImageUrl(final dynamic comboItem) {
    if (comboItem is Map<String, dynamic>) {
      // 处理Map类型的数据
      return comboItem['img'] ?? comboItem['image'] ?? "";
    } else {
      // 处理ComboFoodItem类型的数据
      try {
        return comboItem.restaurantFood?.image ?? "";
      } catch (e) {
        return "";
      }
    }
  }

  /// 获取名称 - 兼容不同数据结构
  String _getName(final dynamic comboItem) {
    if (comboItem is Map<String, dynamic>) {
      // 处理Map类型的数据
      return comboItem['name'] ?? "";
    } else {
      // 处理ComboFoodItem类型的数据
      try {
        return comboItem.restaurantFood?.name ?? "";
      } catch (e) {
        return "";
      }
    }
  }

  /// 获取规格选项名称 - 兼容不同数据结构
  String _getSpecOptionsName(final dynamic comboItem) {
    if (comboItem is Map<String, dynamic>) {
      // 处理Map类型的数据
      final selectedSpec = comboItem['selected_spec'];
      if (selectedSpec != null && selectedSpec is Map<String, dynamic>) {
        final specOptions = selectedSpec['spec_options'];
        if (specOptions != null && specOptions is List) {
          return specOptions
              .map((option) => option['name'] ?? '')
              .where((name) => name.isNotEmpty)
              .join("｜");
        }
      }
      return '';
    } else if (comboItem is ComboFoodItem) {
      // 处理ComboFoodItem类型的数据
      if (comboItem.selectedSpec?.specOptions != null) {
        return comboItem.selectedSpec!.specOptions!
            .map((option) => option.name ?? '')
            .where((name) => name.isNotEmpty)
            .join("｜");
      }
      return '';
    }
    return '';
  }

  /// 获取数量 - 兼容不同数据结构
  int _getCount(final dynamic comboItem) {
    if (comboItem is Map<String, dynamic>) {
      // 处理Map类型的数据
      return comboItem['count'] ?? 1;
    } else {
      // 处理ComboFoodItem类型的数据
      try {
        return comboItem.count ?? 1;
      } catch (e) {
        return 1;
      }
    }
  }

  /// 获取价格 - 兼容不同数据结构
  num _getPrice(final dynamic comboItem) {
    if (comboItem is Map<String, dynamic>) {
      // 处理Map类型的数据
      return comboItem['price'] ?? 0;
    } else {
      // 处理ComboFoodItem类型的数据
      try {
        return comboItem.restaurantFood?.price ?? 0;
      } catch (e) {
        return 0;
      }
    }
  }
}
