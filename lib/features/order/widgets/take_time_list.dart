import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/data/models/order/take_time_list_model.dart';

import 'package:user_app/features/order/providers/order_provider.dart';
import 'package:user_app/generated/l10n.dart';

class TakeTimeListWidget extends ConsumerStatefulWidget {
  final VoidCallback? onTimeSelected;
  
  const TakeTimeListWidget({super.key, this.onTimeSelected});

  @override
  ConsumerState<TakeTimeListWidget> createState() => _TakeTimeListWidgetState();
}

class _TakeTimeListWidgetState extends ConsumerState<TakeTimeListWidget> {
  // 是否显示今天的配送时间
  bool _showTodayTimes = true;
  // 列表滚动控制器
  final ScrollController _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    
    // 默认从全局状态获取是否显示今天的配送时间
    _showTodayTimes = ref.read(showTodayTimesProvider);
    
    // 使用延迟执行，确保在构建完成后执行
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 获取当前选中的配送时间
      final currentTakeTime = ref.read(currentTakeTimeProvider);
      
      // 如果已经选择了时间，并且不是"立即配送"
      if (currentTakeTime.dateTime != null && currentTakeTime.time != S.current.now_time) {
        // 确保选项卡设置为正确的日期（今天/明天）
        if (currentTakeTime.isToday != null) {
          // 如果需要切换选项卡，则先切换选项卡
          if (_showTodayTimes != currentTakeTime.isToday) {
            setState(() {
              _showTodayTimes = currentTakeTime.isToday!;
              ref.read(showTodayTimesProvider.notifier).state = _showTodayTimes;
            });
          }
        }
      }
      
      // 滚动到选中的时间位置
      Future.delayed(Duration(milliseconds: 200), () {
        _scrollToSelectedTime();
      });
    });
  }
  
  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
  
  // 滚动到当前选中的时间
  void _scrollToSelectedTime() {
    // 获取当前选中的时间信息
    final currentTakeTime = ref.read(currentTakeTimeProvider);
    
    // 如果没有选中时间，不需要滚动
    if (currentTakeTime.dateTime == null) return;
    
    // 获取配送时间列表数据
    final takeTimeListAsync = ref.read(submitOrderInfoProvider);
    if (!takeTimeListAsync.hasValue) return;
    
    final timeList = takeTimeListAsync.value!.cst ?? [];
    
    // 获取过滤后的时间列表
    final filteredTimes = timeList.where((time) {
      if (time.isRealTime == 1) return _showTodayTimes;
      return (time.isToday == true) == _showTodayTimes;
    }).toList();
    
    // 查找选中时间在列表中的位置
    int selectedIndex = -1;
    for (int i = 0; i < filteredTimes.length; i++) {
      if (filteredTimes[i].dateTime == currentTakeTime.dateTime) {
        selectedIndex = i;
        break;
      }
    }
    
    // 如果找到选中时间的位置，滚动到该位置
    if (selectedIndex != -1) {
      // 确保列表滚动控制器已经附加到视图
      if (_scrollController.hasClients) {
        // 延迟一下，确保列表已经渲染完成
        Future.delayed(Duration(milliseconds: 100), () {
          // 计算中心位置偏移量
          final itemHeight = 51.h; // 每个时间项的高度 (50.h + 1.h 分隔线)
          final listViewHeight = _scrollController.position.viewportDimension;
          final targetPosition = selectedIndex * itemHeight;
          
          // 计算中心对齐的偏移量
          double offset = targetPosition - (listViewHeight / 2) + (itemHeight / 2);
          
          // 确保偏移量在有效范围内
          offset = offset.clamp(0.0, _scrollController.position.maxScrollExtent);
          
          // 滚动到计算后的偏移位置
          _scrollController.animateTo(
            offset,
            duration: Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取配送时间列表数据
    final takeTimeListAsync = ref.watch(submitOrderInfoProvider);

    return takeTimeListAsync.when(
      data: (data) {
        final timeList = data.cst ?? [];
        // 检查是否有明天的时间段（is_today为false表示明天）
        final hasTomorrow = timeList.where((item) => item.isToday == false).isNotEmpty;
        
        return SizedBox(
          width: double.infinity,
          child: Column(
            children: [
              // 如果有明天的时间，显示今天/明天选择
              if (hasTomorrow) _buildDaySelector(hasTomorrow),
              
              // 时间列表
              Expanded(
                child: _buildTimeList(timeList),
              ),
            ],
          ),
        );
      },
      error: (error, stack) => Center(
        child: Text("加载错误: $error", style: TextStyle(fontSize: 16.sp, color: Colors.red)),
      ),
      loading: () => LoadingWidget(),
    );
  }

  // 构建今天/明天选择器
  Widget _buildDaySelector(bool hasTomorrow) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1.w,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildDaySelectorItem(S.current.today_time, true),
          SizedBox(width: 120.w),
          _buildDaySelectorItem(S.current.yesterday_time, false),
        ],
      ),
    );
  }

  // 构建日期选择器项
  Widget _buildDaySelectorItem(String text, bool isToday) {
    final isSelected = _showTodayTimes == isToday;
    
    return InkWell(
      onTap: () {
        setState(() {
          _showTodayTimes = isToday;
          // 更新全局状态
          ref.read(showTodayTimesProvider.notifier).state = isToday;
          
          // 切换选项卡后，延迟滚动到选中的时间
          Future.delayed(Duration(milliseconds: 100), () {
            _scrollToSelectedTime();
          });
        });
      },
      child: Column(
        children: [
          Text(
            text,
            style: TextStyle(
              fontSize: 16.sp,
              color: isSelected 
                  ? AppColors.primary
                  : AppColors.textPrimaryColor,
              fontWeight: isSelected
                  ? FontWeight.bold
                  : FontWeight.normal,
            ),
          ),
          SizedBox(height: 4.h),
          // 选中指示器
          Container(
            width: 24.w,
            height: 3.h,
            decoration: BoxDecoration(
              color: isSelected ? AppColors.primary : Colors.transparent,
              borderRadius: BorderRadius.circular(2.r),
            ),
          )
        ],
      ),
    );
  }

  // 构建时间列表
  Widget _buildTimeList(List<TakeTimeCst> timeList) {
    // 过滤出今天或明天的时间
    final filteredTimes = timeList.where((time) {
      // 立即送达（isRealTime=1）始终显示在今天的列表中
      if (time.isRealTime == 1) return _showTodayTimes;
      
      // 根据isToday属性过滤今天/明天的时间
      return (time.isToday == true) == _showTodayTimes;
    }).toList();

    return ListView.separated(
      controller: _scrollController, // 添加滚动控制器
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      itemCount: filteredTimes.length,
      separatorBuilder: (context, index) => Divider(
        height: 1.h,
        color: Colors.grey.shade100,
      ),
      itemBuilder: (context, index) {
        final time = filteredTimes[index];
        // 获取当前选中的时间
        final currentTakeTime = ref.watch(currentTakeTimeProvider);
        final isSelected = currentTakeTime.dateTime == time.dateTime;
        final isAvailable = time.state == 1;

        return _buildTimeItem(
          time: time,
          isSelected: isSelected,
          isAvailable: isAvailable,
          index: index,
        );
      },
    );
  }

  // 构建时间项
  Widget _buildTimeItem({
    required TakeTimeCst time,
    required bool isSelected,
    required bool isAvailable,
    required int index,
  }) {
    // 获取当前时间的显示内容
    String timeDisplay;
    String? bookingDay;
    
    if (time.isRealTime == 1) {
      timeDisplay = S.current.now_time;
      bookingDay = null;
    } else {
      timeDisplay = time.time ?? "";
      bookingDay = time.bookingDay;
    }

    // 是否是立即配送
    final isRealTime = time.isRealTime == 1;

    return InkWell(
      onTap: isAvailable
          ? () {
              // 创建新的时间对象
              final newTakeTime = TakeTimeCst(
                time: timeDisplay,
                dateTime: time.dateTime,
                isToday: time.isToday,
                realBookingTime: time.realBookingTime,
              );
              
              // 立即更新Provider状态
              ref.read(currentTakeTimeProvider.notifier).state = newTakeTime;
              
              // 调用选择时间回调
              if (widget.onTimeSelected != null) {
                widget.onTimeSelected!();
              }
              
              // 关闭底部弹窗
              Navigator.pop(context);
            }
          : null,
      child: Container(
        height: 50.h,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 左侧：时间文本
            Expanded(
              child: Row(
                children: [
                  // 如果不是立即配送，且有预订日，则显示在时间前面
                  if (!isRealTime && bookingDay != null && bookingDay.isNotEmpty)
                    Text(
                      "$bookingDay - ",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: isSelected
                            ? AppColors.primary
                            : AppColors.textSecondaryColor,
                      ),
                    ),
                  
                  // 时间显示
                  Text(
                    timeDisplay,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: isSelected
                          ? AppColors.primary
                          : (isRealTime 
                              ? Color(0xFF4CAF50) // 立即配送使用绿色文字
                              : (isAvailable
                                  ? AppColors.textPrimaryColor
                                  : Colors.grey.shade400)),
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),
            
            // 右侧：选中状态或不可用状态
            if (isSelected)
              Icon(
                Icons.check,
                color: AppColors.primary,
                size: 32.sp,
              )
            else if (!isAvailable)
              Text(
                S.current.was_full,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey.shade500,
                ),
              ),
          ],
        ),
      ),
    );
  }
}