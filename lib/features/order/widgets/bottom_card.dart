import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/generated/l10n.dart';

class BottomCard extends ConsumerWidget {
  /// 价格
  final num price;

  /// 原价
  final num? originalPrice;

  /// 是否是朋友支付
  final bool? isFriendPay;

  /// 朋友支付回调
  final Function()? onFriendPay;

  /// 支付回调
  final Function()? onPay;

  /// 构造函数
  const BottomCard({
    super.key,
    required this.price,
    this.originalPrice,
    this.isFriendPay = false,
    this.onFriendPay,
    this.onPay,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(54.r),
              ),
              child: _buildBottomBar(context, ref),
            ),
          ),
        ],
      ),
    );
  }

  // 底部操作栏
  Widget _buildBottomBar(final BuildContext context, final WidgetRef ref) {
    return Container(
      height: 48.h,
      decoration: BoxDecoration(
        color: Color(0xff333333),
        borderRadius: BorderRadius.circular(100.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildCheckoutButtons(context, ref),
          _buildPriceSection(),
        ],
      ),
    );
  }

  // 结算按钮组
  Widget _buildCheckoutButtons(final BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        InkWell(
          onTap: onPay,
          child: Container(
            width: 110.w,
            height: double.infinity,
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50),
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(54.r),
                bottomRight: Radius.circular(54.r),
              ),
            ),
            child: Center(
              child: Text(
                S.current.pay_done,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
        if (isFriendPay == true)
          InkWell(
            onTap: onFriendPay,
            child: Container(
              decoration: BoxDecoration(
                color: Color(0xff4C4C4C),
              ),
              height: double.infinity,
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: Text(
                S.current.agent_pay,
                style: TextStyle(
                  fontSize: 18.sp,
                  color: Colors.amber,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }

  // 价格部分
  Widget _buildPriceSection() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            '¥${FormatUtil.formatPrice(price)}',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          if (price != originalPrice)
            Text(
              '¥${FormatUtil.formatPrice(originalPrice ?? 0)}',
              style: TextStyle(
                fontSize: 15.sp,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
                decoration: TextDecoration.lineThrough,
                decorationColor: Colors.grey,
              ),
            ),
        ],
      ),
    );
  }
}
