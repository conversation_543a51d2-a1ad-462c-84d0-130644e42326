// 三角形裁剪器
import 'package:flutter/material.dart';

class TriangleClipper extends CustomClipper<Path> {
  final bool isLeft;
  final double radius;

  TriangleClipper({this.isLeft = false, this.radius = 10.0});

  @override
  Path getClip(Size size) {
    final path = Path();

    if (isLeft) {
      // 左上角三角形（带圆角）
      path.moveTo(radius, 0);
      path.lineTo(size.width, 0);
      path.lineTo(0, size.width);
      path.lineTo(0, radius);

      // 添加左上角圆角
      path.addArc(
        Rect.fromCircle(center: Offset(radius, radius), radius: radius),
        3.14 * 1, // π
        3.14 / 2, // π/2
      );
    } else {
      // 右上角三角形（带圆角）
      path.moveTo(0, 0);
      path.lineTo(size.width - radius, 0);
      path.quadraticBezierTo(size.width, 0, size.width, radius);
      path.lineTo(size.width, size.height);
      path.lineTo(size.width - size.height, 0);
    }

    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => false;
}

// 三角形绘制器 - 增加圆角效果
class TrianglePainter extends CustomPainter {
  final Color color;
  final double radius;

  TrianglePainter({required this.color, this.radius = 10.0});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // 绘制带圆角的主三角形
    final path = Path();

    // 添加右上角圆角
    path.moveTo(0, 0);
    path.lineTo(size.width - radius, 0);
    path.quadraticBezierTo(size.width, 0, size.width, radius);
    path.lineTo(size.width, size.width);
    path.lineTo(size.width - size.width, 0);
    path.close();

    canvas.drawPath(path, paint);

    // 添加轻微的阴影效果
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.1)
      ..style = PaintingStyle.fill;

    final shadowPath = Path()
      ..moveTo(size.width * 0.9, 0)
      ..lineTo(0, 0)
      ..lineTo(size.width, size.width * 0.9)
      ..close();

    canvas.drawPath(shadowPath, shadowPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
