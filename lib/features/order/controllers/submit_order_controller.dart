import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/data/models/order/create_order_param_model.dart';
import 'package:user_app/data/models/order/current_address_model.dart';
import 'package:user_app/data/models/order/history_address_model.dart';
import 'package:user_app/data/models/order/take_time_list_model.dart';
import 'package:user_app/data/repositories/restaurant/spec_repository.dart';
import 'package:user_app/features/address/providers/address_provider.dart';
import 'package:user_app/features/order/pages/dialogs/time_confirm_dialog.dart';
import 'package:user_app/features/order/pages/dialogs/self_take_confirm_dialog.dart';
import 'package:user_app/features/order/providers/order_provider.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/core/utils/date_util.dart';
import 'package:user_app/core/utils/calculation_utils.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/core/providers/core_providers.dart';

/// 订单提交控制器
class SubmitOrderController {
  final Ref ref;

  /// BuildContext用于显示对话框
  final BuildContext context;

  // 状态变量
  bool _takeTimeSynced = false;
  bool _addressSynced = false;

  /// 配送时间自动刷新定时器 - 与小程序逻辑一致，每5分钟刷新一次
  Timer? _shipmentTimeTimer;

  /// 订单总金额
  num totalPrice = 0;

  /// 原始价格
  num originalPrice = 0;

  /// 美食优惠
  num foodDiscount = 0;

  /// 自取价格
  num pickupPrice = 0;

  /// 配送费
  num shipmentFee = 0;

  /// 减配送费优惠
  num shipmentDiscount = 0;

  /// 饭盒费
  num lunchBoxFee = 0;

  /// 总共优惠金额
  num totalDiscountAmount = 0;

  /// 优惠卷优惠金额
  num couponDiscount = 0;

  /// 满减活动优惠金额
  num reductionDiscount = 0;

  /// 活动优惠金额（情侣专享/抓雪花游戏）
  num activePercentPrice = 0;

  /// 美食小计
  String subTotalPrice = "0";

  /// 配送模式
  int deliveryMode = OrderCalculationConstants.deliveryMode;

  /// isShowSelectShipmentArea 是否显示选择配送区域
  bool isShowSelectShipmentArea = false;

  /// 构造方法
  SubmitOrderController(this.ref, this.context);

  /// 初始化方法
  void initialize(
    final int restaurantId,
    final int buildingId,
    final int? homeBuildingId,
  ) {
    // 重置当前选中的配送时间
    ref.read(currentTakeTimeProvider.notifier).state = TakeTimeCst();

    // 重置当前选中的优惠券
    ref.read(selectedCouponProvider.notifier).state = null;
    ref.read(remarkProvider.notifier).state = '';

    // 重置提交订单信息提供者，确保每次进入页面时都刷新数据
    ref.invalidate(submitOrderInfoProvider);
    // 加载配送时间列表
    ref
        .read(submitOrderInfoProvider.notifier)
        .getTakeTimeList(
          restaurantId: restaurantId,
          buildingId: buildingId,
          homeBuildingId: homeBuildingId ?? buildingId,
        )
        .then((final result) {
      // 检查上下文是否仍然有效，防止在组件销毁后更新状态
      if (!context.mounted) return;

      // 在获取到takeTimeList数据后立即设置默认地址
      if (result.address != null) {
        isShowSelectShipmentArea = result.shipmentInfo?.canMulazimTake == 1 &&
            result.shipmentInfo?.canSelfTake == 1;
        // 设置配送模式
        ref.watch(deliveryModeProvider.notifier).state =
            result.shipmentInfo?.canMulazimTake == 1
                ? OrderCalculationConstants.deliveryMode
                : OrderCalculationConstants.pickupMode;
        // 直接设置默认地址
        ref.read(currentAddressProvider.notifier).setAddress(
              CurrentSelectAddress(
                id: result.address!.addressId,
                name: result.address!.name,
                tel: result.address!.tel,
                address: result.address!.address,
                buildingName: result.address!.buildingName,
                buildingId: result.address!.buildingId,
                showDistanceWarn: result.address!.showDistanceWarn,
              ),
            );
      }

      // 同步配送时间
      syncDefaultTakeTime();

      // 启动配送时间自动刷新定时器 - 与小程序逻辑一致
      startShipmentTimeTimer(restaurantId, buildingId, homeBuildingId);
    }).catchError((error) {
      // 处理错误，避免未捕获的异常
      debugPrint("加载配送时间列表出错: $error");
    });

    final shoppingCartNotifier = ref.read(shoppingCartProvider.notifier);

    // 加载餐厅食品数据用于计算价格
    ref.read(restaurantDetailControllerProvider.notifier).loadFoodsList(
          restaurantId: restaurantId,
          buildingId: buildingId,
          ids: shoppingCartNotifier.state.map((final e) => e.id).toList(),
        );
  }

  /// 同步默认地址到当前地址Provider
  void syncDefaultAddress() {
    // 检查上下文是否仍然有效
    if (!context.mounted) return;

    // 从takeTimeList中获取默认地址
    final takeTimeAsync = ref.read(submitOrderInfoProvider);
    final defaultAddress =
        takeTimeAsync.hasValue ? takeTimeAsync.value?.address : null;

    // 如果获取到了默认地址，且当前没有选中的地址，则设置为当前选中的地址
    if (defaultAddress != null && ref.read(currentAddressProvider).id == null) {
      ref.read(currentAddressProvider.notifier).setAddress(
            CurrentSelectAddress(
              id: defaultAddress.addressId,
              name: defaultAddress.name,
              tel: defaultAddress.tel,
              address: defaultAddress.address,
              buildingName: defaultAddress.buildingName,
              buildingId: defaultAddress.buildingId,
              showDistanceWarn: defaultAddress.showDistanceWarn,
            ),
          );
    }
  }

  /// 同步默认配送时间
  void syncDefaultTakeTime() {
    // 检查上下文是否仍然有效
    if (!context.mounted) return;

    final takeTimeListAsync = ref.read(submitOrderInfoProvider);
    if (!takeTimeListAsync.hasValue) return;

    final timeList = takeTimeListAsync.value?.cst ?? [];
    if (timeList.isEmpty) return;

    // 获取当前选择的配送时间
    final currentTakeTime = ref.read(currentTakeTimeProvider);

    // 如果当前没有选择配送时间，则设置默认值（第一个可用的时间）
    if (currentTakeTime.dateTime == null) {
      // 找到第一个可用的时间（state == 1表示可用）
      final availableTime = timeList.firstWhere(
        (final time) => time.state == 1,
        orElse: () => timeList.first,
      );

      // 更新当前选择的配送时间
      ref.read(currentTakeTimeProvider.notifier).state = TakeTimeCst(
        time: availableTime.isRealTime == 1
            ? S.current.now_time
            : availableTime.time,
        dateTime: availableTime.dateTime,
        isToday: availableTime.isToday,
        realBookingTime: availableTime.realBookingTime,
      );
    } else {
      // 如果已经有选择的时间，更新其realBookingTime等信息
      _updateCurrentSelectedTime();
    }
  }

  /// 计算配送时间与当前时间的差异，返回格式化的时间差
  String calculateDeliveryTimeDifference({final String locale = 'ug'}) {
    try {
      // 获取当前选中的配送时间
      final currentTakeTime = ref.read(currentTakeTimeProvider);

      // 如果没有选择时间或不是立即配送，返回空字符串
      if (currentTakeTime.dateTime == null ||
          currentTakeTime.time != S.current.now_time) {
        return "";
      }

      // 解析时间并计算差异
      final now = DateTime.now();
      final deliveryTime = DateTime.parse(currentTakeTime.dateTime!);

      // 使用日期工具计算时间差
      return DateUtil.calculateTimeDifference(
        startTime: now,
        endTime: deliveryTime,
        locale: locale,
      );
    } catch (e) {
      debugPrint("计算配送时间差异出错: $e");
      return "";
    }
  }

  Future<void> updateCartFoodsData(
    final int restaurantId,
    final int buildingId,
  ) async {
    // 保存更新前的总价格
    final oldTotalPrice = totalPrice;

    final shoppingCartNotifier = ref.read(shoppingCartProvider.notifier);

    // 获取购物车中所有商品的ID（去重）
    final Set<int> uniqueFoodIds = {};
    final Set<int> specFoodIds = {}; // 有规格的商品ID

    bool isSpecialFood = false;
    for (final item in shoppingCartNotifier.state) {
      if (item.id != null) {
        if (item.specialActive == 1) {
          isSpecialFood = true;
          break;
        }
        uniqueFoodIds.add(item.id!);
        // 检查是否有规格选择
        if (item.specSelectedOptions != null &&
            item.specSelectedOptions!.isNotEmpty) {
          specFoodIds.add(item.id!);
        }
      }
    }
    // 如果购物车中有特殊商品，则不更新食品数据
    if (isSpecialFood) {
      return;
    }
    // 1. 先加载基础食品数据
    final foodsData = await ref
        .read(restaurantDetailControllerProvider.notifier)
        .loadFoodsList(
          restaurantId: restaurantId,
          buildingId: buildingId,
          ids: uniqueFoodIds.toList(),
        );

    // 2. 如果有规格商品，单独请求规格数据
    if (specFoodIds.isNotEmpty) {
      final specRepository = ref.read(specRepositoryProvider);
      final specResult =
          await specRepository.getFoodSpecData(specFoodIds.toList());

      if (specResult.success && specResult.data != null) {
        // 规格数据获取成功，使用规格数据更新购物车
        shoppingCartNotifier.updateFoodsData(
          foodItems: foodsData.foods ?? [],
          specData: specResult.data,
        );
      } else {
        // 规格数据获取失败，仍然更新基础数据
        shoppingCartNotifier.updateFoodsData(foodItems: foodsData.foods ?? []);
      }
    } else {
      // 没有规格商品，只更新基础数据
      shoppingCartNotifier.updateFoodsData(foodItems: foodsData.foods ?? []);
    }
    // 重置当前选中的优惠券
    ref.read(selectedCouponProvider.notifier).state = null;
    // 重新计算价格
    calculatePrices();

    // 检查价格是否发生变化
    if (oldTotalPrice > 0 && oldTotalPrice != totalPrice) {
      // 如果价格发生变化，显示提示
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (context.mounted) {
          BotToast.showText(text: S.current.order_price_changed);
        }
      });
    }
  }

  /// 计算所有价格相关变量
  void calculatePrices() {
    deliveryMode = ref.read(deliveryModeProvider);
    final shoppingCartNotifier = ref.read(shoppingCartProvider.notifier);
    final selectedCoupon = ref.read(selectedCouponProvider);
    final foodsData = ref.read(restaurantDetailControllerProvider).foodsData;

    // 计算配送模式下的价格
    final calculation = shoppingCartNotifier.calculateCart(
      foodsData: foodsData,
      deliveryMode: OrderCalculationConstants.deliveryMode,
      couponAmount: selectedCoupon?.price != null
          ? num.tryParse(selectedCoupon!.price!) ?? 0
          : 0,
    );

    // 更新所有价格变量
    shipmentFee = calculation.shipmentFee; // 配送费
    shipmentDiscount = calculation.shipmentDiscount; // 配送费折扣
    lunchBoxFee = calculation.lunchBoxFee; // 餐盒费
    couponDiscount = calculation.couponDiscount; // 优惠券折扣
    reductionDiscount = calculation.reductionDiscount; // 满减折扣

    // 美食优惠, 原始价格 - 优惠后的价格
    foodDiscount = calculation.totalFoodOriginalPrice - calculation.subtotal;

    // 计算活动优惠（抓雪花游戏/情侣专享）
    activePercentPrice = _calculateActivePercentPrice(calculation);

    // 计算配送模式价格（含配送费）
    totalPrice = calculation.totalPrice - activePercentPrice;

    // 计算自取模式价格（不含配送费）
    pickupPrice = calculation.totalPrice -
        shipmentFee +
        shipmentDiscount -
        activePercentPrice;

    // 根据当前模式确定实际展示的总价
    if (deliveryMode == OrderCalculationConstants.pickupMode) {
      totalPrice = pickupPrice;
    }

    // 计算总折扣金额
    totalDiscountAmount =
        couponDiscount + reductionDiscount + activePercentPrice;
    if (deliveryMode == OrderCalculationConstants.deliveryMode) {
      totalDiscountAmount += shipmentDiscount;
    }
    // +美食优惠
    totalDiscountAmount += foodDiscount;

    /// 优惠之前的金额
    originalPrice = calculation.totalOriginalPrice;

    // 获取美食小计
    subTotalPrice = shoppingCartNotifier.getSubtotal(
      foodsData: foodsData,
      deliveryMode: 0, // 0: 配送模式
    );
  }

  /// 计算活动优惠价格（抓雪花游戏/情侣专享）
  /// 参考微信小程序 commitOrder.js 中的逻辑
  num _calculateActivePercentPrice(dynamic calculation) {
    // 获取snow_game数据
    final takeTimeData = ref.read(submitOrderInfoProvider).valueOrNull;
    final snowGame = takeTimeData?.snowGame;

    // 获取优惠券金额
    final selectedCoupon = ref.read(selectedCouponProvider);
    final couponPrice = selectedCoupon?.price != null
        ? num.tryParse(selectedCoupon!.price!) ?? 0
        : 0;

    // 使用CalculationUtils中的方法计算活动优惠
    return CalculationUtils.calculateActivePercentPrice(
      calculation: calculation,
      snowGame: snowGame,
      deliveryMode: deliveryMode,
      lunchBoxFee: lunchBoxFee,
      reductionDiscount: reductionDiscount,
      couponPrice: couponPrice,
      shipmentFee: shipmentFee,
      shipmentDiscount: shipmentDiscount,
    );
  }

  /// 获取配送价格（始终返回含配送费的价格）
  num getDeliveryPrice() {
    final shoppingCartNotifier = ref.read(shoppingCartProvider.notifier);
    final selectedCoupon = ref.read(selectedCouponProvider);
    final foodsData = ref.read(restaurantDetailControllerProvider).foodsData;

    // 计算配送模式下的价格
    final calculation = shoppingCartNotifier.calculateCart(
      foodsData: foodsData,
      deliveryMode: OrderCalculationConstants.deliveryMode,
      couponAmount: selectedCoupon?.price != null
          ? num.tryParse(selectedCoupon!.price!) ?? 0
          : 0,
    );

    return calculation.totalPrice;
  }

  /// 获取自取价格（始终返回不含配送费的价格）
  num getPickupPrice() {
    final shoppingCartNotifier = ref.read(shoppingCartProvider.notifier);
    final selectedCoupon = ref.read(selectedCouponProvider);
    final foodsData = ref.read(restaurantDetailControllerProvider).foodsData;

    // 计算配送模式下的价格，然后减去配送费
    final calculation = shoppingCartNotifier.calculateCart(
      foodsData: foodsData,
      deliveryMode: OrderCalculationConstants.deliveryMode,
      couponAmount: selectedCoupon?.price != null
          ? num.tryParse(selectedCoupon!.price!) ?? 0
          : 0,
    );

    return calculation.totalPrice -
        calculation.shipmentFee +
        calculation.shipmentDiscount;
  }

  /// 创建订单参数
  Future<CreateOrderParams?> prepareOrderParams(
    final int restaurantId,
    final VoidCallback showAddressListCallback,
    final VoidCallback showTakeTimeListCallback,
    final Function(String) showToast,
  ) async {
    // 获取配送模式
    final deliveryMode = ref.read(deliveryModeProvider);

    // 检查自取确认 - 与微信小程序逻辑一致
    if (deliveryMode == OrderCalculationConstants.pickupMode) {
      LoadingDialog().hide();
      final shouldShowSelfTakeConfirm = await _checkSelfTakeConfirm();
      if (shouldShowSelfTakeConfirm) {
        return null; // 需要显示确认对话框，暂停订单创建
      }
      LoadingDialog().show();
    }

    // 获取当前地址
    final currentAddress = ref.read(currentAddressProvider);

    // 如果是配送模式且没有地址ID，显示选择地址的界面
    if (deliveryMode == OrderCalculationConstants.deliveryMode &&
        (currentAddress.id == null || currentAddress.id == 0)) {
      showAddressListCallback();
      showToast(S.current.choice_address);
      return null;
    }

    final addressId = currentAddress.id;

    // 获取当前选中的配送时间
    final currentTakeTime = ref.read(currentTakeTimeProvider);
    if (currentTakeTime.dateTime == null) {
      // 同步配送时间
      syncDefaultTakeTime();

      // 重新获取配送时间
      final updatedTakeTime = ref.read(currentTakeTimeProvider);
      if (updatedTakeTime.dateTime == null) {
        // 如果仍然没有配送时间，显示选择时间界面
        showTakeTimeListCallback();
        showToast(S.current.choice_delivery_time);
        return null;
      }
    }

    /// 获取购物车商品
    final cartFoods = ref.read(shoppingCartProvider);

    List<Food> submitFoods = [];

    // 按照微信小程序的逻辑处理商品
    for (var item in cartFoods) {
      // 处理多重折扣
      if (item.multiDiscountId != null &&
          item.multiDiscountId! > 0 &&
          item.multiDiscountSteps != null &&
          item.multiDiscountSteps!.isNotEmpty) {
        // 找到已选中的折扣步骤，条件是step.number <= item.count
        final checkedSteps = item.multiDiscountSteps!
            .where((step) =>
                step.number != null && step.number! <= (item.count ?? 0))
            .toList();

        // 为每个选中的折扣步骤创建一个食品项（数量为1）
        for (var step in checkedSteps) {
          final optionIds = _getOptionIds(item);
          submitFoods.add(
            Food(
              id: item.id ?? 0,
              count: 1, // 折扣步骤的数量总是1
              // 根据商品类型设置foodType: 0普通商品, 1规格商品, 2套餐商品
              foodType: _getFoodType(item, optionIds),
              discountId: item.multiDiscountId,
              discountNumber: step.number,
              seckillId: _getSeckillId(item),
              optionIds: optionIds,
            ),
          );
        }

        // 计算剩余商品数量（减去已选折扣步骤的数量）
        final remainingCount = (item.count ?? 0) - checkedSteps.length;
        if (remainingCount > 0) {
          final optionIds = _getOptionIds(item);
          submitFoods.add(
            Food(
              id: item.id ?? 0,
              count: remainingCount,
              // 根据商品类型设置foodType: 0普通商品, 1规格商品, 2套餐商品
              foodType: _getFoodType(item, optionIds),
              seckillId: _getSeckillId(item),
              optionIds: optionIds,
            ),
          );
        }
      } else {
        // 普通商品（无多重折扣）
        final optionIds = _getOptionIds(item);
        submitFoods.add(
          Food(
            id: item.id ?? 0,
            count: item.count ?? 0,
            // 根据商品类型设置foodType: 0普通商品, 1规格商品, 2套餐商品
            foodType: _getFoodType(item, optionIds),
            seckillId: _getSeckillId(item),
            optionIds: optionIds,
          ),
        );
      }
    }

    final takeTimeListAsync = ref.read(submitOrderInfoProvider);
    final nowTime = takeTimeListAsync.value?.nowDateTime;

    // 检查是否有nowDateTime
    if (nowTime != null && currentTakeTime.dateTime != null) {
      // 计算当前时间与选择时间的时间差
      final timeDifference = DateUtil.calculateTimeDifferenceInMinutes(
        startTime: DateTime.parse(nowTime),
        endTime: DateTime.parse(currentTakeTime.dateTime!),
      );

      // 如果时间差大于等于120分钟，显示确认对话框
      if (timeDifference['time'] >= 120) {
        LoadingDialog().hide();

        // 显示确认对话框
        final bool? confirmed =
            await showTimeConfirmDialog(timeDifference['title']);
        if (confirmed != true) {
          // 用户取消了确认，返回null
          return null;
        }
        LoadingDialog().show();
      }
    }

    // 获取选中的优惠券
    final selectedCoupon = ref.read(selectedCouponProvider);

    // 获取餐具数量并格式化餐具信息
    final tablerwareCount = ref.read(tablerwareCountProvider);
    final lang = ref.read(languageProvider);

    // 格式化餐具信息 - 与微信小程序逻辑一致
    final tablewareText = lang == 'zh'
        ? "(餐具 $tablerwareCount 人)"
        : "(چوكا قوشۇق $tablerwareCount كىشلىك)";

    // 获取用户备注
    final userRemark = ref.read(remarkProvider);

    // 组合完整的描述信息 - 与微信小程序逻辑一致
    String fullDescription = userRemark;
    if (userRemark.isNotEmpty) {
      // 如果有用户备注，根据语言添加分隔符
      final separator = lang == 'ug' ? '،' : '，';
      fullDescription = "$userRemark$separator$tablewareText";
    } else {
      fullDescription = tablewareText;
    }

    // 检查是否有特价商品并获取specialId
    final hasSpecialFood = cartFoods.any((item) => item.specialActive == 1);
    final specialId = hasSpecialFood ? ref.read(specialIdProvider) : null;

    return CreateOrderParams(
      restaurantId: restaurantId,
      timezone: "CST",
      originalPrice: originalPrice,
      bookingTime: currentTakeTime.dateTime.toString(),
      // 1 实时订单 0 预约订单
      orderType: 1,
      description: fullDescription,
      price: num.parse(FormatUtil.formatPrice(totalPrice)),
      allFoodPrice: num.parse(subTotalPrice), // 美食价格，不含任何优惠
      foodsPrice: num.parse(subTotalPrice),
      lunchBoxFee: num.parse(FormatUtil.formatPrice(lunchBoxFee)),
      deliveryType:
          deliveryMode == OrderCalculationConstants.deliveryMode ? 1 : 2,
      activityId: 0,
      takeTimeListGetTime: nowTime ?? DateTime.now().toIso8601String(),
      addressId: addressId ?? 0,
      shipment:
          num.parse(FormatUtil.formatPrice(shipmentFee - shipmentDiscount)),
      reduceShipment: shipmentDiscount,
      reductionFee: reductionDiscount,
      couponId: selectedCoupon?.id,
      specialId: specialId,
      foods: submitFoods,
    );
  }

  /// 获取秒杀ID
  int? _getSeckillId(SelectFoodItem item) {
    final normalActive = item.seckillId != null &&
            item.seckillActive != null &&
            item.seckillActive! > 0 ||
        item.specialActive != null && item.specialActive! > 0;
    return normalActive ? item.seckillId : null;
  }

  /// 获取规格选项ID列表 - 从SelectFoodItem的specSelectedOptions中提取spec_option_id
  List<int>? _getOptionIds(SelectFoodItem item) {
    // 如果商品没有规格选择信息，返回null
    if (item.specSelectedOptions == null || item.specSelectedOptions!.isEmpty) {
      return null;
    }

    // 从规格选择信息中提取所有的spec_option_id
    return item.specSelectedOptions!
        .map((option) => option['spec_option_id'] as int?)
        .where((id) => id != null)
        .cast<int>()
        .toList();
  }

  /// 获取食品类型 - 根据商品类型和规格信息确定foodType
  /// 返回值：0-普通商品, 1-规格商品, 2-套餐商品
  int _getFoodType(SelectFoodItem item, List<int>? optionIds) {
    // // 如果是套餐商品，返回2
    // if (item.foodType == 2) {
    //   return 2;
    // }

    // // 如果有规格选择，返回1
    // if (optionIds != null && optionIds.isNotEmpty) {
    //   return 1;
    // }

    // 普通商品返回0
    return item.foodType ?? 0;
  }

  /// 检查是否需要显示自取确认对话框
  /// 返回值：true表示需要显示确认对话框，false表示不需要显示
  Future<bool> _checkSelfTakeConfirm() async {
    // 获取自取确认信息 - 对应微信小程序的 items?.self_take_confirm || false
    final takeTimeData = ref.read(submitOrderInfoProvider).valueOrNull;
    final selfTakeConfirmValue = takeTimeData?.selfTakeConfirm;
    // 支持多种格式：字符串"1"、字符串"true"等（因为模型中定义为String?类型）
    final isSelfTakeConfirm = selfTakeConfirmValue == "1" ||
        selfTakeConfirmValue == "true" ||
        selfTakeConfirmValue?.toLowerCase() == "true";
    final selfTakeMsg = takeTimeData?.selfTakeMsg;

    // 如果需要显示自取确认对话框
    if (isSelfTakeConfirm && selfTakeMsg != null) {
      if (ref.context == null) {
        return false;
      }

      // 显示自取确认对话框
      final bool? confirmed = await SelfTakeConfirmDialog.show(
        context: ref.context!,
        title: selfTakeMsg.title ?? S.current.dialog_title_info,
        content: selfTakeMsg.msg ?? "该订单为自取订单，需要到店自取，是否确认?",
        confirmButtonText: selfTakeMsg.btn ?? S.current.confirm,
        cancelButtonText: selfTakeMsg.closeBtn ?? S.current.cancel,
      );

      // 如果用户取消确认，返回true表示需要显示对话框
      return confirmed != true;
    }

    // 不需要显示确认对话框
    return false;
  }

  /// 显示时间确认对话框
  /// 当订单配送时间与当前时间的差值大于等于120分钟时显示
  /// [timeTitle] 时间差的格式化文本
  /// 返回值：用户点击确认返回true，点击取消返回false
  Future<bool?> showTimeConfirmDialog(final String timeTitle) async {
    // 获取当前配送时间
    final currentTakeTime = ref.read(currentTakeTimeProvider);

    // 获取配送时间的小时和分钟
    String hourMinute = '';
    if (currentTakeTime.time != null) {
      if (currentTakeTime.time == S.current.now_time) {
        hourMinute = currentTakeTime.realBookingTime ?? '';
      } else {
        hourMinute = currentTakeTime.time ?? '';
      }
    }

    // 获取配送日期（今天或明天）
    String deliveryDay = '';
    if (currentTakeTime.isToday == null || currentTakeTime.isToday == true) {
      // 今天的配送时间
      deliveryDay = S.current.today_time;
    } else {
      // 明天的配送时间
      deliveryDay = S.current.yesterday_time;
    }

    if (ref.context == null) {
      return false;
    }

    // 使用TimeConfirmDialog显示对话框
    return TimeConfirmDialog.show(
      context: ref.context!,
      timeTitle: timeTitle,
      deliveryDay: deliveryDay,
      hourMinute: hourMinute,
    );
  }

  /// 获取显示距离警告状态
  bool shouldShowDistanceWarning() {
    // 获取当前选中的地址（可能是用户从历史地址列表中选择的）
    final currentAddress = ref.read(currentAddressProvider);
    // 如果用户已选择地址（currentAddress.id不为null）且showDistanceWarn明确为true，则显示警告
    if (currentAddress.id != null) {
      return currentAddress.showDistanceWarn == true;
    }

    // 如果用户未选择地址，尝试使用默认地址的警告状态
    final defaultAddress = ref.read(
      submitOrderInfoProvider.select((final v) => v.valueOrNull?.address),
    );
    return defaultAddress?.showDistanceWarn == true;
  }

  /// 获取配送模式
  int getDeliveryMode() {
    return ref.read(deliveryModeProvider);
  }

  /// 获取当前选中的地址
  CurrentSelectAddress? getCurrentAddress() {
    // 获取默认地址
    final defaultAddress = ref.read(
      submitOrderInfoProvider.select((final v) => v.valueOrNull?.address),
    );

    // 获取用户选中的地址
    final selectAddress = ref.read(currentAddressProvider);

    // 优先使用用户选中的地址，如果没有则显示默认地址
    return selectAddress.id != null
        ? selectAddress
        : (defaultAddress != null
            ? CurrentSelectAddress(
                id: defaultAddress.addressId,
                name: defaultAddress.name,
                tel: defaultAddress.tel,
                address: defaultAddress.address,
                buildingName: defaultAddress.buildingName,
                showDistanceWarn: defaultAddress.showDistanceWarn,
              )
            : null);
  }

  /// 设置用户选择的地址
  void setSelectedAddress(
    final HistoryAddressData address, {
    required final int restaurantId,
    required final int buildingId,
  }) {
    // 同时更新两个地址Provider
    ref.read(setAddressProvider.notifier).setAddress(address);

    // 创建新的CurrentSelectAddress对象
    final selectedAddress = CurrentSelectAddress(
      id: address.id,
      name: address.name,
      tel: address.tel,
      address: address.address,
      buildingName: address.buildingName,
      buildingId: address.buildingId,
      showDistanceWarn: address.showDistanceWarn,
    );

    // 立即更新当前地址
    ref.read(currentAddressProvider.notifier).setAddress(selectedAddress);

    // 选择地址后重新计算价格
    calculatePrices();
  }

  /// 获取配送时间文本
  String getTimeText() {
    // 获取当前选中的配送时间
    final currentTakeTime = ref.read(currentTakeTimeProvider);

    // 默认时间文本
    String timeText = "";

    // 如果用户已选择配送时间
    if (currentTakeTime.time != null) {
      // 获取完整的时间列表数据以判断是否为实时配送
      final takeTimeListAsync = ref.read(submitOrderInfoProvider);
      if (takeTimeListAsync.hasValue) {
        final timeList = takeTimeListAsync.value?.cst ?? [];

        // 找到当前选中的时间项
        final selectedTimeItem = timeList.firstWhere(
          (time) => time.dateTime == currentTakeTime.dateTime,
          orElse: () => timeList.isNotEmpty ? timeList.first : TakeTimeCst(),
        );

        // 根据 is_real_time 字段判断显示逻辑 - 与小程序逻辑一致
        if (selectedTimeItem.isRealTime == 0) {
          // 非实时配送 - 显示预约时间
          String bookingDay = "";
          if (currentTakeTime.isToday == null ||
              currentTakeTime.isToday == true) {
            bookingDay = S.current.today_time;
          } else {
            bookingDay = S.current.yesterday_time;
          }
          timeText = "$bookingDay ${currentTakeTime.time!}";
        } else {
          // 实时配送 - 显示大概时间
          timeText =
              "${S.current.probably} ${currentTakeTime.realBookingTime ?? currentTakeTime.time!}";
        }
      } else {
        // 如果没有时间列表数据，使用默认逻辑
        if (currentTakeTime.isToday == null ||
            currentTakeTime.isToday == true) {
          timeText = "${S.current.today_time} ${currentTakeTime.time!}";
        } else {
          timeText = "${S.current.yesterday_time} ${currentTakeTime.time!}";
        }
      }
    }

    return timeText;
  }

  /// 获取配送时间标题
  String getTimeTitle() {
    final deliveryMode = ref.read(deliveryModeProvider);
    return deliveryMode == OrderCalculationConstants.deliveryMode
        ? S.current.distribution_time
        : S.current.self_take_time;
  }

  /// 设置配送时间
  void setDeliveryTime(
    final TakeTimeCst time, {
    required final int restaurantId,
    required final int buildingId,
  }) {
    // 更新配送时间
    ref.read(currentTakeTimeProvider.notifier).state = time;

    // 标记时间已同步
    _takeTimeSynced = true;

    // 选择时间后重新计算价格
    calculatePrices();
  }

  /// 获取满减规则显示数据
  /// 返回包含 price_start 和 price_end 的 Map，用于UI显示
  Map<String, String> getReductionDisplayData() {
    // 获取购物车计算结果
    final shoppingCartNotifier = ref.read(shoppingCartProvider.notifier);
    final selectedCoupon = ref.read(selectedCouponProvider);
    final foodsData = ref.read(restaurantDetailControllerProvider).foodsData;

    final calculation = shoppingCartNotifier.calculateCart(
      foodsData: foodsData,
      deliveryMode: OrderCalculationConstants.deliveryMode,
      couponAmount: selectedCoupon?.price != null
          ? num.tryParse(selectedCoupon!.price!) ?? 0
          : 0,
    );

    // 使用CalculationUtils获取满减规则显示数据
    return CalculationUtils.getReductionDisplayData(
      reductionCurrentStep: calculation.reductionCurrentStep,
      reductionDiscount: reductionDiscount,
    );
  }

  /// 启动配送时间自动刷新定时器 - 与小程序逻辑一致，每5分钟刷新一次
  void startShipmentTimeTimer(
    final int restaurantId,
    final int buildingId,
    final int? homeBuildingId,
  ) {
    // 先清除之前的定时器
    _clearShipmentTimeTimer();

    // 立即执行一次刷新 - 避免用户返回页面时等待5分钟
    refreshTimeList(restaurantId, buildingId, homeBuildingId);

    // 设置5分钟定时器，与小程序一致（300000毫秒）
    _shipmentTimeTimer = Timer.periodic(
      const Duration(minutes: 5),
      (final timer) {
        // 定时刷新配送时间列表
        refreshTimeList(restaurantId, buildingId, homeBuildingId);
      },
    );
  }

  /// 刷新配送时间列表的通用方法
  void refreshTimeList(
    final int restaurantId,
    final int buildingId,
    final int? homeBuildingId,
  ) {
    // 刷新配送时间列表 - 与小程序 getUserAddr() 逻辑一致
    ref
        .read(submitOrderInfoProvider.notifier)
        .getTakeTimeList(
          restaurantId: restaurantId,
          buildingId: ref.read(currentAddressProvider).buildingId ?? 0,
          homeBuildingId: homeBuildingId ?? buildingId,
        )
        .then((final result) {
      // 检查上下文是否仍然有效，防止在组件销毁后更新状态
      if (!context.mounted) return;

      // 刷新完成后，更新当前选中的时间项 - 与小程序逻辑一致
      _updateCurrentSelectedTime();
    }).catchError((error) {
      // 处理错误，避免未捕获的异常
      debugPrint("刷新配送时间列表出错: $error");
    });
  }

  /// 更新当前选中的时间项 - 确保realBookingTime等信息保持最新
  void _updateCurrentSelectedTime() {
    // 检查上下文是否仍然有效
    if (!context.mounted) return;

    final currentTakeTime = ref.read(currentTakeTimeProvider);
    final takeTimeListAsync = ref.read(submitOrderInfoProvider);

    // 如果没有选中时间或没有时间列表数据，则不更新
    if (currentTakeTime.dateTime == null || !takeTimeListAsync.hasValue) {
      return;
    }

    final timeList = takeTimeListAsync.value?.cst ?? [];
    if (timeList.isEmpty) return;

    // 找到当前选中时间对应的最新时间项
    final updatedTimeItem = timeList.firstWhere(
      (time) => time.dateTime == currentTakeTime.dateTime,
      orElse: () {
        // 如果找不到相同的dateTime，尝试找到第一个可用的时间
        return timeList.firstWhere(
          (time) => time.state == 1,
          orElse: () => timeList.first,
        );
      },
    );

    // 更新当前选中的时间项，特别是realBookingTime
    ref.read(currentTakeTimeProvider.notifier).state = TakeTimeCst(
      time: updatedTimeItem.isRealTime == 1
          ? S.current.now_time
          : updatedTimeItem.time,
      dateTime: updatedTimeItem.dateTime,
      isToday: updatedTimeItem.isToday,
      realBookingTime: updatedTimeItem.realBookingTime,
    );
  }

  /// 清除配送时间定时器 - 与小程序 onHide/onUnload 逻辑一致
  void _clearShipmentTimeTimer() {
    _shipmentTimeTimer?.cancel();
    _shipmentTimeTimer = null;
  }

  /// 页面隐藏时调用 - 与小程序 onHide 逻辑一致
  void onPageHide() {
    _clearShipmentTimeTimer();
  }

  /// 页面销毁时调用 - 与小程序 onUnload 逻辑一致
  void dispose() {
    _clearShipmentTimeTimer();
  }
}

/// 提供controller的Provider
final submitOrderControllerProvider =
    Provider.family<SubmitOrderController, BuildContext>(
        (final ref, final context) {
  return SubmitOrderController(ref, context);
});
