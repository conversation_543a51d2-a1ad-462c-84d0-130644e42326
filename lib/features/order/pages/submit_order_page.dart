import 'package:bot_toast/bot_toast.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/utils/calculation_utils.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/core/widgets/error_page.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/data/models/order/current_address_model.dart';
import 'package:user_app/data/models/order/take_time_list_model.dart';
import 'package:user_app/features/order/pages/remark_page.dart';
import 'package:user_app/features/order/pages/widgets/shipper_number_content.dart';
import 'package:user_app/features/order/providers/order_provider.dart';
import 'package:user_app/features/order/providers/payment_provider.dart';
import 'package:user_app/features/order/widgets/address_list.dart';
import 'package:user_app/features/order/widgets/bottom_card.dart';
import 'package:user_app/features/order/widgets/styles_widget.dart';
import 'package:user_app/features/order/widgets/take_time_list.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/widgets/multi_discount_popup_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/common_widgets.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/features/order/widgets/spec_selected_options_widget.dart';
import 'package:user_app/features/order/widgets/combo_food_items_widget.dart';
import 'package:user_app/features/order/controllers/submit_order_controller.dart'
    as order_controller;
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/features/order/pages/widgets/coupon_selection_modal.dart';

/// 套餐展开状态Provider
/// 每个套餐项目都有独立的展开状态
final comboExpandedStateProvider = StateProvider.family
    .autoDispose<bool, int>((final ref, final index) => false);

/// 提交订单页面
class SubmitOrderPage extends ConsumerStatefulWidget {
  /// 餐厅ID
  final int restaurantId;

  /// 建筑ID
  final int buildingId;

  /// 家庭建筑ID
  final int? homeBuildingId;

  /// 构造函数
  const SubmitOrderPage({
    super.key,
    required this.restaurantId,
    required this.buildingId,
    this.homeBuildingId,
  });

  @override
  ConsumerState<SubmitOrderPage> createState() => _SubmitOrderPageState();
}

class _SubmitOrderPageState extends ConsumerState<SubmitOrderPage>
    with RouteAware, WidgetsBindingObserver {
  // 标记地址和时间是否已同步
  bool _addressSynced = false;
  bool _takeTimeSynced = false;

  // 保存控制器引用，避免在dispose时使用ref
  late order_controller.SubmitOrderController _controller;

  // 优惠券列表滚动控制器
  final ScrollController _couponScrollController = ScrollController();

  // 获取订单控制器
  order_controller.SubmitOrderController get controller => _controller;

  @override
  void initState() {
    super.initState();
// 添加应用生命周期观察者
    WidgetsBinding.instance.addObserver(this);
    // 初始化控制器引用
    _controller =
        ref.read(order_controller.submitOrderControllerProvider(context));

    // 在初始化时重置数据并加载新数据
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      // 只有在组件仍然mounted时才执行初始化
      if (mounted) {
        // 初始化控制器
        controller.initialize(
          widget.restaurantId,
          widget.buildingId,
          widget.homeBuildingId,
        );
      }
    });
  }

  @override
  void didChangeAppLifecycleState(final AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // 刷新美食数据和购物车数据
      controller.updateCartFoodsData(
        widget.restaurantId,
        widget.buildingId,
      );
      // 重新启动定时器 - 与小程序 onShow 逻辑一致
      controller.startShipmentTimeTimer(
        widget.restaurantId,
        widget.buildingId,
        widget.homeBuildingId,
      );
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 注册路由观察者
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      AppContext().routeObserver.subscribe(this, route);
    }
    controller.calculatePrices();
  }

  @override
  void dispose() {
    // 取消注册路由观察者
    AppContext().routeObserver.unsubscribe(this);
    // 清理定时器 - 与小程序 onUnload 逻辑一致
    controller.dispose();
    // 释放优惠券滚动控制器
    _couponScrollController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  /// 当从其他页面返回到当前页面时触发 - 类似微信小程序的onShow
  @override
  void didPopNext() {
    super.didPopNext();
    // 刷新美食数据和购物车数据
    controller.updateCartFoodsData(
      widget.restaurantId,
      widget.buildingId,
    );
    // 重新启动定时器 - 与小程序 onShow 逻辑一致
    controller.startShipmentTimeTimer(
      widget.restaurantId,
      widget.buildingId,
      widget.homeBuildingId,
    );
  }

  /// 当当前页面被其他页面覆盖时触发 - 类似微信小程序的onHide
  @override
  void didPushNext() {
    super.didPushNext();
    // 页面被覆盖，清理定时器 - 与小程序 onHide 逻辑一致
    controller.onPageHide();
  }

  @override
  Widget build(final BuildContext context) {
    // 更新控制器引用以确保使用最新实例
    _controller =
        ref.watch(order_controller.submitOrderControllerProvider(context));

    // 监听订单信息的变化 - 增加mounted检查
    ref.listen(submitOrderInfoProvider, (final previous, final next) {
      if (!mounted) return; // 确保组件仍然存在

      if (next.hasValue && !_addressSynced) {
        controller.syncDefaultAddress();
        _addressSynced = true;
      }

      if (next.hasValue && !_takeTimeSynced) {
        controller.syncDefaultTakeTime();
        _takeTimeSynced = true;
      }
    });

    // 监听地址变化，当地址变化时重新获取配送时间列表
    ref.listen<CurrentSelectAddress>(
      currentAddressProvider,
      (final previous, final next) {
        // 只有当地址ID发生变化时才刷新
        if (previous?.id != next.id && next.id != null) {
          controller.refreshTimeList(
            widget.restaurantId,
            widget.buildingId,
            widget.homeBuildingId,
          );
          // 同时调用updateCartFoodsData更新购物车数据
          // updateCartFoodsData(restaurantId, buildingId);
        }
      },
    );

    // 在build中调用监听函数
    _listenPriceFactors();

    // 监听地址和配送时间变化
    ref.watch(currentAddressProvider);
    ref.watch(currentTakeTimeProvider);

    // 获取订单信息
    final takeTimeListAsync = ref.watch(submitOrderInfoProvider);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      bottomNavigationBar: _buildBottomNavigationBar(),
      appBar: _buildAppBar(),
      body: SafeArea(
        child: takeTimeListAsync.when(
          data: (final data) => Stack(
            children: [
              _buildMainContent(),
            ],
          ),
          error: (final error, final stack) => ErrorPage(
            message: error.toString(),
            onPressed: () {
              ref.invalidate(submitOrderInfoProvider);
            },
          ),
          loading: () => LoadingWidget(),
        ),
      ),
    );
  }

  // AppBar 组件
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.primary,
      title: Text(
        S.current.submit_order,
        style: TextStyle(fontSize: 18.sp, color: Colors.white),
      ),
      foregroundColor: Colors.white,
      iconTheme: IconThemeData(color: Colors.white, size: 24.sp),
    );
  }

  // 主要内容区域
  Widget _buildMainContent() {
    return Container(
      height: MediaQuery.of(context).size.height,
      padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
      child: SingleChildScrollView(
        child: Column(
          children: [
            // 配送/自取选择器
            _buildDeliveryModeSelector(),
            // 地址部分
            _buildAddressSection(),
            // 订单详情
            _buildOrderDetailsSection(),
            // 优惠券选择
            _buildCouponContent(),
            // 活动优惠
            _buildActivityDiscount(),
            // 备注和餐具数量选择
            _buildRemarkContent(),
            // 添加输入骑手编号功能
            ShipperNumberContent(
              deliveryMode: ref.watch(deliveryModeProvider),
            ),
          ],
        ),
      ),
    );
  }

  // 地址部分
  Widget _buildAddressSection() {
    // 监听地址变化以更新UI
    ref.watch(currentAddressProvider);

    // 获取是否显示距离警告
    final showDistanceWarn = controller.shouldShowDistanceWarning();

    // 获取当前配送模式
    final deliveryMode = ref.watch(deliveryModeProvider);
    final isDeliveryMode =
        deliveryMode == OrderCalculationConstants.deliveryMode;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        children: [
          Divider(height: 1.h, color: Colors.grey.shade200),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: isDeliveryMode ? _orderAddress() : _buildPickupInfo(),
          ),
          if (showDistanceWarn && isDeliveryMode)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: Colors.red,
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.white),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      S.current.difference_address,
                      style: TextStyle(color: Colors.white, fontSize: 14.sp),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          Divider(height: 1.h, color: Colors.grey.shade200),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: _buildBookingTime(),
          ),
        ],
      ),
    );
  }

  // 配送/自取选择器
  Widget _buildDeliveryModeSelector() {
    final deliveryMode = ref.watch(deliveryModeProvider);
    final isDeliveryMode =
        deliveryMode == OrderCalculationConstants.deliveryMode;
    final isPickupMode = deliveryMode == OrderCalculationConstants.pickupMode;

    // 总是显示自取免运费标签
    const hasFreeShipping = true;

    // 获取固定的配送价格和自取价格
    final deliveryPrice = controller.getDeliveryPrice();
    final pickupPrice = controller.getPickupPrice();

    return Visibility(
      visible: controller.isShowSelectShipmentArea,
      child: Container(
        margin: EdgeInsets.only(bottom: 5.h),
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Row(
          children: [
            Expanded(
              child: _buildDeliveryModeButton(
                title: S.current.takeout_delivery, // 配送
                price: "¥ ${FormatUtil.formatPrice(deliveryPrice)}",
                subtitle: S.current.takeout_mulazim, // 外卖员配送
                isSelected: isDeliveryMode,
                onTap: () {
                  ref.read(deliveryModeProvider.notifier).state =
                      OrderCalculationConstants.deliveryMode;
                },
              ),
            ),
            SizedBox(width: 10.w),
            Expanded(
              child: _buildDeliveryModeButton(
                title: S.current.self_take_for, // 自取
                price: "¥ ${FormatUtil.formatPrice(pickupPrice)}",
                subtitle: S.current.self_take_restaurant, // 到店自取
                isSelected: isPickupMode,
                onTap: () {
                  ref.read(deliveryModeProvider.notifier).state =
                      OrderCalculationConstants.pickupMode;
                },
                showFreeShipping: hasFreeShipping,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 备注和餐具数量选择
  Widget _buildRemarkContent() {
    return Consumer(
      builder: (final context, final ref, final child) {
        final deliveryMode = ref.watch(deliveryModeProvider);
        final isDeliveryMode =
            deliveryMode == OrderCalculationConstants.deliveryMode;
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 5.w),
          margin: EdgeInsets.only(bottom: 0.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Column(
            children: [
              _buildRemark(),
              Divider(color: Colors.grey.shade100),
              _buildTableware(),
            ],
          ),
        );
      },
    );
  }

  // 配送/自取按钮
  Widget _buildDeliveryModeButton({
    required final String title,
    required final String price,
    required final String subtitle,
    required final bool isSelected,
    required final VoidCallback onTap,
    final bool showFreeShipping = false,
  }) {
    // 获取圆角大小
    final borderRadius = 10.r;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(borderRadius),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // 主要容器
          Container(
            width: double.infinity,
            height: 92.h, // 固定高度避免溢出
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: isSelected ? AppColors.primary : Colors.grey.shade200,
                width: isSelected ? 2.w : 2.w,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Flexible(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 15.sp, // 减小字体
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(height: 4.h), // 减少间距
                Flexible(
                  child: Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12.sp, // 减小字体
                      color: AppColors.textSecondaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(height: 6.h),
                Flexible(
                  child: Directionality(
                    textDirection: TextDirection.ltr,
                    child: Text(
                      price,
                      style: TextStyle(
                        fontSize: 18.sp, // 减小字体
                        color:
                            showFreeShipping ? Colors.red : AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 自取模式显示"免运费"标签（左上角）
          if (showFreeShipping)
            Positioned(
              top: 0,
              left: 0,
              child: SizedBox(
                width: 72.w, // 减小三角形尺寸
                height: 72.w,
                child: Stack(
                  children: [
                    ClipPath(
                      clipper:
                          TriangleClipper(isLeft: true, radius: borderRadius),
                      child: Container(
                        width: 72.w,
                        height: 72.w,
                        color: Colors.red.withAlpha(72),
                      ),
                    ),
                    // 文本内容
                    Positioned(
                      top: 15.h,
                      left: 0.w,
                      child: Transform.rotate(
                        angle: -45 * 3.14159 / 180, // 逆时针旋转45度
                        child: SizedBox(
                          child: Center(
                            child: Text(
                              S.current.delivery_for,
                              style: TextStyle(
                                color: Colors.red,
                                fontSize: 12.sp, // 减小字体
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // 选中状态显示三角形和选中图标（右上角）
          if (isSelected)
            Positioned(
              top: 0,
              right: 0,
              child: ClipPath(
                clipper: TriangleClipper(isLeft: false, radius: borderRadius),
                child: Container(
                  width: 65.w, // 减小三角形尺寸
                  height: 56.w,
                  color: AppColors.primary,
                  alignment: Alignment.topRight,
                  child: Padding(
                    padding: EdgeInsets.only(top: 3.h, right: 3.w),
                    child: Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 28.sp, // 减小图标尺寸
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 订单地址
  Widget _orderAddress() {
    // 监听currentAddressProvider以确保地址更新时UI会刷新
    ref.watch(currentAddressProvider);

    // 使用controller获取当前地址
    final address = controller.getCurrentAddress();

    return InkWell(
      onTap: () => showUserAddressList(),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        child: address == null
            ? _selectAddress()
            : Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${address.buildingName} ${address.address}",
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimaryColor,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 5.h),
                        Text(
                          "${address.name}  ${address.tel}",
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppColors.textSecondaryColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 18.sp,
                    color: AppColors.textHintColor,
                  ),
                ],
              ),
      ),
    );
  }

  Widget _selectAddress() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.add_location_alt_outlined,
          size: 22.sp,
          color: AppColors.primary,
        ),
        SizedBox(width: 8.w),
        Text(
          S.current.choice_address,
          style: TextStyle(fontSize: 16.sp, color: AppColors.primary),
        ),
      ],
    );
  }

  // 展示配送时间区域
  Widget _buildBookingTime() {
    // 监听配送时间变化以更新UI
    ref.watch(currentTakeTimeProvider);

    // 获取配送方式和当前选择的时间
    final deliveryMode = ref.watch(deliveryModeProvider);
    final isDeliveryMode =
        deliveryMode == OrderCalculationConstants.deliveryMode;

    // 使用控制器获取标题和时间文本
    final title = controller.getTimeTitle();
    final timeText = controller.getTimeText();

    return InkWell(
      onTap: () => showTakeTimeList(),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(
                  isDeliveryMode ? Icons.access_time_filled : Icons.schedule,
                  size: 20.sp,
                  color: AppColors.primary,
                ),
                SizedBox(width: 8.w),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
              ],
            ),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Flexible(
                    child: Text(
                      timeText,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: AppColors.textPrimaryColor,
                      ),
                      textAlign: TextAlign.end,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 18.sp,
                    color: AppColors.textHintColor,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 自取店铺信息
  Widget _buildPickupInfo() {
    return Consumer(
      builder: (final context, final ref, final child) {
        final restaurantData = ref.watch(
          restaurantDetailControllerProvider
              .select((final state) => state.restaurantData),
        );
        return Container(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(6.r),
                child: CachedNetworkImage(
                  imageUrl: restaurantData?.logo ?? '',
                  width: 42.w,
                  height: 42.w,
                  fit: BoxFit.cover,
                  placeholder: (final context, final url) => Container(
                    width: 42.w,
                    height: 42.w,
                    color: Colors.grey.shade200,
                  ),
                  errorWidget: (final context, final url, final error) =>
                      Container(
                    width: 42.w,
                    height: 42.w,
                    color: Colors.grey.shade200,
                    child: Icon(
                      Icons.restaurant,
                      color: Colors.grey.shade400,
                      size: 24.sp,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      restaurantData?.name ?? '', // 餐厅名称
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 5.h),
                    Text(
                      restaurantData?.address ?? '', // 餐厅地址
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondaryColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 18.sp,
                color: AppColors.textHintColor,
              ),
            ],
          ),
        );
      },
    );
  }

  // 订单详情部分
  Widget _buildOrderDetailsSection() {
    return Consumer(
      builder: (final context, final ref, final child) {
        // 获取购物车数据和计算结果
        ref.watch(shoppingCartProvider);
        final deliveryMode = ref.watch(deliveryModeProvider);
        final isDeliveryMode =
            deliveryMode == OrderCalculationConstants.deliveryMode;
        final isPickupMode =
            deliveryMode == OrderCalculationConstants.pickupMode;

        // 从controller获取价格信息
        final totalPrice =
            isPickupMode ? controller.pickupPrice : controller.totalPrice;
        final shipmentFee = controller.shipmentFee;
        final shipmentDiscount = controller.shipmentDiscount;
        final lunchBoxFee = controller.lunchBoxFee;
        final totalOriginalPrice =
            isPickupMode ? controller.pickupPrice : controller.originalPrice;

        // 获取当前餐厅的食品数据
        ref.watch(
          restaurantDetailControllerProvider
              .select((final state) => state.foodsData),
        );
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          margin: EdgeInsets.symmetric(vertical: 8.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Column(
            children: [
              _orderFoodsList(),
              Divider(color: Colors.grey.shade100),
              // 配送费（只在配送模式下显示）
              if (isDeliveryMode) ...[
                _otherItem(
                  title: S.current.cart_delivery_fee,
                  price:
                      "¥ ${FormatUtil.formatPrice(shipmentFee - shipmentDiscount)}",
                  originalPrice: "¥ ${FormatUtil.formatPrice(shipmentFee)}",
                ),
                Divider(color: Colors.grey.shade100),
              ],
              // 餐盒费（只有在有餐盒费时显示）
              if (lunchBoxFee > 0) ...[
                _otherItem(
                  title: S.current.box_fee,
                  price: "¥ ${FormatUtil.formatPrice(lunchBoxFee)}",
                ),
                Divider(color: Colors.grey.shade100),
              ],
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    S.current.total_fee,
                    style: TextStyle(
                      fontSize: 16.sp,
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      if (totalPrice != totalOriginalPrice)
                        Text(
                          textDirection: TextDirection.ltr,
                          "¥ ${FormatUtil.formatPrice(totalOriginalPrice)}",
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey,
                            decoration: TextDecoration.lineThrough,
                            decorationColor: Colors.grey,
                          ),
                        ),
                      if (totalPrice != totalOriginalPrice)
                        SizedBox(
                          width: 8.w,
                        ),
                      Text(
                        "¥ ${FormatUtil.formatPrice(totalPrice)}",
                        textDirection: TextDirection.ltr,
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  // 活动优惠布局
  Widget _buildActivityDiscount() {
    final deliveryMode = ref.watch(deliveryModeProvider);
    final isDeliveryMode =
        deliveryMode == OrderCalculationConstants.deliveryMode;
    final isPickupMode = deliveryMode == OrderCalculationConstants.pickupMode;

    // 从controller获取价格信息
    final totalDiscountAmount = controller.totalDiscountAmount;
    final shipmentDiscount = controller.shipmentDiscount;
    final reductionDiscount = controller.reductionDiscount;
    final foodDiscount = controller.foodDiscount;
    final activePercentPrice =
        controller.activePercentPrice; // 活动优惠（情侣专享/抓雪花游戏）
    final totalPrice =
        isPickupMode ? controller.pickupPrice : controller.totalPrice;
    final totalOriginalPrice =
        isPickupMode ? controller.pickupPrice : controller.originalPrice;
    return totalDiscountAmount > 0
        ? Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            margin: EdgeInsets.only(bottom: 8.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Column(
              children: [
                // 情侣专享活动优惠
                if (activePercentPrice > 0) ...[
                  _buildDiscountItem(
                    image: Image.asset(
                      'assets/images/discount/lover_discount.png',
                      width: 20.w,
                      height: 20.h,
                      fit: BoxFit.contain,
                    ),
                    title: S.current.loverCommetOrderTitle,
                    price: "-¥ ${FormatUtil.formatPrice(activePercentPrice)}",
                  ),
                  Divider(color: Colors.grey.shade100),
                ],
                // 美食优惠
                if (foodDiscount > 0) ...[
                  _buildDiscountItem(
                    image: Image.asset(
                      ref.watch(languageProvider) == 'zh'
                          ? 'assets/images/discount/food_discount_zh.png'
                          : 'assets/images/discount/food_discount_ug.png',
                      height: 18.h,
                      fit: BoxFit.fitHeight,
                    ),
                    title: S.current.foodPreferential,
                    price: "-¥ ${FormatUtil.formatPrice(foodDiscount)}",
                  ),
                  Divider(color: Colors.grey.shade100),
                ],
                // 满减优惠
                if (reductionDiscount > 0) ...[
                  _buildReductionDiscountItem(),
                  Divider(color: Colors.grey.shade100),
                ],
                // 配送费优惠（只在配送模式下显示）
                if (isDeliveryMode) ...[
                  if (shipmentDiscount > 0) ...[
                    _buildShipmentDiscountItem(),
                    Divider(color: Colors.grey.shade100),
                  ],
                ],

                Container(
                  padding: EdgeInsets.symmetric(vertical: 5.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        S.current.total_discount_fee,
                        style: TextStyle(
                          fontSize: 18.sp,
                          color: Colors.red,
                        ),
                      ),
                      Directionality(
                        textDirection: TextDirection.ltr,
                        child: Text(
                          "-¥ ${FormatUtil.formatPrice(totalDiscountAmount)}",
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(color: Colors.grey.shade100),
                Container(
                  padding: EdgeInsets.symmetric(vertical: 5.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        S.current.actual_pay,
                        style: TextStyle(
                          fontSize: 16.sp,
                        ),
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          if (totalPrice != totalOriginalPrice)
                            Text(
                              textDirection: TextDirection.ltr,
                              "¥ ${FormatUtil.formatPrice(totalOriginalPrice)}",
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey,
                                decoration: TextDecoration.lineThrough,
                                decorationColor: Colors.grey,
                              ),
                            ),
                          if (totalPrice != totalOriginalPrice)
                            SizedBox(
                              width: 8.w,
                            ),
                          Text(
                            "¥ ${FormatUtil.formatPrice(totalPrice)}",
                            textDirection: TextDirection.ltr,
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          )
        : SizedBox.shrink();
  }

  // 餐具数量选择
  Widget _buildTableware() {
    final tablerwareCount = ref.watch(tablerwareCountProvider);
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 14.w),
      child: Row(
        children: [
          Row(
            children: [
              // 减少按钮
              InkWell(
                onTap: () {
                  if (tablerwareCount > 0) {
                    ref.read(tablerwareCountProvider.notifier).state =
                        tablerwareCount - 1;
                  }
                },
                child: Container(
                  width: 32.w,
                  height: 32.w,
                  decoration: BoxDecoration(
                    color: tablerwareCount > 0
                        ? AppColors.primary.withAlpha(50)
                        : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Center(
                    child: Icon(
                      Icons.remove,
                      size: 18.sp,
                      color:
                          tablerwareCount > 0 ? AppColors.primary : Colors.grey,
                    ),
                  ),
                ),
              ),

              // 数量显示
              Container(
                width: 40.w,
                alignment: Alignment.center,
                child: Text(
                  '$tablerwareCount',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
              ),

              // 增加按钮
              InkWell(
                onTap: () {
                  ref.read(tablerwareCountProvider.notifier).state =
                      tablerwareCount + 1;
                },
                child: Container(
                  width: 32.w,
                  height: 32.w,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withAlpha(50),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Center(
                    child: Icon(
                      Icons.add,
                      size: 18.sp,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(width: 20.w),
          Row(
            children: [
              Text(
                S.current.table_ware,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColors.textPrimaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 备注信息
  Widget _buildRemark() {
    final remark = ref.watch(remarkProvider);

    return InkWell(
      onTap: () => _navigateToRemarkPage(),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 10.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Text(
                  S.current.mother_day_remark,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Container(
                  constraints: BoxConstraints(maxWidth: 200.w),
                  child: Text(
                    remark.isEmpty ? S.current.input_your_require : remark,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: remark.isEmpty
                          ? AppColors.textSecondColor
                          : AppColors.textSecondaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 8.w),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16.sp,
                  color: AppColors.textHintColor,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 跳转到备注页面
  void _navigateToRemarkPage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (final context) => RemarkPage(
          initialRemark: ref.read(remarkProvider),
          onSaved: (final newRemark) {
            ref.read(remarkProvider.notifier).state = newRemark;
          },
        ),
      ),
    );
  }

  // 底部导航栏
  Widget _buildBottomNavigationBar() {
    // 获取当前配送模式
    final deliveryMode = ref.watch(deliveryModeProvider);
    final isPickupMode = deliveryMode == OrderCalculationConstants.pickupMode;

    // 根据配送模式选择价格
    final finalPrice =
        isPickupMode ? controller.pickupPrice : controller.totalPrice;

    return SafeArea(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.5.h),
        decoration: _buildBottomGradient(),
        child: BottomCard(
          price: finalPrice,
          originalPrice: controller.originalPrice,
          isFriendPay: true,
          onFriendPay: () {
            // BotToast.showText(text: S.current.agent_pay);
            _handleSubmitOrder(isAgent: true);
          },

          /// 提交订单
          onPay: () => _handleSubmitOrder(isAgent: false),
        ),
      ),
    );
  }

  /// 提交订单
  void _handleSubmitOrder({final bool isAgent = false}) async {
    // 显示加载提示
    LoadingDialog().show();

    try {
      // 使用controller的方法准备订单参数
      final params = await controller.prepareOrderParams(
        widget.restaurantId,
        showUserAddressList,
        showTakeTimeList,
        (final text) => BotToast.showText(text: text),
      );

      // 如果参数准备成功，则提交订单
      if (params != null) {
        await ref.read(paymentProvider.notifier).createOrder(params, isAgent);
      }
    } catch (e) {
      LoadingDialog().hide();
      // 关闭加载提示
      BotToast.closeAllLoading();
      // 显示错误信息
      BotToast.showText(text: "${S.current.submit_order_fail}: $e");
    }
  }

  /// 显示用户地址列表
  void showUserAddressList() {
    _buildBottomSheet(
      title: S.current.choice_address,
      child: AddressList(
        widget.restaurantId,
        widget.buildingId,
        onAddressSelected: (final address) {
          // 先关闭地址列表对话框
          Navigator.pop(context);

          // 使用controller的方法设置地址
          controller.setSelectedAddress(
            address,
            restaurantId: widget.restaurantId,
            buildingId: widget.buildingId,
          );

          // 强制刷新页面以确保UI更新
          if (mounted) setState(() {});
        },
      ),
    );
  }

  /// 显示配送时间列表
  void showTakeTimeList() {
    final title = controller.getTimeTitle();

    _buildBottomSheet(
      title: title,
      child: TakeTimeListWidget(
        onTimeSelected: () {
          // 标记配送时间已同步
          _takeTimeSynced = true;

          // 强制刷新页面以确保UI更新
          if (mounted) setState(() {});

          // 选择时间后重新计算价格
          controller.calculatePrices();
        },
      ),
    );
  }

  void _buildBottomSheet({
    required final String title,
    required final Widget child,
  }) {
    showModalBottomSheet(
      backgroundColor: Colors.red,
      context: context,
      builder: (final context) => Container(
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(15.r),
            topRight: Radius.circular(15.r),
          ),
        ),
        height: MediaQuery.of(context).size.height * 0.7,
        child: Column(
          children: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.backgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(15.r),
                  topRight: Radius.circular(15.r),
                ),
              ),
              height: 40.h,
              alignment: Alignment.center,
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 20.sp,
                  color: AppColors.textPrimaryColor,
                ),
              ),
            ),
            Expanded(child: child),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  // 订单食品列表
  Widget _orderFoodsList() {
    // 获取购物车商品
    final cartItems = ref.watch(shoppingCartProvider);

    if (cartItems.isEmpty) {
      return Padding(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        child: Center(
          child: Text(
            S.current.basket_no_food,
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textHintColor,
            ),
          ),
        ),
      );
    }

    return Column(
      children: [
        ...cartItems.asMap().entries.map((final entry) {
          final index = entry.key;
          final item = entry.value;

          // 计算商品的总价
          final itemPrice = CalculationUtils.getFinalPrice(
            cartFoodItem: item,
          );

          final foodsOriginalPrice = (item.oldPrice ?? 0) * (item.count ?? 0);

          final isMultiDiscount = item.multiDiscountId != null &&
              item.multiDiscountId! > 0 &&
              item.multiDiscountSteps != null &&
              item.multiDiscountSteps!.isNotEmpty;

          // 判断是否是套餐商品
          final isComboFood = item.foodType == 2 &&
              item.comboFoodItems != null &&
              item.comboFoodItems!.isNotEmpty;

          return Padding(
            padding: EdgeInsets.only(bottom: 12.h),
            child: Column(
              children: [
                // 普通商品或套餐主体信息
                // 普通商品布局
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 商品图片
                    ClipRRect(
                      borderRadius: BorderRadius.circular(6.r),
                      child: CachedNetworkImage(
                        imageUrl: item.foodsImage ?? "",
                        width: 55.w,
                        height: 55.w,
                        fit: BoxFit.cover,
                        placeholder: (final context, final url) => Container(
                          width: 55.w,
                          height: 55.w,
                          color: Colors.grey.shade200,
                        ),
                        errorWidget: (final context, final url, final error) =>
                            Container(
                          width: 55.w,
                          height: 55.w,
                          color: Colors.grey.shade200,
                          child: Icon(
                            Icons.fastfood,
                            color: Colors.grey.shade400,
                            size: 24.sp,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    // 商品信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 商品价格
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Expanded(
                                child: Text(
                                  item.foodsName ?? "",
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.textPrimaryColor,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (itemPrice != foodsOriginalPrice)
                                Text(
                                  "¥ ${FormatUtil.formatPrice(foodsOriginalPrice)}",
                                  textDirection: TextDirection.ltr,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: AppColors.textSecondaryColor,
                                    decoration: TextDecoration.lineThrough,
                                    decorationColor:
                                        AppColors.textSecondaryColor,
                                  ),
                                ),
                              if (itemPrice != foodsOriginalPrice)
                                SizedBox(width: 5.w),
                              Text(
                                "¥ ${FormatUtil.formatPrice(itemPrice)}",
                                textDirection: TextDirection.ltr,
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: AppColors.redColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 4.h),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // 商品数量
                              Row(
                                children: [
                                  Text(
                                    S.current.count,
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: AppColors.textSecondaryColor,
                                    ),
                                  ),
                                  SizedBox(width: 5.w),
                                  Directionality(
                                    textDirection: TextDirection.ltr,
                                    child: Text(
                                      " ×${item.count}",
                                      style: TextStyle(
                                        fontSize: 16.sp,
                                        color: AppColors.textSecondaryColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              //套餐 展开/折叠按钮
                              if (isComboFood)
                                Consumer(
                                  builder: (
                                    final contex,
                                    final WidgetRef ref,
                                    final child,
                                  ) {
                                    final isExpanded = ref.watch(
                                      comboExpandedStateProvider(index),
                                    );

                                    return InkWell(
                                      onTap: () {
                                        ref
                                            .read(
                                              comboExpandedStateProvider(index)
                                                  .notifier,
                                            )
                                            .state = !isExpanded;
                                      },
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: AppColors.backgroundColor,
                                          borderRadius:
                                              BorderRadius.circular(20.r),
                                        ),
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 8.w,
                                          vertical: 4.h,
                                        ),
                                        child: Row(
                                          children: [
                                            Text(
                                              S.current.snow_detail, // "详情"
                                              style: TextStyle(
                                                fontSize: 14.sp,
                                                color: AppColors.primary,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            SizedBox(width: 4.w),
                                            AnimatedRotation(
                                              duration:
                                                  Duration(milliseconds: 250),
                                              curve: Curves.easeInOut,
                                              turns: isExpanded
                                                  ? 0.5
                                                  : 0, // 0.5 = 180度
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  color: AppColors.primary,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                    20.r,
                                                  ),
                                                ),
                                                child: Icon(
                                                  Icons.keyboard_arrow_down,
                                                  size: 20.sp,
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                if (isComboFood)
                  // 套餐商品布局
                  _buildComboFoodItem(
                    item,
                    index,
                    itemPrice,
                    foodsOriginalPrice,
                  ),

                // 规格选择信息 - 与微信小程序逻辑一致
                if (item.specUniqueId != null &&
                    item.specUniqueId!.isNotEmpty &&
                    item.specSelectedOptions != null &&
                    item.specSelectedOptions!.isNotEmpty)
                  Container(
                    margin: EdgeInsets.symmetric(vertical: 4.h),
                    child: SpecSelectedOptionsWidget.withBackground(
                      specSelectedOptions: item.specSelectedOptions!,
                    ),
                  ),
                // 多重优惠显示（垂直布局）
                if (isMultiDiscount) ...[
                  SizedBox(height: 4.h),
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.backgroundColor, // 灰色背景
                      borderRadius: BorderRadius.circular(10.r), // 圆角10
                    ),
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.w, vertical: 6.h),
                    child: Column(
                      children: MultiDiscountPopupWidget.buildStepItems(
                        item.multiDiscountSteps!,
                        item.count ?? 0,
                        showOnlyToCount: true, // 只显示到当前数量的步骤
                        forceGrayColor: true, // 强制使用灰色
                        itemHeight: 16.h, // 较小的高度适合这里的布局
                      ),
                    ),
                  ),
                ],
              ],
            ),
          );
        }),
      ],
    );
  }

  /// 构建套餐美食项目
  /// 包含主要信息和可展开的详情列表
  Widget _buildComboFoodItem(
    final SelectFoodItem item,
    final int index,
    final num itemPrice,
    final num foodsOriginalPrice,
  ) {
    return Consumer(
      builder: (final context, final ref, final child) {
        // 获取展开状态 - 临时使用本地状态，后续可以移到provider中
        final isExpanded = ref.watch(comboExpandedStateProvider(index));

        return AnimatedContainer(
          duration: Duration(milliseconds: 250),
          height: isExpanded ? null : 0,
          child: isExpanded
              ? ComboFoodItemsWidget(comboItems: item.comboFoodItems)
              : null,
        );
      },
    );
  }

  Widget _otherItem({
    required final String title,
    required final String price,
    final bool isDiscount = false,
    final String? originalPrice,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondaryColor,
            ),
          ),
          Directionality(
            textDirection: TextDirection.ltr,
            child: Row(
              children: [
                Text(
                  price,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: isDiscount ? Colors.red : AppColors.textPrimaryColor,
                    fontWeight:
                        isDiscount ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                SizedBox(width: 10.w),
                if (price != originalPrice)
                  Text(
                    originalPrice ?? "",
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColors.textSecondaryColor,
                      decoration: TextDecoration.lineThrough,
                      decorationColor: AppColors.textSecondaryColor,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建优惠项 - 带图标和标题的优惠项
  Widget _buildDiscountItem({
    required final Widget image,
    required final String title,
    required final String price,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              // 图标
              image,
              SizedBox(width: 8.w),
              // 标题
              Text(
                title,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColors.textPrimaryColor,
                ),
              ),
            ],
          ),
          // 价格
          Directionality(
            textDirection: TextDirection.ltr,
            child: Text(
              price,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建配送费优惠项 - 带图标的配送费优惠显示
  /// 参考微信小程序中的配送费优惠显示逻辑
  Widget _buildShipmentDiscountItem() {
    // 从controller获取配送费优惠信息
    final shipmentDiscount = controller.shipmentDiscount;

    // 如果没有配送费优惠，不显示此项
    if (shipmentDiscount <= 0) {
      return const SizedBox.shrink();
    }

    // 获取配送费优惠的详细信息
    final takeTimeData = ref.read(submitOrderInfoProvider).valueOrNull;
    final shipmentInfo = takeTimeData?.shipmentInfo;
    final shipmentDiscountImage = shipmentInfo?.shipmentDiscountImage;
    final foodsData = ref.read(restaurantDetailControllerProvider).foodsData;
    final market = foodsData?.market;

    return InkWell(
      onTap: () {
        showShipmentReductionDialog(
          market?.tags?.shipmentReductionTags,
          context,
          ref,
        );
      },
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                // 配送费优惠图标
                if (shipmentDiscountImage != null &&
                    shipmentDiscountImage.isNotEmpty)
                  CachedNetworkImage(
                    imageUrl: shipmentDiscountImage,
                    height: 18.h,
                    fit: BoxFit.fitHeight,
                  )
                else
                  const SizedBox.shrink(),
                SizedBox(width: 8.w),

                // 配送费减免金额（直接显示优惠金额）
                Text(
                  FormatUtil.formatPrice(shipmentDiscount),
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
                SizedBox(width: 5.w),
                // "配送费"文字
                Text(
                  S.current.shipment_fee1,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
                SizedBox(width: 8.w),
                // 详情图标
                Icon(
                  Icons.help_outline,
                  size: 18.sp,
                  color: AppColors.textSecondaryColor,
                ),
              ],
            ),
            // 优惠金额
            Directionality(
              textDirection: TextDirection.ltr,
              child: Text(
                "-¥ ${FormatUtil.formatPrice(shipmentDiscount)}",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建满减优惠项 - 根据语言显示不同的布局
  Widget _buildReductionDiscountItem() {
    final currentLang = ref.read(languageProvider);
    final isZh = currentLang == "zh";

    // 从controller获取满减活动信息
    final reductionDiscount = controller.reductionDiscount;

    // 如果没有满减优惠，不显示此项
    if (reductionDiscount <= 0) {
      return const SizedBox.shrink();
    }

    // 获取满减活动的详细信息
    final takeTimeData = ref.read(submitOrderInfoProvider).valueOrNull;
    final shipmentInfo = takeTimeData?.shipmentInfo;
    final priceDiscountImage = shipmentInfo?.priceDiscountImage;

    // 从controller获取满减规则显示数据
    final reduction = controller.getReductionDisplayData();

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              // 满减图标
              if (priceDiscountImage != null && priceDiscountImage.isNotEmpty)
                CachedNetworkImage(
                  imageUrl: priceDiscountImage,
                  height: 18.h,
                  fit: BoxFit.fitHeight,
                ),
              SizedBox(width: 8.w),
              // 满减文案
              if (isZh) ...[
                Text(
                  '满',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
                Text(
                  reduction['price_start']!,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
                Text(
                  '减',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
                Text(
                  reduction['price_end']!,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
              ] else ...[
                Text(
                  reduction['price_end']!,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
                SizedBox(width: 2.w),
                Text(
                  S.current.price_end,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
                SizedBox(width: 2.w),
                Text(
                  reduction['price_start']!,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
                SizedBox(width: 2.w),
                Text(
                  S.current.price_start,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
              ],
            ],
          ),
          // 优惠金额
          Directionality(
            textDirection: TextDirection.ltr,
            child: Text(
              "-¥ ${reduction['price_end']}",
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCouponContent() {
    final takeTimeListAsync = ref.read(submitOrderInfoProvider);
    // 获取购物车计算结果
    final shoppingCartNotifier = ref.read(shoppingCartProvider.notifier);
    final deliveryMode = ref.read(deliveryModeProvider);

    // 计算购物车总计
    final calculation = shoppingCartNotifier.calculateCart(
      foodsData: ref.read(restaurantDetailControllerProvider).foodsData,
      deliveryMode: deliveryMode,
    );

    // 获取可用的优惠券列表
    final availableCoupons = takeTimeListAsync.when(
      data: (final data) {
        return data.coupon?.list
                ?.where((final coupon) {
                  final minPrice = num.tryParse(coupon.minPrice ?? "0") ?? 0;
                  return calculation.totalPrice >= minPrice;
                })
                .toList()
                .reversed
                .toList() ??
            [];
      },
      error: (final _, final __) => [],
      loading: () => [],
    );

    if (takeTimeListAsync.value?.coupon?.list?.isEmpty ?? true) {
      return SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.w),
      margin: EdgeInsets.only(bottom: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7.r),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              _showCouponSelectionModal(
                calculation,
              );
            },
            child: Row(
              children: [
                SvgPicture.asset(
                  "assets/images/mine/courtesy-icon.svg",
                  height: 20.h,
                ),
                SizedBox(width: 8.w),
                Text(
                  S.current.courtesy_page_title,
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
                Spacer(),
                Row(
                  children: [
                    Text(
                      S.current.get_more,
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: AppColors.textPrimaryColor,
                      ),
                    ),
                    SizedBox(
                      width: 8.w,
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 18.sp,
                    ),
                  ],
                ),
              ],
            ),
          ),
          // 只有当有可用优惠券时才显示优惠券列表
          if (availableCoupons.isNotEmpty) ...[
            SizedBox(height: 5.h),
            Container(
              alignment: ref.watch(languageProvider) == 'ug'
                  ? Alignment.centerRight
                  : Alignment.centerLeft,
              child: SingleChildScrollView(
                controller: _couponScrollController,
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: availableCoupons
                      .map((final coupon) => _buildCouponItem2(item: coupon))
                      .toList(),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCouponItem2({required final CouponItem item}) {
    // 获取当前选中的优惠券
    final selectedCoupon = ref.read(selectedCouponProvider);
    // 判断当前优惠券是否被选中
    final isSelected = selectedCoupon?.id == item.id;

    final isUg = Localizations.localeOf(context).languageCode == "en";

    return GestureDetector(
      onTap: () {
        // 如果当前优惠券已选中，则取消选中；否则选中当前优惠券
        if (isSelected) {
          ref.read(selectedCouponProvider.notifier).state = null;
        } else {
          ref.read(selectedCouponProvider.notifier).state = item;
        }
      },
      child: Column(
        children: [
          Stack(
            children: [
              // 原有内容
              Container(
                padding: EdgeInsets.all(6.w),
                decoration: BoxDecoration(
                  // 选中状态、这里需要渐变色显示背景
                  gradient: isSelected
                      ? LinearGradient(
                          colors: [
                            Color(0xffFF8F05),
                            Color(0xffFF190B),
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        )
                      : null,
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Container(
                  height: 100.w,
                  width: MediaQuery.of(context).size.width * 0.4,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(
                        "assets/images/order/coupon-two-image-${isUg ? "ug" : "zh"}.png",
                      ),
                      fit: BoxFit.fill,
                    ),
                  ),
                  child: Column(
                    children: [
                      SizedBox(height: 10.h),
                      // 价格部分 - 对应小程序的 courtesy-price
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            num.parse(item.price ?? "0") % 1 == 0
                                ? num.parse(item.price ?? "0")
                                    .toInt()
                                    .toString()
                                : num.parse(item.price ?? "0").toString(),
                            style: TextStyle(
                              fontSize: 16.sp, // 32rpx / 2
                              color: Colors.red,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(width: 2.w),
                          Text(
                            S.current.yuan,
                            style: TextStyle(
                              fontSize: 14.sp, // 28rpx / 2
                              color: Colors.red,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 5.h), // 10rpx / 2
                      // 满减条件文字 - 对应小程序的 min-price
                      Text(
                        S.current.coupon_image_price.replaceAll(
                          '%s',
                          num.parse(item.minPrice ?? "0") % 1 == 0
                              ? num.parse(item.minPrice ?? "0")
                                  .toInt()
                                  .toString()
                              : num.parse(item.minPrice ?? "0").toString(),
                        ),
                        style: TextStyle(
                          fontSize: 14.sp, // 24rpx / 2
                          color: Colors.red,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              // 选中状态指示器
              if (isSelected)
                Positioned(
                  top: 0,
                  right: 0,
                  child: ClipPath(
                    clipper: TriangleClipper(isLeft: false, radius: 10.r),
                    child: Container(
                      width: 65.w, // 减小三角形尺寸
                      height: 56.w,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Color(0xffFF8F05), Color(0xffFF190B)],
                        ),
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      alignment: Alignment.topRight,
                      child: Padding(
                        padding: EdgeInsets.only(top: 3.h, right: 3.w),
                        child: Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 28.sp, // 减小图标尺寸
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
          if (isSelected)
            Column(
              children: [
                SizedBox(height: 5.h),
                Text(
                  "${S.current.discount_ticket}${FormatUtil.formatPrice(num.parse(item.price ?? "0"))}${S.current.discount_for}",
                  style: TextStyle(fontSize: 12.sp, color: Color(0xffFF0202)),
                ),
              ],
            ),
        ],
      ),
    );
  }

  // 显示优惠券选择弹窗
  void _showCouponSelectionModal(
    final CartCalculationResult calculation,
  ) {
    final takeTimeListAsync = ref.read(submitOrderInfoProvider);
    final usingCoupons = takeTimeListAsync.value?.coupon?.using ?? [];
    final coupons = takeTimeListAsync.value?.coupon?.list ?? [];

    //倒序
    final reversedCoupons = coupons.reversed.toList();
    final reversedUsingCoupons = usingCoupons.reversed.toList();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (final context) {
        return CouponSelectionModal(
          availableCoupons: reversedCoupons,
          usingCoupons: reversedUsingCoupons,
          calculation: calculation,
        );
      },
    );
  }

  // 底部渐变背景
  BoxDecoration _buildBottomGradient() {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.bottomCenter,
        end: Alignment.topCenter,
        colors: [
          Colors.white,
          Colors.white.withAlpha(255),
          Colors.white.withAlpha(100),
          Colors.white.withAlpha(0),
        ],
        stops: [0, 0.5, 0.8, 1.0],
      ),
    );
  }

  // 监听价格相关因素变化并重新计算价格
  void _listenPriceFactors() {
    /// 监听配送方式变化
    final deliveryMode = ref.watch(deliveryModeProvider);

    /// 监听优惠券变化
    final selectedCoupon = ref.watch(selectedCouponProvider);
    // 监听购物车变化
    final cartItems = ref.watch(shoppingCartProvider);

    // 当这些因素变化时重新计算价格
    if (ref.read(restaurantDetailControllerProvider).foodsData != null) {
      controller.calculatePrices();
      // UI需要更新，所以调用setState
      if (mounted) setState(() {});
    }

    // 当选中优惠券变化时，自动滚动到对应位置
    if (selectedCoupon != null) {
      _scrollToSelectedCoupon(selectedCoupon);
    }
  }

  /// 滚动到选中的优惠券位置
  void _scrollToSelectedCoupon(final CouponItem selectedCoupon) {
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      final takeTimeListAsync = ref.read(submitOrderInfoProvider);
      final shoppingCartNotifier = ref.read(shoppingCartProvider.notifier);
      final deliveryMode = ref.read(deliveryModeProvider);

      // 计算购物车总计
      final calculation = shoppingCartNotifier.calculateCart(
        foodsData: ref.read(restaurantDetailControllerProvider).foodsData,
        deliveryMode: deliveryMode,
      );

      // 获取可用的优惠券列表
      final availableCoupons = takeTimeListAsync.when(
        data: (final data) {
          return data.coupon?.list
                  ?.where((final coupon) {
                    final minPrice = num.tryParse(coupon.minPrice ?? "0") ?? 0;
                    return calculation.totalPrice >= minPrice;
                  })
                  .toList()
                  .reversed
                  .toList() ??
              [];
        },
        error: (final _, final __) => [],
        loading: () => [],
      );

      if (availableCoupons.isNotEmpty) {
        // 找到选中优惠券在列表中的索引
        final selectedIndex = availableCoupons.indexWhere(
          (final coupon) => coupon.id == selectedCoupon.id,
        );

        if (selectedIndex != -1 && _couponScrollController.hasClients) {
          // 计算每个优惠券项的宽度（根据实际UI：屏幕宽度的40% + padding）
          final screenWidth = MediaQuery.of(context).size.width;
          final itemWidth =
              screenWidth * 0.4 + 12.0; // 0.4倍屏幕宽度 + padding(6.w * 2)
          final scrollOffset = selectedIndex * itemWidth;

          // 获取ScrollView的可视宽度
          final viewportWidth =
              _couponScrollController.position.viewportDimension;

          // 计算目标滚动位置，让选中的优惠券居中显示
          final targetOffset =
              (scrollOffset - viewportWidth / 2 + itemWidth / 2)
                  .clamp(0.0, _couponScrollController.position.maxScrollExtent);

          // 平滑滚动到目标位置
          _couponScrollController.animateTo(
            targetOffset,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    });
  }
}
