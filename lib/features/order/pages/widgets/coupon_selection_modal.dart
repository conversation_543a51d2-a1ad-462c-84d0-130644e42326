import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/config.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/order/take_time_list_model.dart';
import 'package:user_app/data/models/user/courtesy_model.dart';
import 'package:user_app/features/index/pages/main_page.dart';
import 'package:user_app/features/mine/pages/courtesy/widgets/courtesy_coupon_card.dart';
import 'package:user_app/features/mine/pages/courtesy/courtesy_controller_provider.dart';
import 'package:user_app/features/order/providers/order_provider.dart';
import 'package:user_app/features/order/widgets/styles_widget.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/generated/l10n.dart';

final localSelectedCouponProvider =
    StateProvider.autoDispose<CouponItem?>((final ref) {
  return null;
});

/// 优惠券选择模态框组件
class CouponSelectionModal extends ConsumerStatefulWidget {
  final List<CouponItem> availableCoupons;
  final List<CouponItem> usingCoupons;
  final CartCalculationResult calculation;
  const CouponSelectionModal({
    super.key,
    required this.availableCoupons,
    required this.usingCoupons,
    required this.calculation,
  });

  @override
  ConsumerState<CouponSelectionModal> createState() =>
      _CouponSelectionModalState();
}

class _CouponSelectionModalState extends ConsumerState<CouponSelectionModal>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    //等待UI渲染完成
    WidgetsBinding.instance.addPostFrameCallback((final timeStamp) {
      //
      ref.read(localSelectedCouponProvider.notifier).state =
          ref.read(selectedCouponProvider.notifier).state;
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.7,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Align(
                alignment: Alignment.centerLeft,
                child: InkWell(
                  onTap: () => Navigator.pop(context),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Icon(Icons.close_rounded, size: 24.sp),
                  ),
                ),
              ),
              Expanded(
                child: Center(
                  child: Text(
                    S.current.courtesy_page_title,
                    style:
                        TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
              SizedBox(width: 16.sp),
            ],
          ),
          SizedBox(height: 8.h),
          Divider(
            height: 1.h,
            color: Colors.grey.shade200,
          ),
          SizedBox(height: 8.h),

          // 标签栏
          TabBar(
            controller: _tabController,
            labelColor: AppColors.textPrimaryColor,
            unselectedLabelColor: AppColors.textPrimaryColor,
            // 指示器样式
            indicator: UnderlineTabIndicator(
              // 指示器边框样式
              borderSide: BorderSide(width: 3.h, color: AppColors.primary),
              // 指示器边框圆角
              borderRadius: BorderRadius.circular(5.r),
            ),
            // 指示器内边距
            indicatorPadding: EdgeInsets.all(5.h),
            indicatorSize: TabBarIndicatorSize.label,
            indicatorWeight: 1.h,
            dividerColor: Colors.transparent,
            labelStyle: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.mainFont,
            ),
            unselectedLabelStyle: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.normal,
              fontFamily: AppConstants.mainFont,
            ),
            tabs: [
              Tab(text: S.current.courtesy_select_tap1),
              Tab(text: S.current.courtesy_select_tap2),
            ],
          ),
          SizedBox(height: 8.h),

          // 标签页内容
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: AppColors.baseBackgroundColor,
              ),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: AnimatedBuilder(
                  animation: _tabController,
                  builder: (final context, final child) {
                    return IndexedStack(
                      index: _tabController.index,
                      children: [
                        // 最新优惠券标签页
                        Column(
                          children: [
                            //显示最新标签和优惠券数量信息
                            Row(
                              children: [
                                Text(
                                  S.current.courtesy_card_new_text,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: AppColors.redColor,
                                  ),
                                ),
                                Spacer(),
                                Text(
                                  S.current.courtesy_sum_text.replaceAll(
                                    '%s',
                                    widget.availableCoupons.length.toString(),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 5.h),
                            Expanded(
                              child: _buildAvailableCouponsTab(),
                            ),
                          ],
                        ),
                        // 使用中优惠券标签页
                        _buildUsingCouponsTab(),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
          //
          // 底部确认按钮
          Container(
            width: MediaQuery.of(context).size.width,
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            height: 40.h,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(50.r),
            ),
            child: TextButton(
              style: TextButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              onPressed: () {
                ref.read(selectedCouponProvider.notifier).state =
                    ref.watch(localSelectedCouponProvider);
                Navigator.pop(context);
              },
              child: Text(
                S.current.confirm,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建可用优惠券标签页
  Widget _buildAvailableCouponsTab() {
    return ListView.builder(
      itemCount: widget.availableCoupons.length,
      itemBuilder: (final context, final index) {
        final coupon = widget.availableCoupons[index];
        final minPrice = num.tryParse(coupon.minPrice ?? "0") ?? 0;
        final isAvailable = widget.calculation.totalPrice >= minPrice;
        return GestureDetector(
          onTap: () {
            if (!isAvailable) {
              return;
            }
            final selectedCoupon = ref.watch(localSelectedCouponProvider);
            final isSelected = selectedCoupon?.id == coupon.id;
            if (!isSelected) {
              ref.read(localSelectedCouponProvider.notifier).state = coupon;
            } else {
              ref.read(localSelectedCouponProvider.notifier).state = null;
            }
          },
          child: Consumer(
            builder: (final context, final ref, final child) {
              final selectedCoupon = ref.watch(localSelectedCouponProvider);
              final isSelected = selectedCoupon?.id == coupon.id;
              return Stack(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSelected ? 6.w : 0,
                    ),
                    decoration: BoxDecoration(
                      // 选中状态、这里需要渐变色显示背景
                      gradient: isSelected
                          ? LinearGradient(
                              colors: [
                                Color(0xffFF8F05),
                                Color(0xffFF190B),
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            )
                          : null,
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: CourtesyCouponCard(
                      coupon: _convertToCourtesy(coupon),
                      controller: ref.read(courtesyControllerProvider.notifier),
                      isGray: !isAvailable, // 使用中的优惠券显示为灰色
                      isUsing: true,
                      // 是否显示没有满足条件的优惠券
                      isShowNoAvailable: !isAvailable,
                    ),
                  ),

                  // 选中状态指示器
                  if (isSelected)
                    Positioned(
                      top: 0,
                      right: 0,
                      child: ClipPath(
                        clipper: TriangleClipper(isLeft: false, radius: 10.r),
                        child: Container(
                          width: 65.w, // 减小三角形尺寸
                          height: 56.w,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Color(0xffFF8F05), Color(0xffFF190B)],
                            ),
                            borderRadius: BorderRadius.circular(10.r),
                          ),
                          alignment: Alignment.topRight,
                          child: Padding(
                            padding: EdgeInsets.only(top: 3.h, right: 3.w),
                            child: Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 28.sp, // 减小图标尺寸
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  // 构建使用中优惠券标签页
  Widget _buildUsingCouponsTab() {
    if (widget.usingCoupons.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.card_giftcard_outlined,
              size: 64.sp,
              color: Colors.grey.shade400,
            ),
            SizedBox(height: 16.h),
            Text(
              S.current.courtesy_using_card_no_data,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: widget.usingCoupons.length,
      itemBuilder: (final context, final index) {
        final usingCoupon = widget.usingCoupons[index];

        // 将usingCoupon转换为Courtesy对象
        final courtesy = _convertToCourtesy(usingCoupon);

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.w),
          child: GestureDetector(
            onTap: () {
              // 关闭当前页面
              Navigator.pop(context);
              // 跳转至优惠券详情页
              MainPageTabs.navigateToTab(context, MainPageTabs.order);
            },
            child: Column(
              children: [
                Container(
                  width: MediaQuery.of(context).size.width,
                  margin: EdgeInsets.symmetric(horizontal: 1.w),
                  child: CourtesyCouponCard(
                    coupon: courtesy,
                    controller: ref.read(courtesyControllerProvider.notifier),
                    isGray: false, // 使用中的优惠券显示为红色
                    isUsing: true,
                  ),
                ),
                // 红色区域：显示订单价格和“使用中”文字（都在一行）
                Transform.translate(
                  offset: Offset(0.5.w, -15.h),
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(8.r),
                        bottomRight: Radius.circular(8.r),
                      ),
                    ),
                    child: Row(
                      children: [
                        Row(
                          children: [
                            Text(
                              courtesy.orderPrice,
                              style: TextStyle(
                                color: Colors.red,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              S.current.courtesy_using_text,
                              style: TextStyle(
                                color: Colors.red,
                                fontSize: 16.sp,
                              ),
                            ),
                          ],
                        ),
                        Spacer(),
                        // 灰色区域：显示“查看”及不同语言的图标（都在一行，查看在最后）
                        Row(
                          children: [
                            Text(
                              S.current.eye,
                              style: TextStyle(
                                color: Colors.grey.shade700,
                                fontSize: 14.sp,
                              ),
                            ),
                            SizedBox(width: 4.w),
                            // 图标
                            Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.grey.shade600,
                              size: 14.sp,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 将动态对象转换为Courtesy对象
  Courtesy _convertToCourtesy(final CouponItem couponData) {
    // 根据实际的数据结构进行转换
    // 这里假设usingCoupon是一个Map或者有类似的结构
    return Courtesy(
      id: couponData.id ?? 0,
      userId: couponData.userId ?? 0,
      couponId: couponData.couponId ?? 0,
      state: couponData.state ?? 0,
      cityId: couponData.cityId ?? 0,
      areaId: couponData.areaId ?? 0,
      startTime: couponData.startTime ?? '',
      endTime: couponData.endTime ?? '',
      startUseTime: couponData.startUseTime ?? '',
      endUseTime: couponData.endUseTime ?? '',
      lotteryOrderId: couponData.lotteryOrderId ?? 0,
      couponType: couponData.couponType ?? 0,
      userCouponStartUseTime: couponData.userCouponStartUseTime ?? '',
      userCouponEndUseTime: couponData.userCouponEndUseTime ?? '',
      price: num.tryParse(couponData.price ?? '0') ?? 0,
      minPrice: num.tryParse(couponData.minPrice ?? '0') ?? 0,
      count: couponData.count ?? 0,
      notice: couponData.notice ?? '',
      orderPrice: couponData.order_price.toString(),
      areaName: '',
      useArea: '',
      area: '',
    );
  }
}
