import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';

/// 自取确认对话框
/// 当用户选择自取模式且需要确认时显示
/// 对应微信小程序中的resTip组件
class SelfTakeConfirmDialog extends StatefulWidget {
  final String title;
  final String content;
  final String confirmButtonText;
  final String cancelButtonText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;

  const SelfTakeConfirmDialog({
    super.key,
    required this.title,
    required this.content,
    required this.confirmButtonText,
    required this.cancelButtonText,
    this.onConfirm,
    this.onCancel,
  });

  /// 显示自取确认对话框
  static Future<bool?> show({
    required final BuildContext context,
    required final String title,
    required final String content,
    required final String confirmButtonText,
    required final String cancelButtonText,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false, // 不允许点击遮罩层关闭
      builder: (final context) => SelfTakeConfirmDialog(
        title: title,
        content: content,
        confirmButtonText: confirmButtonText,
        cancelButtonText: cancelButtonText,
        onConfirm: () => Navigator.of(context).pop(true),
        onCancel: () => Navigator.of(context).pop(false),
      ),
    );
  }

  @override
  State<SelfTakeConfirmDialog> createState() => _SelfTakeConfirmDialogState();
}

class _SelfTakeConfirmDialogState extends State<SelfTakeConfirmDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (final context, final child) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: EdgeInsets.fromLTRB(
              20.w, 0, 20.w, 70.h), // 对应微信小程序的 margin: -140rpx 40rpx 0px
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(14.r), // 对应微信小程序的 28rpx/2
                  image: const DecorationImage(
                    image: AssetImage('assets/images/tag.png'),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // 标题栏
                    _buildHeader(),

                    // 内容区域
                    _buildContent(),

                    // 按钮区域
                    _buildButtons(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建标题栏 - 对应微信小程序的 .close 样式
  Widget _buildHeader() {
    return Container(
      height: 45.h, // 对应微信小程序的 90rpx/2
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 20.w), // 对应微信小程序的 0 40rpx
      child: Row(
        children: [
          // 标题 - 对应微信小程序的 .popup-title
          Expanded(
            child: Text(
              widget.title,
              style: TextStyle(
                fontSize: 19.sp, // 对应微信小程序的 36rpx/2
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(width: 5.w),
          // 关闭图标
          GestureDetector(
            onTap: widget.onCancel,
            child: Icon(
              Icons.close,
              size: 20.sp, // 对应微信小程序的 20rpx/2
              color: AppColors.textHintColor,
            ),
          ), // 占位，保持标题居中
        ],
      ),
    );
  }

  /// 构建内容区域 - 对应微信小程序的 .item 和 .popup-scroll
  Widget _buildContent() {
    return Container(
      constraints: BoxConstraints(
        maxHeight: 210.h, // 对应微信小程序的 420rpx/2
      ),
      child: SingleChildScrollView(
        child: Container(
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(
              horizontal: 16.w, vertical: 12.h), // 对应微信小程序的 32rpx 25rpx 16rpx
          constraints: BoxConstraints(
            minHeight: 100.h, // 对应微信小程序的 200rpx/2
          ),
          child: Html(
            data: widget.content,
            style: {
              "body": Style(
                fontSize: FontSize(18.sp), // 对应微信小程序的 34rpx/2
                color: const Color(0xFF424242), // 对应微信小程序的 #424242
                lineHeight:
                    const LineHeight(1.65), // 对应微信小程序的 56rpx/34rpx ≈ 1.65
                textAlign: TextAlign.center,
                margin: Margins.zero,
                padding: HtmlPaddings.zero,
                fontWeight: FontWeight.bold,
              ),
              "p": Style(
                margin: Margins.zero,
                padding: HtmlPaddings.zero,
              ),
              "div": Style(
                margin: Margins.zero,
                padding: HtmlPaddings.zero,
              ),
            },
          ),
        ),
      ),
    );
  }

  /// 构建按钮区域 - 对应微信小程序的 .btn-grid
  Widget _buildButtons() {
    // 如果有取消按钮，显示两个按钮；否则只显示一个确认按钮
    final hasCancel = widget.cancelButtonText.isNotEmpty;

    return Container(
      padding: EdgeInsets.fromLTRB(0, 0, 0, 15.h), // 对应微信小程序的 margin: 30rpx 0px
      child: Row(
        mainAxisAlignment:
            MainAxisAlignment.spaceEvenly, // 对应微信小程序的 space-evenly
        children: [
          // 确认按钮 - 对应微信小程序的 .btn 或 .isBtn
          Expanded(
            flex: hasCancel ? 1 : 1,
            child: Padding(
              padding:
                  EdgeInsets.symmetric(horizontal: hasCancel ? 10.w : 40.w),
              child: GestureDetector(
                onTap: widget.onConfirm,
                child: Container(
                  padding: EdgeInsets.symmetric(
                      vertical:
                          hasCancel ? 10.h : 11.h), // 对应微信小程序的 20rpx/22rpx
                  decoration: BoxDecoration(
                    color:
                        AppColors.primary, // 对应微信小程序的 var(--theme-main-color)
                    borderRadius:
                        BorderRadius.circular(26.r), // 对应微信小程序的 52rpx/2
                  ),
                  child: Text(
                    widget.confirmButtonText,
                    style: TextStyle(
                      fontSize: 18.sp, // 对应微信小程序的 32rpx/2
                      fontWeight: FontWeight.w400,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ),

          // 取消按钮 - 对应微信小程序的 .closeBtn
          if (hasCancel)
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                child: GestureDetector(
                  onTap: widget.onCancel,
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(vertical: 10.h), // 对应微信小程序的 20rpx
                    decoration: BoxDecoration(
                      color: const Color(0xFFEEEEEE), // 对应微信小程序的 #EEEEEE
                      borderRadius:
                          BorderRadius.circular(26.r), // 对应微信小程序的 52rpx/2
                    ),
                    child: Text(
                      widget.cancelButtonText,
                      style: TextStyle(
                        fontSize: 18.sp, // 对应微信小程序的 32rpx/2
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF424242), // 对应微信小程序的 #424242
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
