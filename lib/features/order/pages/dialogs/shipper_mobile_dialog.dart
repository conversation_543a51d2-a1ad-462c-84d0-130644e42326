import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/dash_painter.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/features/order/providers/order_provider.dart';
import 'package:user_app/generated/l10n.dart';

/// 骑手手机号码选择对话框
class ShipperMobileDialog extends ConsumerWidget {
  final bool isUg;

  /// 构造函数
  const ShipperMobileDialog(this.isUg, {super.key});

  /// 显示对话框
  static Future<void> show(final BuildContext context) async {
    final isUg = Localizations.localeOf(context).languageCode == 'en';

    return showDialog(
      context: context,
      barrierDismissible: true,
      builder: (final context) => ShipperMobileDialog(isUg),
    );
  }

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final textController =
        TextEditingController(text: ref.read(shipperMobileProvider));
    textController.text =
        ref.read(localStorageRepositoryProvider).getShipperNumber() ?? "";

    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 15.w),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题
          Container(
            alignment: isUg ? Alignment.centerRight : Alignment.centerLeft,
            padding: EdgeInsets.all(15.w), // 对应30rpx
            child: Text(
              S.current.shipper_select_title,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          CustomPaint(
            painter: DashPainter(
              color: Colors.grey.shade200,
              strokeWidth: 1.h,
              borderRadius: 0,
              dashPattern: [1, 1],
            ),
            size: Size(double.infinity, 1.h),
          ),
          // 内容区域
          Container(
            padding: EdgeInsets.all(15.w),
            child: Column(
              crossAxisAlignment:
                  isUg ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                // 手机号标题
                Padding(
                  padding: EdgeInsets.only(bottom: 8.h),
                  child: Text(
                    S.current.shipper_mobile,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.black87,
                    ),
                  ),
                ),

                // 输入框
                Container(
                  height: 40.h, // 对应80rpx
                  decoration: BoxDecoration(
                    color: Color(0xFFF5F5F5), // 原始颜色#f5f5f5
                    borderRadius: BorderRadius.circular(5.r), // 对应10rpx
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 10.w), // 对应20rpx
                  child: TextField(
                    maxLength: 11,
                    maxLines: 1,
                    textDirection: TextDirection.ltr,
                    controller: textController,
                    keyboardType: TextInputType.phone,
                    style: TextStyle(fontSize: 20.sp), // 对应28rpx
                    decoration: InputDecoration(
                      hintText: S.current.shipper_placeholder,
                      hintStyle: TextStyle(fontSize: 15.sp),
                      hintTextDirection:
                          isUg ? TextDirection.rtl : TextDirection.ltr,
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                      counterText: '',
                    ),
                    onChanged: (final value) {
                      ref.read(shipperMobileProvider.notifier).state = value;
                    },
                  ),
                ),
              ],
            ),
          ),

          // 底部按钮
          Container(
            padding: EdgeInsets.all(15.w), // 对应30rpx
            child: Row(
              children: [
                // 取消按钮
                Expanded(
                  child: SizedBox(
                    height: 44.h, // 对应88rpx
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade200,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(44.r), // 对应44rpx
                        ),
                        padding: EdgeInsets.zero,
                        elevation: 0,
                      ),
                      child: Text(
                        S.current.cancel,
                        style: TextStyle(
                          fontSize: 16.sp, // 对应32rpx
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 17.w), // 对应34rpx

                // 确定按钮
                Expanded(
                  child: SizedBox(
                    height: 44.h, // 对应88rpx
                    child: ElevatedButton(
                      onPressed: () {
                        if (textController.text.isEmpty ||
                            textController.text.length != 11) {
                          BotToast.showText(
                              text: S.current.shipper_placeholder);
                          return;
                        }
                        ref.read(hasShipperNumberProvider.notifier).state =
                            true;
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        elevation: 0,
                        backgroundColor: Color(0xFF2ECC71),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(44.r), // 对应44rpx
                        ),
                        padding: EdgeInsets.zero,
                      ),
                      child: Text(
                        S.current.confirm,
                        style: TextStyle(
                          fontSize: 16.sp, // 对应32rpx
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
