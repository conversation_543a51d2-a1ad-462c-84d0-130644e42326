import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/environment_config.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/utils/wechat_util.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/features/order/providers/payment_provider.dart';
import 'package:user_app/features/order/widgets/bottom_card.dart';
import 'package:user_app/generated/l10n.dart';

/// 支付页面
class PaymentPage extends ConsumerStatefulWidget {
  /// 支付参数
  final Map<String, dynamic>? orderData;

  /// 构造方法
  const PaymentPage({
    super.key,
    this.orderData,
  });

  @override
  ConsumerState<PaymentPage> createState() => _PaymentPageState();
}

class _PaymentPageState extends ConsumerState<PaymentPage> {
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    // 如果订单数据存在，启动定时查询支付结果
    if (widget.orderData != null && widget.orderData!['orderId'] != null) {
      final orderId = widget.orderData!['orderId'].toString();

      // 启动支付结果查询
      ref.read(paymentProvider.notifier).startPayResultCheck(orderId: orderId);

      // 初始化现金支付状态检查
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!_isDisposed && mounted) {
          // 确保在widget构建完成后且未销毁时触发现金支付状态检查
          ref.read(cashPaymentStatusProvider(orderId));
        }
      });
    }
  }

  @override
  void dispose() {
    _isDisposed = true;

    // 停止支付结果查询定时器
    if (mounted) {
      try {
        ref.read(paymentProvider.notifier).stopPayResultCheck();
      } catch (e) {
        // 忽略dispose过程中的错误
      }
    }

    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    final orderData = widget.orderData;
    final orderId = orderData?['orderId']?.toString() ?? '';

    return Scaffold(
      appBar: AppBar(
        title: Text(S.current.pay_title),
      ),
      bottomNavigationBar: SafeArea(
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.5.h),
          child: BottomCard(
            price: orderData?['totalPrice'] ?? 0,
            originalPrice: orderData?['originalPrice'] ?? 0,
            onPay: () => _handlePayment(orderData),
          ),
        ),
      ),
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.all(10.w),
          child: _buildPaymentMethod(orderId),
        ),
      ),
    );
  }

  /// 处理支付逻辑
  Future<void> _handlePayment(Map<String, dynamic>? orderData) async {
    if (_isDisposed || !mounted || orderData == null) return;

    final paymentMethod = ref.read(paymentProvider);
    final orderId = orderData['orderId']?.toString() ?? '';

    if (paymentMethod == 2) {
      // 现金支付
      if (!_isDisposed && mounted) {
        final success = await ref
            .read(paymentProvider.notifier)
            .processCashPayment(orderId);
        if (!success) {
          // 支付失败，可以在这里做额外处理
        }
      }
    } else {
      // 微信支付
      if (!_isDisposed && mounted) {
        LoadingDialog().show();
        bool isSuccess = await WechatUtil().payWithWechatMiniProgram(
          orderId: orderId,
          path:
              "pages/miniPay/index?lang=ug&order_id=$orderId&pay_from=4&platform=app",
          type: EnvironmentConfig.wechatPayType,
          originalId: "gh_4c15504e80b3",
        );

        if (!_isDisposed && mounted) {
          LoadingDialog().hide();

          // 支付完成后开始查询支付结果
          ref
              .read(paymentProvider.notifier)
              .startPayResultCheck(orderId: orderId);
        }
      }
    }
  }

  /// 支付方式选择
  Widget _buildPaymentMethod(String orderId) {
    // 如果页面已销毁，返回空容器
    if (_isDisposed || !mounted) {
      return SizedBox.shrink();
    }

    final paymentMethod = ref.watch(paymentProvider);

    // 始终通过API查询现金支付状态，确保每次进入页面都会检查
    final cashPaymentStatus = ref.watch(cashPaymentStatusProvider(orderId));

    return Column(
      children: [
        /// 微信支付
        _buildPaymentItem(
          title: S.current.pay_wx,
          image: "assets/images/order/wechat.png",
          isYunshanfu: true,
          onTap: () {
            if (!_isDisposed && mounted) {
              ref.read(paymentProvider.notifier).changePayMethod(1);
            }
          },
          isSelected: paymentMethod == 1,
        ),
        SizedBox(height: 10.h),

        /// 现金支付 - 通过API查询现金支付状态
        cashPaymentStatus.when(
          data: (canCashPay) {
            if (canCashPay) {
              return _buildPaymentItem(
                title: S.current.cash_pay,
                image: "assets/images/order/cash.png",
                onTap: () {
                  if (!_isDisposed && mounted) {
                    ref.read(paymentProvider.notifier).changePayMethod(2);
                  }
                },
                isSelected: paymentMethod == 2,
              );
            } else {
              return SizedBox.shrink(); // 不可以现金支付时不显示
            }
          },
          loading: () => Container(
            height: 80.h,
            child: Center(
              child: CircularProgressIndicator(
                strokeWidth: 2.w,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
          ), // 加载中显示进度指示器
          error: (error, stack) => SizedBox.shrink(), // 错误时不显示
        ),
      ],
    );
  }

  ///  支付方式选项
  Widget _buildPaymentItem({
    required final String image,
    required final String title,
    required final bool isSelected,
    required final onTap,
    final bool? isYunshanfu,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(15.w),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(10.r)),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Image.asset(image, width: 54.w, height: 54.w),
                    SizedBox(width: 15.w),
                    Text(
                      title,
                      style: TextStyle(
                          fontSize: 16.sp, fontWeight: FontWeight.w400),
                    ),
                  ],
                ),
                Icon(
                  Icons.check_circle,
                  color: isSelected ? AppColors.primary : Colors.grey[300],
                  size: 24.w,
                )
              ],
            ),
            if (isYunshanfu == true)
              Column(
                children: [SizedBox(height: 20.h), _buildYunShanFu()],
              )
          ],
        ),
      ),
    );
  }

  /// 云闪付支付提示
  Widget _buildYunShanFu() {
    return Container(
      decoration: BoxDecoration(
          color: Colors.red.withAlpha(50),
          borderRadius: BorderRadius.circular(5.r)),
      child: Row(
        children: [
          Image.asset(
            "assets/images/order/yunshanfu.png",
            width: 32.w,
          ),
          SizedBox(width: 15.w),
          Text(
            S.current.yunshanfu_title,
            style: TextStyle(color: Colors.red),
          )
        ],
      ),
    );
  }
}
