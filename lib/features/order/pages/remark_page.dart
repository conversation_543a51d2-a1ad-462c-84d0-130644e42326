import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';


class RemarkPage extends ConsumerStatefulWidget {
  final String initialRemark;
  final Function(String) onSaved;

  const RemarkPage({
    super.key,
    this.initialRemark = '',
    required this.onSaved,
  });

  @override
  ConsumerState createState() => _RemarkPageState();
}

class _RemarkPageState extends ConsumerState<RemarkPage> {

  late TextEditingController _remarkController;
  bool _isEditMode = false;

  // 常用备注列表
  List<Map<String, dynamic>> _commonRemarks = [];

  @override
  void initState() {
    super.initState();
    _remarkController = TextEditingController(text: widget.initialRemark);
    _commonRemarks = [
      {'name': S.current.quick_come, 'active': true},
      {'name': S.current.food_many, 'active': true},
      {'name': S.current.dish_many, 'active': true},
      {'name': S.current.peppery_many, 'active': true},
      {'name': S.current.peppery_few, 'active': true},
      {'name': S.current.oil_few, 'active': true},
    ];
  }

  @override
  void dispose() {
    _remarkController.dispose();
    super.dispose();
  }

  void _onTextChanged(String text) {
    setState(() {});
  }

  void _toggleEditMode() {
    setState(() {
      _isEditMode = !_isEditMode;
    });
  }

  void _onTagPressed(int index) {
    if (_isEditMode) {
      // 编辑模式下，移除标签
      setState(() {
        _commonRemarks.removeAt(index);
      });
    } else {
      // 非编辑模式下，点击添加到输入框或移除
      final remarkTag = _commonRemarks[index]['name'] as String;

      // 检查标签是否已经被选中
      if (_remarkController.text.contains(remarkTag)) {
        // 如果已选中，则移除该标签
        String currentText = _remarkController.text;

        // 处理不同情况的移除逻辑
        if (currentText == remarkTag) {
          // 如果只有这一个标签，直接清空
          _remarkController.text = '';
        } else if (currentText.contains('، $remarkTag')) {
          // 如果标签在中间或末尾
          _remarkController.text = currentText.replaceAll('، $remarkTag', '');
        } else if (currentText.contains('$remarkTag، ')) {
          // 如果标签在开头
          _remarkController.text = currentText.replaceAll('$remarkTag، ', '');
        }
      } else {
        // 如果未选中，则添加该标签
        if (_remarkController.text.isEmpty) {
          _remarkController.text = remarkTag;
        } else {
          _remarkController.text = '${_remarkController.text}، $remarkTag';
        }
      }

      setState(() {});
    }
  }

  void _saveRemark() {
    widget.onSaved(_remarkController.text.trim());
    Navigator.pop(context);
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.white),
        title: Text(
          S.current.mother_day_remark,
          style: TextStyle(
            fontSize: 18.sp,
            color: Colors.white,
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: SingleChildScrollView(
          child: SizedBox(
            height: MediaQuery.of(context).size.height,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 文本输入区域
                _buildTextArea(),
                SizedBox(height: 10.h),
                // 快速标签区域
                _buildRemarkBody(),
                SizedBox(height: 45.h),
                // 保存按钮
                _buildSaveButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextArea() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Color(0xFFF1F1F1),
        borderRadius: BorderRadius.circular(10.r),
      ),
      margin: EdgeInsets.all(10.w),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            // margin: EdgeInsets.all(10.w),
            child: TextField(
              controller: _remarkController,
              onChanged: _onTextChanged,
              maxLines: null,
              maxLength: 200,
              textDirection: ref.watch(languageProvider) == 'ug' ? TextDirection.rtl : TextDirection.ltr,
              textAlign: ref.watch(languageProvider) == 'ug' ? TextAlign.right : TextAlign.left,
              style: TextStyle(
                fontSize: 17.sp,
                color: Colors.black,
              ),
              decoration: InputDecoration(
                filled: true,
                fillColor: Color(0xFFF1F1F1),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(5.r),
                  borderSide: BorderSide.none,
                ),
                hintText: S.current.input_your_require,
                hintStyle: TextStyle(
                  fontSize: 15.sp,
                  color: Colors.grey.shade500,
                ),
                counterText: '',
                isDense: true,
                constraints: BoxConstraints(
                  minHeight: 120.h,
                ),
              ),
            ),
          ),
          Container(
            width: double.infinity,
            margin: EdgeInsets.all(10.w),
            alignment: Alignment.centerLeft,
            child: Text(
              '${_remarkController.text.length}/200',
              style: TextStyle(
                fontSize: 15.sp,
                color: Color(0xFF757575),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRemarkBody() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: Column(
        children: [
          Container(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.current.save_remark,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Color(0xFF666666),
                  ),
                ),
                GestureDetector(
                  onTap: _toggleEditMode,
                  child: Text(
                    S.current.edit,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Color(0xFF757575),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 10.h),
          SizedBox(
            width: double.infinity,
            child: Wrap(
              spacing: 10.w,
              runSpacing: 10.h,
              alignment: WrapAlignment.start,
              runAlignment: WrapAlignment.start,
              children: [
                ...List.generate(
                  _commonRemarks.length,
                      (final int index) => _buildRemarkItem(index),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 单个备注标签
  Widget _buildRemarkItem(final int index) {
    final remark = _commonRemarks[index];
    final bool isActive = remark['active'] ?? true;

    // 检查该备注是否已包含在输入框文本中
    final bool isSelected = _remarkController.text.contains(remark['name']);

    return GestureDetector(
      onTap: () => _onTagPressed(index),
      child: Stack(
        children: [
          Container(
            margin: EdgeInsets.only(right: 5.w),
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 5.h),
            decoration: BoxDecoration(
              color: isSelected ? Color(0xFFE8F5E9) : Colors.transparent,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: isSelected
                    ? AppColors.primary
                    : (_isEditMode && !isActive
                    ? Color(0xFFE6E6E6).withAlpha(50)
                    : Color(0xFFE6E6E6)),
                width: 1,
              ),
            ),
            child: Text(
              remark['name'],
              style: TextStyle(
                fontSize: 14.sp,
                color: isSelected
                    ? AppColors.primary
                    : (_isEditMode && !isActive
                    ? Color(0xFF919191)
                    : Color(0xFF757575)),
              ),
            ),
          ),
          if (_isEditMode)
            Positioned(
              top: 0,
              right: 0,
              child: GestureDetector(
                onTap: () => _onTagPressed(index),
                child: Container(
                  width: 18.w,
                  height: 18.w,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 2,
                        offset: Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Icon(
                      Icons.close,
                      color: Colors.red,
                      size: 12.sp,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 保存按钮
  Widget _buildSaveButton() {
    return GestureDetector(
      onTap: _saveRemark,
      child: Container(
        width: double.infinity,
        margin: EdgeInsets.symmetric(horizontal: 20.w),
        padding: EdgeInsets.symmetric(vertical: 12.h),
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(50.r),
        ),
        child: Text(
          S.current.save_remark,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 17.sp,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
