// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$orderRepositoryHash() => r'4784a3041ad2359cdce689f6857da5b7957b305b';

/// 订单仓库提供者
///
/// Copied from [orderRepository].
@ProviderFor(orderRepository)
final orderRepositoryProvider = AutoDisposeProvider<OrderRepository>.internal(
  orderRepository,
  name: r'orderRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$orderRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef OrderRepositoryRef = AutoDisposeProviderRef<OrderRepository>;
String _$paymentHash() => r'957c4acde7f433b1b8b970d08854258d37f5a066';

/// 支付方式提供者
///
/// Copied from [Payment].
@ProviderFor(Payment)
final paymentProvider = AutoDisposeNotifierProvider<Payment, int>.internal(
  Payment.new,
  name: r'paymentProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$paymentHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Payment = AutoDisposeNotifier<int>;
String _$cashPaymentStatusHash() => r'34bea304fdcb837086bbe2d3b094d5b389736467';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$CashPaymentStatus
    extends BuildlessAutoDisposeNotifier<AsyncValue<bool>> {
  late final String orderId;

  AsyncValue<bool> build(
    String orderId,
  );
}

/// 现金支付状态提供者
///
/// Copied from [CashPaymentStatus].
@ProviderFor(CashPaymentStatus)
const cashPaymentStatusProvider = CashPaymentStatusFamily();

/// 现金支付状态提供者
///
/// Copied from [CashPaymentStatus].
class CashPaymentStatusFamily extends Family<AsyncValue<bool>> {
  /// 现金支付状态提供者
  ///
  /// Copied from [CashPaymentStatus].
  const CashPaymentStatusFamily();

  /// 现金支付状态提供者
  ///
  /// Copied from [CashPaymentStatus].
  CashPaymentStatusProvider call(
    String orderId,
  ) {
    return CashPaymentStatusProvider(
      orderId,
    );
  }

  @override
  CashPaymentStatusProvider getProviderOverride(
    covariant CashPaymentStatusProvider provider,
  ) {
    return call(
      provider.orderId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'cashPaymentStatusProvider';
}

/// 现金支付状态提供者
///
/// Copied from [CashPaymentStatus].
class CashPaymentStatusProvider extends AutoDisposeNotifierProviderImpl<
    CashPaymentStatus, AsyncValue<bool>> {
  /// 现金支付状态提供者
  ///
  /// Copied from [CashPaymentStatus].
  CashPaymentStatusProvider(
    String orderId,
  ) : this._internal(
          () => CashPaymentStatus()..orderId = orderId,
          from: cashPaymentStatusProvider,
          name: r'cashPaymentStatusProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$cashPaymentStatusHash,
          dependencies: CashPaymentStatusFamily._dependencies,
          allTransitiveDependencies:
              CashPaymentStatusFamily._allTransitiveDependencies,
          orderId: orderId,
        );

  CashPaymentStatusProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.orderId,
  }) : super.internal();

  final String orderId;

  @override
  AsyncValue<bool> runNotifierBuild(
    covariant CashPaymentStatus notifier,
  ) {
    return notifier.build(
      orderId,
    );
  }

  @override
  Override overrideWith(CashPaymentStatus Function() create) {
    return ProviderOverride(
      origin: this,
      override: CashPaymentStatusProvider._internal(
        () => create()..orderId = orderId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        orderId: orderId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<CashPaymentStatus, AsyncValue<bool>>
      createElement() {
    return _CashPaymentStatusProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CashPaymentStatusProvider && other.orderId == orderId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, orderId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CashPaymentStatusRef on AutoDisposeNotifierProviderRef<AsyncValue<bool>> {
  /// The parameter `orderId` of this provider.
  String get orderId;
}

class _CashPaymentStatusProviderElement
    extends AutoDisposeNotifierProviderElement<CashPaymentStatus,
        AsyncValue<bool>> with CashPaymentStatusRef {
  _CashPaymentStatusProviderElement(super.provider);

  @override
  String get orderId => (origin as CashPaymentStatusProvider).orderId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
