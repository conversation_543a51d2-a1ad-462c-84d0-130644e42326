import 'dart:async';
import 'dart:developer';

import 'package:bot_toast/bot_toast.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/data/models/order/create_order_param_model.dart';
import 'package:user_app/data/repositories/order/order_repositiry.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/features/index/pages/main_page.dart';
import 'package:user_app/features/my_order/pages/index/my_order_controller.dart';
import 'package:user_app/features/order/providers/order_provider.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/routes/paths.dart';

part 'payment_provider.g.dart';

/// 订单仓库提供者
@riverpod
OrderRepository orderRepository(final Ref ref) {
  return OrderRepository(apiClient: ref.watch(apiClientProvider));
}

/// 支付方式提供者
@riverpod
class Payment extends _$Payment {
  Timer? _timer;
  Timer? _payResultTimer;
  bool _isDisposed = false;

  @override
  int build() {
    ref.onDispose(() {
      _isDisposed = true;
      _timer?.cancel();
      _timer = null;
      _payResultTimer?.cancel();
      _payResultTimer = null;
    });
    return 1;
  }

  /// 修改付款方式
  void changePayMethod(final int index) {
    state = index;
  }

  /// 安全地取消定时器
  void _safeCancelTimer(Timer? timer) {
    if (!_isDisposed && timer != null) {
      timer.cancel();
    }
  }

  /// 创建新订单
  Future<void> createOrder(final CreateOrderParams params, bool isAgent) async {
    try {
      // 将 foods 转换为符合接口要求的格式
      final Map<String, dynamic> foodsMap = {};

      for (int i = 0; i < params.foods.length; i++) {
        final food = params.foods[i];
        foodsMap['foods[$i][id]'] = food.id.toString();
        foodsMap['foods[$i][count]'] = food.count.toString();
        foodsMap['foods[$i][food_type]'] = food.foodType.toString();

        // 多重折扣参数
        if (food.discountId != null && food.discountId! > 0) {
          foodsMap['foods[$i][discount_id]'] = food.discountId.toString();
        }
        if (food.discountNumber != null && food.discountNumber! > 0) {
          foodsMap['foods[$i][discount_number]'] =
              food.discountNumber.toString();
        }

        // 规格选项参数
        if (food.optionIds != null && food.optionIds!.isNotEmpty) {
          for (int j = 0; j < food.optionIds!.length; j++) {
            foodsMap['foods[$i][option_ids][$j]'] =
                food.optionIds![j].toString();
          }
        }

        // 秒杀参数
        if (food.seckillId != null && food.seckillId! > 0) {
          foodsMap['foods[$i][seckill_id]'] = food.seckillId.toString();
        }
      }

      final param = {
        "restaurant_id": params.restaurantId.toString(),
        "timezone": params.timezone,
        "booking_time": params.bookingTime,
        "order_type": params.orderType.toString(),
        "description": params.description,
        "price": params.allFoodPrice.toString(),// 美食原始价格，不含任何优惠
        "lunch_box_fee": params.lunchBoxFee.toString(),
        "delivery_type": params.deliveryType.toString(),
        "activity_id": params.activityId.toString(),
        "take_time_list_get_time": params.takeTimeListGetTime,
        "shipment": params.shipment.toString(),
        ...foodsMap,
      };

      // 配送地址ID（只在配送模式下添加）
      if (params.deliveryType == 1) {
        param['address_id'] = params.addressId.toString();
      }

      // 优惠券ID
      if (params.couponId != null && params.couponId! > 0) {
        param['coupon_id'] = params.couponId.toString();
      }

      // 特殊ID
      if (params.specialId != null && params.specialId! > 0) {
        param['special_id'] = params.specialId.toString();
      }

      // 配送员手机号
      final shipperNumber =
          ref.read(localStorageRepositoryProvider).getShipperNumber();
      final hasShipperNumber = ref.read(hasShipperNumberProvider);
      if (params.deliveryType == 1 &&
          hasShipperNumber &&
          shipperNumber != null &&
          shipperNumber.isNotEmpty) {
        param['shipper_mobile'] = shipperNumber;
      }

      /// 如果有减配送费择加上减配送费金额
      if (params.reduceShipment != null && params.reduceShipment != 0) {
        param['reduce_shipment'] = params.reduceShipment.toString();
      }

      /// 如果满减活动则加上减配送费金额
      if (params.reductionFee != null && params.reductionFee != 0) {
        param['reduction_fee'] = params.reductionFee.toString();
      }

      log("订单创建参数: $param");

      final repository = ref.read(orderRepositoryProvider);
      final response = await repository.createOrder(param);

      if (response.status != 200) {
        BotToast.showText(text: response.msg ?? '获取数据失败');
        LoadingDialog().hide();
        return;
      }
      // 更新我的订单列表
      Future.delayed(const Duration(seconds: 1), () {
        ref.invalidate(myOrderControllerProvider);
      });

      _timer = Timer.periodic(Duration(seconds: 2), (final timer) {
        checkQueryOrder(
            tmpOrderId: response.data?.tempOrderId ?? '',
            totalPrice: params.price,
            originalPrice: params.originalPrice,
            isAgent: isAgent);
      });
    } catch (e) {
      _timer?.cancel();
      BotToast.showText(text: e.toString());
    }
  }

  /// 查询创建的订单信息
  Future<void> checkQueryOrder(
      {required final String tmpOrderId,
      required final num totalPrice,
      required final num originalPrice,
      required final bool isAgent}) async {
    try {
      final repository = ref.read(orderRepositoryProvider);
      final response =
          await repository.queryOrder({"temp_order_id": tmpOrderId});
      if (response.status != 200) {
        _timer?.cancel();
        BotToast.showText(text: response.msg ?? '查询订单失败');
        LoadingDialog().hide();
        return;
      }
      if (response.data?.state == 2) {
        _timer?.cancel();
      }
      if (response.data?.state == 5) {
        _timer?.cancel();
        _timer = null;
        if (isAgent) {
          ref.context?.push(
            AppPaths.agentPay,
            extra: {
              'orderId': response.data?.orderId,
            },
          );
        } else {
          // 使用统一的支付页面跳转方法
          navigateToPaymentPage(
            orderId: response.data?.orderId,
            state: response.data?.state,
            totalPrice: totalPrice,
            originalPrice: originalPrice,
          );
        }
      }
    } catch (e) {
      _timer?.cancel();
      BotToast.showText(text: '查询订单失败 ${e.toString()}');
    }
  }

  /// 开始查询支付结果
  void startPayResultCheck({required final String orderId}) {
    // 如果已经有定时器在运行，先取消它
    _safeCancelTimer(_payResultTimer);

    // 每2秒查询一次支付结果
    _payResultTimer = Timer.periodic(Duration(seconds: 4), (final timer) {
      if (!_isDisposed) {
        checkPayResult(orderId: orderId);
      }
    });
  }

  /// 停止查询支付结果
  void stopPayResultCheck() {
    _safeCancelTimer(_payResultTimer);
    _payResultTimer = null;
  }

  /// 构造支付页面参数 - 统一支付页面跳转的参数格式
  /// 此方法确保订单确认页面和代理支付页面使用相同的参数结构
  static Map<String, dynamic> buildPaymentPageParams({
    required final dynamic orderId,
    required final dynamic state,
    required final num totalPrice,
    required final num originalPrice,
  }) {
    return {
      'orderId': orderId,
      'state': state,
      'totalPrice': totalPrice,
      'originalPrice': originalPrice,
    };
  }

  /// 跳转到支付页面 - 统一的跳转逻辑
  /// 此方法确保所有页面都使用相同的跳转逻辑和参数格式
  void navigateToPaymentPage({
    required final dynamic orderId,
    required final dynamic state,
    required final num totalPrice,
    required final num originalPrice,
  }) {
    final orderDataMap = buildPaymentPageParams(
      orderId: orderId,
      state: state,
      totalPrice: totalPrice,
      originalPrice: originalPrice,
    );

    LoadingDialog().hide();
    ref.context?.push(AppPaths.paymentPage, extra: orderDataMap);
  }

  /// 查询支付结果
  Future<void> checkPayResult({required final String orderId}) async {
    if (_isDisposed) return;

    try {
      final repository = ref.read(orderRepositoryProvider);
      final response = await repository.queryPayResult({"order_id": orderId});

      if (_isDisposed) return;

      // 如果支付成功
      if (response.status == 200 && response.data?.payState == 1) {
        // 取消定时器
        stopPayResultCheck();

        // 清空购物车
        ref.read(shoppingCartProvider.notifier).clearCardFoods();

        // 显示支付成功提示
        BotToast.showText(text: "پۇل تۆلەش مۇۋاپىقىيەتلىك بولدى");
        // 更新我的订单列表
        ref.invalidate(myOrderControllerProvider);
        // 跳转到首页，使用 go 替换当前页面
        if (ref.context != null) {
          MainPageTabs.navigateToTab(ref.context!, MainPageTabs.order);
        }
      }
      // 如果支付失败
      else if (response.status == 200 && response.data?.payState == 2) {
        // 取消定时器
        stopPayResultCheck();

        // 显示支付失败提示
        BotToast.showText(text: "پۇل تۆلەش مەغلۇب بولدى");
        // 更新我的订单列表
        ref.invalidate(myOrderControllerProvider);
      }
      // 如果支付中，继续查询
      else if (response.status == 200 && response.data?.payState == 0) {
        // 继续查询，不做处理
      }
      // 其他错误情况
      else {
        // 取消定时器
        stopPayResultCheck();

        // 显示错误提示
        BotToast.showText(
            text: response.msg ?? "تۆلەش نەتىجىسىنى تەكشۈرۈش مەغلۇپ بولدى");
      }
    } catch (e) {
      if (!_isDisposed) {
        // 取消定时器
        stopPayResultCheck();

        BotToast.showText(
            text: 'تۆلەش نەتىجىسىنى تەكشۈرۈش مەغلۇپ بولدى ${e.toString()}');
      }
    }
  }

  /// 检查是否可以现金支付
  Future<bool> checkCanCashPay(final String orderId) async {
    try {
      final repository = ref.read(orderRepositoryProvider);
      final response = await repository.checkCanCashPay(orderId);

      if (response.success && response.data?.status == 200) {
        return response.data?.data?.canCashPay ?? false;
      }
      return false;
    } catch (e) {
      log('检查现金支付失败: $e');
      return false;
    }
  }

  /// 现金支付
  Future<bool> processCashPayment(final String orderId) async {
    try {
      LoadingDialog().show();

      final repository = ref.read(orderRepositoryProvider);
      final response = await repository.cashPayment(orderId);

      LoadingDialog().hide();

      if (response.success) {
        // 清空购物车
        ref.read(shoppingCartProvider.notifier).clearCardFoods();

        // 显示支付成功提示
        BotToast.showText(text: "نەقپۇل تۆلەش تاپشۇرۇش مۇۋاپىقىيەتلىك بولدى");

        // 更新我的订单列表
        ref.invalidate(myOrderControllerProvider);

        // 跳转到首页
        if (ref.context != null) {
          MainPageTabs.navigateToTab(ref.context!, MainPageTabs.order);
        }
        return true;
      } else {
        BotToast.showText(text: response.msg);
        return false;
      }
    } catch (e) {
      LoadingDialog().hide();
      BotToast.showText(text: 'نەق پۇل تۆلەش مەغلۇپ بولدى: ${e.toString()}');
      return false;
    }
  }
}

/// 现金支付状态提供者
@riverpod
class CashPaymentStatus extends _$CashPaymentStatus {
  bool _isDisposed = false;

  @override
  AsyncValue<bool> build(String orderId) {
    _isDisposed = false;

    // 当 provider 被销毁时设置标记
    ref.onDispose(() {
      _isDisposed = true;
    });

    // 立即开始检查现金支付状态
    Future.microtask(() => _checkCashPayStatus(orderId));
    return const AsyncValue.loading();
  }

  /// 检查现金支付状态
  Future<void> _checkCashPayStatus(String orderId) async {
    // 如果已经被销毁，直接返回
    if (_isDisposed) return;

    try {
      if (!_isDisposed) {
        state = const AsyncValue.loading();
      }

      final paymentNotifier = ref.read(paymentProvider.notifier);
      final canCashPay = await paymentNotifier.checkCanCashPay(orderId);

      // 再次检查是否已被销毁
      if (_isDisposed) return;

      state = AsyncValue.data(canCashPay);
    } catch (e, stack) {
      // 检查是否已被销毁
      if (_isDisposed) return;

      state = AsyncValue.error(e, stack);
    }
  }

  /// 刷新现金支付状态
  Future<void> refresh(String orderId) async {
    if (!_isDisposed) {
      await _checkCashPayStatus(orderId);
    }
  }
}
