import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/repositories/order/order_repositiry.dart';

import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/order/current_address_model.dart';
import 'package:user_app/data/models/order/take_time_list_model.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';

part 'order_provider.g.dart';

/// 订单模块数据仓库提供者
final orderRepositoryProvider = Provider<OrderRepository>((final ref) {
  return OrderRepository(apiClient: ref.watch(apiClientProvider));
});

/// 提交订单页面数据提供者
@riverpod
class SubmitOrderInfo extends _$SubmitOrderInfo {
  @override
  FutureOr<TakeTimeListData> build() async {
    // 返回空数据，等待 getTakeTimeList 方法获取实际数据
    return TakeTimeListData();
  }

  /// 创建另一个方法以支持动态参数
  Future<TakeTimeListData> getTakeTimeList({
    required final int restaurantId,
    required final int buildingId,
    required final int homeBuildingId,
  }) async {
    try {
      // 获取购物车数据
      final cartFoods = ref.read(shoppingCartProvider);

      // 构建食物ID参数
      final Map<String, dynamic> postParams = {
        "restaurant_id": restaurantId,
        "building_id": buildingId,
        "home_building_id": homeBuildingId,
        "page": 1,
        "limit": 10,
      };
       // 检查是否有特价商品
      final cartItems = ref.read(shoppingCartProvider);
      final hasSpecialFood = cartItems.any((final item) => item.specialActive == 1);
      final currentSpecialId = hasSpecialFood ? ref.read(specialIdProvider) : null;
      if (currentSpecialId != null) {
        postParams["special_id"] = currentSpecialId;
      }

      // 添加购物车中的食物ID
      for (int i = 0; i < cartFoods.length; i++) {
        postParams["foods_id[$i]"] = cartFoods[i].id;
      }

      final orderRepository = ref.read(orderRepositoryProvider);
      final response = await orderRepository.getTakeTimeList(postParams);

      /// 如果获取失败，返回空数据
      if (response.data == null) {
        state = AsyncData(TakeTimeListData());
        return TakeTimeListData();
      }

      final data = response.data ?? TakeTimeListData();
      // 更新提供者的状态
      state = AsyncData(data);

      // 获取用户选中的地址
      final selectAddress = ref.read(currentAddressProvider);

      if (selectAddress.id != null) {
        ref.read(currentAddressProvider.notifier).setAddress(selectAddress);
        return data;
      }

      // 如果有默认地址，立即更新当前地址
      if (data.address != null) {
        ref.read(currentAddressProvider.notifier).setAddress(
              CurrentSelectAddress(
                id: data.address!.addressId ?? data.address!.id,
                name: data.address!.name,
                tel: data.address!.tel,
                address: data.address!.address,
                buildingName: data.address!.buildingName,
                buildingId: data.address!.buildingId,
                showDistanceWarn: data.address!.showDistanceWarn,
              ),
            );
      }

      return data;
    } catch (e, stack) {
      // 更新状态为错误
      state = AsyncError(e, stack);
      // 重新抛出错误，让Riverpod处理
      rethrow;
    }
  }
}

/// 当前选中的地址
@riverpod
class CurrentAddress extends _$CurrentAddress {
  @override
  CurrentSelectAddress build() {
    return CurrentSelectAddress();
  }

  /// 更新用户选择的地址
  void setAddress(final CurrentSelectAddress address) {
    state = address;
  }
}

/// 当前选中的配送时间
final currentTakeTimeProvider =
    StateProvider<TakeTimeCst>((final ref) => TakeTimeCst());

/// 是否显示今天的配送时间
final showTodayTimesProvider = StateProvider<bool>((final ref) {
  return true;
});

/// 配送方式 - DELIVERY_MODE: 配送, PICKUP_MODE: 自取
final deliveryModeProvider =
    StateProvider<int>((final ref) => OrderCalculationConstants.deliveryMode);

/// 餐具数量 Provider
final tablerwareCountProvider = StateProvider<int>((final ref) => 1);

/// 备注信息 Provider
final remarkProvider = StateProvider<String>((final ref) => '');

/// 当前选择的优惠券 Provider
final selectedCouponProvider = StateProvider<CouponItem?>((final ref) => null);

// 骑手编号 Providers
final hasShipperNumberProvider =
    StateProvider.autoDispose<bool>((final ref) => false);
final shipperMobileProvider = StateProvider.autoDispose<String>((final ref) {
  final shipperNumber =
      ref.read(localStorageRepositoryProvider).getShipperNumber();

  // 监听状态变化
  ref.listenSelf((final previous, final current) {
    if (previous != current && current.isNotEmpty) {
      // 当状态变化时，保存到本地存储
      ref.read(localStorageRepositoryProvider).saveHasShipperNumber(current);
    }
  });

  return shipperNumber ?? "";
});
