// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'my_order_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$myOrderServiceHash() => r'3bf67bfbbb4c062cdeb1c50987e62ebd5745fe77';

/// 订单服务，处理业务逻辑
///
/// Copied from [myOrderService].
@ProviderFor(myOrderService)
final myOrderServiceProvider = AutoDisposeProvider<MyOrderService>.internal(
  myOrderService,
  name: r'myOrderServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$myOrderServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MyOrderServiceRef = AutoDisposeProviderRef<MyOrderService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
