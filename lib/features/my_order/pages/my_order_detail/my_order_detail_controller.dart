import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/features/mine/pages/mine/mine_controller_provider.dart';
import 'package:user_app/features/my_order/pages/my_order_detail/my_order_detail_state.dart';
import 'package:user_app/features/my_order/providers/order_detail_provider.dart';

part 'my_order_detail_controller.g.dart';

@riverpod
class MyOrderDetailController extends _$MyOrderDetailController {

  Timer? _expiredTimer;
  bool _isInitialized = false;
  bool _isDisposed = false;

  @override
  MyOrderDetailState build(final int orderId) {
    // 在dispose时取消定时器
    ref.onDispose(() {
      _isDisposed = true;
      if (_expiredTimer != null) {
        _expiredTimer!.cancel();
        _expiredTimer = null;
      }
    });


    // 在build方法中加载订单详情数据，但只加载一次
    Future.microtask(() async {
      if (!_isInitialized && !_isDisposed) {
        await ref
            .read(orderDetailProvider.notifier)
            .orderDetailInfo(orderId: orderId);
      }
    });

    return const MyOrderDetailState();
  }

  /// 设置是否可以滚动
  void setCanScroll(final bool canScroll) {
    if (_isDisposed) return;
    state = state.copyWith(canScroll: canScroll);
  }

  /// 检查控制器是否已被销毁
  bool get isActive => !_isDisposed;

  /// 拨打电话功能
  void makePhoneCall(final String phoneNumber) {
    if (_isDisposed) return;
    ref.read(mineControllerProvider.notifier).makePhoneCall(phoneNumber);
  }

  /// 取消订单功能
  Future<void> cancelOrder(final int orderId) async {
    if (_isDisposed) return;
    // 使用OrderDetailProvider取消订单
    await ref.read(orderDetailProvider.notifier).cancelOrder(orderId: orderId);
    // 取消订单后刷新订单详情
    await ref
        .read(orderDetailProvider.notifier)
        .orderDetailInfo(orderId: orderId);
  }

  /// 每秒更新一次倒计时
  void startCountdown(final num totalSeconds, final int orderId) {
    // 确保取消旧的定时器
    if (_expiredTimer != null) {
      _expiredTimer!.cancel();
      _expiredTimer = null;
    }

    if (_isDisposed) return;
    state = state.copyWith(restSecond: totalSeconds);

    _expiredTimer = Timer.periodic(const Duration(seconds: 1), (final timer) {
      // 如果控制器已经被释放，则取消定时器
      if (_isDisposed) {
        timer.cancel();
        return;
      }

      if (state.restSecond > 0) {
        state = state.copyWith(restSecond: state.restSecond - 1000);
      } else {
        timer.cancel();
        _expiredTimer = null;
        // 倒计时结束时刷新订单详情
        if (!_isDisposed) {
          ref
              .read(orderDetailProvider.notifier)
              .orderDetailInfo(orderId: orderId);
        }
      }
    });
  }
}
