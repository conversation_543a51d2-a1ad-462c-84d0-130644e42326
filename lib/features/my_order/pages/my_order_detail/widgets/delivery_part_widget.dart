import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/generated/l10n.dart';


class DeliveryPartWidget extends ConsumerStatefulWidget {
  const DeliveryPartWidget({super.key,required this.orderDetailData,});
  final OrderDetailData orderDetailData;

  @override
  ConsumerState createState() => _DeliveryPartWidgetState();
}

class _DeliveryPartWidgetState extends ConsumerState<DeliveryPartWidget> {
  String originalBookingTime = '';
  String newBookingTime = '';
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    originalBookingTime = widget.orderDetailData.delayed?.originalBookingTime ?? '';
    newBookingTime = widget.orderDetailData.delayed?.newBookingTime ?? '';
    if(originalBookingTime.length > 5){
      originalBookingTime = originalBookingTime.substring(5,16);
    }
    if(newBookingTime.length > 5){
      newBookingTime = newBookingTime.substring(5,16);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 15.w),
      margin: EdgeInsets.only(right: 10.w, left: 10.w, top: 10.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(10.w)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(bottom: 10.w, right: 10.w, left: 10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Text(S.current.shipment_info),
              ],
            ),
          ),
          (widget.orderDetailData.delayed != null && widget.orderDetailData.delayed!.agreeState == 2) ?
          Column(
            children: [
              Divider(height: 0.5.w, color: AppColors.searchBackColor),
              _labelRemarkItem(S.current.original_booking_time, '',originalBookingTime, false, context, ref, true,true,widget.orderDetailData.delayed!.delayedDuration ?? 0),
              Divider(height: 0.5.w, color: AppColors.searchBackColor),
              _labelRemarkItem(S.current.new_booking_time, '',newBookingTime, false, context, ref, true,true,null),
            ],
          ):Column(
            children: [
              Divider(height: 0.5.w, color: AppColors.searchBackColor),
              _labelRemarkItem(S.current.shipment_time, '','${widget.orderDetailData.bookingDateTimeCst}', false, context, ref, true,true,null),
            ],
          ),
          Divider(height: 0.5.w, color: AppColors.searchBackColor),
          _labelRemarkItem(S.current.pick_up_person, '${widget.orderDetailData.name}','${widget.orderDetailData.mobile}', false, context, ref, false,false,null),
          Divider(height: 0.5.w, color: AppColors.searchBackColor),
          _labelRemarkItem(
              S.current.pick_up_address,
              '',
              '${widget.orderDetailData.buildingName} ${widget.orderDetailData.orderAddress}',
              false,
              context,
              ref, false,false,null),
        ],
      ),
    );
  }

  Widget _labelRemarkItem(String label, String remark, String value, bool isBtn,
      BuildContext context, WidgetRef ref, bool isLtr,bool isShort, num? delayedDuration) {

    Widget thisValue = delayedDuration != null ? Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if(ref.watch(languageProvider) == 'ug')
        Text(
          ' +$delayedDuration ',
          textDirection: isLtr
              ? TextDirection.ltr
              : null,
          textAlign: TextAlign.start,
          style: TextStyle(color: Colors.orange),
        ),
        Text(
          value,
          textDirection: isLtr
              ? TextDirection.ltr
              : null,
          textAlign: TextAlign.start,
        ),
        if(ref.watch(languageProvider) == 'zh')
          Text(
            ' +$delayedDuration ',
            textDirection: isLtr
                ? TextDirection.ltr
                : null,
            textAlign: TextAlign.start,
            style: TextStyle(color: Colors.orange),
          ),
      ],
    ):Text(
      value,
      textDirection: isLtr
          ? TextDirection.ltr
          : null,
      textAlign: TextAlign.start,
    );


    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w,vertical:15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Flexible(
            flex: isShort ? 5 : 3,
            child: Text(
              label,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 8.w),
          Flexible(
            flex: 6,
            child: remark.isNotEmpty
                ? Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width:126.w,
                      child: Text(
                        remark,
                        textAlign: TextAlign.start,
                        // overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 16.sp,
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      value,
                      textDirection: isLtr
                          ? TextDirection.ltr
                          : null,
                      textAlign: TextAlign.end,
                    ),
                  ],
                ),
              ],
            )
                :  thisValue,
          ),
        ],
      ),
    );
  }
}

