import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/generated/l10n.dart';

class OrderStateBottomPanelWidget extends ConsumerWidget {
  final List<OrderStateLog>? orderStateLog;
  final String logStr;

  const OrderStateBottomPanelWidget({
    Key? key,
    required this.orderStateLog,
    required this.logStr,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SafeArea(
      child: Container(
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(20.w)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.symmetric(vertical: 15.w),
              decoration: BoxDecoration(
                color: AppColors.baseBackgroundColor,
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(20.w),
                  topLeft: Radius.circular(20.w),
                ),
              ),
              alignment: Alignment.center,
              child: Text(
                S.current.order_state,
                style: TextStyle(fontSize: soBigSize, color: Colors.black),
              ),
            ),
            Column(
              children: List.generate(
                  (orderStateLog ?? []).length,
                  (index) => _orderStateItem(orderStateLog![index], index,
                      orderStateLog!, logStr, ref)),
            ),
            SizedBox(
              height: 20.w,
            ),
          ],
        ),
      ),
    );
  }

  Widget _orderStateItem(OrderStateLog orderStateLogItem, int index,
      List<OrderStateLog> orderStateLog, String logStr, WidgetRef ref) {
    return Column(
      children: [
        Directionality(
          textDirection: ref.watch(languageProvider) == 'zh'
              ? TextDirection.ltr
              : TextDirection.rtl,
          child: Container(
            padding: EdgeInsets.all(14.w),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.lens,
                      size: 10.sp,
                      color: index == 0
                          ? AppColors.baseGreenColor
                          : AppColors.grayColor,
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    Directionality(
                      textDirection: ref.watch(languageProvider) == 'zh'
                          ? TextDirection.ltr
                          : TextDirection.rtl,
                      child: Text(
                        '${index == 0 ? logStr : orderStateLogItem.name}',
                        style: TextStyle(fontSize: mainSize),
                      ),
                    ),
                  ],
                ),
                Directionality(
                  textDirection: TextDirection.ltr,
                  child: Text(
                    '${orderStateLogItem.createdAt}',
                    style: TextStyle(fontSize: mainSize),
                  ),
                ),
              ],
            ),
          ),
        ),
        orderStateLog.length - 1 == index
            ? Container()
            : Divider(color: AppColors.searchBackColor, height: 1),
      ],
    );
  }
}

Future<String> showOrderStateBottomPanel(
    BuildContext context, List<OrderStateLog>? orderStateLog, String logStr) {
  return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      useSafeArea: true,
      builder: (BuildContext bottomPanelContext) {
        return OrderStateBottomPanelWidget(
          orderStateLog: orderStateLog,
          logStr: logStr,
        );
      }).then((value) {
    return value ?? '';
  });
}
