import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/generated/l10n.dart';

class OrderPartWidget extends ConsumerWidget {
  final OrderDetailData orderDetailData;

  const OrderPartWidget({
    Key? key,
    required this.orderDetailData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: EdgeInsets.only(top: 15.w),
      margin: EdgeInsets.only(right: 10.w, left: 10.w, top: 10.w, bottom: 10.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(10.w)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(bottom: 10.w, right: 10.w, left: 10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Text(S.current.order_info),
              ],
            ),
          ),
          Divider(height: 0.5.w, color: AppColors.searchBackColor),
          _labelRemarkItem(S.current.order_no, S.current.copy,
              '${orderDetailData.orderId}', true, context, ref, false),
          Divider(height: 0.5.w, color: AppColors.searchBackColor),
          _labelRemarkItem(S.current.mother_day_remark, '',
              '${orderDetailData.description}', false, context, ref, false),
          Divider(height: 0.5.w, color: AppColors.searchBackColor),
          _labelRemarkItem(S.current.payment_type, '',
              '${orderDetailData.payTypeName}', false, context, ref, false),
          Divider(height: 0.5.w, color: AppColors.searchBackColor),
          _labelRemarkItem(S.current.order_time, '',
              '${orderDetailData.createdAt}', false, context, ref, true),
        ],
      ),
    );
  }

  Widget _labelRemarkItem(String label, String remark, String value, bool isBtn,
      BuildContext context, WidgetRef ref, bool isLtr) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            flex: 4,
              child: Text(label)
          ),
          remark.isNotEmpty
              ? Row(
                mainAxisAlignment: MainAxisAlignment.end,
                mainAxisSize: MainAxisSize.max,
                children: [
                  InkWell(
                      onTap: () {
                        if (isBtn) {
                          Clipboard.setData(ClipboardData(text:value));
                          BotToast.showText(text: S.of(context).has_copy);
                        }
                      },
                      child: Text(
                        remark,
                        style: TextStyle(
                            color: isBtn
                                ? AppColors.baseGreenColor
                                : Colors.black,
                            fontSize: mainSize),
                      )
                  ),
                  SizedBox(
                    width: 15.w,
                  ),
                  Container(
                      alignment: ref.watch(languageProvider) == 'ug'
                          ? Alignment.centerLeft
                          : Alignment.centerRight,
                      width: MediaQuery.of(context).size.width / (ref.watch(languageProvider) == 'ug' ? 2.4 : 2.1),
                      child: Text(
                        value,
                        textDirection: isLtr
                            ? TextDirection.ltr
                            : null,
                        textAlign: TextAlign.end,
                      )),
                ],
              ):
              Expanded(
                flex: 8,
                child: Container(
                    alignment: ref.watch(languageProvider) == 'ug'
                        ? Alignment.centerLeft
                        : Alignment.centerRight,
                    // width: MediaQuery.of(context).size.width / 1.8,
                    child: Text(
                      value,
                      textDirection: isLtr
                          ? TextDirection.ltr
                          : null,
                      textAlign: TextAlign.start,
                    )),
              ),
        ],
      ),
    );
  }
}
