import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/features/my_order/pages/my_order_detail/my_order_detail_controller.dart';
import 'package:user_app/generated/l10n.dart';

class MobileListBottomPanelWidget extends ConsumerWidget {
  final OrderDetailData orderDetailData;

  const MobileListBottomPanelWidget({
    final Key? key,
    required this.orderDetailData,
  }) : super(key: key);

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final controller = ref
                        .read(myOrderDetailControllerProvider(orderDetailData.id ?? 0).notifier);
    return SafeArea(
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.baseBackgroundColor,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(children: [
              if ((orderDetailData.adminPhone ?? '') != '')
                InkWell(
                  onTap: () async {
                    
                        controller.makePhoneCall(orderDetailData.adminPhone ?? '');
                  },
                  child: Container(
                    color: Colors.white,
                    padding: EdgeInsets.all(15.w),
                    alignment: Alignment.center,
                    child: Text(
                      S.current.connect_to_shipper,
                      style: TextStyle(fontSize: mainSize),
                    ),
                  ),
                ),
              Divider(color: AppColors.searchBackColor, height: 1),
              InkWell(
                onTap: () async {
                  controller.makePhoneCall(orderDetailData.restaurantPhone ?? '');
                },
                child: Container(
                  color: Colors.white,
                  padding: EdgeInsets.all(15.w),
                  alignment: Alignment.center,
                  child: Text(
                    S.current.connect_to_restaurant,
                    style: TextStyle(fontSize: mainSize),
                  ),
                ),
              ),
              Divider(color: AppColors.searchBackColor, height: 1),
              InkWell(
                onTap: () async {
                  controller.makePhoneCall(orderDetailData.servicePhone ?? '');
                },
                child: Container(
                  color: Colors.white,
                  padding: EdgeInsets.all(15.w),
                  alignment: Alignment.center,
                  child: Text(
                    S.current.connect_to_service,
                    style: TextStyle(fontSize: mainSize),
                  ),
                ),
              ),
              SizedBox(
                height: 15.w,
              ),
              InkWell(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  color: Colors.white,
                  padding: EdgeInsets.all(20.w),
                  alignment: Alignment.center,
                  child: Text(
                    S.current.back,
                    style: TextStyle(fontSize: mainSize, color: Colors.red),
                  ),
                ),
              ),
            ]),
          ],
        ),
      ),
    );
  }
}

Future<String> showMobileListBottomPanel(
  final BuildContext context,
  final OrderDetailData orderDetailData,) {
  return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      useSafeArea: true,
      builder: (final BuildContext bottomPanelContext) {
        return MobileListBottomPanelWidget(
          orderDetailData: orderDetailData,
        );
      }).then((final value) {
    return value ?? '';
  });
}
