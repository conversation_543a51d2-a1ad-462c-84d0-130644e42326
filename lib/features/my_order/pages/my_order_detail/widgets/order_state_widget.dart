import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/generated/l10n.dart';

class OrderStateWidget extends ConsumerWidget {
  final OrderDetailData orderDetailData;
  final Function onTap;

  const OrderStateWidget({
    Key? key,
    required this.orderDetailData,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String logStr = '';
    if (orderDetailData.deliveryType == 1) {
      if ((orderDetailData.state ?? 0) < 3 && orderDetailData.expired == 1) {
        logStr = S.current.order_time_out;
      } else {
        if((orderDetailData.orderStateLog ?? []).isNotEmpty){
          logStr = orderDetailData.orderStateLog?[0].name ?? '';
        }
      }
    } else if (orderDetailData.deliveryType == 2) {
      if ((orderDetailData.state ?? 0) < 3 && orderDetailData.expired == 1) {
        logStr = S.current.order_time_out;
      } else {
        if ((orderDetailData.state ?? 0) == 7) {
          logStr = S.current.order_done;
        } else {
          if((orderDetailData.orderStateLog ?? []).isNotEmpty){
            logStr = orderDetailData.orderStateLog?[0].name ?? '';
          }
        }
      }
    }

    return InkWell(
      onTap: () => onTap(),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15.w),
        margin: EdgeInsets.only(right: 10.w, left: 10.w, top: 10.w),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(10.w)),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              logStr,
              style: TextStyle(
                  color: AppColors.baseGreenColor, fontSize: titleSize),
            ),
            SizedBox(
              width: 5.w,
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 20.sp,
              color: AppColors.baseGreenColor,
            )
          ],
        ),
      ),
    );
  }
}
