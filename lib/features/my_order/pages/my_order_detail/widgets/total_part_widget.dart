import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/generated/l10n.dart';

// 总价组件
class TotalPartWidget extends ConsumerStatefulWidget {
  /// 总价组件
  TotalPartWidget({
    super.key,
    required this.orderDetailData,
    required this.preferentialPrice,
  });

  /// 订单详情数据
  final OrderDetailData orderDetailData;

  /// 优惠价格
  final String preferentialPrice;

  @override
  ConsumerState createState() => _TotalPartWidgetState();
}

class _TotalPartWidgetState extends ConsumerState<TotalPartWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(final BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.w),
      margin: EdgeInsets.only(right: 10.w, left: 10.w, top: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.w),
      ),
      child: Column(
        children: [
          _labelItem(
            S.current.discount_total_fee,
            widget.preferentialPrice,
            null,
            Colors.red,
            true,
          ),
          Divider(height: 0.5.w, color: AppColors.searchBackColor),
          _labelItem(
            S.current.order_total_fee,
            '${widget.orderDetailData.actualPaid}',
            FormatUtil.formatPrice(
              (widget.orderDetailData.actualPaid ?? 0) +
                  num.parse(widget.preferentialPrice),
            ),
            AppColors.baseGreenColor,
            false,
          ),
        ],
      ),
    );
  }

  Widget _labelItem(
    final String label,
    final String val,
    final String? originalPrice,
    final Color color,
    final bool isAll,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(color: isAll ? color : Colors.black),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (originalPrice != null &&
                  (num.parse(originalPrice) > (num.parse(val))))
                Container(
                  padding: EdgeInsets.only(top: 3.w),
                  child: Text(
                    '￥$originalPrice',
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: AppColors.dialogCancelColor,
                      decoration: TextDecoration.lineThrough,
                      decorationColor: AppColors.dialogCancelColor,
                    ),
                  ),
                ),
              SizedBox(
                width: 8.w,
              ),
              Directionality(
                textDirection: TextDirection.rtl,
                child: Row(
                  children: [
                    Text(
                      '￥$val',
                      style: TextStyle(
                        fontSize: titleSize,
                        fontWeight: !isAll ? FontWeight.bold : null,
                        color: color,
                      ),
                    ),
                    if (isAll)
                      Text(
                        '-',
                        style: TextStyle(fontSize: titleSize, color: color),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
