import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';

class OrderStateNewWidget extends ConsumerWidget {
  final OrderDetailData data;

  const OrderStateNewWidget({
    Key? key,
    required this.data,
  }) : super(key: key);

  Map<String, dynamic> getApiSteps(OrderStateLogNew steps) {
    List<Items> apiSteps = [];
    if (steps.items != null && steps.items!.isNotEmpty) {
      apiSteps = steps.items!;
    }

    List<UpdatedStep> updatedSteps = apiSteps.asMap().entries.map((entry) {
      int index = entry.key;
      Items step = entry.value;
      return UpdatedStep(
        title: step.title,
        time: step.time,
        status: "icon",
        icons: {
          "normal":
              "https://acdn.mulazim.com/wechat_mini/img/partial-refund/refund-${index + 1}-1.png",
          "active":
              "https://acdn.mulazim.com/wechat_mini/img/partial-refund/refund-${index + 1}-2.png",
          "completed":
              "https://acdn.mulazim.com/wechat_mini/img/partial-refund/refund-${index + 1}-3.png",
        },
      );
    }).toList();

    return {'stepsList': updatedSteps, 'currentStep': steps.step ?? 0};
  }

  Widget _stateItem(int index, UpdatedStep updatedStep, int currentStep, String lang) {
    String stepIcon = '';
    if (currentStep > index) {
      stepIcon = updatedStep.icons?['completed'] ?? '';
    } else if (currentStep == index) {
      stepIcon = updatedStep.icons?['active'] ?? '';
    } else {
      stepIcon = updatedStep.icons?['normal'] ?? '';
    }


    return Stack(
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 46.w,
                  height: 46.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppColors.baseGreenColor,
                      width: 2.w,
                    ),
                  ),
                  child: ClipOval(
                    child: Container(
                      color: index > (currentStep - 1)
                          ? Colors.white
                          : AppColors.baseGreenColor,
                      alignment: Alignment.center,
                      width: 44.w,
                      height: 44.w,
                      child: PrefectImage(
                        imageUrl: stepIcon,
                        width: 27.w,
                        height: 27.w,
                        fit: BoxFit.fitWidth,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 10.w,
            ),
            Text(
              '${updatedStep.title}',
              style: TextStyle(
                  fontSize: secondSize,
                  color: index == currentStep ? AppColors.baseGreenColor :  (index > currentStep ? Colors.grey : Colors.black),
                  fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            Container(
              height: 25.w,
              child: Column(
                children: [
                  SizedBox(
                    height: 4.w,
                  ),
                  Directionality(
                    textDirection: TextDirection.ltr,
                    child: Text(
                      '${updatedStep.time}',
                      style: TextStyle(
                          fontSize: secondSize, color: AppColors.textSecondaryColor),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            )

          ],
        ),
        if (index != 3 )
          Positioned(
            left: lang == 'ug' ? 0 : null,
            right: lang == 'zh' ? 0 : null,
            top: 22.w,
            child: Container(
              width: 25.w,
              height: 3.w,
              color: index > (currentStep)
                  ? AppColors.homeSearchIconColor
                  : AppColors.baseGreenColor,
            ),
          ),
        if (index != 0 && index != 3)
          Positioned(
            right: lang == 'ug' ? 0 : null,
            left: lang == 'zh' ? 0 : null,
            top: 22.w,
            child: Container(
              width: 25.w,
              height: 3.w,
              color: (index > (currentStep - 1) && index == (currentStep - 1))
                  ? AppColors.homeSearchIconColor
                  : AppColors.baseGreenColor,
            ),
          ),
        if (index == 3)
          Positioned(
            right: lang == 'ug' ? 0 : null,
            left: lang == 'zh' ? 0 : null,
            top: 22.w,
            child: Container(
              width: 25.w,
              height: 3.w,
              color: index > (currentStep - 1)
                  ? AppColors.homeSearchIconColor
                  : AppColors.baseGreenColor,
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Map<String, dynamic> stepsData = getApiSteps(data.orderStateLogNew!);
    List<UpdatedStep> updatedSteps = stepsData['stepsList'];
    int currentStep = stepsData['currentStep'];
    String lang = ref.read(languageProvider);
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.w),
      margin: EdgeInsets.only(right: 10.w, left: 10.w, top: 10.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(10.w)),
      child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
              updatedSteps.length,
              (index) => Expanded(
                    child: Container(
                        child: _stateItem(index, updatedSteps[index], currentStep,lang)),
                  ))),
    );
  }
}
