import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/data/models/my_order/part_refund_detail_model.dart';
import 'package:user_app/features/mine/pages/mine/mine_controller_provider.dart';
import 'package:user_app/features/my_order/providers/part_refund_provider.dart';
import 'package:user_app/generated/l10n.dart';

class CancelOrderDetail extends ConsumerStatefulWidget {
  CancelOrderDetail({super.key, required this.partRefundId});
  int partRefundId;

  @override
  ConsumerState createState() => _CancelOrderDetailState();
}

class _CancelOrderDetailState extends ConsumerState<CancelOrderDetail> {

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await ref.read(partRefundProvider.notifier).partRefundInfo(id: widget.partRefundId);
      LoadingDialog().hide();
    });
  }
  
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.baseBackgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.baseGreenColor,
        foregroundColor: Colors.white,
        title: Text(
          S.current.refund_detail,
          style: TextStyle(color: Colors.white, fontSize: titleSize),
        ),
      ),
      body: ref.watch(partRefundProvider).when(
        data: (data) {
          return cardContent(data);
        },
        error: (error, stackTrace) {
          // 数据加载失败，显示错误信息
          return Center(
              child: Column(
                children: [
                  Text('Page Error: $error'),
                  Text('Page stackTrace: $stackTrace'),
                ],
              ));
        },
        loading: () {
          // 正在加载，显示加载指示器
          return Center(
            child: CircularProgressIndicator(),
          );
        },
      ),
    );
  }
  
  
  
  Widget cardContent(PartRefundDetailData? data){
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(height: 10.w,),
                  // Container(
                  //   padding: EdgeInsets.symmetric(horizontal: 12 .w,vertical: 10.w),
                  //   margin: EdgeInsets.symmetric(horizontal: 20.w),
                  //   decoration: BoxDecoration(
                  //     color: AppColors.panelBackGroundOrange,
                  //     borderRadius: BorderRadius.only(
                  //       topLeft: Radius.circular(15.h),
                  //       topRight: Radius.circular(15.h),
                  //     ),
                  //   ),
                  //   child: Text('مەزكۇر زاكازدا ئاشخانا قىسمەن قايتۇرغان تاماق بار،تۆلىگەن زاكاز سوممىسىدىن يەتكۈزۈش ھەققىنى چىقىرۋېتىپ ، ئېتىبار سوممىسىنى تاماقلارغا تەقسىملىگەندىن كېيىن قايتۇرغان پۇل ھېسابلاندى. ئېتىبار سوممىسى قايتۇرۇلمايدۇ.',style: TextStyle(color: AppColors.panelTextOrange,fontSize: mainSize),textAlign: TextAlign.justify,) ,
                  // ),
                  Container(
                    padding: EdgeInsets.only(top: 15.w,right: 12.w,left: 12.w,bottom: 12.w),
                    margin: EdgeInsets.only(right: 10.w, left: 10.w,bottom: 10.w),
                    decoration: BoxDecoration(
                        color: Colors.white, borderRadius: BorderRadius.circular(10.w)),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: EdgeInsets.only(bottom: 12.w),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              SizedBox(width: 3.w,),
                              Icon(Icons.check_circle_outline,color: Colors.green,size: 22.sp,),
                              SizedBox(width: 8.w,),
                              Text(S.current.was_refund,style: TextStyle(color: Colors.black,fontSize: soBigSize,fontWeight: FontWeight.bold),),
                            ],
                          ),
                        ),
                        Divider(height: 0.5.w, color: AppColors.dividerColor,),
                        Container(
                          padding: EdgeInsets.symmetric(vertical: 10.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(S.current.refund_time,style: TextStyle(color: AppColors.textSecondColor),),
                              Directionality(
                                  textDirection: TextDirection.ltr,
                                  child:Text(data?.refundTime ?? ''),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(vertical: 10.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(S.current.refund_account,style: TextStyle(color: AppColors.textSecondColor),),
                              Text(data?.refundAccount ?? ''),
                            ],
                          ),
                        ),
                        Divider(height: 0.5.w, color: AppColors.dividerColor,),

                        Container(
                          padding: EdgeInsets.only(top: 15.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(data?.restaurantName ?? '',style: TextStyle(color: Colors.black,fontSize: soBigSize,fontWeight: FontWeight.bold),),
                            ],
                          ),
                        ),
                        Column(
                          children: List.generate((data?.foods ?? []).length, (index)=>_foodItem(data!.foods![index],index)),
                        ),
                        Divider(height: 0.5.w, color: AppColors.dividerColor,),

                        Container(
                          padding: EdgeInsets.symmetric(vertical: 10.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(S.current.lunch_box,style: TextStyle(color: AppColors.textSecondColor),),
                              Directionality(
                                  textDirection: TextDirection.ltr,
                                  child: Text('￥${data?.lunchBoxFee ?? 0}')
                              ),
                            ],
                          ),
                        ),

                        Divider(height: 0.5.w, color: AppColors.dividerColor,),
                        Container(
                          padding: EdgeInsets.symmetric(vertical: 10.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(S.current.action_man,style: TextStyle(color: AppColors.textSecondColor),),
                              Text((data?.partRefundCreatorType ?? 0) == 3 ? S.current.merchant : S.current.manager),
                            ],
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(vertical: 10.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(S.current.refund_reason,style: TextStyle(color: AppColors.textSecondColor),),
                              Container(
                                alignment: ref.watch(languageProvider) == 'ug' ? Alignment.centerLeft : Alignment.centerRight,
                                  width: MediaQuery.of(context).size.width * 0.58,
                                  child: Text(data?.refundReason ?? '',style: TextStyle(color: AppColors.redColor),textAlign: TextAlign.justify,)
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(vertical: 10.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(S.current.order_number,style: TextStyle(color: AppColors.textSecondColor),),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  InkWell(
                                    onTap:(){
                                      Clipboard.setData(ClipboardData(text:data?.orderNumber ?? ''));
                                      BotToast.showText(text: S.of(context).has_copy);
                                    },
                                    child: Padding(padding: EdgeInsets.symmetric(horizontal: 10.w),
                                      child: Icon(Icons.copy,color: AppColors.baseGreenColor,size: 20.sp,),
                                    ),
                                  ),
                                  Directionality(
                                    textDirection: TextDirection.ltr,
                                    child: SizedBox(
                                      width: MediaQuery.of(context).size.width * 0.45,
                                      child: Text(data?.orderNumber ?? '',style: TextStyle(color: Colors.black,),maxLines: 1,
                                        overflow: TextOverflow.ellipsis,),
                                    ),
                                  ),
                                ],
                              )

                            ],
                          ),
                        ),
                        Divider(height: 0.5.w, color: AppColors.dividerColor,),
                        Container(
                          padding: EdgeInsets.symmetric(vertical: 10.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(S.current.refund_fee,style: TextStyle(color: AppColors.textSecondColor),),
                              Directionality(
                                textDirection: TextDirection.ltr,
                                child: Text('￥${data?.partRefundAmount ?? 0}',style: TextStyle(color: AppColors.baseGreenColor,fontSize: soBigSize,fontWeight: FontWeight.bold),)
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              )
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(15.h),
              topRight: Radius.circular(15.h),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                spreadRadius: 5,
              ),
            ],
          ),
          child: Column(
            children: [
              SizedBox(height: 12.w,),
              InkWell(
                onTap: (){
                  ref.read(mineControllerProvider.notifier).makePhoneCall('2854886');
                },
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 8.w,horizontal: 15.w),
                  decoration: BoxDecoration(
                    color: Colors.white, borderRadius: BorderRadius.circular(30.w),
                    border: Border.all(
                      color: AppColors.baseGreenColor,
                      width: 0.5.w,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.phone,color: AppColors.baseGreenColor,),
                      Text(S.current.connect_merchant,style: TextStyle(color: AppColors.baseGreenColor),)
                    ],
                  ),
                ),
              ),
              SizedBox(height: 12.w,),

              Container(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  alignment: Alignment.center,
                  child: Text(S.current.connect_merchant_remark,style: TextStyle(color: AppColors.textSecondColor,fontSize: mainSize,),textAlign: TextAlign.center,)
              ),
              SizedBox(height: 30.w,),

            ],
          ),
        )
      ],
    );
  }
  

  Widget _foodItem(PartRefundDetailFoods food,int index) {
    return Column(
      children: [
        if(index != 0) Divider(height: 0.5.w, color: AppColors.dividerColor,),
        Container(
          padding: EdgeInsets.symmetric(vertical:  10.w),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(10.w),
                child: PrefectImage(
                  imageUrl: food.image ?? '',
                  fit: BoxFit.cover,
                  width: 80.w,
                  height: 70.w,
                ),
              ),
              SizedBox(
                width: 10.w,
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('${food.foodName}',style: TextStyle(fontSize: mainSize, color: Colors.black), ),
                        SizedBox(
                          height: 2.w,
                        ),
                        Directionality(
                            textDirection: TextDirection.ltr,
                            child: Text(' x${food.count} ',style: TextStyle(fontSize: mainSize, color: AppColors.textSecondColor), )
                        ),
                      ],
                    ),
                    SizedBox(height: 10.w,),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(S.current.refund_amount,style: TextStyle(fontSize: mainSize, color: AppColors.textSecondColor), ),
                        SizedBox(
                          height: 2.w,
                        ),
                        Directionality(
                          textDirection: TextDirection.ltr,
                          child: Text(
                            '￥${food.refundPrice}',
                            style: TextStyle(fontSize: mainSize, color: AppColors.textSecondColor),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        )
      ],
    );
  }


}
