// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'my_order_detail_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$myOrderDetailControllerHash() =>
    r'8777bb6425aad7db355faa9b762feed2f83fbdb3';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$MyOrderDetailController
    extends BuildlessAutoDisposeNotifier<MyOrderDetailState> {
  late final int orderId;

  MyOrderDetailState build(
    int orderId,
  );
}

/// See also [MyOrderDetailController].
@ProviderFor(MyOrderDetailController)
const myOrderDetailControllerProvider = MyOrderDetailControllerFamily();

/// See also [MyOrderDetailController].
class MyOrderDetailControllerFamily extends Family<MyOrderDetailState> {
  /// See also [MyOrderDetailController].
  const MyOrderDetailControllerFamily();

  /// See also [MyOrderDetailController].
  MyOrderDetailControllerProvider call(
    int orderId,
  ) {
    return MyOrderDetailControllerProvider(
      orderId,
    );
  }

  @override
  MyOrderDetailControllerProvider getProviderOverride(
    covariant MyOrderDetailControllerProvider provider,
  ) {
    return call(
      provider.orderId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'myOrderDetailControllerProvider';
}

/// See also [MyOrderDetailController].
class MyOrderDetailControllerProvider extends AutoDisposeNotifierProviderImpl<
    MyOrderDetailController, MyOrderDetailState> {
  /// See also [MyOrderDetailController].
  MyOrderDetailControllerProvider(
    int orderId,
  ) : this._internal(
          () => MyOrderDetailController()..orderId = orderId,
          from: myOrderDetailControllerProvider,
          name: r'myOrderDetailControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$myOrderDetailControllerHash,
          dependencies: MyOrderDetailControllerFamily._dependencies,
          allTransitiveDependencies:
              MyOrderDetailControllerFamily._allTransitiveDependencies,
          orderId: orderId,
        );

  MyOrderDetailControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.orderId,
  }) : super.internal();

  final int orderId;

  @override
  MyOrderDetailState runNotifierBuild(
    covariant MyOrderDetailController notifier,
  ) {
    return notifier.build(
      orderId,
    );
  }

  @override
  Override overrideWith(MyOrderDetailController Function() create) {
    return ProviderOverride(
      origin: this,
      override: MyOrderDetailControllerProvider._internal(
        () => create()..orderId = orderId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        orderId: orderId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<MyOrderDetailController,
      MyOrderDetailState> createElement() {
    return _MyOrderDetailControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MyOrderDetailControllerProvider && other.orderId == orderId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, orderId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin MyOrderDetailControllerRef
    on AutoDisposeNotifierProviderRef<MyOrderDetailState> {
  /// The parameter `orderId` of this provider.
  int get orderId;
}

class _MyOrderDetailControllerProviderElement
    extends AutoDisposeNotifierProviderElement<MyOrderDetailController,
        MyOrderDetailState> with MyOrderDetailControllerRef {
  _MyOrderDetailControllerProviderElement(super.provider);

  @override
  int get orderId => (origin as MyOrderDetailControllerProvider).orderId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
