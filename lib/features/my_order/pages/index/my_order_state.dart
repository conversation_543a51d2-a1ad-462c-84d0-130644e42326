import 'package:user_app/data/models/my_order/my_order_model.dart';

/// 我的订单页面状态
class MyOrderState {
  /// 构造函数
  const MyOrderState({
    this.isLoading = false,
    this.error,
    this.page = 1,
    this.type = 1,
    this.cachedTodayOrders = const [], // 缓存今日订单数据
    this.cachedAllOrders = const [], // 缓存所有订单数据
    this.canLoadMoreToday = true, // 今日订单是否可加载更多
    this.canLoadMoreAll = true, // 所有订单是否可加载更多
    this.hasLoadedToday = false, // 今日订单是否已加载过
    this.hasLoadedAll = false, // 所有订单是否已加载过
  });

  /// 是否加载中
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 当前页码
  final int page;

  /// 订单类型 1:今日订单 2:所有订单
  final int type;

  /// 缓存的今日订单数据
  final List<MyOrderItems> cachedTodayOrders;

  /// 缓存的所有订单数据
  final List<MyOrderItems> cachedAllOrders;

  /// 今日订单是否可加载更多
  final bool canLoadMoreToday;

  /// 所有订单是否可加载更多
  final bool canLoadMoreAll;

  /// 今日订单是否已加载过
  final bool hasLoadedToday;

  /// 所有订单是否已加载过
  final bool hasLoadedAll;

  /// 拷贝方法
  MyOrderState copyWith({
    final bool? isLoading,
    final String? error,
    final int? page,
    final int? type,
    final List<MyOrderItems>? cachedTodayOrders,
    final List<MyOrderItems>? cachedAllOrders,
    final bool? canLoadMoreToday,
    final bool? canLoadMoreAll,
    final bool? hasLoadedToday,
    final bool? hasLoadedAll,
  }) {
    return MyOrderState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      page: page ?? this.page,
      type: type ?? this.type,
      cachedTodayOrders: cachedTodayOrders ?? this.cachedTodayOrders,
      cachedAllOrders: cachedAllOrders ?? this.cachedAllOrders,
      canLoadMoreToday: canLoadMoreToday ?? this.canLoadMoreToday,
      canLoadMoreAll: canLoadMoreAll ?? this.canLoadMoreAll,
      hasLoadedToday: hasLoadedToday ?? this.hasLoadedToday,
      hasLoadedAll: hasLoadedAll ?? this.hasLoadedAll,
    );
  }

  /// 获取当前显示的订单列表
  List<MyOrderItems> get currentOrders {
    return type == 1 ? cachedTodayOrders : cachedAllOrders;
  }

  /// 获取当前类型是否可加载更多
  bool get canLoadMore {
    return type == 1 ? canLoadMoreToday : canLoadMoreAll;
  }

  /// 获取当前类型是否已加载过
  bool get hasLoaded {
    return type == 1 ? hasLoadedToday : hasLoadedAll;
  }
}
