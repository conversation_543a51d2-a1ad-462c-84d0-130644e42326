import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/my_order/my_order_model.dart';
import 'package:user_app/features/my_order/pages/index/my_order_state.dart';
import 'package:user_app/features/my_order/services/my_order_service.dart';
import 'package:user_app/main.dart';

part 'my_order_controller.g.dart';

@riverpod
class MyOrderController extends _$MyOrderController {
  @override
  MyOrderState build() {
    Future.microtask(() {
      initData();
    });

    // 监听登录状态变化，重新加载数据
    ref.listen(isLoggedInProvider, (final previous, final next) {
      if (next) {
        Future.microtask(() {
          initData();
        });
      }
    });

    ref.listen(languageProvider, (final previous, final next) {
      if (next != previous) {
        Future.microtask(() {
          initData();
        });
      }
    });

    return const MyOrderState();
  }

  /// 初始化数据
  Future<void> initData() async {
    // 仅加载初始类型的订单数据，另一种类型的数据会在切换时按需加载
    getMyOrder(type: 1, page: 1, forceLoad: true);
  }

  /// 获取订单数据
  /// [type] - 订单类型
  /// [page] - 页码
  /// [forceLoad] - 是否强制加载，不考虑缓存状态
  Future<void> getMyOrder({
    final int? type,
    final int? page,
    final bool forceLoad = false,
  }) async {
    // 如果没有传递类型和页码，使用当前状态的值
    final currentType = type ?? state.type;
    final currentPage = page ?? state.page;

    // 检查登录状态
    if (!ref.read(isLoggedInProvider)) {
      return;
    }

    // 检查是否已经加载过，且是第一页请求，且不是强制加载
    final hasLoaded =
        currentType == 1 ? state.hasLoadedToday : state.hasLoadedAll;

    if (hasLoaded && currentPage == 1 && !forceLoad) {
      // 如果已加载过且不需要强制刷新，则只更新当前类型
      state = state.copyWith(type: currentType);
      return;
    }

    // 设置加载状态
    state = state.copyWith(
      isLoading: true,
      type: currentType,
      page: currentPage,
    );

    try {
      // 使用Service获取订单数据
      final orderService = ref.read(myOrderServiceProvider);
      final response = await orderService.getMyOrder(
        type: currentType,
        page: currentPage,
      );

      if (!response.success) {
        state = state.copyWith(
          isLoading: false,
          error: response.msg,
        );
        return;
      }

      final orderData = response.data;
      if (orderData != null) {
        // 判断是否可以加载更多
        final canLoad = (orderData.items?.length ?? 0) >= 10;

        // 获取订单列表
        final newOrders = orderData.items ?? [];

        if (currentType == 1) {
          // 今日订单
          _handleTodayOrders(newOrders, currentPage, canLoad);
        } else {
          // 所有订单
          _handleAllOrders(newOrders, currentPage, canLoad);
        }
      }
    } catch (e) {
      // 更新错误状态
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 处理今日订单数据
  void _handleTodayOrders(final List<MyOrderItems> newOrders,
      final int currentPage, final bool canLoad) {
    List<MyOrderItems> updatedOrders;

    if (currentPage > 1) {
      // 添加到现有列表
      updatedOrders = [...state.cachedTodayOrders, ...newOrders];
    } else {
      // 替换现有列表
      updatedOrders = newOrders;
    }

    state = state.copyWith(
      isLoading: false,
      cachedTodayOrders: updatedOrders,
      canLoadMoreToday: canLoad,
      hasLoadedToday: true,
    );
  }

  /// 处理所有订单数据
  void _handleAllOrders(final List<MyOrderItems> newOrders,
      final int currentPage, final bool canLoad) {
    List<MyOrderItems> updatedOrders;

    if (currentPage > 1) {
      // 添加到现有列表
      updatedOrders = [...state.cachedAllOrders, ...newOrders];
    } else {
      // 替换现有列表
      updatedOrders = newOrders;
    }

    state = state.copyWith(
      isLoading: false,
      cachedAllOrders: updatedOrders,
      canLoadMoreAll: canLoad,
      hasLoadedAll: true,
    );
  }

  /// 切换订单类型
  void changeType(final int type) {
    // 如果类型相同，不重复加载
    if (state.type == type) return;

    // 只更新类型，重置页码为1
    state = state.copyWith(
      type: type,
      page: 1,
    );

    // 检查是否需要加载数据（如果该类型数据尚未加载过）
    if (type == 1 && !state.hasLoadedToday) {
      // 加载今日订单
      getMyOrder(type: 1, page: 1);
    } else if (type == 2 && !state.hasLoadedAll) {
      // 加载所有订单
      getMyOrder(type: 2, page: 1);
    }
  }

  /// 加载更多订单
  Future<void> loadMore() async {
    // 检查是否可以加载更多
    if (!state.canLoadMore || state.isLoading) return;

    // 页码加1获取下一页数据
    await getMyOrder(page: state.page + 1);
  }

  /// 刷新今日订单
  Future<void> refreshTodayOrders() async {
    state = state.copyWith(
      // cachedTodayOrders: [],
      hasLoadedToday: false,
      page: 1,
      error: null,
    );

    await getMyOrder(type: 1, page: 1, forceLoad: true);
  }

  /// 刷新所有订单
  Future<void> refreshAllOrders() async {
    state = state.copyWith(
      // cachedAllOrders: [],
      hasLoadedAll: false,
      page: 1,
      error: null,
    );

    await getMyOrder(type: 2, page: 1, forceLoad: true);
  }
}
