import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/my_order/my_order_model.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';
import 'package:user_app/features/mine/pages/mine/mine_controller_provider.dart';
import 'package:user_app/features/my_order/pages/index/my_order_controller.dart';
import 'package:user_app/features/my_order/pages/my_order_detail/cancel_order_detail.dart';
import 'package:user_app/features/my_order/pages/my_order_detail/my_order_detail_page.dart';
import 'package:user_app/features/my_order/providers/order_detail_provider.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';
import 'package:user_app/core/widgets/dialogs/confirm_dialog.dart';

/// 订单项组件
class OrderItem extends ConsumerWidget {
  final MyOrderItems item;

  const OrderItem({
    Key? key,
    required this.item,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 使用select仅监听语言变化
    final lang = ref.watch(languageProvider.select((value) => value));

    return InkWell(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => MyOrderDetailPage(
              orderId: item.id ?? 0,
            ),
          ),
        );
      },
      onLongPress: () {
        ConfirmDialog.show(
          context,
          title: S.current.dialog_title_info,
          content: S.current.do_you_del_this_order,
          confirmText: S.current.dialog_text_yes,
          cancelText: S.current.dialog_text_no,
          onConfirm: () {
            ref
                .read(orderDetailProvider.notifier)
                .delOrder(orderId: item.id ?? 0);
            Future.delayed(Duration(seconds: 1), () async {
              await ref
                  .read(myOrderControllerProvider.notifier)
                  .refreshTodayOrders();
              await ref
                  .read(myOrderControllerProvider.notifier)
                  .refreshAllOrders();
            });
          },
        );
      },
      child: Container(
        padding: EdgeInsets.all(10.w),
        margin: EdgeInsets.only(right: 10.w, left: 10, bottom: 10.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.w),
          color: Colors.white,
        ),
        child: Column(
          children: [
            _buildRestaurantInfo(),
            SizedBox(height: 10.w),
            _buildOrderSummary(context),
            SizedBox(height: 10.w),
            _buildActionButtons(context, ref, lang),
          ],
        ),
      ),
    );
  }

  /// 餐厅信息部分
  Widget _buildRestaurantInfo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10.w),
              child: PrefectImage(
                imageUrl: item.restaurantLogo ?? '',
                width: 45.w,
                height: 45.w,
                fit: BoxFit.cover,
              ),
            ),
            SizedBox(width: 6.w),
            SizedBox(
              width: 140.w, // 使用固定宽度而不是相对值
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      '${item.restaurantName}',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: titleSize,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.sp,
                    color: AppColors.homeSearchPlaceHolderColor,
                  ),
                ],
              ),
            ),
          ],
        ),
        if (item.deliveryType == 1)
          Text(
            (item.expired == 1 && (item.stateId ?? 0) < 3)
                ? S.current.order_time_out
                : '${item.state}',
            style: TextStyle(
              color: item.stateId == 1 && item.expired == 0
                  ? AppColors.restaurantBgStartColor
                  : Colors.black,
              fontSize: mainSize,
            ),
          ),
        // 自取订单
        if (item.deliveryType == 2)
          Text(
            (item.stateId! < 3 && item.expired == 1)
                ? S.current.order_expired
                : (item.stateId == 7)
                    ? S.current.ask_for_state
                    : '${item.state}',
            style: TextStyle(
              color: item.stateId == 1 && item.expired == 0
                  ? AppColors.restaurantBgStartColor
                  : Colors.black,
              fontSize: mainSize,
            ),
          ),
      ],
    );
  }

  /// 订单摘要信息部分
  Widget _buildOrderSummary(BuildContext context) {
    String createTime = item.createdAt ?? '';
    if (createTime.length > 5) {
      createTime = createTime.substring(5);
    }

    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.dividerColor,
            width: 1,
          ),
        ),
      ),
      padding: EdgeInsets.only(top: 5.w, bottom: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Directionality(
            textDirection: TextDirection.ltr,
            child: Text(
              createTime,
              style: TextStyle(
                fontSize: mainSize,
                fontWeight: FontWeight.bold,
                color: AppColors.homeSearchPlaceHolderColor,
              ),
            ),
          ),
          Text(
            '${S.current.count}:${item.sum}',
            style: TextStyle(
              fontSize: mainSize,
              fontWeight: FontWeight.bold,
              color: AppColors.homeSearchPlaceHolderColor,
            ),
          ),
          Row(
            children: [
              Text(
                '${item.actualPaid}',
                style: TextStyle(
                  fontSize: soBigSize,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              Text(
                '￥',
                style: TextStyle(
                  fontSize: mainSize,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _borderContainer(String label, {required Function onClick}) {
    return InkWell(
      onTap: () {
        onClick.call();
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 4.5.w, horizontal: 8.w),
        decoration: BoxDecoration(
          color: AppColors.btnBackGroundOrange,
          border: Border.all(
            color: Colors.orange,
            width: 1.w,
          ),
          borderRadius: BorderRadius.circular(6.w),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.orange,
              size: 24.sp,
            ),
            SizedBox(
              width: 5.w,
            ),
            Text(
              label,
              style: TextStyle(
                color: Colors.orange,
                fontSize: mainSize,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 操作按钮部分
  Widget _buildActionButtons(BuildContext context, WidgetRef ref, String lang) {
    return Container(
      alignment: lang == 'ug' ? Alignment.centerRight : Alignment.centerLeft,
      child: Wrap(
        spacing: 10.w,
        runSpacing: 10.w,
        children: [
          // 再次购买按钮
          if ((item.stateId ?? 0) != 1 ||
              ((item.expired ?? 0) == 1 && (item.stateId ?? 0) == 1))
            _buildActionButton(
              label: S.current.buy_again1,
              onTap: () {
                context.push(
                  AppPaths.restaurantDetailPage,
                  extra: {
                    'restaurantId': item.restaurantId ?? 0,
                    'buildingId': ref.watch(
                      homeNoticeProvider
                          .select((state) => state.value?.location?.id ?? 0),
                    ),
                    'ids': [],
                  },
                );
              },
            ),

          // 支付按钮
          if ((item.stateId ?? 0) == 1 && (item.expired ?? 0) == 0)
            _buildActionButton(
              label: S.current.pay_now,
              onTap: () {
                final Map<String, dynamic> orderDataMap = {
                  'orderId': item.id,
                  'state': item.stateId,
                  'totalPrice': item.actualPaid ?? 0, // 实际支付金额
                  'originalPrice': item.actualPaid ?? 0, // 实际支付金额
                };
                ref.context.push(AppPaths.paymentPage, extra: orderDataMap);
              },
            ),

          // 代付按钮
          if ((item.stateId ?? 0) == 1 && (item.expired ?? 0) == 0)
            _buildActionButton(
              borderColor: AppColors.primary,
              label: S.current.agent_pay,
              onTap: () {
                // 代付功能实现
                context.push(
                  AppPaths.agentPay,
                  extra: {
                    'orderId': item.id,
                  },
                );
              },
            ),

          // 订单关闭按钮（对应微信小程序的 can_close == 1，使用 /v1/order/close API）
          if ((item.canClose ?? 0) == 1)
            _buildActionButton(
              borderColor: AppColors.textHintColor,
              label: S.current.cancel_order,
              onTap: () {
                ConfirmDialog.show(
                  context,
                  title: S.current.dialog_title_info,
                  content: S.current.do_you_close_this_order,
                  confirmText: S.current.dialog_text_yes,
                  cancelText: S.current.dialog_text_no,
                  onConfirm: () {
                    // 调用关闭订单接口 (close)
                    ref
                        .read(orderDetailProvider.notifier)
                        .closeOrder(orderId: item.id ?? 0);
                  },
                );
              },
            ),

          // 取消按钮（state_id == 3时显示，使用 /v1/order/unsubscribe API）
          if ((item.stateId ?? 0) == 3)
            _buildActionButton(
              borderColor: AppColors.textHintColor,
              label: S.current.cancel,
              onTap: () {
                ConfirmDialog.show(
                  context,
                  title: S.current.dialog_title_info,
                  content: S.current.do_you_cancel_this_order,
                  confirmText: S.current.dialog_text_yes,
                  cancelText: S.current.dialog_text_no,
                  onConfirm: () {
                    // 调用取消订单接口 (unsubscribe)
                    ref
                        .read(orderDetailProvider.notifier)
                        .cancelOrder(orderId: item.id ?? 0);
                    if (Navigator.of(context).canPop()) {
                      Navigator.pop(context);
                    }
                  },
                );
              },
            ),

          // 评价按钮
          if ((item.stateId ?? 0) == 7 && (item.isCommented ?? false) == false)
            _buildActionButton(
              label: S.current.give_comment,
              onTap: () {
                context.push(
                  AppPaths.orderEvaluatePage,
                  extra: {
                    'orderId': item.id,
                    'fromPage': 'order',
                  },
                );
              },
            ),

          // 投诉按钮
          if ((item.isComplaints ?? false) == true)
            _buildActionButton(
              label: S.current.complaint,
              onTap: () {
                ref
                    .read(mineControllerProvider.notifier)
                    .makePhoneCall('400-1111-990');
              },
            ),

          // 退款详情按钮
          if ((item.stateId ?? 0) != 8 &&
              (item.stateId ?? 0) != 9 &&
              (item.partRefundType ?? 0) == 2)
            _borderContainer(
              S.current.refund_detail,
              onClick: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => CancelOrderDetail(
                      partRefundId: item.partRefundId ?? 0,
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required String label,
    required VoidCallback onTap,
    Color? borderColor,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.w),
          color: borderColor != null ? null : AppColors.baseGreenColor,
          border: Border.all(
            color: borderColor ?? AppColors.baseGreenColor,
            width: 1.w,
          ),
        ),
        padding: EdgeInsets.symmetric(vertical: 5.w, horizontal: 12.w),
        child: Text(
          label,
          style: TextStyle(
            color: borderColor ?? Colors.white,
            fontSize: mainSize,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
