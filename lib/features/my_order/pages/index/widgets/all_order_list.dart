import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/index.dart';
import 'package:user_app/features/my_order/pages/index/my_order_controller.dart';
import 'package:user_app/features/my_order/pages/index/widgets/order_item.dart';
import 'package:user_app/generated/l10n.dart';

/// 所有订单列表
class AllOrderList extends ConsumerStatefulWidget {
  final VoidCallback onLoadMore;

  const AllOrderList({
    final Key? key,
    required this.onLoadMore,
  }) : super(key: key);

  @override
  ConsumerState<AllOrderList> createState() => _AllOrderListState();
}

class _AllOrderListState extends ConsumerState<AllOrderList> {
  @override
  Widget build(final BuildContext context) {
    // 监听所有订单数据
    final allOrders = ref.watch(myOrderControllerProvider
        .select((final state) => state.cachedAllOrders));

    // // 监听所有订单加载状态
    // final isLoading = ref.watch(myOrderControllerProvider
    //     .select((final state) => state.isLoading && state.type == 2));

    // 监听所有订单是否可加载更多
    final canLoadMore = ref.watch(myOrderControllerProvider
        .select((final state) => state.canLoadMoreAll));

    // // 如果正在加载，显示加载中
    // if (isLoading && allOrders.isEmpty) {
    //   return const Center(child: CircularProgressIndicator());
    // }
    // 显示所有订单列表
    return CustomRefreshIndicator(
      onRefresh: () async {
        // 调用刷新所有订单方法
        await ref.read(myOrderControllerProvider.notifier).refreshAllOrders();
      },
      color: AppColors.primary,
      enablePullUp: true,
      onLoading: () async {
        widget.onLoadMore();
      },
      hasMoreData: canLoadMore,
      child: allOrders.isEmpty
          ? EmptyView(
              message: S.current.no_order,
            )
          : ListView.builder(
              padding: EdgeInsets.only(top: 10.w),
              itemCount: allOrders.length,
              itemBuilder: (final context, final index) {
                // 订单项
                return OrderItem(item: allOrders[index]);
              },
              // 添加性能优化选项
              addAutomaticKeepAlives: true,
              addRepaintBoundaries: true,
            ),
    );
  }
}
