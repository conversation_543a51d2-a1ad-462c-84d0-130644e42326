import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/custom_under_line_tab_Indicator.dart';
import 'package:user_app/generated/l10n.dart';

/// 订单页面顶部选项卡
class OrderTabPanel extends ConsumerWidget {
  final TabController tabController;

  const OrderTabPanel({
    final Key? key,
    required this.tabController,
  }) : super(key: key);

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.baseGreenColor,
        borderRadius: BorderRadius.only(
          bottomRight: Radius.circular(10.w),
          bottomLeft: Radius.circular(10.w),
        ),
        image: DecorationImage(
          image: AssetImage(
            'assets/images/header_img.png',
          ), // 本地图片
          fit: BoxFit.contain, // 让图片覆盖整个容器
        ),
      ),
      height: 150.w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Container(
            alignment: Alignment.center,
            height: 85.w,
            padding: EdgeInsets.only(top: 52.w),
            child: Text(
              S.current.orders,
              style: TextStyle(
                  fontSize: titleSize,
                  color: Colors.white,
                  fontWeight: FontWeight.bold),
            ),
          ),
          Container(
            decoration: BoxDecoration(
                // color: AppColors.baseGreenColor,

                ),
            child: TabBar(
              controller: tabController,
              labelColor: Colors.white,
              labelStyle: TextStyle(
                  fontSize: 18.sp,
                  fontFamily: 'UkijTuzTom',
                  fontWeight: FontWeight.bold),
              unselectedLabelStyle: TextStyle(
                fontSize: 18.sp,
                fontFamily: 'UkijTuzTom',
              ),
              unselectedLabelColor: Colors.white.withOpacity(0.9),
              indicatorSize: TabBarIndicatorSize.label,
              dividerColor: Colors.transparent,
              indicator: CustomUnderlineTabIndicator(
                borderSide: BorderSide(
                  width: 16.w,
                  color: Colors.white,
                ),
              ),
              tabs: [
                Tab(child: Text(S.current.today)),
                Tab(child: Text(S.current.all)),
              ],
            ),
          )
        ],
      ),
    );
  }
}
