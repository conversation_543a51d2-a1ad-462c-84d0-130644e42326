import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/empty_view.dart';
import 'package:user_app/features/my_order/pages/index/my_order_controller.dart';
import 'package:user_app/features/my_order/pages/index/widgets/all_order_list.dart';
import 'package:user_app/features/my_order/pages/index/widgets/today_order_list.dart';
import 'package:user_app/features/my_order/pages/index/widgets/order_tab_panel.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart';
import 'package:user_app/routes/index.dart';

/// 我的订单页面
class MyOrderPage extends ConsumerStatefulWidget {
  const MyOrderPage({super.key});

  @override
  ConsumerState createState() => _MyOrderPageState();
}

class _MyOrderPageState extends ConsumerState<MyOrderPage>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 监听标签切换
    _tabController!.addListener(_onTabChanged);
  }

  /// Tab切换处理
  void _onTabChanged() {
    if (_tabController!.indexIsChanging) return;

    // TabController索引与订单类型的映射：0->类型1(今日)，1->类型2(所有)
    final orderType = _tabController!.index == 0 ? 1 : 2;
    ref.read(myOrderControllerProvider.notifier).changeType(orderType);
  }

  /// 加载更多数据
  void _loadMoreOrders() {
    ref.read(myOrderControllerProvider.notifier).loadMore();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 监听订单类型变化，同步TabController索引
    final orderType =
        ref.watch(myOrderControllerProvider.select((final s) => s.type));

    // 订单类型与TabController索引的映射：类型1(今日)->0，类型2(所有)->1
    final tabIndex = orderType == 1 ? 0 : 1;

    // 仅当TabController初始化完成，且索引不同时才更新，避免循环触发
    if (_tabController != null &&
        _tabController!.index != tabIndex &&
        !_tabController!.indexIsChanging) {
      _tabController!.animateTo(tabIndex);
    }
  }

  @override
  Widget build(final BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      // appBar: AppBar(
      //   title: Text(S.current.orders),
      // ),
      body: Consumer(
        builder: (final context, final ref, final child) {
          // 监听是否已登录
          final isLogin = ref.watch(isLoggedInProvider);

          // 如果没有订单，显示空状态
          if (!isLogin) {
            return EmptyView(
              message: S.current.is_login_text,
              retryMessage: S.current.please_login,
              icon: Image.asset(
                'assets/images/app/mulazim-login-tag.png',
                width: 400.w,
                height: 400.h,
                fit: BoxFit.cover,
              ),
              onRetry: () {
                context.push(AppPaths.login);
              },
            );
          } else {
            return Column(
              children: [
                OrderTabPanel(tabController: _tabController!),

                // 主体内容区域 - 每个Tab视图负责自己的数据加载和展示
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // 今日订单列表（类型1）- 完全独立的组件
                      TodayOrderList(
                        onLoadMore: _loadMoreOrders,
                      ),

                      // 所有订单列表（类型2）- 完全独立的组件
                      AllOrderList(
                        onLoadMore: _loadMoreOrders,
                      ),
                    ],
                  ),
                ),
              ],
            );
            // 顶部选项卡
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    _tabController?.removeListener(_onTabChanged);
    _tabController?.dispose();
    super.dispose();
  }
}
