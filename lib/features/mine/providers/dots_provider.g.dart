// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dots_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dotsHash() => r'974031f2d83f6d39008d41687a41ce190a28f22a';

/// See also [Dots].
@ProviderFor(Dots)
final dotsProvider = AutoDisposeNotifierProvider<Dots, DotsData>.internal(
  Dots.new,
  name: r'dotsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$dotsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Dots = AutoDisposeNotifier<DotsData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
