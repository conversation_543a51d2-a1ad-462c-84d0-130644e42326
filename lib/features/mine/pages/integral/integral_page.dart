import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/mine/pages/integral/integral_controller_provider.dart';
import 'package:user_app/features/mine/pages/integral/widgets/integral_header.dart';
import 'package:user_app/features/mine/pages/integral/widgets/integral_list.dart';
import 'package:user_app/generated/l10n.dart';

/// 积分页面
class IntegralPage extends ConsumerStatefulWidget {
  /// 构造函数
  const IntegralPage({super.key});

  @override
  ConsumerState<IntegralPage> createState() => _IntegralPageState();
}

/// 积分页面状态
class _IntegralPageState extends ConsumerState<IntegralPage> {
  @override
  void initState() {
    super.initState();
    // 加载积分记录
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ref.read(integralControllerProvider.notifier).getPointsLog();
      }
    });
  }

  @override
  Widget build(final BuildContext context) {
    final state = ref.watch(integralControllerProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: S.current.integral_title,
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await ref.read(integralControllerProvider.notifier).refresh();
        },
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: IntegralHeader(),
            ),
            if (state.isLoading)
              const SliverFillRemaining(
                child: Center(
                  child: LoadingWidget(),
                ),
              )
            else if (state.error != null)
              SliverFillRemaining(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(state.error!),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          ref
                              .read(integralControllerProvider.notifier)
                              .refresh();
                        },
                        child: Text(S.current.retry),
                      ),
                    ],
                  ),
                ),
              )
            else if (state.pointsData != null)
              IntegralList(),
          ],
        ),
      ),
    );
  }
}
