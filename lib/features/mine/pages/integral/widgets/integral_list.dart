import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/mine/pages/integral/integral_controller_provider.dart';
import 'package:user_app/features/mine/pages/integral/widgets/integral_list_item.dart';
import 'package:user_app/generated/l10n.dart';

/// 积分列表组件
class IntegralList extends ConsumerWidget {
  /// 构造函数
  const IntegralList({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final pointsLog = ref.watch(
      integralControllerProvider
          .select((final value) => value.pointsData?.pointsLog ?? []),
    );

    return SliverList(
      delegate: SliverChildListDelegate([
        Container(
          margin: EdgeInsets.symmetric(horizontal: 10.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.all(8.w),
                child: Text(
                  S.current.integral_title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              if (pointsLog.isEmpty)
                SizedBox(
                  height: 200.h,
                  child: Center(
                    child: Text(S.current.no_data),
                  ),
                )
              else
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (final context, final index) =>
                      IntegralListItem(item: pointsLog[index]),
                  itemCount: pointsLog.length,
                ),
            ],
          ),
        ),
      ]),
    );
  }
}
