import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/mine/pages/integral/integral_controller_provider.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';

/// 积分头部组件
class IntegralHeader extends ConsumerWidget {
  /// 构造函数
  const IntegralHeader({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final points = ref.watch(
      integralControllerProvider.select((value) => value.pointsData?.points),
    );

    return Container(
      padding: EdgeInsets.all(20.w),
      margin: EdgeInsets.all(10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.w),
      ),
      child: Column(
        children: [
          Text(
            S.current.integral_is,
            style: TextStyle(
              color: AppColors.textPrimaryColor,
              fontWeight: FontWeight.bold,
              fontSize: 14.sp,
            ),
          ),
          SizedBox(height: 10.h),
          Text(
            '${points ?? 0}',
            style: TextStyle(
              color: AppColors.primary,
              fontSize: 25.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 10.h),
          GestureDetector(
            onTap: () => context.push(
              AppPaths.webViewPage,
              extra: {
                'url': ref
                    .read(integralControllerProvider.notifier)
                    .redirectWebAboutDetailUrl(id: 4),
              },
            ),
            child: Text(
              S.current.integral_info,
              style: TextStyle(
                color: AppColors.textSecondColor,
                fontSize: 16.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
