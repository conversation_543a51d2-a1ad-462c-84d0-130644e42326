import 'package:user_app/data/models/mine/points_model.dart';

/// 积分页面状态
class IntegralState {
  /// 积分数据
  final PointsData? pointsData;

  /// 是否正在加载
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 构造函数
  const IntegralState({
    this.pointsData,
    this.isLoading = false,
    this.error,
  });

  /// 复制状态
  IntegralState copyWith({
    final PointsData? pointsData,
    final bool? isLoading,
    final String? error,
  }) {
    return IntegralState(
      pointsData: pointsData ?? this.pointsData,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}
