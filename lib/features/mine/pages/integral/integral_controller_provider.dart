import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/config/api_config.dart';
import 'package:user_app/features/mine/pages/integral/integral_state.dart';
import 'package:user_app/features/mine/services/integral_service.dart';

part 'integral_controller_provider.g.dart';

/// 积分页面控制器
@riverpod
class IntegralController extends _$IntegralController {
  @override
  IntegralState build() => const IntegralState();

  /// 获取积分记录
  Future<void> getPointsLog() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final service = ref.read(integralServiceProvider);
      final pointsData = await service.getPointsLog();
      state = state.copyWith(
        pointsData: pointsData,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 刷新积分记录
  Future<void> refresh() async {
    await getPointsLog();
  }

  /// 获取关于详情的URL
  String redirectWebAboutDetailUrl({final int? id}) {
    // 获取当前app语言
    final currentLocale = Intl.getCurrentLocale().contains('zh') ? 'zh' : 'ug';
    return '${ApiConfig.baseUrl}$currentLocale${Api.web.about}?type=${id ?? '1'}';
  }
}
