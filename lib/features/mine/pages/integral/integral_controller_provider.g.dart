// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'integral_controller_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$integralControllerHash() =>
    r'ec5f40a88ac51910a0b07d6b442ce8bf77fb224f';

/// 积分页面控制器
///
/// Copied from [IntegralController].
@ProviderFor(IntegralController)
final integralControllerProvider =
    AutoDisposeNotifierProvider<IntegralController, IntegralState>.internal(
  IntegralController.new,
  name: r'integralControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$integralControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$IntegralController = AutoDisposeNotifier<IntegralState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
