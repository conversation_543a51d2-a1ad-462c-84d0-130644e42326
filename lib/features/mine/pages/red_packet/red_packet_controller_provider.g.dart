// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'red_packet_controller_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$redPacketControllerHash() =>
    r'84b2a2accc0c7ab48381e4dd5802a45522d2d719';

/// 红包控制器
///
/// Copied from [RedPacketController].
@ProviderFor(RedPacketController)
final redPacketControllerProvider =
    AutoDisposeNotifierProvider<RedPacketController, RedPacketState>.internal(
  RedPacketController.new,
  name: r'redPacketControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$redPacketControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RedPacketController = AutoDisposeNotifier<RedPacketState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
