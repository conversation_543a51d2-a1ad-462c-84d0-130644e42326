import 'package:user_app/data/models/user/red_packet_model.dart';

/// 红包页面状态
class RedPacketState {
  /// 构造函数
  const RedPacketState({
    this.isLoading = false,
    this.redPackets = const [],
    this.totalAmount = 0.0,
    this.error,
  });

  /// 是否加载中
  final bool isLoading;

  /// 红包列表
  final List<RedPacket> redPackets;

  /// 总金额
  final double totalAmount;

  /// 错误信息
  final String? error;

  /// 复制方法
  RedPacketState copyWith({
    bool? isLoading,
    List<RedPacket>? redPackets,
    double? totalAmount,
    String? error,
  }) {
    return RedPacketState(
      isLoading: isLoading ?? this.isLoading,
      redPackets: redPackets ?? this.redPackets,
      totalAmount: totalAmount ?? this.totalAmount,
      error: error ?? this.error,
    );
  }
}
