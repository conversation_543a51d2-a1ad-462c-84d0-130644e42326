import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/features/mine/pages/red_packet/red_packet_state.dart';
import 'package:user_app/features/mine/services/red_packet_service.dart';

part 'red_packet_controller_provider.g.dart';

/// 红包控制器
@riverpod
class RedPacketController extends _$RedPacketController {
  @override
  RedPacketState build() {
    Future.microtask(() {
      fetchRedPackets();
    });
    return const RedPacketState();
  }

  /// 获取红包列表
  Future<void> fetchRedPackets() async {
    state = state.copyWith(isLoading: true);
    try {
      final service = ref.read(redPacketServiceProvider);
      final response = await service.getRedPacketList();

      state = state.copyWith(
        isLoading: false,
        redPackets: response?.items,
        totalAmount: double.parse(response!.header.amount),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
}
