import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/mine/pages/red_packet/red_packet_controller_provider.dart';
import 'package:user_app/generated/l10n.dart';

/// 红包页面
class RedPacketPage extends ConsumerWidget {
  /// 构造函数
  const RedPacketPage({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 获取红包状态
    final state = ref.watch(redPacketControllerProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(S.current.red_packet_page_title),
      ),
      // 根据加载状态显示不同的界面
      body: state.isLoading
          ? const Center(child: LoadingWidget()) // 加载中显示加载指示器
          : state.error != null
              ? Center(child: Text(state.error!)) // 有错误时显示错误信息
              : CustomScrollView(
                  slivers: [
                    // 如果有红包数据则显示红包列表
                    if (state.redPackets.isNotEmpty) ...[
                      // 显示红包总金额
                      SliverToBoxAdapter(
                        child: Container(
                          margin: EdgeInsets.all(10.w),
                          padding: EdgeInsets.all(20.w),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10.r),
                          ),
                          child: Column(
                            children: [
                              // 显示总金额
                              Text(
                                '￥${state.totalAmount}',
                                style: TextStyle(
                                  fontSize: 25.sp,
                                  color: AppColors.primary,
                                ),
                              ),
                              SizedBox(height: 4.h),
                              // 显示总金额标题
                              Text(
                                S.current.red_packet_sum,
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      // 红包列表
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (final context, final index) {
                            final redPacket = state.redPackets[index];
                            // 单个红包项
                            return Container(
                              margin: EdgeInsets.symmetric(
                                horizontal: 10.w,
                                vertical: 5.h,
                              ),
                              padding: EdgeInsets.all(18.w),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(10.r),
                              ),
                              child: Row(
                                children: [
                                  // 红包名称和支付时间
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          redPacket.name,
                                          style: TextStyle(
                                            fontSize: 14.sp,
                                          ),
                                        ),
                                        SizedBox(height: 12.5.h),
                                        Text(
                                          redPacket.paymentTime,
                                          style: TextStyle(
                                            fontSize: 15.sp,
                                            color: Colors.grey[600],
                                          ),
                                          textAlign: TextAlign.right,
                                        ),
                                      ],
                                    ),
                                  ),
                                  // 红包金额
                                  Text(
                                    '￥${redPacket.amount}',
                                    style: TextStyle(
                                      fontSize: 18.sp,
                                      color: AppColors.primary,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                          childCount: state.redPackets.length,
                        ),
                      ),
                    ] else
                      // 没有红包时显示提示信息
                      SliverFillRemaining(
                        child: Center(
                          child: Text(
                            S.current.no_packet_tip,
                            style: TextStyle(
                              fontSize: 17.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
    );
  }
}
