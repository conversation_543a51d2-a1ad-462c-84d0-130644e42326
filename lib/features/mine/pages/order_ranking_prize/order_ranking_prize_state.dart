import 'package:flutter/foundation.dart';

/// 订单排名奖品页面状态
class OrderRankingPrizeState {
  /// 构造函数
  const OrderRankingPrizeState({
    this.isLoading = false,
    this.error,
    this.headerItems = const [],
    this.headerHeight = 0,
    this.prizeList = const [],
    this.activityRule = '',
    this.userPrize,
    this.userOrderNotice = const [],
    this.noticeHeight = 0,
    this.isScrolling = false,
    this.shareName = '',
    this.activityId = 0,
    this.tabIndex = 0,
  });

  /// 是否加载中
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 头部选项
  final List<Map<String, dynamic>> headerItems;

  /// 头部高度
  final double headerHeight;

  /// 奖品列表
  final List<dynamic> prizeList;

  /// 活动规则
  final String activityRule;

  /// 用户奖品
  final Map<String, dynamic>? userPrize;

  /// 用户订单通知
  final List<dynamic> userOrderNotice;

  /// 通知高度
  final double noticeHeight;

  /// 是否滚动中
  final bool isScrolling;

  /// 分享名称
  final String shareName;

  /// 活动ID
  final int activityId;

  /// 当前选中的标签索引
  final int tabIndex;

  /// 拷贝方法
  OrderRankingPrizeState copyWith({
    final bool? isLoading,
    final String? error,
    final List<Map<String, dynamic>>? headerItems,
    final double? headerHeight,
    final List<dynamic>? prizeList,
    final String? activityRule,
    final Map<String, dynamic>? userPrize,
    final List<dynamic>? userOrderNotice,
    final double? noticeHeight,
    final bool? isScrolling,
    final String? shareName,
    final int? activityId,
    final int? tabIndex,
  }) {
    return OrderRankingPrizeState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      headerItems: headerItems ?? this.headerItems,
      headerHeight: headerHeight ?? this.headerHeight,
      prizeList: prizeList ?? this.prizeList,
      activityRule: activityRule ?? this.activityRule,
      userPrize: userPrize ?? this.userPrize,
      userOrderNotice: userOrderNotice ?? this.userOrderNotice,
      noticeHeight: noticeHeight ?? this.noticeHeight,
      isScrolling: isScrolling ?? this.isScrolling,
      shareName: shareName ?? this.shareName,
      activityId: activityId ?? this.activityId,
      tabIndex: tabIndex ?? this.tabIndex,
    );
  }

  @override
  bool operator ==(final Object other) {
    if (identical(this, other)) return true;

    return other is OrderRankingPrizeState &&
        other.isLoading == isLoading &&
        other.error == error &&
        listEquals(other.headerItems, headerItems) &&
        other.headerHeight == headerHeight &&
        listEquals(other.prizeList, prizeList) &&
        other.activityRule == activityRule &&
        mapEquals(other.userPrize, userPrize) &&
        listEquals(other.userOrderNotice, userOrderNotice) &&
        other.noticeHeight == noticeHeight &&
        other.isScrolling == isScrolling &&
        other.shareName == shareName &&
        other.activityId == activityId &&
        other.tabIndex == tabIndex;
  }

  @override
  int get hashCode {
    return isLoading.hashCode ^
        error.hashCode ^
        headerItems.hashCode ^
        headerHeight.hashCode ^
        prizeList.hashCode ^
        activityRule.hashCode ^
        userPrize.hashCode ^
        userOrderNotice.hashCode ^
        noticeHeight.hashCode ^
        isScrolling.hashCode ^
        shareName.hashCode ^
        activityId.hashCode ^
        tabIndex.hashCode;
  }
}
