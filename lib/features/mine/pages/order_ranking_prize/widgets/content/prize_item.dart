import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/generated/l10n.dart';

/// 奖品项组件
class PrizeItem extends StatelessWidget {
  /// 奖品数据
  final dynamic item;

  /// 构造函数
  const PrizeItem({
    super.key,
    required this.item,
  });

  @override
  Widget build(final BuildContext context) {
    final s = S.of(context);
    // 确保所有属性都有默认值，防止API返回数据缺失导致的错误
    final luckyUserIndex = item['lucky_user_index']?.toString() ?? '';
    final luckyState = item['lucky_state'] ?? 0;
    final userName = item['user_name'] ?? '';
    final mobile = item['mobile'] ?? '';
    final userAvatar = item['user_avatar'] ??
        'https://acdn.mulazim.com/wechat_mini/img/lover/nan.png';
    final prizeImage = item['prize_image'] ?? '';

    // 根据屏幕宽度计算各部分宽度
    final avatarSize = 47.w;
    final imageSize = 47.w;

    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: const Color(0xFFFF4B57).withOpacity(0.05),
          ),
        ),
      ),
      child: Row(
        children: [
          // 排名 - 橙色数字
          SizedBox(
            width: 80.w,
            child: Text(
              luckyUserIndex,
              style: TextStyle(
                color: const Color(0xFFFF9900),
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                fontFamily: 'NumberFont',
              ),
            ),
          ),

          // 用户信息 - 头像和信息
          Expanded(
            child: Row(
              children: [
                // 头像
                ClipRRect(
                  borderRadius: BorderRadius.circular(avatarSize / 2),
                  child: CachedNetworkImage(
                    imageUrl: userAvatar,
                    width: avatarSize,
                    height: avatarSize,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      width: avatarSize,
                      height: avatarSize,
                      color: Colors.grey[200],
                    ),
                    errorWidget: (context, url, error) => Container(
                      width: avatarSize,
                      height: avatarSize,
                      color: Colors.grey[300],
                      child: Icon(
                        Icons.person,
                        size: avatarSize / 2,
                        color: Colors.grey[500],
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 10.w),
                // 用户名称和手机号
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        luckyState == 0
                            ? s.reward_achievers
                            : (luckyState == 1 || luckyState == 2
                                ? userName
                                : ''),
                        style: TextStyle(
                          color: luckyState == 1 || luckyState == 2
                              ? Colors.black
                              : const Color(0xFFFF5C6A),
                          fontSize: 15.sp,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        textDirection: TextDirection.ltr,
                        luckyState == 0
                            ? '-------'
                            : (luckyState == 1
                                ? mobile
                                : (luckyState == 2 ? s.reward_declined : '')),
                        style: TextStyle(
                          color: luckyState == 2
                              ? const Color(0xFFFF5C6A)
                              : const Color(0xFF747474),
                          fontSize: 13.sp,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 奖品图片
          ClipRRect(
            borderRadius: BorderRadius.circular(5.r),
            child: CachedNetworkImage(
              imageUrl: prizeImage,
              width: imageSize,
              height: imageSize,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                width: imageSize,
                height: imageSize,
                color: Colors.grey[200],
              ),
              errorWidget: (context, url, error) => Container(
                width: imageSize,
                height: imageSize,
                color: Colors.grey[300],
                child: Icon(
                  Icons.image_not_supported,
                  size: imageSize / 2.5,
                  color: Colors.grey[500],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
