import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/mine/pages/order_ranking_prize/widgets/content/prize_item.dart';
import 'package:user_app/generated/l10n.dart';

/// 奖品内容区域组件
class PrizeContent extends StatefulWidget {
  /// 构造函数
  const PrizeContent({
    super.key,
    this.scrollController,
    required this.prizeList,
    required this.activityRule,
    required this.onRuleClick,
    this.onScrollStateChanged,
  });

  /// 滚动控制器 (可选)
  final ScrollController? scrollController;

  /// 奖品列表
  final List<dynamic> prizeList;

  /// 活动规则
  final String activityRule;

  /// 点击规则按钮回调
  final VoidCallback onRuleClick;

  /// 滚动状态变化回调
  final Function(bool isScrolling)? onScrollStateChanged;

  @override
  State<PrizeContent> createState() => _PrizeContentState();
}

class _PrizeContentState extends State<PrizeContent> {

  @override
  Widget build(final BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        children: [
          // 标题
          Container(
            padding: EdgeInsets.only(top: 15.h, bottom: 5.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.network(
                  'https://acdn.mulazim.com/wechat_mini/img/orderRanking/title.png',
                  width: 17.5.w,
                  height: 17.5.h,
                ),
                SizedBox(width: 6.w),
                Text(
                  S.current.reward_achievers_list,
                  style: TextStyle(
                    fontSize: 17.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(width: 6.w),
                Image.network(
                  'https://acdn.mulazim.com/wechat_mini/img/orderRanking/title.png',
                  width: 17.5.w,
                  height: 17.5.h,
                ),
              ],
            ),
          ),

          // 规则按钮
          GestureDetector(
            onTap: widget.onRuleClick,
            child: Container(
              width: 150.w,
              margin: EdgeInsets.symmetric(vertical: 10.h),
              padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
              decoration: BoxDecoration(
                border: Border.all(
                  color: const Color(0xFFFF4B56).withOpacity(0.1),
                ),
                borderRadius: BorderRadius.circular(25.r),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    S.current.ranking_rule_title,
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: const Color(0xFFFF4B56),
                    ),
                  ),
                  SizedBox(width: 5.w),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12.sp,
                    color: const Color(0xFFFF4B56),
                  ),
                ],
              ),
            ),
          ),

          // 奖品列表
          Expanded(
            child: widget.prizeList.isEmpty
                ? Center(
                    child: Text(
                      S.current.no_data,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey,
                      ),
                    ),
                  )
                : NotificationListener<ScrollNotification>(
                    onNotification: _handleScrollNotification,
                    child: ListView.builder(
                      controller: widget.scrollController,
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      itemCount: widget.prizeList.length,
                      itemBuilder: (final context, final index) {
                        final item = widget.prizeList[index];
                        if (item == null) {
                          return const SizedBox.shrink();
                        }
                        return PrizeItem(item: item);
                      },
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  bool _handleScrollNotification(final ScrollNotification notification) {
    if (widget.onScrollStateChanged == null) return false;

    // 滚动开始
    if (notification is ScrollStartNotification) {
      widget.onScrollStateChanged!(true);
    }
    // 滚动结束
    else if (notification is ScrollEndNotification) {
  
      widget.onScrollStateChanged!(false);
    }

    return false; // 允许事件继续冒泡
  }
}
