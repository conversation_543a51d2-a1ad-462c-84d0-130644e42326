import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/button.dart';
import 'package:user_app/features/mine/pages/add_shop/add_shop_controller_provider.dart';
import 'package:user_app/features/mine/pages/add_shop/widgets/city_area_selector.dart';
import 'package:user_app/features/mine/pages/add_shop/widgets/license_type_list.dart';
import 'package:user_app/features/mine/pages/add_shop/widgets/shop_info_form.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/app_router.dart';

/// 商家入驻表单
class AddShopForm extends ConsumerWidget {
  /// 构造函数
  const AddShopForm({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final isLoading = ref.watch(
      addShopControllerProvider.select((final state) => state.isLoading),
    );

    final controller = ref.read(addShopControllerProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        SizedBox(
          height: 4.h,
        ),

        // 城市选择
        _buildCard(
          children: const [
            CityAreaSelector(),
          ],
        ),

        // 店铺类型
        _buildCard(
          children: const [
            LicenseTypeList(),
          ],
        ),

        // 店铺信息
        _buildCard(
          children: const [
            ShopInfoForm(),
          ],
        ),

        // 提交按钮
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
          child: MulazimButton(
            text: S.current.addShop_btn,
            onPressed: () async {
              final success = await controller.submit();
              if (success) {
                router.pop();
              }
            },
            isLoading: isLoading,
            borderRadius: 50.r,
            color: Theme.of(context).primaryColor,
            height: 40.h,
          ),
        ),
      ],
    );
  }

  /// 构建卡片
  Widget _buildCard({required final List<Widget> children}) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: children,
      ),
    );
  }
}
