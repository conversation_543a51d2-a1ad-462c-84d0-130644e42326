import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/mine/pages/add_shop/add_shop_controller_provider.dart';
import 'package:user_app/generated/l10n.dart';

/// 城市区域选择器
class CityAreaSelector extends ConsumerWidget {
  /// 构造函数
  const CityAreaSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听城市相关状态
    final cityName = ref.watch(
      addShopControllerProvider.select((state) => state.cityName),
    );
    final areaName = ref.watch(
      addShopControllerProvider.select((state) => state.areaName ?? ''),
    );
    final cityId = ref.watch(
      addShopControllerProvider.select((state) => state.cityId),
    );

    return Column(
      children: [
        _buildPickerItem(
          label: S.current.add_shop_city,
          value: cityName,
          onTap: () => _showCityPicker(context, ref),
        ),
        _buildPickerItem(
          label: S.current.add_shop_area,
          value: areaName,
          onTap: () {
            if (cityId != null) {
              _showAreaPicker(context, ref);
            } else {
              _showCityPicker(context, ref);
            }
          },
        ),
      ],
    );
  }

  /// 显示城市选择器
  Future<void> _showCityPicker(BuildContext context, WidgetRef ref) async {
    final controller = ref.read(addShopControllerProvider.notifier);
    final cityList = ref.read(addShopControllerProvider).cityList;

    if (cityList.isEmpty) {
      await controller.loadCityList(null);
      return;
    }

    if (!context.mounted) return;

    await showModalBottomSheet<void>(
      context: context,
      builder: (context) => _CityPickerSheet(
        onCitySelected: (index) async {
          controller.selectCity(index);
          Navigator.pop(context);
          if (context.mounted) {
            _showAreaPicker(context, ref);
          }
        },
      ),
    );
  }

  /// 显示区域选择器
  Future<void> _showAreaPicker(BuildContext context, WidgetRef ref) async {
    if (!context.mounted) return;

    await showModalBottomSheet<void>(
      context: context,
      builder: (context) => _AreaPickerSheet(),
    );
  }

  /// 构建选择器项
  Widget _buildPickerItem({
    required String label,
    required String value,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
        child: Row(
          children: [
            SizedBox(
              width: 100.w,
              child: Text(
                label,
                style: TextStyle(
                  color: const Color(0xFF656565),
                  fontSize: 15.sp,
                ),
              ),
            ),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    value,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 16.sp,
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Icon(
                    Icons.chevron_right,
                    size: 20.sp,
                    color: Colors.grey,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 城市选择器底部弹窗
class _CityPickerSheet extends ConsumerWidget {
  const _CityPickerSheet({
    required this.onCitySelected,
  });

  final void Function(int index) onCitySelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cityList = ref.watch(
      addShopControllerProvider.select((state) => state.cityList),
    );

    return Container(
      height: 300.h,
      padding: EdgeInsets.all(15.w),
      child: Column(
        children: [
          Text(
            S.current.add_shop_city,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 15.h),
          Expanded(
            child: ListView.separated(
              itemCount: cityList.length,
              separatorBuilder: (context, index) =>  Divider(
                color: Colors.grey.shade100,
                height: 1,
                thickness: 1,
              ),
              itemBuilder: (context, index) {
                final city = cityList[index];
                return ListTile(
                  title: Text(city['name'] as String),
                  onTap: () => onCitySelected(index),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

/// 区域选择器底部弹窗
class _AreaPickerSheet extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(addShopControllerProvider.notifier);
    final currentCity = ref.watch(
      addShopControllerProvider.select((state) {
        return state.cityList.firstWhere(
          (city) => city['id'] as int == state.cityId,
          orElse: () => state.cityList.first,
        );
      }),
    );

    final areaList =
        (currentCity['area'] as List<dynamic>).cast<Map<String, dynamic>>();

    if (areaList.isEmpty) {
      Navigator.pop(context);
      return const SizedBox();
    }

    return Container(
      height: 300.h,
      padding: EdgeInsets.all(15.w),
      child: Column(
        children: [
          Text(
            S.current.add_shop_area,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 15.h),
          Expanded(
            child: ListView.separated(
              itemCount: areaList.length,
               separatorBuilder: (context, index) =>  Divider(
                color: Colors.grey.shade100,
                height: 1,
                thickness: 1,
              ),
              itemBuilder: (context, index) {
                final area = areaList[index];
                return ListTile(
                  title: Text(area['name'] as String),
                  onTap: () {
                    controller.selectArea(index);
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
