import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/mine/pages/add_shop/add_shop_controller_provider.dart';
import 'package:user_app/generated/l10n.dart';

/// 店铺信息表单
class ShopInfoForm extends ConsumerWidget {
  /// 构造函数
  const ShopInfoForm({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final shopName = ref.watch(
      addShopControllerProvider.select((state) => state.shopName),
    );
    final phone = ref.watch(
      addShopControllerProvider.select((state) => state.phone),
    );
    final controller = ref.read(addShopControllerProvider.notifier);

    return Column(
      children: [
        _buildInputItem(
          label: S.current.shopName,
          value: shopName,
          onChanged: controller.updateShopName,
          hintText: S.current.input,
        ),
        _buildInputItem(
          label: S.current.tel_num,
          value: phone,
          onChanged: controller.updatePhone,
          keyboardType: TextInputType.phone,
          hintText: S.current.input,
        ),
      ],
    );
  }

  /// 构建输入项
  Widget _buildInputItem({
    required String label,
    required String value,
    required ValueChanged<String> onChanged,
    TextInputType? keyboardType,
    String? hintText,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: const Color(0xFFEFF1F6),
            width: 1.h,
          ),
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              label,
              style: TextStyle(
                color: AppColors.textPrimaryColor,
                fontSize: 15.sp,
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: TextEditingController(text: value)
                ..selection = TextSelection.fromPosition(
                  TextPosition(offset: value.length),
                ),
              onChanged: onChanged,
              keyboardType: keyboardType,
              textAlign: TextAlign.right,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: hintText,
                hintStyle: TextStyle(
                  color: AppColors.textHintColor,
                  fontSize: 14.sp,
                ),
                isDense: true,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
