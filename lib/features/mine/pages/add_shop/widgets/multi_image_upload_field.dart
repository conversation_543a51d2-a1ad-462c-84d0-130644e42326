import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';

/// 多图片上传字段
class MultiImageUploadField extends StatelessWidget {
  /// 构造函数
  const MultiImageUploadField({
    required this.label,
    required this.imagePaths,
    required this.onImagesSelected,
    super.key,
  });

  /// 标签
  final String label;

  /// 图片路径列表
  final List<String> imagePaths;

  /// 图片选择回调
  final void Function(List<String>) onImagesSelected;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 10.h),
        Wrap(
          spacing: 10.w,
          runSpacing: 10.h,
          children: [
            ...imagePaths.map(
              (path) => SizedBox(
                width: 100.w,
                height: 100.w,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.r),
                  child: Image.asset(
                    path,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            if (imagePaths.length < 9)
              GestureDetector(
                onTap: () async {
                  final picker = ImagePicker();
                  final image =
                      await picker.pickImage(source: ImageSource.gallery);
                  if (image != null) {
                    onImagesSelected([...imagePaths, image.path]);
                  }
                },
                child: Container(
                  width: 100.w,
                  height: 100.w,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    Icons.add_photo_alternate_outlined,
                    size: 40.w,
                    color: Colors.grey,
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }
}
