import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/mine/pages/add_shop/add_shop_controller_provider.dart';
import 'package:user_app/generated/l10n.dart';

/// 证件类型列表
class LicenseTypeList extends ConsumerWidget {
  /// 构造函数
  const LicenseTypeList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final licenseTypes = ref.watch(
      addShopControllerProvider.select((state) => state.licenseTypes),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildTitle(S.current.addShop_license),
        ...licenseTypes.map(
          (type) => _buildLicenseItem(
            name: type.name,
          ),
        ),
      ],
    );
  }

  /// 构建标题
  Widget _buildTitle(String title) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: const Color(0xFFEFF1F6),
            width: 1.h,
          ),
        ),
      ),
      child: Text(
        title,
        style: TextStyle(
          color: Colors.black,
          fontSize: 17.sp,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// 构建证件类型项
  Widget _buildLicenseItem({
    required String name,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: const Color(0xFFEFF1F6),
            width: 1.h,
          ),
        ),
      ),
      child: Text(
        name,
        style: TextStyle(
          color: AppColors.redColor,
          fontSize: 17.sp,
        ),
      ),
    );
  }
}
