import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';

/// 图片上传字段
class ImageUploadField extends StatelessWidget {
  /// 构造函数
  const ImageUploadField({
    required this.label,
    required this.imagePath,
    required this.onImageSelected,
    super.key,
  });

  /// 标签
  final String label;

  /// 图片路径
  final String? imagePath;

  /// 图片选择回调
  final void Function(String) onImageSelected;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 10.h),
        GestureDetector(
          onTap: () async {
            final picker = ImagePicker();
            final image = await picker.pickImage(source: ImageSource.gallery);
            if (image != null) {
              onImageSelected(image.path);
            }
          },
          child: Container(
            height: 120.h,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: imagePath == null
                ? Icon(
                    Icons.add_photo_alternate_outlined,
                    size: 40.w,
                    color: Colors.grey,
                  )
                : ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: Image.asset(
                      imagePath!,
                      fit: BoxFit.cover,
                    ),
                  ),
          ),
        ),
      ],
    );
  }
}
