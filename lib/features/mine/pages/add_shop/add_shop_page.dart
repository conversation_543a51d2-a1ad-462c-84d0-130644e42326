import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/mine/pages/add_shop/add_shop_controller_provider.dart';
import 'package:user_app/features/mine/pages/add_shop/widgets/add_shop_form.dart';
import 'package:user_app/generated/l10n.dart';

/// 商家入驻页面
class AddShopPage extends ConsumerWidget {
  /// 构造函数
  const AddShopPage({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 监听城市列表加载状态
    final cityList = ref.watch(
      addShopControllerProvider.select((final state) => state.cityList),
    );
    final isLoading = ref.watch(
      addShopControllerProvider.select((final state) => state.isLoading),
    );

    return Scaffold(
      appBar: CustomAppBar(
        title: S.current.about_restaurant,
      ),
      body: cityList.isEmpty || isLoading
          ? LoadingWidget()
          : const SingleChildScrollView(
              child: SafeArea(
                child: AddShopForm(),
              ),
            ),
    );
  }
}
