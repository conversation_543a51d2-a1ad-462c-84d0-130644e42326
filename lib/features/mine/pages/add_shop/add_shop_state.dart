import 'package:user_app/data/models/add_shop/add_shop_model.dart';
import 'package:user_app/data/models/add_shop/license_type.dart';

/// 商家入驻状态
class AddShopState {
  /// 构造函数
  const AddShopState({
    this.isLoading = false,
    this.error,
    this.shopName = '',
    this.phone = '',
    this.cityId,
    this.cityName = '',
    this.areaId,
    this.areaName,
    this.licenseId,
    this.licenseTypes = const [],
    this.cityList = const [],
    this.areaList = const [],
  });

  /// 是否加载中
  final bool isLoading;

  /// 错误信息（红色文字显示）
  final String? error;

  /// 店铺名称
  final String shopName;

  /// 联系电话
  final String phone;

  /// 城市ID
  final int? cityId;

  /// 城市名称
  final String cityName;

  /// 区域ID
  final int? areaId;

  /// 区域名称
  final String? areaName;

  /// 证件类型ID
  final int? licenseId;

  /// 证件类型列表
  final List<LicenseType> licenseTypes;

  /// 城市列表
  final List<Map<String, dynamic>> cityList;

  /// 区域列表
  final List<Map<String, dynamic>> areaList;

  /// 复制并修改
  AddShopState copyWith({
    bool? isLoading,
    String? error,
    String? shopName,
    String? phone,
    int? cityId,
    String? cityName,
    int? areaId,
    String? areaName,
    int? licenseId,
    List<LicenseType>? licenseTypes,
    List<Map<String, dynamic>>? cityList,
    List<Map<String, dynamic>>? areaList,
  }) =>
      AddShopState(
        isLoading: isLoading ?? this.isLoading,
        error: error ?? this.error,
        shopName: shopName ?? this.shopName,
        phone: phone ?? this.phone,
        cityId: cityId ?? this.cityId,
        cityName: cityName ?? this.cityName,
        areaId: areaId ?? this.areaId,
        areaName: areaName ?? this.areaName,
        licenseId: licenseId ?? this.licenseId,
        licenseTypes: licenseTypes ?? this.licenseTypes,
        cityList: cityList ?? this.cityList,
        areaList: areaList ?? this.areaList,
      );

  /// 转换为模型
  AddShopModel? toModel() {
    if (shopName.isEmpty ||
        phone.isEmpty ||
        cityId == null ||
        cityName.isEmpty ||
        areaId == null ||
        areaName == null ||
        licenseId == null) {
      return null;
    }

    return AddShopModel(
      shopName: shopName,
      phone: phone,
      cityId: cityId!,
      cityName: cityName,
      areaId: areaId!,
      areaName: areaName!,
      licenseId: licenseId!,
    );
  }
}
