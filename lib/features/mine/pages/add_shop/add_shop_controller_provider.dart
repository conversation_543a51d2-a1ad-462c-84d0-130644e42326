import 'package:bot_toast/bot_toast.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/features/mine/pages/add_shop/add_shop_state.dart';
import 'package:user_app/features/mine/services/add_shop_service.dart';

part 'add_shop_controller_provider.g.dart';

/// 商家入驻控制器提供者
@riverpod
class AddShopController extends _$AddShopController {
  @override
  AddShopState build() {
    Future.microtask(() {
      init();
    });
    return const AddShopState();
  }

  /// 初始化
  Future<void> init() async {
    try {
      final service = ref.read(addShopServiceProvider.notifier);

      // 获取证件类型列表
      final licenseTypes = await service.getLicenseTypes();

      // 获取用户信息
      final userInfo = await service.getUserInfo();

      state = state.copyWith(
        licenseTypes: licenseTypes.data,
        phone: userInfo['phone'] != null ? userInfo['phone'] as String : '',
        cityName:
            userInfo['cityName'] != null ? userInfo['cityName'] as String : '',
        cityId: userInfo['cityId'] != null ? userInfo['cityId'] as int : null,
        areaId: userInfo['areaId'] != null ? userInfo['areaId'] as int : null,
        areaName:
            userInfo['areaName'] != null ? userInfo['areaName'] as String : '',
      );

      // 获取城市列表
      await loadCityList(userInfo);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 加载城市列表
  Future<void> loadCityList(final Map<String, dynamic>? userInfo) async {
    try {
      final service = ref.read(addShopServiceProvider.notifier);
      final cityList = await service.getCityAreaList();
      state = state.copyWith(
        cityList: cityList.data,
        areaList: cityList.data?.first['area'].cast<Map<String, dynamic>>(),
        cityName: userInfo?['cityName'] ?? cityList.data?.first['name'].toString() ?? '',
        cityId: userInfo?['cityId'] ?? cityList.data?.first['id'],
        areaId: userInfo?['areaId'] ?? cityList.data?.first['area'].first['id'],
        areaName: userInfo?['areaName'] ?? cityList.data?.first['area'].first['name'].toString() ?? '',
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 更新店铺名称
  void updateShopName(final String value) {
    state = state.copyWith(shopName: value);
  }

  /// 更新联系电话
  void updatePhone(final String value) {
    final service = ref.read(addShopServiceProvider.notifier);
    final phone = service.formatPhone(value);
    state = state.copyWith(phone: phone);
  }

  /// 选择城市
  void selectCity(final int index) {
    if (state.cityList.isEmpty || index >= state.cityList.length) return;

    final city = state.cityList[index];
    final areas = city['area'] as List<dynamic>;

    if (areas.isNotEmpty) {
      final area = areas.first as Map<String, dynamic>;
      state = state.copyWith(
        cityId: city['id'] as int,
        cityName: city['name'] as String,
        areaId: area['id'] as int,
        areaName: area['name'] as String,
        areaList: areas.cast<Map<String, dynamic>>(),
      );
    }
  }

  /// 选择区域
  void selectArea(final int index) {
    if (state.areaList.isEmpty || index >= state.areaList.length) return;

    final area = state.areaList[index];
    state = state.copyWith(
      areaId: area['id'] as int,
      areaName: area['name'] as String,
    );
  }

  /// 提交
  Future<bool> submit() async {
    if (state.isLoading) return false;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final service = ref.read(addShopServiceProvider.notifier);
      final response = await service.submit(
        shopName: state.shopName,
        phone: state.phone,
        cityId: state.cityId!,
        areaId: state.areaId!,
      );
      if (response.success) {
        state = state.copyWith(
          isLoading: false,
          error: null,
        );
        BotToast.showText(text: response.msg ?? "");
        return true; // 返回成功标志
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.msg,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
    if (state.error != null) {
      BotToast.showText(text: state.error!);
    }
    return false; // 返回失败标志
  }
}
