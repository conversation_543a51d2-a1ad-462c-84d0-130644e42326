// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_shop_controller_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$addShopControllerHash() => r'88b608f70a8249e9e96d897a38a40205b51cda60';

/// 商家入驻控制器提供者
///
/// Copied from [AddShopController].
@ProviderFor(AddShopController)
final addShopControllerProvider =
    AutoDisposeNotifierProvider<AddShopController, AddShopState>.internal(
  AddShopController.new,
  name: r'addShopControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$addShopControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AddShopController = AutoDisposeNotifier<AddShopState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
