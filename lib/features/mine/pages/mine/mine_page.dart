import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_about_menu.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_help_menu.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_license_menu.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_logout_button.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_main_menu.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_quick_actions.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_service_menu.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_user_header.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart';
import 'package:user_app/routes/paths.dart';

/// 个人中心页面
class MinePage extends ConsumerStatefulWidget {
  /// 构造函数
  const MinePage({super.key});

  @override
  ConsumerState<MinePage> createState() => _MinePageState();
}

class _MinePageState extends ConsumerState<MinePage> {
  @override
  Widget build(final BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      appBar: CustomAppBar(
        title: S.current.mine,
        actions: [
          Consumer(
            builder: (final context, final ref, final child) {
              return Visibility(
                visible: ref.watch(isLoggedInProvider),
                child: Container(
                  width: 55.r,
                  height: 55.r,
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: GestureDetector(
                    onTap: () {
                      context.push(AppPaths.profilePage);
                    },
                    child: Center(
                      child: Image.asset(
                        "assets/images/mine/setUp.png",
                        width: 24.r,
                        height: 24.r,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: NotificationListener<OverscrollIndicatorNotification>(
        onNotification: (final overscroll) {
          overscroll.disallowIndicator();
          return true;
        },
        child: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Column(
            children: [
              // 用户信息头部和快捷操作区的容器
              Container(
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(80.r),
                    bottomRight: Radius.circular(80.r),
                  ),
                  image: DecorationImage(
                    image: AssetImage(
                      'assets/images/about_bg_origin.png',
                    ),
                    opacity: 0.6,
                  ),
                ),
                child: Column(
                  children: [
                    // 用户信息头部
                    const MineUserHeader(),
                    SizedBox(height: 15.h),
                    // 快捷操作区
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 8.w),
                      padding: EdgeInsets.symmetric(horizontal: 8.h),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.r),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(5),
                            spreadRadius: 1,
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: const MineQuickActions(),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 4.h),

              // 主菜单列表
              const MineMainMenu(),

              // 客服菜单
              const MineServiceMenu(),

              // 帮助菜单
              const MineHelpMenu(),

              // 证书与资质菜单
              const MineLicenseMenu(),

              // 关于信息
              const MineAboutMenu(),

              // 退出登录按钮
              const MineLogoutButton(),
            ],
          ),
        ),
      ),
    );
  }
}
