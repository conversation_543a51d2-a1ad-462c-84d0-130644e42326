import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/features/mine/pages/mine/mine_controller_provider.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_menu_item.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_menu_section.dart';
import 'package:user_app/generated/l10n.dart';

/// 证书与资质菜单区域
class MineLicenseMenu extends ConsumerWidget {
  /// 构造函数
  const MineLicenseMenu({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final webList = ref.watch(mineControllerProvider).webList;

    return MineMenuSection(
      children: [
        // 动态webList列表
        ...webList.map(
          (final item) => MineMenuItem(
            iconPath: 'assets/images/mine/xieyi.png',
            title: item.name,
            onTap: () => ref
                .read(mineControllerProvider.notifier)
                .openWebPage(item.url, item.name),
          ),
        ),
        // 代理资质
        MineMenuItem(
          iconPath: 'assets/images/mine/xieyi.png',
          title: S.current.licenseTypeTitle,
          showDivider: false,
          onTap: () =>
              ref.read(mineControllerProvider.notifier).openLicenseType(),
        ),
      ],
    );
  }
}
