import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/mine/pages/mine/mine_controller_provider.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_menu_item.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_menu_section.dart';
import 'package:user_app/generated/l10n.dart';

/// 客服菜单区域
class MineServiceMenu extends ConsumerWidget {
  /// 构造函数
  const MineServiceMenu({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        // 电话客服菜单
        MineMenuSection(
          children: [
            // 本地客服
            Consumer(
              builder: (context, ref, child) {
                final state = ref.watch(mineControllerProvider);
                final servicePhone = state.servicePhone;
                if (servicePhone == null || servicePhone.isEmpty) {
                  return const SizedBox();
                }
                return MineMenuItem(
                  iconPath: 'assets/images/mine/tel.png',
                  title: S.current.local_area,
                  trailing: Text(
                    servicePhone,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey,
                    ),
                  ),
                  onTap: () {
                    ref
                        .read(mineControllerProvider.notifier)
                        .makePhoneCall(servicePhone);
                  },
                );
              },
            ),

            // 总部热线
            MineMenuItem(
              iconPath: 'assets/images/mine/tel.png',
              title: S.current.general,
              trailing: Text(
                '400-1111-990',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey,
                ),
              ),
              showDivider: false,
              onTap: () {
                ref
                    .read(mineControllerProvider.notifier)
                    .makePhoneCall('400-1111-990');
              },
            ),
          ],
        ),

        // 在线客服菜单
        MineMenuSection(
          children: [
            MineMenuItem(
              iconPath: 'assets/images/mine/suggestions.png',
              title: S.current.about_suggestions,
              showDivider: false,
              onTap: () {
                ref.read(mineControllerProvider.notifier).openCustomerService();
              },
            ),
          ],
        ),
      ],
    );
  }
}
