import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/features/mine/pages/mine/mine_controller_provider.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart';
import 'package:user_app/routes/paths.dart';

/// 用户信息头部
class MineUserHeader extends ConsumerWidget {
  /// 构造函数
  const MineUserHeader({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final userInfo = ref.watch(mineControllerProvider.select((final value) => value.userInfo));
    final isLoggedIn = ref.watch(isLoggedInProvider);
    return GestureDetector(
      onTap: () {
        // 跳转到个人中心详情页
        context.push(AppPaths.profilePage);
      },
      child: Column(
        children: [
          Container(
            width: 70.w,
            height: 70.w,
            margin: EdgeInsets.only(bottom: 5.h, top: 10.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(65.r),
              color: Colors.black.withAlpha(38),
            ),
            child: userInfo?.avatar != null && isLoggedIn
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(65.r),
                    child: Image.network(
                      userInfo?.avatar ?? '',
                      width: 70.w,
                      height: 70.w,
                      fit: BoxFit.cover,
                      errorBuilder:
                          (final context, final error, final stackTrace) {
                        return Image.asset(
                          'assets/images/mine/user.png',
                          width: 70.w,
                          height: 70.w,
                          fit: BoxFit.cover,
                        );
                      },
                    ),
                  )
                : Image.asset(
                    'assets/images/mine/user.png',
                    width: 70.w,
                    height: 70.w,
                    fit: BoxFit.cover,
                  ),
          ),
          if (isLoggedIn)
            Text(
              userInfo?.name ?? S.current.sign_in,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
            )
          else
            Text(
              S.current.sign_in,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
        ],
      ),
    );
  }
}
