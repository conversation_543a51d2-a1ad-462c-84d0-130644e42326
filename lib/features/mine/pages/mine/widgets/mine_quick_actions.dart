import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/dialogs/login_confirm_dialog.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart';
import 'package:user_app/routes/paths.dart';

/// 快捷操作区
class MineQuickActions extends ConsumerWidget {
  /// 构造函数
  const MineQuickActions({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 6.h),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildQuickAction(
            path: "assets/images/mine/integral.png",
            label: S.current.about_integral,
            onTap: () => ref.read(isLoggedInProvider) ? context.push(AppPaths.integralPage) : LoginConfirmDialog.show(context, onConfirm: () => context.push(AppPaths.integralPage)),
          ),
          _buildQuickAction(
            path: "assets/images/mine/addr.png",
            label: S.current.about_addr,
            onTap: () => ref.read(isLoggedInProvider) ? context.push(AppPaths.myAddressPage) : LoginConfirmDialog.show(context, onConfirm: () => context.push(AppPaths.myAddressPage)),
          ),
          _buildQuickAction(
            path: "assets/images/mine/like.png",
            label: S.current.about_like,
            onTap: () => ref.read(isLoggedInProvider) ? context.push(AppPaths.collectPage) : LoginConfirmDialog.show(context, onConfirm: () => context.push(AppPaths.collectPage)),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAction({
    /// 图标
    required final String path,

    /// 标签
    required final String label,

    /// 点击事件
    required final VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(path, width: 35.w, height: 35.h),
          SizedBox(height: 6.h),
          Text(
            label,
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
