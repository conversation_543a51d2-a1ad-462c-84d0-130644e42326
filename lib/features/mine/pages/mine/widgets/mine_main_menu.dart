import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/widgets/dialogs/login_confirm_dialog.dart';
import 'package:user_app/features/mine/pages/mine/mine_controller_provider.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_menu_item.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_menu_section.dart';
import 'package:user_app/features/mine/providers/dots_provider.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart';
import 'package:user_app/routes/paths.dart';

/// 主菜单区域
class MineMainMenu extends ConsumerWidget {
  /// 构造函数
  const MineMainMenu({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return MineMenuSection(
      children: [
        /// 红包
        // MineMenuItem(
        //   iconPath: 'assets/images/mine/packet_icon.png',
        //   title: S.current.red_packet_page_title,
        //   onTap: () => context.push(AppPaths.redPacketPage),
        // ),

        /// 优惠券
        Consumer(
          builder: (final context, final ref, final child) {
            final newCoupons = ref.watch(dotsProvider).coupon;
            return MineMenuItem(
              iconPath: 'assets/images/mine/courtesy-icon.svg',
              title: S.current.courtesy_page_title,
              onTap: () => ref.read(isLoggedInProvider)
                  ? context.push(AppPaths.courtesyPage)
                  : LoginConfirmDialog.show(context,
                      onConfirm: () => context.push(AppPaths.courtesyPage)),
              showRedDot: newCoupons > 0,
            );
          },
        ),

        // /// 下雪活动
        // Consumer(
        //   builder: (final context, final ref, final child) {
        //     final dotsData = ref.watch(dotsProvider);
        //     return dotsData.snowAmount > 0
        //         ? MineMenuItem(
        //             iconPath:
        //                 'https://acdn.mulazim.com/wechat_mini/img/snow/mini_snow.png',
        //             title: S.current.snow_rank,
        //             amount: "￥${FormatUtil.formatAmount(dotsData.snowAmount)}",
        //             onTap: () {
        //               context.push(AppPaths.activityOrderList);
        //             },
        //           )
        //         : SizedBox.shrink();
        //   },
        // ),

        // /// 214活动
        // Consumer(
        //   builder: (final context, final ref, final child) {
        //     final dotsData = ref.watch(dotsProvider);
        //     return dotsData.amount214 > 0
        //         ? MineMenuItem(
        //             iconPath:
        //                 'https://acdn.mulazim.com/wechat_mini/img/lover/like_active.png',
        //             title: S.current.lover_title,
        //             amount: "￥${FormatUtil.formatAmount(dotsData.amount214)}",
        //             onTap: () {
        //               context.push(AppPaths.activityOrderList);
        //             },
        //           )
        //         : SizedBox.shrink();
        //   },
        // ),

        /// 订单排名活动（3.8妇女节）
        Consumer(
          builder: (final context, final ref, final child) {
            final dotsData = ref.watch(dotsProvider);
            return dotsData.orderRankingActivityTitle.isNotEmpty
                ? MineMenuItem(
                    iconPath:
                        'https://acdn.mulazim.com/wechat_mini/img/orderRanking/my.png',
                    title: dotsData.orderRankingActivityTitle,
                    onTap: () {
                      ref.read(isLoggedInProvider)
                          ? context.push(AppPaths.orderRankingMy)
                          : LoginConfirmDialog.show(context,
                              onConfirm: () =>
                                  context.push(AppPaths.orderRankingMy));
                    },
                  )
                : SizedBox.shrink();
          },
        ),

        /// 活动订单列表 @depracted
        // Consumer(
        //   builder: (final context, final ref, final child) {
        //     final newCoupons = ref.watch(dotsControllerProvider).coupon;
        //     return MineMenuItem(
        //       iconPath:
        //           'https://acdn.mulazim.com/wechat_mini/img/lottery/myIcon.png',
        //       title: S.current.active_order_list,
        //       onTap: () {
        //         context.push(AppPaths.activityOrderList);
        //       },
        //     );
        //   },
        // ),

        /// 消息中心
        MineMenuItem(
          iconPath: 'assets/images/mine/comment.png',
          title: S.current.chat_room_list,
          onTap: () {
            final currentLocation = AppContext().navigatorKey.currentState;
            if (currentLocation == AppPaths.chatListPage) {
              return;
            }
            ref.read(isLoggedInProvider)
                ? context.push(AppPaths.chatListPage)
                : LoginConfirmDialog.show(
                    context,
                    onConfirm: () => context.push(AppPaths.chatListPage),
                  );
          },
        ),

        /// 我要当代理
        MineMenuItem(
          iconPath: 'assets/images/mine/agent.png',
          title: S.current.nov_be_proxy,
          onTap: () {
            context.push(
              AppPaths.webViewPage,
              extra: {
                'url': UrlConstants.supportUrl,
                'title': S.current.nov_be_proxy,
              },
            );
          },
        ),

        /// 商家入驻
        MineMenuItem(
          iconPath: 'assets/images/mine/restaurant.png',
          title: S.current.about_restaurant,
          onTap: () {
            context.push(AppPaths.addShopPage);
          },
        ),

        /// 我当配送员
        MineMenuItem(
          iconPath: 'assets/images/mine/shipment.png',
          title: S.current.nov_be_agent,
          onTap: () {
            context.push(
              AppPaths.webViewPage,
              extra: {
                'url': UrlConstants.agentUrl,
                'title': S.current.nov_be_agent,
              },
            );
          },
        ),

        /// 我的评论
        MineMenuItem(
          iconPath: 'assets/images/mine/evaluate.png',
          title: S.current.my_comment,
          onTap: () {
            ref.read(isLoggedInProvider)
                ? context.push(AppPaths.myEvaluatePage)
                : LoginConfirmDialog.show(context,
                    onConfirm: () => context.push(AppPaths.myEvaluatePage));
          },
        ),

        /// 分享
        MineMenuItem(
          iconPath: 'assets/images/mine/share.png',
          title: S.current.about_share,
          showDivider: false,
          onTap: () {
            ref.read(mineControllerProvider.notifier).shareApp();
          },
        ),
      ],
    );
  }
}
