import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/dialogs/confirm_dialog.dart';
import 'package:user_app/features/mine/pages/mine/mine_controller_provider.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart';

/// 退出登录按钮
class MineLogoutButton extends ConsumerWidget {
  /// 构造函数
  const MineLogoutButton({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final isLoggedIn = ref.watch(isLoggedInProvider);

    if (!isLoggedIn) {
      return const SizedBox();
    }

    return Container(
      margin: EdgeInsets.only(
        left: 8.w,
        right: 8.w,
        bottom: 8.h,
        top: 4.h,
      ),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () async {
          final shouldLogout = await ConfirmDialog.show(
            context,
            title: S.current.dialog_title_info,
            content: S.current.dialog_text_login_out,
            confirmText: S.current.dialog_text_yes,
            cancelText: S.current.dialog_text_no,
            // onConfirm: () {
            //   // 对话框关闭时会自动调用
            // },
          );

          // 如果用户确认退出，则执行退出操作
          if (shouldLogout == true) {
            await ref.read(mineControllerProvider.notifier).logout();
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          foregroundColor: Colors.red,
          padding: EdgeInsets.symmetric(vertical: 14.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
          elevation: 0,
          shadowColor: Colors.transparent,
        ),
        child: Text(
          S.current.logout,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: Colors.red,
          ),
        ),
      ),
    );
  }
}
