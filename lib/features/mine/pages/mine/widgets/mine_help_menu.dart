import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/features/mine/pages/mine/mine_controller_provider.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_menu_item.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_menu_section.dart';
import 'package:user_app/generated/l10n.dart';

/// 帮助菜单区域
class MineHelpMenu extends ConsumerWidget {
  /// 构造函数
  const MineHelpMenu({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MineMenuSection(
      children: [
        //如何从美滋来平台下单？
        MineMenuItem(
          iconPath: 'assets/images/mine/help_order.png',
          title: S.current.how_to_order,
          onTap: () =>
              ref.read(mineControllerProvider.notifier).openHowToOrder(),
        ),
        MineMenuItem(
          iconPath: 'assets/images/mine/help.png',
          title: S.current.about_help,
          onTap: () => ref.read(mineControllerProvider.notifier).openHelp(),
        ),
        MineMenuItem(
          iconPath: 'assets/images/mine/xieyi.png',
          title: S.current.about_info_syxy,
          onTap: () =>
              ref.read(mineControllerProvider.notifier).openUserAgreement(),
        ),
      ],
    );
  }
}
