import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/prefect_image_width.dart';

/// 菜单项
class MineMenuItem extends StatelessWidget {
  /// 图标
  final String iconPath;

  /// 标题
  final String title;

  /// 点击回调
  final VoidCallback onTap;

  /// 图标颜色
  final Color? iconColor;

  /// 是否显示底部分隔线
  final bool showDivider;

  /// 右侧自定义组件，如果提供则替换默认的箭头图标
  final Widget? trailing;

  /// 是否显示红点
  final dynamic showRedDot;

  /// 活动获取到的金额
  final String? amount;

  // 缓存箭头图标
  static final _defaultTrailing = Icon(
    Icons.chevron_right,
    size: 25.sp,
    color: AppColors.grayColor,
  );

  /// 构造函数
  const MineMenuItem({
    super.key,
    required this.iconPath,
    required this.title,
    required this.onTap,
    this.iconColor,
    this.showDivider = true,
    this.showRedDot = false,
    this.amount,
    this.trailing,
  });

  @override
  Widget build(final BuildContext context) {
    return RepaintBoundary(
      child: InkWell(
        onTap: onTap,
        // 确保水波纹效果在圆角内
        borderRadius: showDivider
            ? null
            : BorderRadius.vertical(bottom: Radius.circular(5.r)),
        child: Row(
          children: [
            // 左侧图标区域，无分隔线
            _buildIconSection(),

            // 右侧内容区域，带分隔线
            Expanded(
              child: Container(
                padding: EdgeInsets.only(top: 16.h, bottom: 16.h, left: 10.w),
                decoration: showDivider
                    ? BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: Colors.grey.shade200,
                            width: 0.5,
                          ),
                        ),
                      )
                    : null,
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18.sp,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(right: 16.w),
                      child: Row(
                        children: [
                          if (showRedDot)
                            Container(
                              width: 12.w,
                              height: 12.w,
                              margin: EdgeInsets.only(right: 4.w),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                                border:
                                    Border.all(color: Colors.white, width: 1.h),
                              ),
                            ),
                          if (amount != null && amount!.isNotEmpty)
                            Text(
                              style: TextStyle(
                                color: Colors.red,
                                fontSize: 14.sp,
                              ),
                              amount ?? "",
                            ),
                          trailing ?? _defaultTrailing,
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建图标部分
  Widget _buildIconSection() {
    return Container(
      width: 40.w,
      margin: EdgeInsets.symmetric(horizontal: 10.w),
      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
      alignment: Alignment.center,
      child: _buildIcon(),
    );
  }

  /// 构建图标，根据不同类型优化
  Widget _buildIcon() {
    // 网络图片
    if (iconPath.startsWith('http')) {
      return PrefectImageWidth(
        imageUrl: iconPath,
        width: 30.w,
      );
    }

    // SVG图片
    if (iconPath.toLowerCase().endsWith('.svg')) {
      return SvgPicture.asset(
        iconPath,
        width: 25.w,
        height: 25.h,
      );
    }

    // 本地图片
    return Image.asset(
      iconPath,
      width: 30.w,
      height: 30.h,
    );
  }
}

/// 用于预加载菜单图标的工具类
class MineMenuItemPreloader {
  /// 预加载多个图标
  static Future<void> preloadIcons(
    final BuildContext context,
    final List<String> iconPaths,
  ) async {
    for (final iconPath in iconPaths) {
      if (iconPath.startsWith('http')) {
        // 预加载网络图片
        await precacheImage(NetworkImage(iconPath), context);
      } else if (!iconPath.toLowerCase().endsWith('.svg')) {
        // 预加载本地图片，SVG不需要预加载
        await precacheImage(AssetImage(iconPath), context);
      }
    }
  }
}
