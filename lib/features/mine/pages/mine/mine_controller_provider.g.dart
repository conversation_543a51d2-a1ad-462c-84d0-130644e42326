// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mine_controller_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$mineControllerHash() => r'855ec4b29aa79291655bd19796714b2734b69d70';

/// 个人中心控制器
///
/// Copied from [MineController].
@ProviderFor(MineController)
final mineControllerProvider =
    AutoDisposeNotifierProvider<MineController, MineState>.internal(
  MineController.new,
  name: r'mineControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mineControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MineController = AutoDisposeNotifier<MineState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
