import 'package:user_app/data/models/auth/auth_model.dart';
import 'package:user_app/data/models/mine/web_item_model.dart';

/// 个人中心页面状态
class MineState {
  /// 是否加载中
  final bool isLoading;

  /// 网页列表
  final List<WebItem> webList;

  /// 错误信息
  final String? error;

  /// 用户信息
  final UserInfo? userInfo;

  /// 本地客服电话
  final String? servicePhone;

  /// 应用版本号
  final String? version;

  /// 补丁版本号
  final int? patchNumber;

  /// 构建版本号
  final String? buildNumber;

  /// 构造函数
  const MineState({
    this.isLoading = false,
    this.webList = const [],
    this.error,
    this.userInfo,
    this.servicePhone,
    this.version,
    this.patchNumber,
    this.buildNumber,
  });

  /// 复制方法
  MineState copyWith({
    final bool? isLoading,
    final List<WebItem>? webList,
    final String? error,
    final UserInfo? userInfo,
    final String? servicePhone,
    final String? version,
    final int? patchNumber,
    final String? buildNumber,
  }) {
    return MineState(
      isLoading: isLoading ?? this.isLoading,
      webList: webList ?? this.webList,
      error: error ?? this.error,
      userInfo: userInfo ?? this.userInfo,
      servicePhone: servicePhone ?? this.servicePhone,
      version: version ?? this.version,
      patchNumber: patchNumber ?? this.patchNumber,
      buildNumber: buildNumber ?? this.buildNumber,
    );
  }
}
