import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/activity_order/order_ranking_model.dart';
import 'package:user_app/generated/l10n.dart';

/// 奖品项组件
class PrizeItemWidget extends StatelessWidget {
  final PrizeItem item;

  const PrizeItemWidget({
    final Key? key,
    required this.item,
  }) : super(key: key);

  @override
  Widget build(final BuildContext context) {
    final s = S.current;

    return SizedBox(
      width: 95.w,
      child: Column(
        children: [
          // 奖品图片
          Container(
            width: 95.w,
            height: 95.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(5.r),
              child: CachedNetworkImage(
                imageUrl: item.prizeImage,
                fit: BoxFit.cover,
                errorWidget: (final context, final url, final error) =>
                    const Icon(Icons.error),
              ),
            ),
          ),

          // 奖品名称
          Padding(
            padding: EdgeInsets.symmetric(vertical: 5.r),
            child: Text(
              item.prizeName,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),

          // 奖品排名
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: 5.w,
              vertical: 1.h,
            ),
            decoration: BoxDecoration(
              color: AppColors.redColor,
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (Localizations.localeOf(context).languageCode == "zh")
                  Text(
                    "第",
                    style: TextStyle(fontSize: 13.sp, color: Colors.white),
                  ),
                Text(
                  item.luckyUserIndex,
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: Colors.white,
                  ),
                ),
                Text(
                  s.order,
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
