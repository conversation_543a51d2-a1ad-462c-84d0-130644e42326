import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/routes/paths.dart';
import 'package:user_app/generated/l10n.dart';

/// 奖品标题头部组件
class PrizeHeader extends StatelessWidget {
  const PrizeHeader({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final s = S.current;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          s.snowAward,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        GestureDetector(
          onTap: () => context.replace(AppPaths.orderRankingPrizePage),
          child: Row(
            children: [
              CachedNetworkImage(
                imageUrl:
                    "https://acdn.mulazim.com/wechat_mini/img/orderRanking/orderLevel.png",
                width: 20.w,
                height: 20.h,
              ),
              SizedBox(width: 5.w),
              Text(
                s.eleven_lucky_user,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.redColor,
                ),
              ),
              Icon(
                Icons.chevron_right,
                size: 13.sp,
                color: AppColors.redColor,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
