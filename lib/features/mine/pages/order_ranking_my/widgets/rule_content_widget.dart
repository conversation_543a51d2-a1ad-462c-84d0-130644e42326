import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_html_table/flutter_html_table.dart';
import 'package:user_app/core/theme/app_colors.dart';

/// 通用规则内容展示组件
/// 用于显示HTML格式的规则内容
class RuleContentWidget extends StatelessWidget {
  /// HTML格式的规则内容
  final String rule;

  /// 内边距
  final EdgeInsetsGeometry? padding;

  /// 构造函数
  const RuleContentWidget({
    Key? key,
    required this.rule,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 处理HTML内容,确保表格正确显示
    final processedRule = rule.replaceAll(
      '<td',
      '<td style="border: 1px solid #000; padding: 0.5px;"',
    );

    return SingleChildScrollView(
      padding:
          padding ?? EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
      child: Html(
        data: processedRule,
        style: {
          "body": Style(
            fontSize: FontSize(14.sp),
            color: AppColors.textPrimaryColor,
            fontFamily: "UkijTuzTom",
          ),
          "p": Style(fontFamily: "UkijTuzTom"),
          "span": Style(fontFamily: "UkijTuzTom"),
          "td": Style(fontFamily: "UkijTuzTom"),
          "th": Style(fontFamily: "UkijTuzTom"),
          // "img": Style(
          //   alignment: Alignment.center,
          // ),
        },
        extensions: [
          TableHtmlExtension(),
        ],
      ),
    );
  }
}
