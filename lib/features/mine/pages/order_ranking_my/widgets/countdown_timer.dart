import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 极简高性能倒计时组件
class CountdownTimer extends StatefulWidget {
  final int remainTime;
  final Color backgroundColor;
  final Color timeItemBgColor;
  final EdgeInsetsGeometry padding;
  final VoidCallback? onTimeUp;
  final String? prefixText;
  final String? endText;
  /// 是否为维吾尔语布局
  final bool isUg;

  const CountdownTimer({
    final Key? key,
    required this.remainTime,
    this.backgroundColor = Colors.transparent,
    this.timeItemBgColor = Colors.white,
    this.padding = EdgeInsets.zero,
    this.onTimeUp,
    this.isUg = false,
    this.prefixText,
    this.endText,
  }) : super(key: key);

  @override
  State<CountdownTimer> createState() => _CountdownTimerState();
}

class _CountdownTimerState extends State<CountdownTimer>
    with SingleTickerProviderStateMixin {
  late int _remainSeconds;
  late Ticker _ticker;
  int _lastTickTime = 0;
  String? _dayText; // 缓存国际化字符串
  String? _endText;
  String? _prefixText;

  @override
  void initState() {
    super.initState();
    _remainSeconds = widget.remainTime;
    _cacheTexts();

    // 仅在需要时启动ticker
    if (_remainSeconds > 0) {
      _ticker = createTicker(_onTick)..start();
    } else {
      _ticker = createTicker(_onTick);
    }
  }

  void _cacheTexts() {
    _dayText = S.current.day;
    _endText = widget.endText ?? S.current.time_end;
    _prefixText = widget.prefixText ?? S.current.snowGameOverTips;
  }

  void _onTick(final Duration elapsed) {
    // 精确控制每秒更新一次
    final now = DateTime.now().millisecondsSinceEpoch;
    if (now - _lastTickTime >= 1000) {
      _lastTickTime = now;
      if (_remainSeconds > 0) {
        setState(() {
          _remainSeconds--;
        });

        if (_remainSeconds <= 0) {
          _ticker.stop();
          if (widget.onTimeUp != null) {
            widget.onTimeUp!();
          }
        }
      }
    }
  }

  @override
  void didUpdateWidget(final CountdownTimer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.remainTime != oldWidget.remainTime) {
      setState(() {
        _remainSeconds = widget.remainTime;
      });

      if (_remainSeconds > 0 && !_ticker.isActive) {
        _ticker.start();
      }
    }
  }

  @override
  void dispose() {
    _ticker.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    // 如果时间到，显示活动结束
    if (_remainSeconds <= 0) {
      return Container(
        padding: EdgeInsets.symmetric(vertical: 10.r),
        child: Center(
          child: Text(
            S.current.activityIsOver,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textPrimaryColor,
            ),
          ),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.r).add(widget.padding),
      height: 40.h,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 前缀文本
          Text(
            '$_prefixText ',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textPrimaryColor,
            ),
          ),

          // 倒计时部分（使用缓存绘制）
          CachedTimerDisplay(
            seconds: _remainSeconds,
            timeBoxColor: widget.timeItemBgColor,
            dayText: _dayText!,
          ),

          // 后缀文本
          SizedBox(width: 4.w),
          Text(
            _endText!,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }
}

/// 缓存优化的倒计时显示
class CachedTimerDisplay extends StatefulWidget {
  final int seconds;
  final Color timeBoxColor;
  final String dayText;

  const CachedTimerDisplay({
    final Key? key,
    required this.seconds,
    required this.timeBoxColor,
    required this.dayText,
  }) : super(key: key);

  @override
  State<CachedTimerDisplay> createState() => _CachedTimerDisplayState();
}

class _CachedTimerDisplayState extends State<CachedTimerDisplay> {
  // 缓存画笔和尺寸
  late Paint _boxPaint;
  late final TextStyle _numberStyle;
  late final TextStyle _colonStyle;
  late final TextStyle _dayStyle;

  @override
  void initState() {
    super.initState();
    _initStyles();
  }

  void _initStyles() {
    _boxPaint = Paint()..style = PaintingStyle.fill;

    // 预先创建文本样式以避免重复创建
    _numberStyle = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.bold,
      color: AppColors.redColor,
    );

    _colonStyle = TextStyle(
      fontSize: 16.sp,
      color: AppColors.textPrimaryColor,
    );

    _dayStyle = TextStyle(
      fontSize: 14.sp,
      color: AppColors.textPrimaryColor,
    );
  }

  @override
  void didUpdateWidget(final CachedTimerDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.timeBoxColor != oldWidget.timeBoxColor) {
      // 仅在颜色变化时更新Paint
      _boxPaint.color = widget.timeBoxColor;
    }
  }

  @override
  Widget build(final BuildContext context) {
    // 更新画笔颜色
    _boxPaint.color = widget.timeBoxColor;
    final isUg = Localizations.localeOf(context).languageCode == 'en';

    // 计算时间
    final duration = Duration(seconds: widget.seconds);
    final days = duration.inDays;
    final hours = duration.inHours % 24;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    // 计算总宽度
    double totalWidth = 0;
    if (days > 0) {
      totalWidth += 31.w + 4.w + 28.w + 4.w;
    }
    totalWidth += (31.w * 3) + (8.w * 2); // 时分秒 + 冒号

    // 使用RepaintBoundary隔离重绘
    return RepaintBoundary(
      child: CustomPaint(
        size: Size(totalWidth, 26.h),
        isComplex: true, // 告知框架这是复杂绘制
        painter: _OptimizedTimerPainter(
          days: days,
          hours: hours,
          minutes: minutes,
          seconds: seconds,
          boxPaint: _boxPaint,
          numberStyle: _numberStyle,
          colonStyle: _colonStyle,
          dayStyle: _dayStyle,
          dayText: widget.dayText,
          isUg: isUg, // 传递isUg参数
        ),
      ),
    );
  }
}

/// 高度优化的倒计时画笔
class _OptimizedTimerPainter extends CustomPainter {
  final int days;
  final int hours;
  final int minutes;
  final int seconds;
  final Paint boxPaint;
  final TextStyle numberStyle;
  final TextStyle colonStyle;
  final TextStyle dayStyle;
  final String dayText;
  final bool isUg;

  // 缓存格式化后的时间文本，避免每次重新格式化
  late final String _daysText;
  late final String _hoursText;
  late final String _minutesText;
  late final String _secondsText;

  // 预先创建并复用TextPainter对象
  final TextPainter _textPainter = TextPainter(
    textDirection: TextDirection.ltr,
    textAlign: TextAlign.center,
  );

  _OptimizedTimerPainter({
    required this.days,
    required this.hours,
    required this.minutes,
    required this.seconds,
    required this.boxPaint,
    required this.numberStyle,
    required this.colonStyle,
    required this.dayStyle,
    required this.dayText,
    this.isUg = false,
  })  : _daysText = days.toString().padLeft(2, '0'),
        _hoursText = hours.toString().padLeft(2, '0'),
        _minutesText = minutes.toString().padLeft(2, '0'),
        _secondsText = seconds.toString().padLeft(2, '0');

  @override
  void paint(final Canvas canvas, final Size size) {
    double x = 0;
    final boxWidth = 31.w;
    final boxHeight = 26.h;
    final radius = 5.r;

    if (isUg) {
      // 维吾尔语模式：首先绘制时分秒，然后绘制天（如果有）
      // 只有在天数大于0时，才在最后附加天数

      // 1. 绘制小时
      _drawTimeBox(canvas, x, 0, boxWidth, boxHeight, radius);
      _drawText(canvas, _hoursText, x, 0, boxWidth, boxHeight, numberStyle);
      x += boxWidth;

      // 2. 绘制冒号
      _drawText(canvas, ':', x, 0, 8.w, boxHeight, colonStyle);
      x += 8.w;

      // 3. 绘制分钟
      _drawTimeBox(canvas, x, 0, boxWidth, boxHeight, radius);
      _drawText(canvas, _minutesText, x, 0, boxWidth, boxHeight, numberStyle);
      x += boxWidth;

      // 4. 绘制冒号
      _drawText(canvas, ':', x, 0, 8.w, boxHeight, colonStyle);
      x += 8.w;

      // 5. 绘制秒数
      _drawTimeBox(canvas, x, 0, boxWidth, boxHeight, radius);
      _drawText(canvas, _secondsText, x, 0, boxWidth, boxHeight, numberStyle);
      x += boxWidth + 4.w;

      // 6. 绘制天数（如果有）- 放在最后
      if (days > 0) {
        // 先绘制天数文案
        _drawText(canvas, dayText, x, 0, 24.w, boxHeight, dayStyle);
        x += 24.w + 4.w;
        
        // 再绘制天数数值框
        _drawTimeBox(canvas, x, 0, boxWidth, boxHeight, radius);
        _drawText(canvas, _daysText, x, 0, boxWidth, boxHeight, numberStyle);
      }
    } else {
      // 中文模式：首先绘制天（如果有），然后绘制时分秒

      // 1. 绘制天数（如果有）
      if (days > 0) {
        _drawTimeBox(canvas, x, 0, boxWidth, boxHeight, radius);
        _drawText(canvas, _daysText, x, 0, boxWidth, boxHeight, numberStyle);
        x += boxWidth + 4.w;

        _drawText(canvas, dayText, x, 0, 24.w, boxHeight, dayStyle);
        x += 24.w + 4.w;
      }

      // 2. 绘制小时
      _drawTimeBox(canvas, x, 0, boxWidth, boxHeight, radius);
      _drawText(canvas, _hoursText, x, 0, boxWidth, boxHeight, numberStyle);
      x += boxWidth;

      // 3. 绘制冒号
      _drawText(canvas, ':', x, 0, 8.w, boxHeight, colonStyle);
      x += 8.w;

      // 4. 绘制分钟
      _drawTimeBox(canvas, x, 0, boxWidth, boxHeight, radius);
      _drawText(canvas, _minutesText, x, 0, boxWidth, boxHeight, numberStyle);
      x += boxWidth;

      // 5. 绘制冒号
      _drawText(canvas, ':', x, 0, 8.w, boxHeight, colonStyle);
      x += 8.w;

      // 6. 绘制秒数
      _drawTimeBox(canvas, x, 0, boxWidth, boxHeight, radius);
      _drawText(canvas, _secondsText, x, 0, boxWidth, boxHeight, numberStyle);
    }
  }

  // 优化的时间框绘制
  void _drawTimeBox(final Canvas canvas, final double x, final double y, final double width,
      final double height, final double radius) {
    // 使用更高效的RRect绘制方式
    final rect = RRect.fromRectAndRadius(
      Rect.fromLTWH(x, y, width, height),
      Radius.circular(radius),
    );
    canvas.drawRRect(rect, boxPaint);
  }

  // 优化的文本绘制，复用TextPainter对象
  void _drawText(final Canvas canvas, final String text, final double x, final double y, final double width,
      final double height, final TextStyle style) {
    _textPainter
      ..text = TextSpan(
        text: text,
        style: style.copyWith(
          fontFamily: AppConstants.mainFont,
        ),
      )
      ..layout(minWidth: 0, maxWidth: width);

    final xPos = x + (width - _textPainter.width) / 2;
    final yPos = y + (height - _textPainter.height) / 2;
    _textPainter.paint(canvas, Offset(xPos, yPos));
  }

  @override
  bool shouldRepaint(final _OptimizedTimerPainter oldDelegate) {
    // 只在时间数字变化时重绘
    return oldDelegate.days != days ||
        oldDelegate.hours != hours ||
        oldDelegate.minutes != minutes ||
        oldDelegate.seconds != seconds ||
        oldDelegate.boxPaint.color != boxPaint.color ||
        oldDelegate.isUg != isUg;
  }
}
