import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/order_ranking_my_controller.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/order_button.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/prize_content.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/prize_header.dart';

/// 奖品列表组件（未中奖状态）
class PrizeList extends ConsumerWidget {
  const PrizeList({final Key? key}) : super(key: key);

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 使用select精确监听需要的状态
    final orderRanking = ref.watch(orderRankingMyControllerProvider
        .select((final state) => state.orderRanking));

    // 如果没有数据，不显示任何内容
    if (orderRanking == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(10.r),
      margin: EdgeInsets.only(bottom: 10.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        children: [
          // 标题
          const PrizeHeader(),

          // 奖品内容
          PrizeContent(orderRanking: orderRanking),

          // 下单按钮
          const OrderButton(),
        ],
      ),
    );
  }
}
