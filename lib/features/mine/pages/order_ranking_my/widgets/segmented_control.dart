import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/order_ranking_my_state.dart';

/// 分段控制器组件
class SegmentedControl extends StatefulWidget {
  final List<HeaderItem> items;
  final String current;
  final Function(String) onChange;
  final Color backgroundColor;
  final Color borderColor;
  final Color sliderColor;

  const SegmentedControl({
    super.key,
    required this.items,
    required this.current,
    required this.onChange,
    this.backgroundColor = Colors.transparent,
    this.borderColor = Colors.white,
    this.sliderColor = Colors.white,
  });

  @override
  State<SegmentedControl> createState() => _SegmentedControlState();
}

class _SegmentedControlState extends State<SegmentedControl> {
  late String _currentId;

  @override
  void initState() {
    super.initState();
    _currentId = widget.current;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40.h,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(
          color: widget.borderColor,
          width: 1.w,
        ),
      ),
      child: Row(
        children: widget.items.map((item) {
          final isSelected = item.id == _currentId;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                if (item.id != _currentId) {
                  setState(() {
                    _currentId = item.id;
                  });
                  widget.onChange(item.id);
                }
              },
              child: Container(
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Colors.white.withOpacity(0.2)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Text(
                  item.title,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14.sp,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
