import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/data/models/activity_order/order_ranking_model.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/order_ranking_my_controller.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/countdown_timer.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/prize_item.dart';
import 'package:user_app/generated/l10n.dart';

/// 奖品内容组件
class PrizeContent extends ConsumerWidget {
  final OrderRankingModel orderRanking;

  const PrizeContent({
    final Key? key,
    required this.orderRanking,
  }) : super(key: key);

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final s = S.current;
    final controller = ref.read(orderRankingMyControllerProvider.notifier);

    return Container(
      margin: EdgeInsets.only(top: 15.r),
      padding: EdgeInsets.all(10.r),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        children: [
          Text(
            s.order_qualification_message,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),

          // 排名标题和奖品列表
          _buildPrizeList(context),

          // 倒计时或活动结束提示
          CountdownTimer(
            remainTime: orderRanking.remainEndTime,
            onTimeUp: () => controller.onTimeUp(),
          ),
        ],
      ),
    );
  }

  // 奖品列表部分
  Widget _buildPrizeList(final BuildContext context) {

    return Container(
      margin: EdgeInsets.symmetric(vertical: 15.r),
      padding: EdgeInsets.all(10.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        children: [
          // 标题
          _buildTitle(context),

          SizedBox(height: 10.h),

          // 奖品滚动列表
          SizedBox(
            height: 125.h,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: orderRanking.prize.length,
              separatorBuilder: (final context, final index) => SizedBox(width: 7.5.w),
              itemBuilder: (final context, final index) {
                return PrizeItemWidget(item: orderRanking.prize[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  // 标题部分
  Widget _buildTitle(final BuildContext context) {
    final s = S.current;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.network(
          "https://acdn.mulazim.com/wechat_mini/img/orderRanking/title.png",
          width: 15.w,
          height: 15.h,
        ),
        SizedBox(width: 4.w),
        Text(
          s.order_ranking_title,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(width: 4.w),
        Image.network(
          "https://acdn.mulazim.com/wechat_mini/img/orderRanking/title.png",
          width: 15.w,
          height: 15.h,
        ),
      ],
    );
  }
}
