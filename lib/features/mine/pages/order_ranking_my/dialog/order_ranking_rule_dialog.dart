import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/rule_content_widget.dart';

/// 规则弹出框
class OrderRankingRuleDialog extends StatelessWidget {
  final String rule;
  final VoidCallback onClose;

  const OrderRankingRuleDialog({
    final Key? key,
    required this.rule,
    required this.onClose,
  }) : super(key: key);

  /// 显示规则对话框
  static Future<void> show(
    final BuildContext context, {
    required final String rule,
    required final VoidCallback onClose,
  }) async {
    return showDialog(
      context: context,
      builder: (final context) => OrderRankingRuleDialog(
        rule: rule,
        onClose: () {
          Navigator.of(context).pop();
          onClose();
        },
      ),
    );
  }

  @override
  Widget build(final BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxHeight: 0.7.sh,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题区域
            Container(
              padding: EdgeInsets.symmetric(vertical: 15.h),
              child: Text(
                S.current.activity_rules,
                style: TextStyle(
                  fontSize: 17.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimaryColor,
                ),
              ),
            ),

            // 规则内容区域 - 使用通用组件
            Flexible(
              child: RuleContentWidget(rule: rule),
            ),

            // 按钮区域
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(15.r),
              child: ElevatedButton(
                onPressed: onClose,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4CAF50), // 绿色按钮
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5.r),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  S.current.got_it,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.normal,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
