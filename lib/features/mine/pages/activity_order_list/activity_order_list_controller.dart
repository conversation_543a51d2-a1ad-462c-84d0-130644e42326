import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/data/models/activity_order/lottery_detail_model.dart';
import 'package:user_app/data/models/activity_order/lottery_result_model.dart';
import 'package:user_app/features/mine/services/activity_order_service.dart';
import 'package:user_app/main.dart';
import 'package:user_app/routes/paths.dart';
import 'activity_order_list_state.dart';

part 'activity_order_list_controller.g.dart';

/// 活动订单列表控制器
@riverpod

/// 活动订单列表控制器
class ActivityOrderListController extends _$ActivityOrderListController {
  @override
  ActivityOrderListState build() {
    // 初始化时加载订单列表
    Future.microtask(() {
      getOrderList();
    });
   
    // 监听登录状态，登录成功后重新加载数据
    ref.listen(isLoggedInProvider, (final previous, final next) {
      if (next) {
        Future.microtask(() => getOrderList());
      }
    });

    return const ActivityOrderListState();
  }

  /// 获取订单列表
  Future<void> getOrderList() async {
    state = state.copyWith(isLoading: true);
    try {
      final service = ref.read(activityOrderServiceProvider);
      final orderList = await service.getOrderList();
      state = state.copyWith(
        isLoading: false,
        orderList: orderList.data,
        isRefreshing: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
        isRefreshing: false,
      );
    }
  }

  /// 下拉刷新
  Future<void> refreshOrderList() async {
    state = state.copyWith(isRefreshing: true);
    try {
      final service = ref.read(activityOrderServiceProvider);
      final orderList = await service.getOrderList();
      state = state.copyWith(
        orderList: orderList.data,
        isRefreshing: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isRefreshing: false,
      );
    }
  }


  /// 导航到订单详情页
  void goToOrderDetail(final int id) {
    final context = AppContext().currentContext;
    if (context == null) return;

    context.push(AppPaths.prizeExchangePage, extra: {'id': id});
  }

  /// 显示抽奖弹窗
  Future<void> showLotteryModal(final int id) async {
    state = state.copyWith(currentId: id);
    try {
      final service = ref.read(activityOrderServiceProvider);
      final lotteryDetail = await service.getLotteryData(id);

      state = state.copyWith(
        lotteryDetail: lotteryDetail,
        canTurnItems: lotteryDetail.data?.tableInfo.canTurnItems,
      );

      // 显示抽奖弹窗
      _showLotteryDialog(lotteryDetail.data);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 显示抽奖弹窗
  void _showLotteryDialog(final LotteryDetailModel? lotteryDetail) {
    final context = AppContext().currentContext;
    if (context == null) return;

    // TODO: 实现显示抽奖弹窗逻辑
    // showDialog(
    //   context: context,
    //   builder: (context) => TurntableLotteryModal(
    //     lotteryDetail: lotteryDetail,
    //     onTurntableEnd: _handleTurntableEnd,
    //     onCancel: () => getOrderList(),
    //     onNoCount: _handleNoCount,
    //   ),
    // );
  }

  /// 处理抽奖结果
  Future<void> _handleTurntableEnd(final LotteryResultModel result) async {
    // 关闭抽奖弹窗
    final context = AppContext().currentContext;
    if (context == null) return;

    // 检查是否还有可抽奖的项目
    if (state.canTurnItems.length > 1) {
      final ids = state.canTurnItems
          .where((final id) => id != state.currentId)
          .toList();
      if (ids.isNotEmpty) {
        // 获取下一个抽奖详情
        await getLotteryData(ids[0], false);
      } else {
        // 没有更多抽奖机会
        _updateLotteryChance(false);
      }
    } else {
      // 没有更多抽奖机会
      _updateLotteryChance(false);
    }

    // 显示抽奖结果弹窗
    _showLotteryResultDialog(result);
  }

  /// 更新抽奖机会
  void _updateLotteryChance(final bool hasChance) {
    if (state.lotteryDetail == null) return;

    // TODO: 更新抽奖机会逻辑
  }

  /// 显示抽奖结果弹窗
  void _showLotteryResultDialog(final LotteryResultModel result) {
    final context = AppContext().currentContext;
    if (context == null) return;

    // TODO: 实现显示抽奖结果弹窗逻辑
  }

  /// 处理没有抽奖次数
  void _handleNoCount(final dynamic data) {
    final context = AppContext().currentContext;
    if (context == null) return;

    // TODO: 实现显示没有抽奖次数弹窗逻辑
  }

  /// 获取抽奖详情
  Future<void> getLotteryData(final int id, final bool showDialog) async {
    try {
      final service = ref.read(activityOrderServiceProvider);
      final lotteryDetail = await service.getLotteryData(id);

      state = state.copyWith(
        lotteryDetail: lotteryDetail,
        currentId: id,
        canTurnItems: lotteryDetail.data?.tableInfo.canTurnItems,
      );

      if (showDialog) {
        _showLotteryDialog(lotteryDetail.data);
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 显示分享海报
  void showPrizeSharePoster(final Map<String, dynamic> item) {
    final context = AppContext().currentContext;
    if (context == null) return;

    // TODO: 实现类似小程序中 #lottery-poster-image 组件的功能
    // 在这里仅显示分享海报，不调用API
  }

  /// 去奖品兑换
  void goPrizeExchange(final int id) {
    final context = AppContext().currentContext;
    if (context == null) return;

    // 直接导航到奖品兑换页面，与小程序保持一致
    context.push(
      AppPaths.prizeExchangePage,
      extra: {'prize_id': id},
    );
  }

  void sharePrize(final id, final prizeId) {}
}
