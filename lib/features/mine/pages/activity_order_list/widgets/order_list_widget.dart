import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../activity_order_list_controller.dart';
import 'order_item_widget.dart';

/// 订单列表组件
class OrderListWidget extends ConsumerWidget {
  /// 构造函数
  const OrderListWidget({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 只关注订单列表数据变化
    final orderList = ref.watch(
      activityOrderListControllerProvider
          .select((final state) => state.orderList),
    );
    final isUg = Localizations.localeOf(context).languageCode == 'en';

    // 如果没有数据，返回空容器
    if (orderList?.items == null || orderList!.items.isEmpty) {
      return const SizedBox();
    }

    // 使用ListView.builder构建订单列表
    // shrinkWrap: true 确保ListView适应内容高度
    // physics: NeverScrollableScrollPhysics() 禁用ListView的滚动，因为外层已有ScrollView
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      // 设置列表内边距
      padding: EdgeInsets.all(10.r),
      // 设置列表项数量
      itemCount: orderList.items.length,
      // 构建每个列表项
      itemBuilder: (final context, final index) {
        final item = orderList.items[index];
        // 使用单独的组件展示每个订单项
        return GestureDetector(
          // 点击订单项时跳转到订单详情页
          onTap: () {
            ref
                .read(activityOrderListControllerProvider.notifier)
                .goToOrderDetail(item.id);
          },
          // 使用OrderItemWidget展示订单项内容
          child: OrderItemWidget(
            item: item,
            isUg: isUg,
          ),
        );
      },
    );
  }
}
