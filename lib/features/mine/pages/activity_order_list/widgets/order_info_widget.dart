import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/generated/l10n.dart';

/// 订单信息组件
class OrderInfoWidget extends StatelessWidget {
  /// 订单数据
  final dynamic item;

  /// 是否为维吾尔语
  final bool isUg;

  /// 订单信息组件构造函数
  /// [item] 订单数据
  /// [isUg] 是否为维吾尔语
  const OrderInfoWidget({
    super.key,
    required this.item,
    required this.isUg,
  });

  @override
  Widget build(final BuildContext context) {
    final s = S.current;

    // 检查是否需要显示订单信息
    // 确保显示条件与微信小程序匹配
    if (!(item.type == 1 ||
        item.type == 2 ||
        item.type == 3 ||
        item.type == 4)) {
      return const SizedBox();
    }

    // 根据类型选择标题
    String titleText = s.info_title;
    if (item.type == 3 || item.type == 4) {
      titleText = s.share_info_title; // "分享人"/"پۇرسەتكە ئېرىشتۈرگۈچى"
    }

    // 检查是否需要显示底部边框 - 只有当没有按钮时才显示
    final bool hasActionButtons = item.state == 1 ||
        (item.state == 2 && item.prizeId > 0) ||
        item.state == 5 ||
        item.prizeId > 0;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.r, vertical: 10.r),
      margin: EdgeInsets.only(bottom: 5.r),
      decoration: BoxDecoration(
        border: Border(
          bottom: hasActionButtons
              ? BorderSide.none
              : BorderSide(
                  color: AppColors.dividerColor,
                  width: 0.5.h,
                ),
        ),
      ),
      child: Column(
        crossAxisAlignment:
            isUg ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // 信息标题和状态
          Row(
            textDirection: isUg ? TextDirection.rtl : TextDirection.ltr,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                titleText,
                style: TextStyle(
                  fontSize: 18.sp, // 微信小程序中该字段为18px
                  color: AppColors.textPrimaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              // 状态名称（仅当state==2时显示）
              if (item.state == 2)
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 10.r, vertical: 3.r),
                  decoration: BoxDecoration(
                    color: const Color(0xFFD9FFE9), // 微信小程序中的背景色
                    borderRadius:
                        BorderRadius.circular(15.r), // 30rpx / 2 = 15r
                  ),
                  child: Text(
                    item.stateName,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF15C45B), // 微信小程序中的颜色
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 7.h), // 14rpx / 2 = 7h，与微信小程序row-gap一致

          // 根据订单类型显示不同信息
          if (item.type == 1 && item.lotteryOrder != null) ...[
            _buildInfoItem(s.order_no, item.lotteryOrder!.orderId),
            _buildInfoItem(
              s.Lottery_order_detail_pay,
              '￥${FormatUtil.formatPrice(item.lotteryOrder!.price)}',
            ),
          ],
          if (item.type == 2 && item.foodOrder != null) ...[
            _buildInfoItem(s.order_no, item.foodOrder!.orderId),
            _buildInfoItem(
              s.lottery_order_pay,
              '￥${FormatUtil.formatPrice(item.foodOrder!.price)}',
            ),
          ],
          if ((item.type == 3 || item.type == 4) &&
              item.sourceUser != null) ...[
            _buildInfoItem(s.share_user_name, item.sourceUser!.name),
            _buildInfoItem(s.mobile_num, item.sourceUser!.mobile),
          ],
        ],
      ),
    );
  }

  /// 构建信息项
  Widget _buildInfoItem(final String label, final String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 7.h), // 14rpx / 2 = 7h
      child: Row(
        textDirection: isUg ? TextDirection.rtl : TextDirection.ltr,
        mainAxisAlignment:
            MainAxisAlignment.spaceBetween, // 对应小程序的space-between
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF979797), // 微信小程序中的text-key颜色
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
