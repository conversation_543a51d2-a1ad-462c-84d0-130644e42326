import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 订单头部组件
class OrderHeaderWidget extends StatelessWidget {
  /// 订单数据
  final dynamic item;

  /// 是否为维吾尔语
  final bool isUg;

  /// 订单头部组件构造函数
  /// [item] 订单数据
  /// [isUg] 是否为维吾尔语
  const OrderHeaderWidget({
    super.key,
    required this.item,
    required this.isUg,
  });

  @override
  Widget build(final BuildContext context) {
    final s = S.current;
    String title = '';

    // 根据类型确定标题
    switch (item.type) {
      case 1:
        title = s.lottery_history_pay_title;
        break;
      case 2:
        title = s.lottery_history_order_title;
        break;
      case 3:
      case 4:
        title = s.lottery_history_share_title;
        break;
    }

    return Container(
      padding: EdgeInsets.all(10.r),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.dividerColor,
            width: 0.5.h,
          ),
        ),
      ),
      margin: EdgeInsets.only(bottom: 10.r),
      child: Row(
        textDirection: isUg ? TextDirection.rtl : TextDirection.ltr,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textPrimaryColor,
              fontWeight: FontWeight.normal,
            ),
          ),
          Text(
            item.createdAt,
            style: TextStyle(
              fontSize: 16.sp, // 微信小程序中日期字体大小与标题一致
              color: const Color(0xFF8E8E8E), // 微信小程序中的text_grey颜色
            ),
            textDirection: TextDirection.ltr,
          ),
        ],
      ),
    );
  }
}
