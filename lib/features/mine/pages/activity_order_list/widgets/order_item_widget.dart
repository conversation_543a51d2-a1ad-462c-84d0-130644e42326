import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/data/models/activity_order/lottery_order_model.dart';
import 'package:user_app/features/mine/pages/activity_order_list/widgets/order_header_widget.dart';
import 'package:user_app/features/mine/pages/activity_order_list/widgets/prize_box_widget.dart';
import 'package:user_app/features/mine/pages/activity_order_list/widgets/prize_success_widget.dart';
import 'package:user_app/features/mine/pages/activity_order_list/widgets/order_info_widget.dart';
import 'package:user_app/features/mine/pages/activity_order_list/widgets/action_buttons_widget.dart';

/// 订单项组件
class OrderItemWidget extends ConsumerWidget {
  /// 订单数据
  final LotteryOrderItem item;

  /// 是否为维吾尔语
  final bool isUg;

  /// 订单项组件构造函数
  /// [item] 订单数据
  /// [isUg] 是否为维吾尔语
  const OrderItemWidget({
    super.key,
    required this.item,
    required this.isUg,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.r), // 微信小程序中的mb
      padding:
          EdgeInsets.symmetric(horizontal: 0.r, vertical: 0.r), // 微信小程序中的pd-1
      decoration: BoxDecoration(
        color: Colors.white, // 微信小程序中的bg-white
        borderRadius: BorderRadius.circular(5.r), // 微信小程序中的radius
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Directionality(
        textDirection: isUg ? TextDirection.rtl : TextDirection.ltr,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 订单头部（标题和时间）
            OrderHeaderWidget(item: item, isUg: isUg),

            // 按照微信小程序WXML中的顺序渲染组件
            // 1. 未抽奖状态显示奖品框
            if (item.state == 1) PrizeBoxWidget(item: item, isUg: isUg),

            // 2. 已抽奖状态显示奖品成功框
            if (item.state != 1 && item.prizeId > 0)
              PrizeSuccessWidget(item: item, isUg: isUg),

            // 3. 根据订单类型显示不同的信息区域
            OrderInfoWidget(item: item, isUg: isUg),

            // 4. 根据条件显示操作按钮区域 - 与微信小程序条件一致
            if (item.state == 1 ||
                (item.state == 2 && item.prizeId > 0) ||
                item.state == 5 ||
                item.prizeId > 0)
              ActionButtonsWidget(item: item, isUg: isUg),
          ],
        ),
      ),
    );
  }
}
