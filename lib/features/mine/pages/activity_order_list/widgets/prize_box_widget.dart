import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/generated/l10n.dart';

/// 奖品展示组件（未抽奖状态）
class PrizeBoxWidget extends StatelessWidget {
  final dynamic item;
  final bool isUg;

  const PrizeBoxWidget({
    super.key,
    required this.item,
    required this.isUg,
  });

  @override
  Widget build(final BuildContext context) {
    final s = S.current;

    return Container(
      padding: EdgeInsets.all(10.r),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.dividerColor,
            width: 0.5.h,
          ),
        ),
      ),
      child: Column(
        children: [
          // 遍历所有奖品项
          ...item.prizeItems
              .map((final prize) => _buildPrizeItem(prize, s))
              .toList(),
          // 显示优惠券
          _buildCouponItem(item, s),
        ],
      ),
    );
  }

  /// 构建奖品项
  Widget _buildPrizeItem(final prize, final s) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      child: Row(
        textDirection: isUg ? TextDirection.rtl : TextDirection.ltr,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: 80.w,
            height: 80.w,
            decoration: BoxDecoration(
              color: AppColors.baseBackgroundColor,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: CachedNetworkImage(
                    imageUrl: prize.prizeImg,
                    width: 80.w,
                    height: 80.w,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => const Center(
                      child: CircularProgressIndicator(),
                    ),
                    errorWidget: (context, url, error) =>
                        const Icon(Icons.error),
                  ),
                ),
                Positioned(
                  left: isUg ? null : 0,
                  right: isUg ? 0 : null,
                  top: 0,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 5.w,
                      vertical: 3.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.redColor,
                      borderRadius: BorderRadius.only(
                        bottomRight: Radius.circular(
                          isUg ? 0 : 10.r,
                        ),
                        bottomLeft: Radius.circular(
                          isUg ? 10.r : 0,
                        ),
                      ),
                    ),
                    child: Text(
                      '${prize.prizeLevel}-${s.elevenPrizeCardLavel}',
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 添加占位区域以实现between布局
          const Expanded(child: SizedBox()),
        ],
      ),
    );
  }

  /// 构建优惠券项
  Widget _buildCouponItem(final item, final s) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      child: Row(
        textDirection: isUg ? TextDirection.rtl : TextDirection.ltr,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: 80.w,
            height: 80.w,
            decoration: BoxDecoration(
              color: AppColors.baseBackgroundColor,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                // 优惠券背景图片
                ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: CachedNetworkImage(
                    imageUrl:
                        'https://acdn.mulazim.com/wechat_mini/img/lottery/coupon-image-${isUg ? 'ug' : 'zh'}.png',
                    width: 80.w,
                    height: 80.w,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => const Center(
                      child: CircularProgressIndicator(),
                    ),
                    errorWidget: (context, url, error) =>
                        const Icon(Icons.error),
                  ),
                ),
                // 优惠券提示文本
                Positioned(
                  left: isUg ? null : 0,
                  right: isUg ? 0 : null,
                  top: 0,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 5.w,
                      vertical: 3.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.redColor,
                      borderRadius: BorderRadius.only(
                        bottomRight: Radius.circular(
                          isUg ? 0 : 10.r,
                        ),
                        bottomLeft: Radius.circular(
                          isUg ? 10.r : 0,
                        ),
                      ),
                    ),
                    child: Text(
                      s.lottery_tips,
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                // 优惠券价格
                SizedBox(
                  width: 80.w,
                  height: 80.w,
                  child: FittedBox(
                    fit: BoxFit.contain,
                    child: Padding(
                      padding: EdgeInsets.all(5.r),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(height: 10.h), // 为顶部标签留出空间，减小了间距
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.baseline,
                            textBaseline: TextBaseline.alphabetic,
                            children: [
                              Text(
                                '￥',
                                style: TextStyle(
                                  fontSize: 14.sp, // 减小了字体大小
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                FormatUtil.formatPrice(item.couponPrice),
                                style: TextStyle(
                                  fontSize: 28.sp, // 减小了字体大小
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 2.h), // 减小了间距
                          // 根据语言显示不同格式的优惠券门槛
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 4.w,
                              vertical: 1.h,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                            child: isUg
                                ? FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      textDirection: TextDirection.rtl,
                                      children: [
                                        Text(
                                          '￥${FormatUtil.formatPrice(item.couponMinPrice)}',
                                          style: TextStyle(
                                            fontSize: 9.sp, // 减小了字体大小
                                            color: Colors.white,
                                          ),
                                        ),
                                        Text(
                                          s.lottery_coupon_min_price_1,
                                          style: TextStyle(
                                            fontSize: 9.sp, // 减小了字体大小
                                            color: Colors.white,
                                          ),
                                        ),
                                        Text(
                                          '￥${FormatUtil.formatPrice(item.couponPriceOne)}',
                                          style: TextStyle(
                                            fontSize: 9.sp, // 减小了字体大小
                                            color: Colors.white,
                                          ),
                                        ),
                                        Text(
                                          s.lottery_coupon_min_price_2,
                                          style: TextStyle(
                                            fontSize: 9.sp, // 减小了字体大小
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          s.lottery_coupon_min_price_1,
                                          style: TextStyle(
                                            fontSize: 9.sp, // 减小了字体大小
                                            color: Colors.white,
                                          ),
                                        ),
                                        Text(
                                          FormatUtil.formatPrice(
                                              item.couponMinPrice),
                                          style: TextStyle(
                                            fontSize: 9.sp, // 减小了字体大小
                                            color: Colors.white,
                                          ),
                                        ),
                                        Text(
                                          s.lottery_coupon_min_price_2,
                                          style: TextStyle(
                                            fontSize: 9.sp, // 减小了字体大小
                                            color: Colors.white,
                                          ),
                                        ),
                                        Text(
                                          '${FormatUtil.formatPrice(item.couponPriceOne)}元',
                                          style: TextStyle(
                                            fontSize: 9.sp, // 减小了字体大小
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 添加占位区域以实现between布局
          const Expanded(child: SizedBox()),
        ],
      ),
    );
  }
}
