import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 奖品成功组件（已抽奖状态）
class PrizeSuccessWidget extends StatelessWidget {
  final dynamic item;
  final bool isUg;

  const PrizeSuccessWidget({
    super.key,
    required this.item,
    required this.isUg,
  });

  @override
  Widget build(final BuildContext context) {
    final s = S.current;

    if (item.prize == null) return const SizedBox();

    return Container(
      padding: EdgeInsets.all(10.r),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.dividerColor,
            width: 0.5.h,
          ),
        ),
      ),
      margin: EdgeInsets.only(bottom: 10.r),
      child: Row(
        textDirection: isUg ? TextDirection.rtl : TextDirection.ltr,
        children: [
          // 奖品图片
          Container(
            width: 87.5.w,
            height: 87.5.w,
            decoration: BoxDecoration(
              color: AppColors.baseBackgroundColor,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: CachedNetworkImage(
                    imageUrl: item.prize!.prizeImg,
                    width: 87.5.w,
                    height: 87.5.w,
                    fit: BoxFit.cover,
                    placeholder: (final context, final url) => const Center(
                      child: CircularProgressIndicator(),
                    ),
                    errorWidget: (final context, final url, final error) =>
                        const Icon(Icons.error),
                  ),
                ),
                // 奖品等级标签
                Positioned(
                  left: isUg ? null : 0,
                  right: isUg ? 0 : null,
                  top: 0,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 5.w,
                      vertical: 3.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.redColor,
                      borderRadius: BorderRadius.only(
                        bottomRight: Radius.circular(
                          isUg ? 0 : 10.r,
                        ),
                        bottomLeft: Radius.circular(
                          isUg ? 10.r : 0,
                        ),
                      ),
                    ),
                    child: Text(
                      '${item.prize!.prizeLevel}-${s.elevenPrizeCardLavel}',
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: 10.w),
          // 奖品信息
          Expanded(
            child: Column(
              crossAxisAlignment:
                  isUg ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                Text(
                  item.prize!.prizeName,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
                Text(
                  item.prize!.prizeModel,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
