import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';
import '../activity_order_list_controller.dart';

/// 操作按钮组件
class ActionButtonsWidget extends ConsumerWidget {
  final dynamic item;
  final bool isUg;

  const ActionButtonsWidget({
    super.key,
    required this.item,
    required this.isUg,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final s = S.current;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.r, vertical: 5.r),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppColors.dividerColor,
            width: 1.h,
          ),
        ),
      ),
      child: Row(
        textDirection: isUg ? TextDirection.rtl : TextDirection.ltr,
        mainAxisAlignment: MainAxisAlignment.end, // 靠右对齐，对应微信小程序的flex-end
        children: [
          // 分享按钮
          if (item.prizeId > 0)
            TextButton.icon(
              onPressed: () {
                // 实现分享功能
                ref
                    .read(activityOrderListControllerProvider.notifier)
                    .sharePrize(item.id, item.prizeId);
              },
              icon: Image.asset(
                'assets/images/mine/poster.png',
                width: 15.w,
                height: 15.w,
              ),
              label: Text(s.goods_share_button),
              style: TextButton.styleFrom(
                backgroundColor: const Color(0xFF15C45B), // 微信小程序中的绿色
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                  horizontal: 10.w,
                  vertical: 5.h,
                ),
                textStyle: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.normal,
                  fontFamily: "UkijTuzTom",
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5.r), // 微信小程序中的radius-1
                ),
              ),
            ),

          SizedBox(width: 10.w),
          // 抽奖按钮
          if (item.state == 1)
            Padding(
              padding:
                  EdgeInsets.only(right: 10.r), // 增加按钮之间的间距，与小程序的gap: 10rpx对应
              child: TextButton(
                onPressed: () {
                  ref
                      .read(activityOrderListControllerProvider.notifier)
                      .showLotteryModal(item.id);
                },
                style: TextButton.styleFrom(
                  backgroundColor: const Color(0xFFFC2E45), // 微信小程序中的红色
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: 10.w,
                    vertical: 5.h,
                  ),
                  textStyle: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.normal,
                    fontFamily: "UkijTuzTom",
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5.r), // 微信小程序中的radius-1
                  ),
                ),
                child: Text(s.Lottery),
              ),
            ),

          // 奖品兑换按钮
          if (item.state == 2 && item.prizeId > 0)
            TextButton(
              onPressed: () {
                ref
                    .read(activityOrderListControllerProvider.notifier)
                    .goPrizeExchange(item.id);
              },
              child: Text(s.prize_xchange),
              style: TextButton.styleFrom(
                backgroundColor: const Color(0xFFFC2E45), // 微信小程序中的红色
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                  horizontal: 10.w,
                  vertical: 5.h,
                ),
                textStyle: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.normal,
                  fontFamily: "UkijTuzTom",
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5.r), // 微信小程序中的radius-1
                ),
              ),
            ),

          // 状态文本
          if (item.state == 5)
            Text(
              item.stateName,
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFFfc9c20), // 微信小程序中的橙色
              ),
            ),
        ],
      ),
    );
  }
}
