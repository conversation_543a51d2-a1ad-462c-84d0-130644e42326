import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/mine/pages/activity_order_list/activity_order_list_controller.dart';

/// 统计数据展示组件
class StatisticsBoxWidget extends ConsumerWidget {
  /// 构造函数
  const StatisticsBoxWidget({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 使用select只监听统计数据的变化
    final stats = ref.watch(
      activityOrderListControllerProvider
          .select((final state) => state.orderList),
    );
    final s = S.current;
    final isUg = Localizations.localeOf(context).languageCode == 'en';

    return Directionality(
      textDirection: isUg ? TextDirection.ltr : TextDirection.rtl,
      child: Container(
        width: double.infinity,
        height: 130.h, // 260rpx / 2 = 130.h
        color: AppColors.redColor,
        padding: EdgeInsets.all(5.r),
        margin: EdgeInsets.only(bottom: 55.h), // 200rpx / 2 = 100.h
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            // 统计数据卡片
            Positioned(
              left: 0,
              right: 0,
              top: 0,
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 5.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.r),
                  boxShadow: [],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min, // 确保Column只取所需的最小空间
                  children: [
                    // 使用Grid布局展示四种类型的统计数据
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      // 调整宽高比，使其稍微小一点，减少高度
                      childAspectRatio: 2.5,
                      padding: EdgeInsets.zero,
                      children: [
                        _buildGridStatItem(
                          stats?.typeOneCount ?? 0,
                          s.lottery_statis_text_1,
                          showRight: isUg ? true : false,
                        ),
                        _buildGridStatItem(
                          stats?.typeTwoCount ?? 0,
                          s.lottery_statis_text_2,
                          showRight: isUg ? false : true,
                        ),
                        _buildGridStatItem(
                          stats?.typeThreeCount ?? 0,
                          s.lottery_statis_text_3,
                          showBottom: false,
                        ),
                        _buildGridStatItem(
                          stats?.typeFourCount ?? 0,
                          s.lottery_statis_text_4,
                          showBottom: false,
                        ),
                      ],
                    ),
                    // 底部统计总数
                    Container(
                      margin: EdgeInsets.symmetric(
                          horizontal: 10.w, vertical: 10.h),
                      padding: EdgeInsets.symmetric(
                        vertical: 10.h,
                        horizontal: 10.w,
                      ), // 减少垂直内边距
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFE4E8),
                        borderRadius: BorderRadius.all(Radius.circular(10.r)),
                      ),
                      child: Row(
                        textDirection:
                            isUg ? TextDirection.rtl : TextDirection.ltr,
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildBottomSumItem(
                            s.lottery_statis_sum_1,
                            stats?.allCount ?? 0,
                          ),
                          _buildBottomSumItem(
                            s.lottery_statis_sum_2,
                            stats?.unusedCount ?? 0,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建网格中的统计项
  Widget _buildGridStatItem(
    final int count,
    final String text, {
    final bool showBottom = true,
    final bool showRight = true,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: showBottom
              ? BorderSide(
                  color: AppColors.dividerColor,
                  width: 0.5.h,
                )
              : BorderSide.none,
          right: showRight
              ? BorderSide(
                  color: AppColors.dividerColor,
                  width: 0.5.h,
                )
              : BorderSide.none,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimaryColor,
            ),
          ),
          SizedBox(height: 2.h), // 减少间距
          Text(
            text,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondColor,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建底部总计项
  Widget _buildBottomSumItem(final String label, final int count) {
    return Text(
      "$label $count",
      style: TextStyle(
        fontSize: 14.sp,
        color: AppColors.redColor,
      ),
    );
  }
}
