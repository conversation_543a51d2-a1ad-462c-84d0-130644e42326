import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/core/widgets/empty_view.dart';
import 'package:user_app/core/widgets/index.dart';
import 'package:user_app/features/mine/pages/activity_order_list/widgets/order_list_widget.dart';
import 'package:user_app/features/mine/pages/activity_order_list/widgets/statistics_box_widget.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/mine/pages/activity_order_list/activity_order_list_controller.dart';

/// 活动订单列表页面
/// 展示用户的活动订单、抽奖统计和操作
class ActivityOrderListPage extends ConsumerWidget {
  /// 构造
  const ActivityOrderListPage({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 监听加载状态，只有加载状态变化时才会重建整个页面
    final isLoading = ref.watch(
      activityOrderListControllerProvider.select((final state) => state.isLoading),
    );
    final hasItems = ref.watch(
      activityOrderListControllerProvider.select(
        (final state) => state.orderList?.items.isNotEmpty ?? false,
      ),
    );
    final s = S.current;

    return Scaffold(
      backgroundColor: AppColors.baseBackgroundColor,
      appBar: CustomAppBar(
        title: s.lottery_order_title,
        backgroundColor: AppColors.redColor,
        titleColor: Colors.white,
      ),
      body: CustomRefreshIndicator(
        onRefresh: () async {
          ref.read(activityOrderListControllerProvider.notifier).getOrderList();
        },
        child: CustomScrollView(
                slivers: [
                  // 统计区域组件
                  const SliverToBoxAdapter(
                    child: StatisticsBoxWidget(),
                  ),
                  // 根据是否有数据显示订单列表或空状态
                  SliverToBoxAdapter(
                    child: hasItems
                        ? const OrderListWidget()
                        : EmptyView(message: s.search_result_no),
                  ),
                ],
              ),
      ),
    );
  }
}
