import 'package:user_app/data/models/activity_order/lottery_order_model.dart';

class ActivityOrderListState {
  const ActivityOrderListState({
    this.isLoading = false,
    this.error,
    this.orderList,
    this.lotteryDetail,
    this.currentId = 0,
    this.isRefreshing = false,
    this.canTurnItems = const [],
  });

  /// 是否加载中
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 订单列表数据
  final LotteryOrderListModel? orderList;

  /// 抽奖详情数据
  final dynamic lotteryDetail;

  /// 当前选中的订单ID
  final int currentId;

  /// 是否正在下拉刷新
  final bool isRefreshing;

  /// 可抽奖的项目ID列表
  final List<int> canTurnItems;

  ActivityOrderListState copyWith({
    final bool? isLoading,
    final String? error,
    final LotteryOrderListModel? orderList,
    final dynamic lotteryDetail,
    final int? currentId,
    final bool? isRefreshing,
    final List<int>? canTurnItems,
  }) {
    return ActivityOrderListState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      orderList: orderList ?? this.orderList,
      lotteryDetail: lotteryDetail ?? this.lotteryDetail,
      currentId: currentId ?? this.currentId,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      canTurnItems: canTurnItems ?? this.canTurnItems,
    );
  }
}
