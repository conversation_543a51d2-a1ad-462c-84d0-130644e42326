// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity_order_list_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$activityOrderListControllerHash() =>
    r'0d809adc5dc54009012568ba1f8585b43db4e6f7';

/// 活动订单列表控制器
///
/// Copied from [ActivityOrderListController].
@ProviderFor(ActivityOrderListController)
final activityOrderListControllerProvider = AutoDisposeNotifierProvider<
    ActivityOrderListController, ActivityOrderListState>.internal(
  ActivityOrderListController.new,
  name: r'activityOrderListControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$activityOrderListControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ActivityOrderListController
    = AutoDisposeNotifier<ActivityOrderListState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
