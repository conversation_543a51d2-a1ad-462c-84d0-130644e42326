import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/core/widgets/image_viewer.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/data/models/mine/license_model.dart';
import 'package:user_app/features/mine/pages/license/license_provider.dart';
import 'package:user_app/generated/l10n.dart';

/// 代理资质页面
/// 代理资质页面
class AgentLicensePage extends ConsumerWidget {
  /// 构造函数
  const AgentLicensePage({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    /// 获取代理资质列表
    final licenseList = ref.watch(licenseProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: S.current.licenseTypeTitle,
      ),
      body: licenseList.when(
        /// 数据加载成功时显示列表
        data: (final licenses) => _buildLicenseList(context, licenses),

        /// 加载中显示加载动画
        loading: () => const Center(child: LoadingWidget()),

        /// 错误时显示错误信息
        error: (final error, final stackTrace) => Center(
          child: Text(error.toString()),
        ),
      ),
    );
  }

  /// 构建代理资质列表
  Widget _buildLicenseList(
      final BuildContext context, final List<LicenseModel> licenses) {
    /// 列表为空时显示无数据提示
    if (licenses.isEmpty) {
      return Center(
        child: Text(S.current.no_data),
      );
    }

    /// 构建列表视图
    return ListView.builder(
      padding: EdgeInsets.all(8.w),
      itemCount: licenses.length,
      itemBuilder: (final context, final index) {
        final license = licenses[index];
        return _buildLicenseItem(context, license);
      },
    );
  }

  /// 构建单个代理资质项
  Widget _buildLicenseItem(
      final BuildContext context, final LicenseModel license) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h),
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// 资质类型名称
          Padding(
            padding: EdgeInsets.all(8.w),
            child: Text(
              license.docTypeName,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textPrimaryColor,
              ),
            ),
          ),

          /// 资质图片,点击可预览
          GestureDetector(
            onTap: () =>
                showImageViewer(context, imageUrls: [license.filePath]),
            child: Container(
              height: 200.h,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Image.network(
                license.filePath,
                fit: BoxFit.contain,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
