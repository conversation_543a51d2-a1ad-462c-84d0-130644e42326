import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/models/mine/license_model.dart';
import 'package:user_app/data/repositories/user/user_repository.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';

part 'license_provider.g.dart';

/// 代理资质列表提供者
@riverpod
Future<List<LicenseModel>> license(Ref ref) async {
  final repository = ref.watch(userRepositoryProvider);
  final areaId = ref.read(homeNoticeProvider).value?.location?.areaId ?? 1;
  final response = await repository.fetchLicenseList(areaId);
  return response.data ?? [];
}
