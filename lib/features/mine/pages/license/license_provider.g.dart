// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'license_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$licenseHash() => r'ac8f92115acd0542ad3096b409cda3ac6ff3a7af';

/// 代理资质列表提供者
///
/// Copied from [license].
@ProviderFor(license)
final licenseProvider = AutoDisposeFutureProvider<List<LicenseModel>>.internal(
  license,
  name: r'licenseProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$licenseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LicenseRef = AutoDisposeFutureProviderRef<List<LicenseModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
