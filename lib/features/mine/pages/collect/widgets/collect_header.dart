import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/generated/l10n.dart';

/// 收藏页面头部组件
class CollectHeader extends ConsumerWidget {
  /// TabController
  final TabController tabController;

  /// 切换回调
  final Function(int) onTabChanged;

  /// 构造函数
  const CollectHeader({
    super.key,
    required this.tabController,
    required this.onTabChanged,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return TabBar(
      // 标签点击回调
      onTap: onTabChanged,
      // 标签控制器
      controller: tabController,
      // 选中标签文本颜色
      labelColor: Colors.white,
      // 未选中标签文本颜色
      unselectedLabelColor: Colors.white,
      // 选中标签文本样式
      labelStyle: TextStyle(
        fontSize: 20.sp,
        fontWeight: FontWeight.w600,
        fontFamily: 'UkijTuzTom',
      ),
      // 未选中标签文本样式
      unselectedLabelStyle: TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.normal,
        fontFamily: 'UkijTuzTom',
      ),
      // 指示器样式
      indicator: UnderlineTabIndicator(
        // 指示器边框样式
        borderSide: BorderSide(width: 3.h, color: Colors.white),
        // 指示器边框圆角
        borderRadius: BorderRadius.circular(5.r),
      ),
      // 指示器内边距
      indicatorPadding: EdgeInsets.all(5.h),
      // 移除分隔线
      dividerColor: Colors.transparent,
      // 指示器大小与标签文本同宽
      indicatorSize: TabBarIndicatorSize.label,
      // 标签列表
      tabs: [
        // 餐厅标签
        Tab(text: S.current.res_name),
        // 美食标签
        Tab(text: S.current.food_name),
      ],
    );
  }
}
