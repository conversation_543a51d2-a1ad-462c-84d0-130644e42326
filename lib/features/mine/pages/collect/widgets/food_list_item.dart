import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/collect/collect_food_list.dart'
    as CollectFoodList;
import 'package:user_app/generated/l10n.dart';

/// 收藏美食列表项组件
class FoodListItem extends StatelessWidget {
  /// 收藏美食数据
  final CollectFoodList.Items food;

  /// 构造函数
  const FoodListItem({
    super.key,
    required this.food,
  });

  @override
  Widget build(final BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 3.h),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          // 美食图片
          ClipRRect(
            borderRadius: BorderRadius.circular(5.r),
            child: Image.network(
              food.image ?? '',
              width: 80.w,
              height: 80.w,
              fit: BoxFit.cover,
              errorBuilder: (final context, final error, final stackTrace) {
                return Container(
                  width: 80.w,
                  height: 80.w,
                  color: Colors.grey[200],
                  child: const Icon(Icons.food_bank),
                );
              },
            ),
          ),
          SizedBox(width: 10.w),
          // 美食信息
          Expanded(
            child: SizedBox(
              height: 80.w,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    food.name ?? '',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4.h),
                  if (food.restaurantName != null)
                    Text(
                      food.restaurantName!,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[600],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    )
                  else
                    Text(
                      '${S.current.sales_count}: ${food.monthOrderCount ?? 0}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  SizedBox(height: 4.h),
                  Row(
                    children: [
                      Text(
                        '￥',
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontFamily: 'NumberFont',
                          color: AppColors.foodPriceColor,
                        ),
                      ),
                      Text(
                        '${food.price ?? 0}',
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontFamily: 'NumberFont',
                          color: AppColors.foodPriceColor,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
