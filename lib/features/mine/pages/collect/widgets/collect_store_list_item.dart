import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/collect/collect_store_list.dart'
    as CollectStoreList;
import 'package:user_app/generated/l10n.dart';

/// 收藏餐厅列表项组件
class CollectStoreListItem extends StatelessWidget {
  /// 收藏餐厅数据
  final CollectStoreList.Items collectStore;

  /// 构造函数
  const CollectStoreListItem({
    super.key,
    required this.collectStore,
  });

  @override
  Widget build(final BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 3.h),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 餐厅图片
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(5.r),
                child: CachedNetworkImage(
                  imageUrl: collectStore.logo ?? '',
                  width: 100.w,
                  height: 100.w,
                  fit: BoxFit.cover,
                  errorWidget: (final context, final url, final error) {
                    return Container(
                      width: 100.w,
                      height: 100.w,
                      color: Colors.grey[200],
                      child: const Icon(Icons.restaurant),
                    );
                  },
                ),
              ),
              if (collectStore.resting == 1 ||
                  collectStore.hasFoodPre == 1 ||
                  collectStore.is_new == 1)
                Container(
                  width: 100.w,
                  height: 100.w,
                  padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: const Color.fromARGB(150, 0, 0, 0),
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(5.r),
                      bottomLeft: Radius.circular(5.r),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      collectStore.resting == 1
                          ? S.current.resting
                          : collectStore.hasFoodPre == 1
                              ? S.current.has_food_pre
                              : collectStore.is_new == 1
                                  ? S.current.is_new
                                  : '',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(width: 10.w),
          // 餐厅信息, 包括名称、（star_avg， 月销量mounth_order_count）、标签take_tag
          Expanded(
            child: SizedBox(
              height: 100.w, // 与图片高度保持一致
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    collectStore.name ?? '',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Row(
                    children: [
                      Text(
                        '${collectStore.starAvg ?? 0}',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.red[400],
                        ),
                      ),
                      Icon(
                        Icons.star,
                        color: Colors.red[400],
                        size: 18.sp,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        '${S.current.month_order_count}: ${collectStore.mounthOrderCount ?? 0}',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.textSecondColor,
                        ),
                      ),
                    ],
                  ),
                  if (collectStore.takeTag != null &&
                      collectStore.takeTag!.isNotEmpty)
                    Wrap(
                      spacing: 4.w,
                      runSpacing: 4.h,
                      children: collectStore.takeTag!.map((final tag) {
                        return Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 6.w,
                            vertical: 2.h,
                          ),
                          decoration: BoxDecoration(
                            color: Color(
                              int.parse(
                                '0xFF${tag.background?.substring(1)}',
                              ),
                            ),
                            borderRadius: BorderRadius.circular(4.r),
                            border: Border.all(
                              color: Color(
                                int.parse(
                                  '0xFF${tag.borderColor?.substring(1)}',
                                ),
                              ),
                            ),
                          ),
                          child: Text(
                            tag.title ?? '',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Color(
                                int.parse(
                                  '0xFF${tag.color?.substring(1)}',
                                ),
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
