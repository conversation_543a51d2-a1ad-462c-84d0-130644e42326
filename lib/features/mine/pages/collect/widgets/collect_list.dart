import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/widgets/empty_view.dart';
import 'package:user_app/data/models/collect/collect_food_list.dart'
    as CollectFoodList;
import 'package:user_app/data/models/collect/collect_store_list.dart'
    as CollectStoreList;
import 'package:user_app/features/mine/pages/collect/widgets/collect_store_list_item.dart';
import 'package:user_app/features/mine/pages/collect/widgets/food_list_item.dart';
import 'package:user_app/routes/paths.dart';

/// 收藏列表组件
class CollectList extends ConsumerWidget {
  /// 餐厅列表
  final List<CollectStoreList.Items>? shopList;

  /// 美食列表
  final List<CollectFoodList.Items>? foodsList;

  /// 是否已获取数据
  final bool getInfoAfter;

  /// 构造函数
  const CollectList({
    super.key,
    this.shopList,
    this.foodsList,
    this.getInfoAfter = false,
  }) : assert(shopList != null || foodsList != null);

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 如果数据尚未获取，返回空白组件
    if (!getInfoAfter) {
      return const SliverToBoxAdapter(child: SizedBox.shrink());
    }

    // 如果餐厅列表为空，显示空数据提示
    if (shopList != null && shopList!.isEmpty) {
      return SliverFillRemaining(
        child: EmptyView(),
      );
    }

    // 如果美食列表为空，显示空数据提示
    if (foodsList != null && foodsList!.isEmpty) {
      return SliverFillRemaining(
        child: EmptyView(),
      );
    }

    // 显示列表数据
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (final context, final index) {
          // 根据列表类型显示不同的列表项
          if (shopList != null) {
            // 显示餐厅收藏项
            return InkWell(
              onTap: () {
                context.push(
                  AppPaths.restaurantDetailPage,
                  extra: {
                    'restaurantId': shopList![index].restaurantId,
                    'buildingId': 1208,
                    'ids': [],
                  },
                );
              },
              child: CollectStoreListItem(collectStore: shopList![index]),
            );
          } else {
            // 显示美食收藏项
            return InkWell(
              onTap: () {
                context.push(
                  AppPaths.restaurantDetailPage,
                  extra: {
                    'restaurantId': foodsList![index].restaurantId,
                    'buildingId': 1208,
                    'ids': [],
                  },
                );
              },
              child: FoodListItem(food: foodsList![index]),
            );
          }
        },
        // 列表项数量
        childCount: shopList?.length ?? foodsList?.length ?? 0,
      ),
    );
  }
}
