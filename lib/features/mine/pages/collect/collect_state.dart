import 'package:user_app/data/models/collect/collect_food_list.dart'
    as CollectFoodList show Items;
import 'package:user_app/data/models/collect/collect_store_list.dart'
    as CollectStoreList show Items;

/// 收藏页面状态
class CollectState {
  /// 当前选中的标签页（0: 餐厅, 1: 美食）
  final int current;

  /// 餐厅列表
  final List<CollectStoreList.Items> shopList;

  /// 美食列表
  final List<CollectFoodList.Items> foodsList;

  /// 是否正在加载
  final bool isLoading;

  /// 是否已获取数据
  final bool getInfoAfter;

  /// 错误信息
  final String? error;

  /// 构造函数
  const CollectState({
    this.current = 0,
    this.shopList = const [],
    this.foodsList = const [],
    this.isLoading = false,
    this.getInfoAfter = false,
    this.error,
  });

  /// 复制状态
  CollectState copyWith({
    final int? current,
    final List<CollectStoreList.Items>? shopList,
    final List<CollectFoodList.Items>? foodsList,
    final bool? isLoading,
    final bool? getInfoAfter,
    final String? error,
  }) {
    return CollectState(
      current: current ?? this.current,
      shopList: shopList ?? this.shopList,
      foodsList: foodsList ?? this.foodsList,
      isLoading: isLoading ?? this.isLoading,
      getInfoAfter: getInfoAfter ?? this.getInfoAfter,
      error: error ?? this.error,
    );
  }
}
