// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'collect_controller_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$collectControllerHash() => r'ab17bbbadd2c31f96e7f47837001db54c4553c25';

/// 收藏页面控制器
///
/// Copied from [CollectController].
@ProviderFor(CollectController)
final collectControllerProvider =
    AutoDisposeNotifierProvider<CollectController, CollectState>.internal(
  CollectController.new,
  name: r'collectControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$collectControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CollectController = AutoDisposeNotifier<CollectState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
