import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/core/widgets/empty_view.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/mine/pages/collect/collect_controller_provider.dart';
import 'package:user_app/features/mine/pages/collect/widgets/collect_header.dart';
import 'package:user_app/features/mine/pages/collect/widgets/collect_list.dart';
import 'package:user_app/generated/l10n.dart';

/// 收藏页面
class CollectPage extends ConsumerStatefulWidget {
  /// 构造函数
  const CollectPage({super.key});

  @override
  ConsumerState<CollectPage> createState() => _CollectPageState();
}

class _CollectPageState extends ConsumerState<CollectPage>
    with SingleTickerProviderStateMixin {
  late final TabController _tabController;
  final GlobalKey<RefreshIndicatorState> _refreshKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 增加监听器监听index变化，而不是indexIsChanging
    _tabController.addListener(() {
      final currentTabIndex = ref.read(collectControllerProvider).current;
      if (_tabController.index != currentTabIndex) {
        ref
            .read(collectControllerProvider.notifier)
            .changeOrderType(_tabController.index);
      }
    });

    // 确保初始加载数据
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      ref.read(collectControllerProvider.notifier).refresh();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    // 只在根节点监听当前标签变化
    ref.listen<int>(
        collectControllerProvider.select((final state) => state.current),
        (final prev, final next) {
      if (mounted && _tabController.index != next) {
        _tabController.animateTo(next);
      }
    });

    return Scaffold(
      appBar: CustomAppBar(
        title: S.current.collect_title,
      ),
      body: Column(
        children: [
          // 添加TabBar作为固定头部
          Container(
            color: AppColors.primary,
            child: CollectHeader(
              tabController: _tabController,
              onTabChanged: (final index) {
                // 直接通过TabController处理索引变化
                if (_tabController.index != index) {
                  // 先更新UI，让用户看到切换效果
                  _tabController.animateTo(index);

                  // 然后触发数据加载
                  ref
                      .read(collectControllerProvider.notifier)
                      .changeOrderType(index);
                }
              },
            ),
          ),
          // 内容区域 - 使用Consumer隔离状态变化
          Expanded(
            child: Consumer(
              builder: (final context, final ref, final child) {
                // 只在Consumer内获取状态，避免整个页面重建
                final isLoading = ref.watch(
                  collectControllerProvider
                      .select((final state) => state.isLoading),
                );

                return Stack(
                  children: [
                    // 标签内容
                    TabBarView(
                      controller: _tabController,
                      children: [
                        // 餐厅列表
                        _buildShopTab(),
                        // 美食列表
                        _buildFoodTab(),
                      ],
                    ),
                    // 加载遮罩层
                    if (isLoading)
                      Container(
                        color: Colors.black.withOpacity(0.1),
                        child: const LoadingWidget(),
                      ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // 构建餐厅标签页
  Widget _buildShopTab() {
    return RefreshIndicator(
      key: _refreshKey,
      onRefresh: () async {
        // 执行刷新操作
        await ref.read(collectControllerProvider.notifier).refresh();
      },
      child: Consumer(
        builder: (final context, final ref, final _) {
          final shopList = ref.watch(
            collectControllerProvider.select((final state) => state.shopList),
          );
          final getInfoAfter = ref.watch(
            collectControllerProvider
                .select((final state) => state.getInfoAfter),
          );

          return shopList.isEmpty && getInfoAfter
              ? EmptyView()
              : _buildList(
                  CollectList(
                    shopList: shopList,
                    getInfoAfter: getInfoAfter,
                  ),
                );
        },
      ),
    );
  }

  // 构建美食标签页
  Widget _buildFoodTab() {
    return RefreshIndicator(
      onRefresh: () async {
        // 执行刷新操作
        await ref.read(collectControllerProvider.notifier).refresh();
      },
      child: Consumer(
        builder: (final context, final ref, final _) {
          final foodsList = ref.watch(
            collectControllerProvider.select((final state) => state.foodsList),
          );
          final getInfoAfter = ref.watch(
            collectControllerProvider
                .select((final state) => state.getInfoAfter),
          );

          return foodsList.isEmpty && getInfoAfter
              ? EmptyView()
              : _buildList(
                  CollectList(
                    foodsList: foodsList,
                    getInfoAfter: getInfoAfter,
                  ),
                );
        },
      ),
    );
  }

  // 构建可滚动列表
  Widget _buildList(final Widget sliver) {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        CustomScrollView(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          slivers: [sliver],
        ),
      ],
    );
  }
}
