import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/features/mine/pages/collect/collect_state.dart';
import 'package:user_app/features/mine/services/collect_service.dart';
import 'package:user_app/main.dart';

part 'collect_controller_provider.g.dart';

/// 收藏页面控制器
@riverpod
class CollectController extends _$CollectController {
  @override
  CollectState build() {
    // 初始加载数据
    Future.microtask(() => getCollectList());

    // 监听登录状态，登录成功后重新加载数据
    ref.listen(isLoggedInProvider, (final previous, final next) {
      if (next) {
        Future.microtask(() => getCollectList());
      }
    });

    return const CollectState();
  }

  /// 切换标签页
  void changeOrderType(final int tab) {
    // 如果当前标签与要切换的标签相同，不执行操作
    if (state.current == tab) {
      return;
    }

    // 更新状态
    state = state.copyWith(current: tab);

    // 立即请求对应标签的数据
    getCollectList();
  }

  /// 获取收藏列表
  Future<void> getCollectList() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final service = ref.read(collectServiceProvider.notifier);
      if (state.current == 0) {
        final storeList = await service.getStoreCollectList();
        state = state.copyWith(
          shopList: storeList.data?.items ?? [],
          isLoading: false,
          getInfoAfter: true,
        );
      } else {
        final foodList = await service.getFoodCollectList();
        state = state.copyWith(
          foodsList: foodList?.foodList?.items ?? [],
          isLoading: false,
          getInfoAfter: true,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 刷新收藏列表 - 仅刷新当前标签
  Future<void> refresh() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final service = ref.read(collectServiceProvider.notifier);
      // 只刷新当前标签的数据，不改变当前标签
      if (state.current == 0) {
        final storeList = await service.getStoreCollectList();
        state = state.copyWith(
          shopList: storeList.data?.items ?? [],
          isLoading: false,
          getInfoAfter: true,
        );
      } else {
        final foodList = await service.getFoodCollectList();
        state = state.copyWith(
          foodsList: foodList?.foodList?.items ?? [],
          isLoading: false,
          getInfoAfter: true,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
}
