import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/features/mine/pages/video/video_state.dart';
import 'package:video_player/video_player.dart';

part 'video_controller_provider.g.dart';

/// 视频控制器
@riverpod
class VideoController extends _$VideoController {
  @override
  VideoState build() {
    // 在控制器销毁时清理资源,防止内存泄漏
    ref.onDispose(() {
      _disposeControllers();
    });

    // 返回初始状态
    return const VideoState();
  }

  /// 初始化视频播放器
  /// [url] 视频URL地址
  /// 该方法会:
  /// 1. 检查是否已初始化,避免重复初始化
  /// 2. 创建并初始化视频控制器
  /// 3. 设置错误监听
  /// 4. 配置播放器UI控制器
  Future<void> _initializeVideoPlayer(final String url) async {
    // 如果已经初始化则直接返回
    if (state.isInitialized) return;

    try {
      // 清理之前的控制器资源
      _disposeControllers();

      // 创建视频播放器控制器
      final videoPlayerController = VideoPlayerController.networkUrl(
        Uri.parse(url),
      );

      // 更新状态中的视频控制器
      state = state.copyWith(videoPlayerController: videoPlayerController);

      // 设置错误监听器,当播放出错时更新状态
      videoPlayerController.addListener(() {
        final error = videoPlayerController.value.errorDescription;
        if (error != null) {
          state = state.copyWith(
            isLoading: false,
            error: error,
          );
        }
      });

      // 初始化视频播放器,可能会抛出平台异常
      try {
        await videoPlayerController.initialize();
      } on PlatformException catch (e) {
        state = state.copyWith(
          isLoading: false,
          error: '视频初始化失败: ${e.message}',
        );
        return;
      }

      // 创建Chewie控制器用于视频播放UI控制
      // 创建Chewie控制器用于视频播放UI控制
      // ChewieController负责视频播放器的UI展示和交互控制
      final chewieController = ChewieController(
        // 绑定视频播放控制器
        videoPlayerController: videoPlayerController,

        // 播放控制
        autoPlay: true, // 视频加载完成后自动开始播放
        looping: false, // 播放结束后不自动循环播放

        // 视频显示比例,使用视频原始宽高比
        aspectRatio: videoPlayerController.value.aspectRatio,

        // 进度条样式配置
        materialProgressColors: ChewieProgressColors(
          playedColor: AppColors.primary, // 已播放部分颜色
          handleColor: AppColors.primary, // 进度条滑块颜色
          backgroundColor: Colors.grey, // 进度条背景色
          bufferedColor: Colors.grey.shade300, // 已缓冲部分颜色
        ),

        // 控制器UI显示配置
        showControls: true, // 显示播放控制器
        showControlsOnInitialize: false, // 初始化时不显示控制器
        showOptions: false, // 不显示设置选项按钮
        allowFullScreen: true, // 允许全屏播放
        allowMuting: true, // 允许静音控制

        // 系统UI配置
        systemOverlaysOnEnterFullScreen: SystemUiOverlay.values, // 全屏时显示所有系统UI

        // 设备方向控制
        deviceOrientationsOnEnterFullScreen: const [
          // 进入全屏时支持的方向
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ],
        deviceOrientationsAfterFullScreen: const [
          // 退出全屏后恢复的方向
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
        ],

        // 加载占位组件
        placeholder: Container(
          color: Colors.black,
          child: const Center(
            child: CircularProgressIndicator(
              color: Colors.white, // 加载动画为白色
            ),
          ),
        ),

        // 错误显示组件构建器
        errorBuilder: (final context, final errorMessage) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  color: Colors.white,
                  size: 32.sp,
                ),
                const SizedBox(height: 8),
                Text(
                  errorMessage, // 显示具体错误信息
                  style: const TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        },
      );

      // 更新状态,标记初始化完成
      state = state.copyWith(
        chewieController: chewieController,
        isInitialized: true,
        isLoading: false,
      );
    } catch (e) {
      // 捕获其他可能的错误并更新状态
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 清理视频播放器资源
  /// 该方法会:
  /// 1. 释放Chewie控制器
  /// 2. 释放视频播放器控制器
  /// 3. 重置初始化状态
  void _disposeControllers() {
    state.chewieController?.dispose();
    state.videoPlayerController?.dispose();
    state = state.copyWith(
      chewieController: null,
      videoPlayerController: null,
      isInitialized: false,
    );
  }

  /// 获取并加载"如何下单"教学视频
  /// 该方法会:
  /// 1. 设置加载状态
  /// 2. 获取视频URL
  /// 3. 初始化视频播放器
  /// 4. 处理可能的错误
  Future<void> fetchHowToOrderVideo() async {
    // 设置加载状态
    state = state.copyWith(isLoading: true);

    try {
      // 获取视频URL并更新状态
      const videoUrl = UrlConstants.howToOrderVideoUrl;
      state = state.copyWith(videoUrl: videoUrl);
      // 初始化视频播放器
      await _initializeVideoPlayer(videoUrl);
    } catch (e) {
      // 处理错误并更新状态
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
}
