import 'package:chewie/chewie.dart';
import 'package:video_player/video_player.dart';

class VideoState {
  final String? videoUrl;
  final bool isLoading;
  final String? error;
  final ChewieController? chewieController;
  final VideoPlayerController? videoPlayerController;
  final bool isInitialized;

  const VideoState({
    this.videoUrl,
    this.isLoading = false,
    this.error,
    this.chewieController,
    this.videoPlayerController,
    this.isInitialized = false,
  });

  VideoState copyWith({
    final String? videoUrl,
    final bool? isLoading,
    final String? error,
    final ChewieController? chewieController,
    final VideoPlayerController? videoPlayerController,
    final bool? isInitialized,
  }) {
    return VideoState(
      videoUrl: videoUrl ?? this.videoUrl,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      chewieController: chewieController ?? this.chewieController,
      videoPlayerController:
          videoPlayerController ?? this.videoPlayerController,
      isInitialized: isInitialized ?? this.isInitialized,
    );
  }
}
