// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_controller_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$videoControllerHash() => r'8e1cfb65216252fb4d42abf93c555eb24b33b396';

/// 视频控制器
///
/// Copied from [VideoController].
@ProviderFor(VideoController)
final videoControllerProvider =
    AutoDisposeNotifierProvider<VideoController, VideoState>.internal(
  VideoController.new,
  name: r'videoControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$videoControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$VideoController = AutoDisposeNotifier<VideoState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
