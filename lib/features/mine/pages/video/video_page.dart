import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/mine/pages/video/video_controller_provider.dart';
import 'package:user_app/generated/l10n.dart';

class VideoPage extends ConsumerStatefulWidget {
  const VideoPage({super.key});

  @override
  ConsumerState<VideoPage> createState() => _VideoPageState();
}

class _VideoPageState extends ConsumerState<VideoPage> {
  @override
  void initState() {
    super.initState();
    // 在页面加载时初始化视频
    Future.microtask(() {
      ref.read(videoControllerProvider.notifier).fetchHowToOrderVideo();
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(videoControllerProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: S.current.how_to_order,
      ),
      body: state.isLoading
          ? const LoadingWidget()
          : state.error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(state.error!),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          ref
                              .read(videoControllerProvider.notifier)
                              .fetchHowToOrderVideo();
                        },
                        child: const Text('重试'),
                      ),
                    ],
                  ),
                )
              : state.chewieController == null
                  ? const Center(child: Text('暂无视频'))
                  : Center(
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: AspectRatio(
                          aspectRatio:
                              state.videoPlayerController?.value.aspectRatio ??
                                  16 / 9,
                          child: Chewie(
                            controller: state.chewieController!,
                          ),
                        ),
                      ),
                    ),
    );
  }
}
