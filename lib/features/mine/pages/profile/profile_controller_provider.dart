import 'package:bot_toast/bot_toast.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/models/auth/auth_model.dart';
import 'package:user_app/features/mine/services/profile_service.dart';
import 'package:user_app/features/mine/pages/profile/profile_state.dart';
import 'package:user_app/main.dart';
import 'package:user_app/routes/app_router.dart';
part 'profile_controller_provider.g.dart';

/// 个人资料页面控制器
@riverpod
class ProfileController extends _$ProfileController {
  @override
  ProfilePageState build() {
    Future.microtask(() {
      loadUserData();
    });
    ref.listen(isLoggedInProvider, (final previous, final next) {
      if (next) {
        Future.microtask(() => loadUserData());
      }
    });
    return const ProfilePageState();
  }

  /// 加载用户数据
  Future<void> loadUserData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final userInfo =
          await ref.read(profileServiceProvider.notifier).loadUserData();

      if (userInfo != null) {
        state = state.copyWith(
          isLoading: false,
          userInfo: userInfo,
          nickname: userInfo.name,
          gender: userInfo.gender,
          birthday: userInfo.birthday,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: '未找到用户信息',
        );
        BotToast.showText(text: '未找到用户信息');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '加载用户数据失败：$e',
      );
      BotToast.showText(text: '加载用户数据失败');
    }
  }

  /// 更新用户昵称
  void updateNickname(final String nickname) {
    if (nickname != state.nickname) {
      state = state.copyWith(nickname: nickname, isDirty: true);
    }
  }

  /// 更新用户性别
  void updateGender(final int gender) {
    if (gender != state.gender) {
      state = state.copyWith(gender: gender, isDirty: true);
    }
  }

  /// 更新用户生日
  void updateBirthday(final String birthday) {
    if (birthday != state.birthday) {
      state = state.copyWith(birthday: birthday, isDirty: true);
    }
  }

  /// 选择头像
  Future<void> pickAvatar() async {
    final newAvatarUrl = await ref
        .read(profileServiceProvider.notifier)
        .pickAndUploadAvatar();
    if (newAvatarUrl != null) {
      final updatedUserInfo = UserInfo(
        id: state.userInfo?.id ?? 0,
        name: state.userInfo?.name,
        mobile: state.userInfo?.mobile,
        avatar: newAvatarUrl,
        gender: state.userInfo?.gender,
        birthday: state.userInfo?.birthday,
        money: state.userInfo?.money,
        points: state.userInfo?.points,
        profilePoint: state.userInfo?.profilePoint,
        recommendClientPoint: state.userInfo?.recommendClientPoint,
      );

      state = state.copyWith(
        userInfo: updatedUserInfo,
        isDirty: true,
      );
    }
  }

  /// 保存个人资料
  Future<void> _saveProfile() async {
    if (!state.isDirty) {
      return;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final updatedUserInfo = UserInfo(
        id: state.userInfo?.id ?? 0,
        name: state.nickname,
        mobile: state.userInfo?.mobile,
        avatar: state.userInfo?.avatar,
        gender: state.gender,
        birthday: state.birthday,
        money: state.userInfo?.money,
        points: state.userInfo?.points,
        profilePoint: state.userInfo?.profilePoint,
        recommendClientPoint: state.userInfo?.recommendClientPoint,
      );

      await ref
          .read(profileServiceProvider.notifier)
          .updateUserInfo(updatedUserInfo);

      state = state.copyWith(
        isLoading: false,
        userInfo: updatedUserInfo,
        isDirty: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '保存失败: $e',
      );
    }
  }

  /// 绑定微信信息
  Future<void> bindWechatInfo() async {
    try {
      await ref.read(profileServiceProvider.notifier).bindWechatInfo(
            name: state.nickname ?? state.userInfo?.name ?? 'mulazim',
            avatar: state.userInfo?.avatar,
            gender: state.gender ?? state.userInfo?.gender,
            birthDay: state.birthday ?? state.userInfo?.birthday,
          );
      _saveProfile();
      router.pop();
    } catch (e) {
      state = state.copyWith(error: "$e");
    }
  }
}
