import 'package:user_app/data/models/auth/auth_model.dart';

/// ProfilePageState - 管理个人资料页面的状态
class ProfilePageState {
  /// 标识是否正在加载数据
  final bool isLoading;

  /// 保存错误信息
  final String? error;

  /// 用户信息对象
  final UserInfo? userInfo;

  /// 是否有未保存的更改
  final bool isDirty;

  /// 用户昵称
  final String? nickname;

  /// 用户性别: 1-男, 2-女
  final int? gender;

  /// 用户生日，格式：YYYY-MM-DD
  final String? birthday;

  /// 创建一个新的 ProfilePageState 实例
  const ProfilePageState({
    this.isLoading = false,
    this.error,
    this.userInfo,
    this.isDirty = false,
    this.nickname,
    this.gender,
    this.birthday,
  });

  /// 创建一个新的实例，仅更新部分属性
  ProfilePageState copyWith({
    final bool? isLoading,
    final String? error,
    final UserInfo? userInfo,
    final bool? isDirty,
    final String? nickname,
    final int? gender,
    final String? birthday,
  }) {
    return ProfilePageState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      userInfo: userInfo ?? this.userInfo,
      isDirty: isDirty ?? this.isDirty,
      nickname: nickname ?? this.nickname,
      gender: gender ?? this.gender,
      birthday: birthday ?? this.birthday,
    );
  }
}
