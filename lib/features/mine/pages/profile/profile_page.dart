import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/utils/status_bar_util.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/mine/pages/profile/profile_controller_provider.dart';
import 'package:user_app/features/mine/pages/profile/widgets/profile_avatar_picker.dart';
import 'package:user_app/features/mine/pages/profile/widgets/profile_birthday_picker.dart';
import 'package:user_app/features/mine/pages/profile/widgets/profile_gender_picker.dart';
import 'package:user_app/features/mine/pages/profile/widgets/profile_nickname_input.dart';
import 'package:user_app/generated/l10n.dart';

/// 个人资料页面
/// 允许用户查看和编辑个人信息，包括头像、昵称、性别和生日

class ProfilePage extends ConsumerStatefulWidget {
  const ProfilePage({super.key});

  @override
  ConsumerState createState() => _ProfilePageState();
}

class _ProfilePageState extends ConsumerState<ProfilePage> {

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Future.delayed(Duration(milliseconds: 200));
      StatusBarUtil.setDarkMode();
      await Future.delayed(Duration(milliseconds: 800));
      StatusBarUtil.setDarkMode();
    });
  }

  @override
  Widget build(final BuildContext context) {
    // 监听配置文件页面状态
    final state = ref.watch(profileControllerProvider);

    return Scaffold(
      // 设置页面背景色
      backgroundColor: const Color(0xFFF6F6F6),
      // 移除AppBar，使用自定义的顶部栏，让背景图片能够延伸到状态栏
      extendBodyBehindAppBar: true,
      body: Container(
        // 设置整个页面的背景图片，包括状态栏和AppBar区域
        decoration: const BoxDecoration(
          image: DecorationImage(
            alignment: Alignment.topCenter,
            image: CachedNetworkImageProvider(
                "https://acdn.mulazim.com/wechat_mini/tag.png"),
          ),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFE9FFEA), Color.fromARGB(255, 255, 255, 255)],
            stops: [0.0, 0.78],
          ),
        ),
        child: Column(
          children: [
            // 自定义AppBar
            SafeArea(
              child: Container(
                height: kToolbarHeight,
                padding: EdgeInsets.symmetric(horizontal: 8.w),
                alignment: Alignment.center,
                child: Row(
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.arrow_back_ios,
                        color: AppColors.textPrimaryColor,
                      ),
                      onPressed: () => context.pop(),
                    ),
                    Expanded(
                      child: Center(
                        child: Text(
                          S.current.profile,
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimaryColor,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 48.w), // 为了平衡左侧的返回按钮
                  ],
                ),
              ),
            ),
            // 根据加载状态显示加载指示器或内容
            Expanded(
              child: state.isLoading && state.userInfo == null
                  ? const Center(child: LoadingWidget())
                  : Container(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            // 标题
                            Container(
                              width: double.infinity,
                              margin: EdgeInsets.only(top: 30.h),
                              child: Text(
                                S.current.complete_profile,
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimaryColor,
                                ),
                              ),
                            ),
                            // 头像选择器
                            Container(
                              margin: EdgeInsets.only(top: 20.h),
                              width: double.infinity,
                              child: ProfileAvatarPicker(
                                avatarUrl: state.userInfo?.avatar,
                                onTap: () => ref
                                    .read(profileControllerProvider
                                    .notifier)
                                    .pickAvatar(),
                              ),
                            ),
                            SizedBox(height: 12.h),
                            // 个人信息表单区域 - 昵称和微信 ID
                            Column(
                              children: [
                                // 昵称输入框
                                ProfileNicknameInput(
                                  nickname: state.nickname ??
                                      state.userInfo?.name ??
                                      S.current.nickname_placeholder,
                                  onChanged: (final value) => ref
                                      .read(profileControllerProvider
                                      .notifier)
                                      .updateNickname(value),
                                ),
                                Divider(color: AppColors.dividerColor),
                              ],
                            ),
                            SizedBox(height: 12.h),
                            // 性别选择区域
                            ProfileGenderPicker(
                              gender: state.gender ??
                                  state.userInfo?.gender ??
                                  0,
                              onGenderChanged: (final value) => ref
                                  .read(
                                  profileControllerProvider.notifier)
                                  .updateGender(value),
                            ),
                            SizedBox(height: 12.h),
                            // 生日选择区域
                            Column(
                              children: [
                                ProfileBirthdayPicker(
                                  birthday: state.birthday ??
                                      state.userInfo?.birthday,
                                  onBirthdayChanged: (final value) => ref
                                      .read(profileControllerProvider
                                      .notifier)
                                      .updateBirthday(value),
                                ),
                                Divider(color: AppColors.dividerColor),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    // 底部确认按钮区域
                    Container(
                      margin: EdgeInsets.only(bottom: 16.h),
                      child: ElevatedButton(
                        // 仅当表单有修改且不在加载状态时启用保存按钮
                        onPressed: state.isDirty && !state.isLoading
                            ? () => ref
                            .read(profileControllerProvider.notifier)
                            .bindWechatInfo()
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          minimumSize: Size.fromHeight(44.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          disabledBackgroundColor: Colors.grey,
                        ),
                        // 根据加载状态显示加载指示器或确认文本
                        child: state.isLoading
                            ? SizedBox(
                          height: 20.h,
                          width: 20.h,
                          child: const CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                            AlwaysStoppedAnimation<Color>(
                                Colors.white),
                          ),
                        )
                            : Text(
                          S.current.confirm,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
