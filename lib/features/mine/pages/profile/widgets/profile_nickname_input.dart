import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 昵称输入框
class ProfileNicknameInput extends StatefulWidget {
  /// 昵称
  final String nickname;

  /// 昵称改变回调
  final Function(String) onChanged;

  /// 构造函数
  const ProfileNicknameInput({
    super.key,
    required this.nickname,
    required this.onChanged,
  });

  @override
  State<ProfileNicknameInput> createState() => _ProfileNicknameInputState();
}

class _ProfileNicknameInputState extends State<ProfileNicknameInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.nickname);
    _focusNode = FocusNode();
  }

  @override
  void didUpdateWidget(final ProfileNicknameInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.nickname != widget.nickname &&
        widget.nickname != _controller.text) {
      _controller.text = widget.nickname;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.r),
      child: Row(
        children: [
          Text(
            S.current.nickname,
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textPrimaryColor,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: TextField(
              controller: _controller,
              focusNode: _focusNode,
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textPrimaryColor,
              ),
              decoration: InputDecoration(
                hintText: S.current.nickname_placeholder,
                hintStyle: TextStyle(
                  fontSize: 16.sp,
                  color: AppColors.textHintColor,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
                isDense: true,
              ),
              textAlign: TextAlign.end,
              onChanged: widget.onChanged,
            ),
          ),
        ],
      ),
    );
  }
}
