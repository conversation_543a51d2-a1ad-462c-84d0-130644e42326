import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart' show DateFormat;
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/numeric_date_picker.dart';
import 'package:user_app/generated/l10n.dart';

/// 生日选择器
class ProfileBirthdayPicker extends StatelessWidget {
  /// 生日
  final String? birthday;

  /// 生日改变回调
  final Function(String) onBirthdayChanged;

  /// 构造函数
  const ProfileBirthdayPicker({
    super.key,
    required this.birthday,
    required this.onBirthdayChanged,
  });

  @override
  Widget build(final BuildContext context) {
    return InkWell(
      onTap: () => _showDatePicker(context),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 16.r),
        child: Row(
          children: [
            Text(
              S.current.birthday,
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textPrimaryColor,
              ),
            ),
            const Spacer(),
            Text(
              textDirection: TextDirection.ltr,
              birthday != null && birthday!.isNotEmpty
                  ? DateFormat('yyyy-MM-dd')
                      .format(DateFormat('yyyy/MM/dd').parse(birthday!))
                  : S.current.birthday_placeholder,
              style: TextStyle(
                fontSize: 16.sp,
                color: birthday != null
                    ? AppColors.textPrimaryColor
                    : AppColors.textHintColor,
              ),
            ),
            SizedBox(width: 8.w),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.sp,
              color: AppColors.textHintColor,
            ),
          ],
        ),
      ),
    );
  }

  void _showDatePicker(final BuildContext context) {
    // 解析初始日期，默认为1990-01-01
    DateTime initialDate = DateTime(1990, 1, 1);
    if (birthday != null && birthday!.isNotEmpty) {
      try {
        initialDate = DateFormat('yyyy/MM/dd').parse(birthday!);
      } catch (e) {
        print('Invalid date format: $birthday');
      }
    }

    // 临时存储选择的日期，只有在用户点击确定后才会更新
    DateTime tempSelectedDate = initialDate;

    showModalBottomSheet(
      context: context,
      builder: (final BuildContext context) {
        return Container(
          height: 300.h,
          padding: EdgeInsets.only(top: 16.h),
          child: Column(
            children: [
              // 顶部操作栏：取消和确定按钮
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        S.current.cancel,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.textHintColor,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        final formattedDate =
                            DateFormat('yyyy/MM/dd').format(tempSelectedDate);
                        onBirthdayChanged(formattedDate);
                        Navigator.pop(context);
                      },
                      child: Text(
                        S.current.confirm,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Divider(height: 1.h, color: AppColors.dividerColor),
              // 日期选择器
              Expanded(
                child: NumericDatePicker(
                  initialDate: initialDate,
                  onDateChanged: (final date) {
                    tempSelectedDate = date;
                  },
                  minYear: 1900,
                  maxYear: DateTime.now().year,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
