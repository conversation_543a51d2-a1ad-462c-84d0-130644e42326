import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:user_app/generated/l10n.dart';

/// 图片源选择对话框
class ImageSourceDialog extends StatelessWidget {
  /// 构造函数
  const ImageSourceDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Text(
              S.current.profile_photo_source_dialog_title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                fontFamily: "UkijTuzTom",
              ),
            ),
          ),
          const Divider(
            height: 1,
            color: Color(0xFFE0E0E0),
            indent: 16,
            endIndent: 16,
          ),
          ListTile(
            onTap: () => Navigator.pop(context, ImageSource.camera),
            title: Center(
              child: Text(
                S.current.profile_photo_source_dialog_camera,
                style: TextStyle(fontFamily: "UkijTuzTom"),
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16),
          ),
          const Divider(
            height: 0.1,
            color: Color(0xFFE0E0E0),
            indent: 16,
            endIndent: 16,
          ),
          ListTile(
            onTap: () => Navigator.pop(context, ImageSource.gallery),
            title: Center(
              child: Text(
                S.current.profile_photo_source_dialog_gallery,
                style: TextStyle(fontFamily: "UkijTuzTom"),
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16),
          ),
          const Divider(
            height: 0.1,
            color: Color(0xFFE0E0E0),
            indent: 16,
            endIndent: 16,
          ),
          ListTile(
            onTap: () => Navigator.pop(context),
            title: Center(
              child: Text(
                S.current.cancel,
                style: TextStyle(fontFamily: "UkijTuzTom"),
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}
