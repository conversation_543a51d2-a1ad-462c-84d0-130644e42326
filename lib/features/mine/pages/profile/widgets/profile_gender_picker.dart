import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 性别选择器
class ProfileGenderPicker extends StatelessWidget {
  /// 性别
  final int gender;

  /// 性别改变回调
  final Function(int) onGenderChanged;

  /// 构造函数
  const ProfileGenderPicker({
    super.key,
    required this.gender,
    required this.onGenderChanged,
  });

  @override
  Widget build(final BuildContext context) {
    return Row(
      children: [
        Text(
          S.current.gender,
          style: TextStyle(
            fontSize: 16.sp,
            color: AppColors.textPrimaryColor,
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              _buildGenderButton(
                context: context,
                label: S.current.gender_male,
                value: 1,
                isSelected: gender == 1,
              ),
              SizedBox(width: 20.w),
              _buildGenderButton(
                context: context,
                label: S.current.gender_female,
                value: 2,
                isSelected: gender == 2,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildGenderButton({
    required final BuildContext context,
    required final String label,
    required final int value,
    required final bool isSelected,
  }) {
    return GestureDetector(
      onTap: () => onGenderChanged(value),
      child: Container(
        height: 32.h,
        width: 80.w,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey.shade300,
            width: 1.w,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 16.sp,
            color: isSelected ? Colors.white : AppColors.textPrimaryColor,
            fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
