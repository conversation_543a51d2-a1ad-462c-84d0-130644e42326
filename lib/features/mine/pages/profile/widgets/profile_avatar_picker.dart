import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 头像选择器
class ProfileAvatarPicker extends StatelessWidget {
  /// 头像地址
  final String? avatarUrl;

  /// 点击事件
  final VoidCallback onTap;

  /// 构造函数
  const ProfileAvatarPicker({
    super.key,
    this.avatarUrl,
    required this.onTap,
  });

  @override
  Widget build(final BuildContext context) {
    return Column(
      children: [
        SizedB<PERSON>(height: 24.h),
        InkWell(
          onTap: onTap,
          child: _buildAvatar(),
        ),
        SizedBox(height: 24.h),
      ],
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 72.w,
      height: 72.h,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: const Color.fromARGB(255, 250, 250, 250),
        border: Border.all(
          color: const Color(0xFFE8E8E8),
          width: 3.r,
        ),
      ),
      clipBehavior: Clip.antiAlias,
      margin: EdgeInsets.zero,
      padding: EdgeInsets.all(4.w),
      child: avatarUrl != null && avatarUrl!.isNotEmpty
          ? Image.network(
              avatarUrl!,
              fit: BoxFit.cover,
              errorBuilder: (final context, final error, final stackTrace) {
                return Image.asset(
                  'assets/images/mine/user.png',
                  width: 70.w,
                  height: 70.w,
                  fit: BoxFit.cover,
                );
              },
            )
          : Image.asset(
              'assets/images/mine/user.png',
              width: 70.w,
              height: 70.w,
              fit: BoxFit.cover,
            ),
    );
  }
}
