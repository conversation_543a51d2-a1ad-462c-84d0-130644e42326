import 'package:user_app/features/mine/pages/order_ranking_my/order_ranking_my_state.dart';

/// 订单排名预览页面状态
class OrderRankingPreviewState {
  /// 是否加载中
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 当前选择的标签索引
  final int currentId;

  /// 活动数据
  final ActivityPreviewModel? activityObj;

  /// 头部菜单项
  final List<HeaderItem> headerItems;

  /// 头部高度
  final String headerHeight;

  /// 构造函数
  const OrderRankingPreviewState({
    this.isLoading = false,
    this.error,
    this.currentId = 0,
    this.activityObj,
    this.headerItems = const [],
    this.headerHeight = "0",
  });

  /// 拷贝方法
  OrderRankingPreviewState copyWith({
    bool? isLoading,
    String? error,
    int? currentId,
    ActivityPreviewModel? activityObj,
    List<HeaderItem>? headerItems,
    String? headerHeight,
  }) {
    return OrderRankingPreviewState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      currentId: currentId ?? this.currentId,
      activityObj: activityObj ?? this.activityObj,
      headerItems: headerItems ?? this.headerItems,
      headerHeight: headerHeight ?? this.headerHeight,
    );
  }
}

/// 活动预览模型
class ActivityPreviewModel {
  final String name;
  final String rule;
  final int id;
  final int remainStartTime;
  final String announcePage;
  final List<String> shareCoverImages;

  ActivityPreviewModel({
    required this.name,
    required this.rule,
    required this.id,
    required this.remainStartTime,
    required this.announcePage,
    required this.shareCoverImages,
  });

  factory ActivityPreviewModel.fromJson(Map<String, dynamic> json) {
    List<String> images = [];
    if (json['share_cover_images'] != null) {
      if (json['share_cover_images'] is List) {
        images = (json['share_cover_images'] as List)
            .map((e) => e.toString())
            .toList();
      }
    }

    return ActivityPreviewModel(
      name: json['name'] as String? ?? '',
      rule: json['rule'] as String? ?? '',
      id: json['id'] as int? ?? 0,
      remainStartTime: json['remain_start_time'] as int? ?? 0,
      announcePage: json['announce_page_image'] as String? ?? '',
      shareCoverImages: images,
    );
  }
}
