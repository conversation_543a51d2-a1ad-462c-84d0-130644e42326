import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/features/mine/pages/my_evaluate/widgets/my_evaluate_body.dart';
import 'package:user_app/generated/l10n.dart';

/// 我的评价页面
class MyEvaluatePage extends ConsumerWidget {
  /// 构造函数
  const MyEvaluatePage({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return Scaffold(
      appBar: CustomAppBar(
        title: S.current.my_comment,
      ),
      body: const MyEvaluateBody(),
    );
  }
}
