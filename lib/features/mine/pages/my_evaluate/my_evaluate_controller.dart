import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/widgets/toast_widget.dart';
import 'package:user_app/features/mine/pages/my_evaluate/my_evaluate_state.dart';
import 'package:user_app/features/mine/services/my_evaluate_service.dart';
import 'package:user_app/main.dart';
import 'package:url_launcher/url_launcher.dart';

part 'my_evaluate_controller.g.dart';

/// 我的评价页面控制器
@riverpod
class MyEvaluateController extends _$MyEvaluateController {
  /// 每页数据量
  static const int limit = 30;

  @override
  MyEvaluateState build() {
    Future.microtask(() => _loadData());
    // 监听登录状态，登录成功后重新加载数据
    ref.listen(isLoggedInProvider, (final previous, final next) {
      if (next) {
        Future.microtask(() => _loadData());
      }
    });
    return const MyEvaluateState();
  }

  /// 加载评论数据
  Future<void> _loadData() async {
    if (!state.canLoadMore || state.isLoading) {
      return;
    }

    state = state.copyWith(isLoading: true);

    try {
      final service = ref.read(myEvaluateServiceProvider);
      final result = await service.getCommentList(state.currentPage, limit);

      if (result != null && result.status == 200 && result.data != null) {
        final newItems = result.data!.items ?? [];
        final updatedList = [...state.commentList, ...newItems];

        // 判断是否可以继续加载
        final canLoadMore = newItems.length == limit;

        state = state.copyWith(
          isLoading: false,
          commentList: updatedList,
          currentPage: state.currentPage + 1,
          canLoadMore: canLoadMore,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result?.msg ?? '加载失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 刷新数据
  Future<void> refreshData() async {
    state = state.copyWith(
      isLoading: false,
      commentList: [],
      currentPage: 1,
      canLoadMore: true,
    );
    await _loadData();
  }

  /// 加载更多数据
  Future<void> loadMoreData() async {
    if (!state.isLoading) {
      await _loadData();
    }
  }

  /// 删除评论
  Future<void> deleteComment(final int index) async {
    if (state.isDeleting) {
      return;
    }

    state = state.copyWith(isDeleting: true);

    try {
      final commentId = state.commentList[index].id;
      if (commentId == null) {
        throw Exception('评论ID不能为空');
      }

      final service = ref.read(myEvaluateServiceProvider);
      final result = await service.deleteComment(commentId);

      if (result.success) {
        // 先将评论标记为已删除（用于动画效果）
        final updatedList = [...state.commentList];
        updatedList[index] = updatedList[index]..deleted = true;

        state = state.copyWith(
          commentList: updatedList,
          isDeleting: false,
        );

        // 延迟后从列表中移除
        Future.delayed(const Duration(milliseconds: 800), () {
          final filteredList = [...state.commentList];
          filteredList.removeAt(index);
          state = state.copyWith(commentList: filteredList);
        });
      } else {
        state = state.copyWith(
          isDeleting: false,
          error: '删除失败',
        );
        // 使用 ToastWidget 显示错误提示
        ToastWidget.showError('删除失败');
      }
    } catch (e) {
      state = state.copyWith(
        isDeleting: false,
        error: e.toString(),
      );
      // 使用 ToastWidget 显示错误提示
      ToastWidget.showError(e.toString());
    }
  }

  /// 拨打配送员电话
  Future<void> callShipper(final String? mobile) async {
    if (mobile == null || mobile.isEmpty) {
      ToastWidget.showError('配送员电话号码不可用');
      return;
    }

    final url = 'tel:$mobile';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      ToastWidget.showError('无法拨打电话');
    }
  }
}
