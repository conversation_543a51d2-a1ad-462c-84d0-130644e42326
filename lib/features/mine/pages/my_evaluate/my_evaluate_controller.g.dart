// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'my_evaluate_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$myEvaluateControllerHash() =>
    r'ec9d237bc75b7bd9f4d5d555c4e2cc91f0db83f0';

/// 我的评价页面控制器
///
/// Copied from [MyEvaluateController].
@ProviderFor(MyEvaluateController)
final myEvaluateControllerProvider =
    AutoDisposeNotifierProvider<MyEvaluateController, MyEvaluateState>.internal(
  MyEvaluateController.new,
  name: r'myEvaluateControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$myEvaluateControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MyEvaluateController = AutoDisposeNotifier<MyEvaluateState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
