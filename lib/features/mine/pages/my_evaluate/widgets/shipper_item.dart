import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/mine/comment_list_model.dart';
import 'package:user_app/features/mine/pages/my_evaluate/my_evaluate_controller.dart';
import 'package:user_app/routes/paths.dart';

/// 配送员信息组件
class ShipperItem extends ConsumerWidget {
  /// 评论数据
  final CommentItem data;

  /// 是否显示电话图标
  final bool showPhone;

  /// 点击配送员回调
  final VoidCallback? onTap;

  /// 构造函数
  const ShipperItem({
    super.key,
    required this.data,
    this.showPhone = false,
    this.onTap,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return InkWell(
      onTap: onTap ?? (() => _navigateToShipperDetail(context)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 配送员信息部分
          Row(
            children: [
              // 头像 - 100rpx 除以2 = 50.w
              ClipRRect(
                borderRadius: BorderRadius.circular(50.r),
                child: CachedNetworkImage(
                  imageUrl: data.shipperAvatar ?? '',
                  width: 50.w,
                  height: 50.w,
                  fit: BoxFit.cover,
                  placeholder: (final context, final url) => Container(
                    color: Colors.grey[200],
                    width: 50.w,
                    height: 50.w,
                  ),
                  errorWidget: (final context, final url, final error) =>
                      Container(
                    width: 50.w,
                    height: 50.w,
                    color: Colors.grey[200],
                    child: Icon(Icons.person, size: 25.sp, color: Colors.grey),
                  ),
                ),
              ),

              // 名称和区域
              SizedBox(width: 10.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.w),
                    child: Text(
                      data.shipperName ?? '',
                      style: TextStyle(
                        fontSize: 18.sp, // 36rpx / 2
                        color: const Color(0xFF424242),
                      ),
                    ),
                  ),
                  if (data.areaName != null && data.areaName!.isNotEmpty)
                    Container(
                      margin: EdgeInsets.only(top: 5.h),
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      height: 20.h, // 40rpx / 2
                      decoration: BoxDecoration(
                        color: const Color(0xFFEAEAEA),
                        borderRadius: BorderRadius.circular(50.r),
                      ),
                      child: Center(
                        child: Text(
                          data.areaName!,
                          style: TextStyle(
                            fontSize: 14.sp, // 28rpx / 2
                            color: const Color(0xFF616161),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),

          // 右侧图标
          GestureDetector(
            onTap: () => showPhone
                ? ref
                    .read(myEvaluateControllerProvider.notifier)
                    .callShipper(data.shipperMobile)
                : (() => _navigateToShipperDetail(context)),
            child: showPhone
                ? Icon(
                    Icons.phone_android,
                    size: 30.sp, // 60rpx / 2
                    color: AppColors.primary,
                  )
                : Icon(
                    Icons.chevron_right,
                    size: 30.sp, // 26rpx / 2
                    color: const Color(0xFF9E9E9E),
                  ),
          ),
        ],
      ),
    );
  }

  /// 跳转到配送员详情页面
  void _navigateToShipperDetail(final BuildContext context) {
    if (data.shipperId == null) return;

    context.push(
      AppPaths.shipperDetailPage,
      extra: {
        'shipperId': data.shipperId,
        'orderId': data.orderId,
      },
    );
  }
}
