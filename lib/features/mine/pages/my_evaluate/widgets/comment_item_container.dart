import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/data/models/mine/comment_list_model.dart';
import 'package:user_app/features/mine/pages/my_evaluate/my_evaluate_controller.dart';
import 'package:user_app/features/mine/pages/my_evaluate/widgets/comment_item.dart';
import 'package:user_app/core/widgets/dialogs/confirm_dialog.dart';
import 'package:user_app/generated/l10n.dart';

/// 单个评论项容器
class CommentItemContainer extends ConsumerWidget {
  /// 评论数据
  final CommentItem item;

  /// 在列表中的索引
  final int index;

  /// 构造函数
  const CommentItemContainer({
    super.key,
    required this.item,
    required this.index,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final controller = ref.read(myEvaluateControllerProvider.notifier);

    return CommentItemWidget(
      item: item,
      onDelete: () => _showDeleteConfirmDialog(context, controller, index),
    );
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(
    final BuildContext context,
    final MyEvaluateController controller,
    final int index,
  ) {
    ConfirmDialog.show(
      context,
      content: S.current.evaluate_delete_msg,
      onConfirm: () => controller.deleteComment(index),
    );
  }
}
