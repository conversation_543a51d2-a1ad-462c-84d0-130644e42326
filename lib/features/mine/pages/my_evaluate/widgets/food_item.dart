import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/data/models/mine/comment_list_model.dart';

/// 美食信息组件
class FoodItem extends StatelessWidget {
  /// 评论数据
  final CommentItem data;

  /// 构造函数
  const FoodItem({
    super.key,
    required this.data,
  });

  @override
  Widget build(final BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      child: Row(
        children: [
          // 美食图片
          ClipRRect(
            borderRadius: BorderRadius.circular(4.r),
            child: CachedNetworkImage(
              imageUrl: data.foodImage ?? '',
              width: 60.w,
              height: 60.w,
              fit: BoxFit.cover,
              placeholder: (final context, final url) => Container(
                color: Colors.grey[200],
                width: 60.w,
                height: 60.w,
              ),
              errorWidget: (final context, final url, final error) => Container(
                width: 60.w,
                height: 60.w,
                color: Colors.grey[200],
                child: Icon(Icons.restaurant, size: 24.sp, color: Colors.grey),
              ),
            ),
          ),

          // 美食信息
          SizedBox(width: 10.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 美食名称
                Text(
                  data.foodName ?? '',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.normal,
                    color: Colors.black87,
                  ),
                ),

                // 美食价格
                SizedBox(height: 5.h),
                if (data.foodPrice != null && data.foodPrice!.isNotEmpty)
                  Text(
                    "￥${data.foodPrice!.replaceAll("￥", "")}", // 确保价格格式正确
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: Colors.redAccent,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
