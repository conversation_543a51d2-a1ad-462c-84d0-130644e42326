import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/loading_more_widget.dart';
import 'package:user_app/data/models/mine/comment_list_model.dart';
import 'package:user_app/features/mine/pages/my_evaluate/my_evaluate_controller.dart';
import 'package:user_app/features/mine/pages/my_evaluate/widgets/comment_item_container.dart';
import 'package:user_app/core/theme/app_colors.dart';

/// 评论列表视图
class CommentListView extends ConsumerWidget {
  /// 评论列表
  final List<CommentItem> commentList;

  /// 是否正在加载
  final bool isLoading;

  /// 构造函数
  const CommentListView({
    super.key,
    required this.commentList,
    required this.isLoading,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final controller = ref.read(myEvaluateControllerProvider.notifier);

    return Container(
      color: AppColors.backgroundColor,
      margin: EdgeInsets.only(top: 5.h),
      child: RefreshIndicator(
        onRefresh: controller.refreshData,
        child: NotificationListener<ScrollNotification>(
          onNotification: (final notification) {
            // 滚动到底部时加载更多
            if (notification is ScrollEndNotification &&
                notification.metrics.extentAfter == 0 &&
                !isLoading &&
                ref.read(myEvaluateControllerProvider).canLoadMore) {
              controller.loadMoreData();
            }
            return false;
          },
          child: Consumer(
            builder: (final context, final ref, final child) {
              final canLoadMore = ref.watch(
                myEvaluateControllerProvider
                    .select((final state) => state.canLoadMore),
              );

              return ListView.builder(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).padding.bottom),
                physics: const AlwaysScrollableScrollPhysics(),
                itemCount: commentList.length + (canLoadMore ? 1 : 0),
                itemBuilder: (final context, final index) {
                  // 如果到达列表底部并且可以加载更多，显示加载提示
                  if (index == commentList.length) {
                    return const LoadingMoreWidget();
                  }

                  // 显示评论项
                  return CommentItemContainer(
                    item: commentList[index],
                    index: index,
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }
}
