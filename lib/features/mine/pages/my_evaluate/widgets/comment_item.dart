import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/mine/comment_list_model.dart';
import 'package:user_app/features/mine/pages/my_evaluate/widgets/food_item.dart';
import 'package:user_app/features/mine/pages/my_evaluate/widgets/shipper_item.dart';
import 'package:user_app/core/widgets/star_rating.dart';
import 'package:user_app/generated/l10n.dart';

/// 评论项组件
/// 评论项组件
/// 用于显示用户对配送员或美食的评价内容
/// 包含评分、文字评价、图片展示、商家回复等功能
class CommentItemWidget extends StatelessWidget {
  /// 评论数据模型
  final CommentItem item;

  /// 删除评论的回调函数
  final VoidCallback? onDelete;

  /// 构造函数
  /// [item] - 评论数据
  /// [onDelete] - 删除评论的回调函数
  const CommentItemWidget({
    super.key,
    required this.item,
    this.onDelete,
  });

  @override
  Widget build(final BuildContext context) {
    // 使用动画效果处理评论删除状态
    return AnimatedOpacity(
      opacity: item.deleted == true ? 0.0 : 1.0,
      duration: const Duration(milliseconds: 800),
      child: AnimatedSlide(
        offset: item.deleted == true ? const Offset(-2.0, 0.0) : Offset.zero,
        duration: const Duration(milliseconds: 800),
        curve: Curves.easeOutCubic,
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 10.r, vertical: 5.r),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(5.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                spreadRadius: 1.r,
                blurRadius: 5.r,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(10.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 根据评论类型显示不同的评价内容
                if (item.type == 1)
                  ShipperItem(data: item) // 配送员评价
                else if (item.type == 2)
                  FoodItem(data: item), // 美食评价

                // 评分部分
                _buildRatingSection(),

                // 文字评价内容
                if (item.text != null && item.text!.isNotEmpty)
                  Container(
                    margin: EdgeInsets.only(top: 10.r),
                    padding: EdgeInsets.all(7.r),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(5.r),
                    ),
                    child: Text(
                      item.text!,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF616161),
                      ),
                    ),
                  ),

                // 评价图片网格
                if (item.images != null && item.images!.isNotEmpty)
                  _buildImagesGrid(),

                // 商家回复内容
                if (item.replies != null && item.replies!.isNotEmpty)
                  _buildReplies(),

                // 底部操作栏（删除按钮和时间）
                _buildFooter(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建评分部分
  /// 包含总体评分、口味评分（美食评价）和包装评分（美食评价）
  Widget _buildRatingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 总体评分
        Padding(
          padding: EdgeInsets.only(top: 10.r),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                S.current.evaluate_in_general,
                style: TextStyle(
                  fontSize: 15.sp, // 30rpx / 2
                  color: AppColors.textSecondaryColor,
                ),
              ),
              SizedBox(width: 8.w), // 间距为8.w
              StarRating(
                rating: item.star?.toDouble() ?? 5.0,
                size: 16.sp, // 32rpx / 2
                gap: 4.0, // 设置星星间隔
                hideTip: true,
              ),
            ],
          ),
        ),

        // 美食评价特有的口味和包装评分
        if (item.type == 2 &&
            (item.star != item.foodStar || item.star != item.boxStar)) ...[
          // 口味评分
          Padding(
            padding: EdgeInsets.only(top: 10.r),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  S.current.evaluate_taste,
                  style: TextStyle(
                    fontSize: 15.sp, // 30rpx / 2
                    color: AppColors.textSecondaryColor,
                  ),
                ),
                SizedBox(width: 8.w),
                StarRating(
                  rating: item.foodStar?.toDouble() ?? 5.0,
                  size: 16.sp, // 32rpx / 2
                  gap: 4.0,
                  hideTip: true,
                ),
              ],
            ),
          ),

          // 包装评分
          Padding(
            padding: EdgeInsets.only(top: 10.r),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  S.current.evaluate_packing,
                  style: TextStyle(
                    fontSize: 15.sp, // 30rpx / 2
                    color: AppColors.textSecondaryColor,
                  ),
                ),
                SizedBox(width: 8.w),
                StarRating(
                  rating: item.boxStar?.toDouble() ?? 5.0,
                  size: 16.sp, // 32rpx / 2
                  gap: 4.0,
                  hideTip: true,
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// 构建图片网格
  /// 支持多图展示，点击可查看大图
  Widget _buildImagesGrid() {
    return Padding(
      padding: EdgeInsets.only(top: 10.r),
      child: Builder(
        builder: (final context) => Wrap(
          spacing: 10.w,
          runSpacing: 10.h,
          children: List.generate(
            item.images!.length,
            (final index) => GestureDetector(
              onTap: () => _openImageViewer(context, index),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6.r), // 12rpx / 2
                child: CachedNetworkImage(
                  imageUrl: item.images![index],
                  width: 65.w, // 130rpx / 2
                  height: 55.h, // 110rpx / 2
                  fit: BoxFit.cover,
                  // placeholder: (final context, final url) => Container(
                  //   color: Colors.grey[200],
                  //   child: Center(
                  //     child: SizedBox(
                  //       width: 20.w,
                  //       height: 20.h,
                  //       child: CircularProgressIndicator(
                  //         strokeWidth: 2.w,
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  errorWidget: (final context, final url, final error) =>
                      Container(
                    color: Colors.grey[200],
                    child: Icon(Icons.error, size: 20.sp),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 打开图片查看器
  /// [context] - 上下文
  /// [initialIndex] - 初始显示的图片索引
  void _openImageViewer(final BuildContext context, final int initialIndex) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (final context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            elevation: 0,
            iconTheme: const IconThemeData(color: Colors.white),
          ),
          body: PageView.builder(
            controller: PageController(initialPage: initialIndex),
            itemCount: item.images!.length,
            itemBuilder: (final context, final index) {
              return Center(
                child: InteractiveViewer(
                  minScale: 0.5,
                  maxScale: 3.0,
                  child: CachedNetworkImage(
                    imageUrl: item.images![index],
                    fit: item.images!.length == 1
                        ? BoxFit.contain
                        : BoxFit.cover,
                    // placeholder: (final context, final url) => Center(
                    //   child: CircularProgressIndicator(
                    //     color: Colors.white,
                    //     strokeWidth: 2.w,
                    //   ),
                    // ),
                    errorWidget: (final context, final url, final error) =>
                        Icon(
                      Icons.error,
                      color: Colors.white,
                      size: 30.sp,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 构建回复部分
  /// 显示商家对评价的回复内容
  Widget _buildReplies() {
    return Container(
      margin: EdgeInsets.only(top: 7.5.r), // 15rpx / 2
      padding: EdgeInsets.all(10.r), // 20rpx / 2
      decoration: BoxDecoration(
        color: AppColors.backgroundColor, // 使用全局颜色
        borderRadius: BorderRadius.circular(5.r), // 10rpx / 2
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: item.replies!.map((final reply) {
          return Padding(
            padding: EdgeInsets.only(bottom: 5.r), // 10rpx / 2
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: reply.type == 1
                        ? S.current.reply_res
                        : S.current.reply_mlz,
                    style: TextStyle(
                      fontSize: 17.sp, // 34rpx / 2
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF515F9C), // 保留特殊蓝色
                      fontFamily: AppConstants.mainFont, // 添加字体family
                    ),
                  ),
                  TextSpan(
                    text: ' ${reply.text}',
                    style: TextStyle(
                      fontSize: 15.sp, // 30rpx / 2
                      color: AppColors.textPrimaryColor, // 使用全局文本颜色
                      fontFamily: AppConstants.mainFont, // 添加字体family
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建底部（删除按钮和时间）
  Widget _buildFooter() {
    return Padding(
      padding: EdgeInsets.only(top: 15.r), // 30rpx / 2
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 删除按钮和状态
          GestureDetector(
            onTap: onDelete,
            child: Row(
              children: [
                // 删除图标
                Icon(
                  Icons.delete_outline,
                  size: 20.sp, // 38rpx / 2
                  color: AppColors.primary,
                ),
                // 状态文本
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 10.w),
                  child: Text(
                    item.state == 0
                        ? S.current.comment_reviewing
                        : item.state == 1
                            ? S.current.comment_reviewed
                            : S.current.comment_no_reviewing,
                    style: TextStyle(
                      fontSize: 16.sp, // 32rpx / 2
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 时间戳
          Text(
            item.createdAt ?? '',
            textDirection: TextDirection.ltr,
            style: TextStyle(
              fontSize: 12.5.sp, // 25rpx / 2
              color: const Color(0xFF757575), // 微信小程序使用的 #757575
            ),
          ),
        ],
      ),
    );
  }
}
