import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/widgets/empty_view.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/mine/pages/my_evaluate/my_evaluate_controller.dart';
import 'package:user_app/features/mine/pages/my_evaluate/widgets/comment_list_view.dart';
import 'package:user_app/generated/l10n.dart';

/// 评价列表主体内容
class MyEvaluateBody extends ConsumerWidget {
  /// 构造函数
  const MyEvaluateBody({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 分别监听所需的状态，只有相关状态变化时才会重建对应的Widget
    final isLoading = ref.watch(
      myEvaluateControllerProvider.select((state) => state.isLoading),
    );
    final commentList = ref.watch(
      myEvaluateControllerProvider.select((state) => state.commentList),
    );

    // 计算高度，考虑安全区域
    final screenHeight = MediaQuery.of(context).size.height;
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final appBarHeight = AppBar().preferredSize.height;
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final availableHeight =
        screenHeight - statusBarHeight - appBarHeight - bottomPadding;

    // 加载中且没有数据时显示加载界面
    if (isLoading && commentList.isEmpty) {
      return Container(
        color: const Color(0xFFEFF1F6), // 微信小程序背景色
        height: availableHeight,
        child: const Center(child: LoadingWidget()),
      );
    }

    // 没有数据时显示空视图
    if (!isLoading && commentList.isEmpty) {
      return Container(
        color: const Color(0xFFEFF1F6), // 微信小程序背景色
        height: availableHeight,
        child: EmptyView(
          message: S.current.comment_no_data,
        ),
      );
    }

    return CommentListView(
      commentList: commentList,
      isLoading: isLoading,
    );
  }
}
