import 'package:user_app/data/models/mine/comment_list_model.dart';

/// 我的评价页面状态
class MyEvaluateState {
  /// 是否加载中
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 评论列表
  final List<CommentItem> commentList;

  /// 当前页
  final int currentPage;

  /// 是否可以加载更多
  final bool canLoadMore;

  /// 是否正在删除评论
  final bool isDeleting;

  /// 构造函数
  const MyEvaluateState({
    this.isLoading = false,
    this.error,
    this.commentList = const [],
    this.currentPage = 1,
    this.canLoadMore = true,
    this.isDeleting = false,
  });

  /// 拷贝方法
  MyEvaluateState copyWith({
    bool? isLoading,
    String? error,
    List<CommentItem>? commentList,
    int? currentPage,
    bool? canLoadMore,
    bool? isDeleting,
  }) {
    return MyEvaluateState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      commentList: commentList ?? this.commentList,
      currentPage: currentPage ?? this.currentPage,
      canLoadMore: canLoadMore ?? this.canLoadMore,
      isDeleting: isDeleting ?? this.isDeleting,
    );
  }
}
