// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'courtesy_controller_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$courtesyControllerHash() =>
    r'9c427e32d9407181f1397a5d2ea30ac578536a6d';

/// 优惠券控制器
///
/// Copied from [CourtesyController].
@ProviderFor(CourtesyController)
final courtesyControllerProvider =
    AutoDisposeNotifierProvider<CourtesyController, CourtesyState>.internal(
  CourtesyController.new,
  name: r'courtesyControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$courtesyControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CourtesyController = AutoDisposeNotifier<CourtesyState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
