import 'package:user_app/data/models/user/courtesy_model.dart';

/// 优惠券状态
class CourtesyState {
  /// 构造函数
  const CourtesyState({
    this.isLoading = false,
    this.error,
    this.currentTab = 0,
    this.page = 1,
    this.hasMore = false,
    this.list = const [],
    this.notNow = const [],
    this.used = const [],
    this.expired = const [],
    this.newCoupons = const [],
  });

  /// 是否加载中
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 当前选中的标签（0: 我的优惠券, 1: 新的优惠券）
  final int currentTab;

  /// 当前页码
  final int page;

  /// 是否有更多数据
  final bool hasMore;

  /// 可使用的优惠券列表
  final List<Courtesy> list;

  /// 未到使用时间的优惠券列表
  final List<Courtesy> notNow;

  /// 已使用的优惠券列表
  final List<Courtesy> used;

  /// 已过期的优惠券列表
  final List<Courtesy> expired;

  /// 新的优惠券列表
  final List<Courtesy> newCoupons;

  /// 拷贝方法
  CourtesyState copyWith({
    final bool? isLoading,
    final String? error,
    final int? currentTab,
    final int? page,
    final bool? hasMore,
    final List<Courtesy>? list,
    final List<Courtesy>? notNow,
    final List<Courtesy>? used,
    final List<Courtesy>? expired,
    final List<Courtesy>? newCoupons,
  }) {
    return CourtesyState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      currentTab: currentTab ?? this.currentTab,
      page: page ?? this.page,
      hasMore: hasMore ?? this.hasMore,
      list: list ?? this.list,
      notNow: notNow ?? this.notNow,
      used: used ?? this.used,
      expired: expired ?? this.expired,
      newCoupons: newCoupons ?? this.newCoupons,
    );
  }
}
