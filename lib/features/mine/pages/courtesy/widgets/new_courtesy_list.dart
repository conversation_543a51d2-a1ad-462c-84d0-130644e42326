import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/mine/pages/courtesy/courtesy_controller_provider.dart';
import 'package:user_app/features/mine/pages/courtesy/widgets/courtesy_coupon_card.dart';
import 'package:user_app/generated/l10n.dart';

/// 新的优惠券列表
/// 包含以下功能:
/// - 显示空数据提示
class NewCourtesyList extends ConsumerWidget {
  /// 构造函数
  const NewCourtesyList({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final state = ref.watch(courtesyControllerProvider);
    final controller = ref.read(courtesyControllerProvider.notifier);

    if (state.isLoading && state.newCoupons.isEmpty) {
      return const SliverFillRemaining(
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (state.error != null && state.newCoupons.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Text(state.error!),
        ),
      );
    }

    if (state.newCoupons.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Text(S.of(context).courtesy_page_no_data1,style: TextStyle(
            fontSize: 16.sp,
            color: Colors.grey[600],
          ),),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (final context, final index) {
          if (index == state.newCoupons.length) {
            if (state.isLoading) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }
            return null;
          }

          final courtesy = state.newCoupons[index];
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 10.w),
            child: CourtesyCouponCard(
              coupon: courtesy,
              controller: controller,
              showTakeButton: true,
            ),
          );
        },
        childCount: state.newCoupons.length + (state.hasMore ? 1 : 0),
      ),
    );
  }
}
