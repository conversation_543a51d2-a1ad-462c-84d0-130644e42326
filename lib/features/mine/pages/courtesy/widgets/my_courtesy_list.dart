import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/mine/pages/courtesy/courtesy_controller_provider.dart';
import 'package:user_app/features/mine/pages/courtesy/courtesy_state.dart';
import 'package:user_app/features/mine/pages/courtesy/widgets/courtesy_coupon_card.dart';
import 'package:user_app/generated/l10n.dart';

/// 我的优惠券列表
/// 我的优惠券列表组件
/// 展示用户的优惠券,包括:
/// - 可使用的优惠券 [CourtesyState.list]
/// - 暂不可用的优惠券 [CourtesyState.notNow]
/// - 已使用的优惠券 [CourtesyState.used]
/// - 已过期的优惠券 [CourtesyState.expired]
class MyCourtesyList extends ConsumerWidget {
  /// 构造函数
  const MyCourtesyList({
    super.key,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final state = ref.watch(courtesyControllerProvider);
    // 当所有类型的优惠券列表都为空时,显示无数据提示
    if (state.list.isEmpty &&
        state.notNow.isEmpty &&
        state.used.isEmpty &&
        state.expired.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Text(
            S.current.courtesy_card_no_data,
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
            ),
          ),
        ),
      );
    }

    final List<Widget> children = [];

    // 可使用的优惠券列表
    if (state.list.isNotEmpty) {
      children.add(
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          child: Text(
            S.current.courtesy_page_tab2,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
      children.addAll(
        state.list.map(
          (final coupon) => Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 5.h),
            child: CourtesyCouponCard(
              coupon: coupon,
              controller: ref.read(courtesyControllerProvider.notifier),
            ),
          ),
        ),
      );
    }

    // 暂不可用的优惠券列表
    if (state.notNow.isNotEmpty) {
      children.add(
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          child: Text(
            S.current.courtesy_card_not_now_text,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
      children.addAll(
        state.notNow.map(
          (final coupon) => Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 5.h),
            child: CourtesyCouponCard(
              coupon: coupon,
              controller: ref.read(courtesyControllerProvider.notifier),
              isGray: true,
            ),
          ),
        ),
      );
    }

    // 已使用的优惠券列表
    if (state.used.isNotEmpty) {
      children.add(
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          child: Text(
            S.current.courtesy_card_use_text,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
      children.addAll(
        state.used.map(
          (final coupon) => Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 5.h),
            child: CourtesyCouponCard(
              coupon: coupon,
              controller: ref.read(courtesyControllerProvider.notifier),
              isGray: true,
            ),
          ),
        ),
      );
    }

    // 已过期的优惠券列表
    if (state.expired.isNotEmpty) {
      children.add(
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          child: Text(
            S.current.courtesy_card_expire_text,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
      children.addAll(
        state.expired.map(
          (final coupon) => Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 5.h),
            child: CourtesyCouponCard(
              coupon: coupon,
              controller: ref.read(courtesyControllerProvider.notifier),
              isGray: true,
            ),
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildListDelegate(children),
    );
  }
}
