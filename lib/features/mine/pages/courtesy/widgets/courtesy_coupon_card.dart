import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/folded_corner_card.dart';
import 'package:user_app/data/models/user/courtesy_model.dart';
import 'package:user_app/features/mine/pages/courtesy/courtesy_controller_provider.dart';
import 'package:user_app/generated/l10n.dart';

/// 优惠券卡片
class CourtesyCouponCard extends ConsumerWidget {
  /// 构造函数
  const CourtesyCouponCard({
    required this.coupon,
    required this.controller,
    this.showTakeButton = false,
    this.isGray = false,
    this.isUsing = false,
    this.isShowNoAvailable = false,
    super.key,
  });

  /// 优惠券数据
  final Courtesy coupon;

  /// 控制器
  final CourtesyController controller;

  /// 是否显示领取按钮
  final bool showTakeButton;

  /// 是否显示灰色（已使用/已过期）
  final bool isGray;

  /// 是否使用中
  final bool isUsing;

  /// 是否显示没有满足条件的优惠券
  final bool isShowNoAvailable;

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return _CourtesyCouponCardContent(
      coupon: coupon,
      controller: controller,
      showTakeButton: showTakeButton,
      isGray: isGray,
      isUsing: isUsing,
      isShowNoAvailable: isShowNoAvailable,
    );
  }
}

/// 优惠券卡片内容
/// 使用StatelessWidget避免不必要的重建
class _CourtesyCouponCardContent extends StatelessWidget {
  /// 构造函数
  const _CourtesyCouponCardContent({
    required this.coupon,
    required this.controller,
    required this.showTakeButton,
    required this.isGray,
    required this.isUsing,
    required this.isShowNoAvailable,
  });

  /// 优惠券数据
  final Courtesy coupon;

  /// 控制器
  final CourtesyController controller;

  /// 是否显示领取按钮
  final bool showTakeButton;

  /// 是否显示灰色（已使用/已过期）
  final bool isGray;

  /// 是否使用中
  final bool isUsing;

  /// 是否显示没有满足条件的优惠券
  final bool isShowNoAvailable;

  @override
  Widget build(final BuildContext context) {
    // 检查是否为维吾尔语
    final isUg = Localizations.localeOf(context).languageCode == 'en';

    // 根据不同状态设置颜色
    final Color priceBoxColor =
        isGray ? Colors.grey[400]! : const Color(0xFFFF190D);
    final Color priceBoxColorGradient =
        isGray ? Colors.grey[300]! : const Color.fromARGB(255, 255, 129, 6);
    final Color infoBoxColor =
        isGray ? Colors.grey[100]! : const Color(0xFFFEEBC0);
    final Color textColor =
        isGray ? Colors.grey[600]! : const Color(0xFFFF260C);
    final Color borderColor =
        isGray ? Colors.grey[300]! : const Color(0xFFF5cfac);
    final Color foldColor =
        isGray ? Colors.grey[300]! : const Color(0xFFE6D4A7);

    final double height = 75.h;

    // 统一的边框宽度
    final double borderWidth = 2.5.w;

    //左侧缩进
    final double priceBoxWidth = 75.w;
    return Container(
      height: height,
      margin: EdgeInsets.symmetric(vertical: 5.h),
      child: Stack(
        children: [
          // 信息区域 - 放在底层
          Positioned(
            left: isUg ? 0 : priceBoxWidth, // 根据语言调整位置
            right: isUg ? priceBoxWidth : 0, // 根据语言调整位置
            top: 0,
            bottom: 0,
            child: FoldedCornerCard(
              backgroundColor: Colors.transparent,
              foldColor: foldColor,
              borderColor: borderColor,
              foldSize: 15.r,
              borderWidth: borderWidth,
              isRtl: isUg, // 根据语言设置RTL模式
              child: Container(
                height: height,
                decoration: BoxDecoration(
                  color: infoBoxColor,
                  border: Border.all(
                    color: borderColor,
                    width: borderWidth,
                  ),
                  borderRadius: BorderRadius.only(
                    topRight: isUg ? Radius.zero : Radius.circular(8.r),
                    bottomRight: isUg ? Radius.zero : Radius.circular(8.r),
                    topLeft: isUg ? Radius.circular(8.r) : Radius.zero,
                    bottomLeft: isUg ? Radius.circular(8.r) : Radius.zero,
                  ),
                ),
                padding: EdgeInsets.fromLTRB(
                    isUg ? 15.w : 20.w, 5.h, isUg ? 20.w : 15.w, 5.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: isUsing
                      ? MainAxisAlignment.center
                      : MainAxisAlignment.spaceBetween,
                  children: [
                    // 标题
                    Text(
                      S.current.courtesy_card_item_title
                          .replaceAll(
                              '%min_price',
                              coupon.minPrice % 1 == 0
                                  ? coupon.minPrice.toInt().toString()
                                  : coupon.minPrice.toString())
                          .replaceAll(
                              '%price',
                              coupon.price % 1 == 0
                                  ? coupon.price.toInt().toString()
                                  : coupon.price.toString()),
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: textColor,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // 使用条件和按钮
                    Row(
                      textDirection:
                          isUg ? TextDirection.rtl : TextDirection.ltr,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // 使用条件
                              Text(
                                coupon.notice,
                                style: TextStyle(
                                  fontSize: 11.sp,
                                  color: textColor,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textAlign:
                                    isUg ? TextAlign.right : TextAlign.left,
                              ),

                              // 使用区域
                              ...[
                                SizedBox(height: 2.h),
                                if (coupon.useArea.isNotEmpty ||
                                    coupon.areaName.isNotEmpty)
                                  Text(
                                    '${coupon.useArea} : ${coupon.areaName}',
                                    style: TextStyle(
                                      fontSize: 10.sp,
                                      color: textColor,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                              ],
                            ],
                          ),
                        ),

                        // 领取按钮 - 提取为单独方法以减少重建范围
                        if (showTakeButton) _buildTakeButton(textColor),
                      ],
                    ),

                    // 有效期
                    Text(
                      '${coupon.startUseTime.replaceAll("-", "/")}~${coupon.endUseTime.replaceAll("-", "/")}',
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: textColor,
                      ),
                      maxLines: 1,
                      textDirection: TextDirection.ltr,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (isShowNoAvailable)
                      Text(
                        S.current.courtesy_card_not_select,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.textPrimaryColor,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),

          // 金额区域 - 放在上层
          Positioned(
            right: isUg ? 0 : null, // 在维吾尔语模式下放右侧
            left: isUg ? null : 0, // 在中文模式下放左侧
            top: 0,
            bottom: 0,
            width: priceBoxWidth,
            child: _PriceBox(
              price: coupon.price,
              backgroundColor: priceBoxColor,
              priceBoxColorGradient: priceBoxColorGradient,
              borderColor: borderColor,
              borderWidth: borderWidth,
              height: height,
              isRtl: isUg, // 传递RTL信息给PriceBox
            ),
          ),
        ],
      ),
    );
  }

  /// 构建领取按钮
  Widget _buildTakeButton(final Color textColor) {
    return Container(
      height: 28.h,
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isGray
              ? [Colors.grey[300]!, Colors.grey[300]!]
              : [
                  const Color(0xFFFF190D),
                  const Color(0xFFFF8106),
                ],
        ),
        borderRadius: BorderRadius.circular(28.r),
      ),
      child: TextButton(
        onPressed: () => controller.takeCoupon((coupon.id ?? 0).toString()),
        style: TextButton.styleFrom(
          padding: EdgeInsets.zero,
          minimumSize: Size.zero,
        ),
        child: Text(
          S.current.courtesy_page_new,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}

/// 价格框组件
/// 提取为单独组件以优化性能
class _PriceBox extends StatelessWidget {
  const _PriceBox({
    required this.price,
    required this.backgroundColor,
    required this.priceBoxColorGradient,
    required this.borderColor,
    required this.borderWidth,
    required this.height,
    this.isRtl = false,
  });

  final num price;
  final Color backgroundColor;
  final Color priceBoxColorGradient;
  final Color borderColor;
  final double borderWidth;
  final double height;
  final bool isRtl;

  @override
  Widget build(final BuildContext context) {
    return CustomPaint(
      painter: _PriceBoxPainter(
        backgroundColor: backgroundColor,
        priceBoxColorGradient: priceBoxColorGradient,
        borderColor: borderColor,
        borderWidth: borderWidth,
        height: height,
        isRtl: isRtl, // 传递RTL信息给绘制器
      ),
      child: SizedBox(
        width: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 5.w, right: isRtl ? 5.w : 0),
              child: Text(
                '¥ ${price % 1 == 0 ? price.toInt() : price}',
                style: TextStyle(
                  fontSize: 25.sp,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 金额区域绘制器 - 实现凸出的扇区效果
class _PriceBoxPainter extends CustomPainter {
  const _PriceBoxPainter({
    required this.backgroundColor,
    required this.priceBoxColorGradient,
    required this.borderColor,
    required this.borderWidth,
    required this.height,
    required this.isRtl,
  });

  final Color backgroundColor;
  final Color priceBoxColorGradient;
  final Color borderColor;
  final double borderWidth;
  final double height;
  final bool isRtl;

  @override
  void paint(final Canvas canvas, final Size size) {
    final double width = size.width;
    final double height = size.height;

    // 绘制路径
    final Path path = Path();

    // 圆角参数
    final double cornerRadius = 8.0;

    // 曲线参数
    final double curveRadius = 40.0;

    if (isRtl) {
      // 维吾尔语模式下，绘制凸出在左侧的路径
      // 右上角圆角
      path.moveTo(width, cornerRadius);
      path.quadraticBezierTo(width, 0, width - cornerRadius, 0);

      // 上边
      path.lineTo(0, 0);

      // 左侧曲线 - 使用三次贝塞尔曲线创建更自然的凸出效果
      path.cubicTo(
        -curveRadius * 0.45, height * 0.25, // 第一个控制点
        -curveRadius * 0.45, height * 0.75, // 第二个控制点
        0, height, // 终点
      );

      // 下边
      path.lineTo(width - cornerRadius, height);

      // 右下角圆角
      path.quadraticBezierTo(width, height, width, height - cornerRadius);

      // 右边
      path.lineTo(width, cornerRadius);
    } else {
      // 中文模式下，绘制凸出在右侧的路径
      // 左上角圆角
      path.moveTo(0, cornerRadius);
      path.quadraticBezierTo(0, 0, cornerRadius, 0);

      // 上边
      path.lineTo(width, 0);

      // 右侧曲线 - 使用三次贝塞尔曲线创建更自然的凸出效果
      path.cubicTo(
        width + curveRadius * 0.45, height * 0.25, // 第一个控制点
        width + curveRadius * 0.45, height * 0.75, // 第二个控制点
        width, height, // 终点
      );

      // 下边
      path.lineTo(cornerRadius, height);

      // 左下角圆角
      path.quadraticBezierTo(0, height, 0, height - cornerRadius);

      // 左边
      path.lineTo(0, cornerRadius);
    }

    // 填充渐变背景色
    final Paint fillPaint = Paint();

    // CSS: linear-gradient(-39.99deg, rgb(255, 25, 13) 16.051%, rgb(255, 129, 6) 95.574%)
    // Angle calculation for Flutter Alignment:
    // rad = angle * pi / 180
    // begin = Alignment(sin(rad), -cos(rad))
    // end = Alignment(-sin(rad), cos(rad))
    // Note: Using pre-calculated approximate values for sin/cos for clarity.
    // For higher precision, use dart:math `sin` and `cos`.
    const double sinAngle = -0.6426; // sin(-39.99 * pi / 180)
    const double cosAngle = 0.7661; // cos(-39.99 * pi / 180)

    // LTR Alignment (gradient from bottom-leftish to top-rightish)
    const Alignment ltrBegin =
        Alignment(sinAngle, -cosAngle); // (-0.6426, -0.7661)
    const Alignment ltrEnd = Alignment(-sinAngle, cosAngle); // (0.6426, 0.7661)

    // RTL Alignment (flip horizontally: gradient from bottom-rightish to top-leftish)
    const Alignment rtlBegin =
        Alignment(-sinAngle, -cosAngle); // (0.6426, -0.7661)
    const Alignment rtlEnd = Alignment(sinAngle, cosAngle); // (-0.6426, 0.7661)

    fillPaint.shader = LinearGradient(
      begin: isRtl ? rtlBegin : ltrBegin,
      end: isRtl ? rtlEnd : ltrEnd,
      colors: [
        backgroundColor, // rgb(255, 25, 13)
        priceBoxColorGradient, // rgb(255, 129, 6)
      ],
      stops: const [
        0.16051, // 16.051%
        0.95574, // 95.574%
      ],
    ).createShader(Rect.fromLTWH(0, 0, width, height));
    fillPaint.style = PaintingStyle.fill;

    canvas.drawPath(path, fillPaint);

    // 绘制边框
    final Paint borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant final _PriceBoxPainter oldDelegate) {
    return oldDelegate.backgroundColor != backgroundColor ||
        oldDelegate.borderColor != borderColor ||
        oldDelegate.borderWidth != borderWidth ||
        oldDelegate.height != height ||
        oldDelegate.isRtl != isRtl;
  }
}
