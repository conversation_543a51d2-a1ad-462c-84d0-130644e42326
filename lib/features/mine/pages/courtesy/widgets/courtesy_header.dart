import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/mine/pages/courtesy/courtesy_state.dart';
import 'package:user_app/generated/l10n.dart';

/// 优惠券页面标签栏
class CourtesyHeader extends StatelessWidget {
  /// 构造函数
  const CourtesyHeader({
    required this.state,
    required this.onTabTap,
    required this.tabController,
    super.key,
  });

  /// 状态
  final CourtesyState state;

  /// 点击标签回调
  final void Function(int) onTabTap;

  /// Tab控制器
  final TabController tabController;

  @override
  Widget build(final BuildContext context) {
    return Container(
      height: 44.h,
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(25.r),
          bottomRight: Radius.circular(25.r),
        ),
      ),
      child: Tab<PERSON><PERSON>(
        controller: tabController,
        // 选中标签文本颜色
        labelColor: Colors.white,
        // 未选中标签文本颜色
        unselectedLabelColor: Colors.white,
        // 选中标签文本样式
        labelStyle: TextStyle(
          fontSize: 20.sp,
          fontWeight: FontWeight.w600,
          fontFamily: 'UkijTuzTom',
        ),
        // 未选中标签文本样式
        unselectedLabelStyle: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.normal,
          fontFamily: 'UkijTuzTom',
        ),
        // 指示器样式
        indicator: UnderlineTabIndicator(
          // 指示器边框样式
          borderSide: BorderSide(width: 3.h, color: Colors.white),
          // 指示器边框圆角
          borderRadius: BorderRadius.circular(5.r),
          // 指示器宽度
          insets: EdgeInsets.symmetric(horizontal: 80.w),
        ),
        // 指示器内边距
        indicatorPadding: EdgeInsets.only(bottom: 5.h),
        // 移除分隔线
        dividerHeight: 0,
        // 指示器大小与标签文本同宽
        indicatorSize: TabBarIndicatorSize.tab,
        tabs: [
       
          _buildTab(
            text: S.current.courtesy_page_tab2,
            showBadge: state.list.isNotEmpty,
          ),
             _buildTab(
            text: S.current.courtesy_page_tab1,
            showBadge: state.newCoupons.isNotEmpty,
            badgeCount: state.newCoupons.length,
          ),
        ],
      ),
    );
  }

  /// 构建标签
  Widget _buildTab({
    required final String text,
    required final bool showBadge,
    final int? badgeCount,
  }) {
    return Tab(
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Text(text),
          if (showBadge)
            Positioned(
              right: -5.w,
              top: -10.h,
              child: Container(
                alignment: Alignment.center,
                constraints: BoxConstraints.tight(
                  badgeCount == null ? Size(12.w, 12.w) : Size(14.w, 14.w),
                ),
                decoration: BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 1.h),
                ),
                child: badgeCount == null
                    ? const SizedBox.shrink()
                    : Text(
                        badgeCount.toString(),
                        style: TextStyle(
                    fontSize: 10.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
