import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/features/mine/pages/courtesy/courtesy_state.dart';
import 'package:user_app/features/mine/services/courtesy_service.dart';
import 'package:user_app/main.dart';

part 'courtesy_controller_provider.g.dart';

/// 优惠券控制器
@riverpod
class CourtesyController extends _$CourtesyController {
  late final TabController _tabController;

  /// 获取TabController
  TabController get tabController => _tabController;

  @override
  CourtesyState build() {
    // 加载优惠券数据
    Future.microtask(() => loadCourtesyData());
    // 监听登录状态，登录成功后重新加载数据
    ref.listen(isLoggedInProvider, (final previous, final next) {
      if (next) {
        Future.microtask(() => loadCourtesyData());
      }
    });
    ref.onDispose(() {
      try{
        _tabController.dispose();

      }catch(e){

      }
    });

    return const CourtesyState();
  }

  /// 初始化TabController
  void initTabController(final TickerProvider vsync) {
    _tabController = TabController(length: 2, vsync: vsync);
    _tabController.addListener(_handleTabChange);
  }

  /// 处理标签变化
  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      // 使用Future延迟状态更新，避免在构建期间修改状态
      Future(() {
        switchTab(_tabController.index);
      });
    }
  }

  /// 切换到指定标签
  void animateToTab(final int index) {
    if (!_tabController.indexIsChanging) {
      _tabController.animateTo(index);
    }
  }

  /// 同步标签状态
  void syncTabState() {
    if (_tabController.index != state.currentTab) {
      _tabController.animateTo(state.currentTab);
    }
  }

  /// 加载优惠券数据
  Future<void> loadCourtesyData() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final service = ref.read(courtesyServiceProvider.notifier);
      final response = await service.getCourtesyList(
        page: 1,
        pageSize: 10,
      );
      final data = response.data;

      // 检查是否有新优惠券，如果有则切换到index=1标签
      final bool hasNewCoupons = data?.newCoupons.isNotEmpty ?? false;
      final int targetTab = hasNewCoupons ? 1 : state.currentTab;

      state = state.copyWith(
        isLoading: false,
        list: data?.list,
        notNow: data?.notNow,
        used: data?.used,
        expired: data?.expired,
        newCoupons: data?.newCoupons,
        page: 1,
        hasMore: data?.hasMore,
        currentTab: targetTab,
      );

      // 如果有新优惠券，同步TabController到index=1
      if (hasNewCoupons && _tabController.index != 1) {
        Future.microtask(() => _tabController.animateTo(1));
      }
    } catch (e) {
      _handleError(e);
    }
  }

  /// 切换标签
  void switchTab(final int index) {
    state = state.copyWith(currentTab: index);
  }

  /// 加载更多
  Future<void> loadMore() async {
    if (!state.hasMore || state.isLoading) {
      return;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final service = ref.read(courtesyServiceProvider.notifier);
      final response = await service.loadMore(
        page: state.page + 1,
        pageSize: 10,
      );
      final data = response.data;
      if (data == null) {
        state = state.copyWith(
          isLoading: false,
        );
        return;
      }

      state = state.copyWith(
        isLoading: false,
        list: [...state.list, ...data.list],
        notNow: [...state.notNow, ...data.notNow],
        used: [...state.used, ...data.used],
        expired: [...state.expired, ...data.expired],
        newCoupons: [...state.newCoupons, ...data.newCoupons],
        page: state.page + 1,
        hasMore: data.hasMore,
      );
    } catch (e) {
      _handleError(e);
    }
  }

  /// 领取优惠券
  Future<void> takeCoupon(final String ids,{int? userId}) async {
    if (state.isLoading) {
      return;
    }

    LoadingDialog().show();
    try {
      final service = await ref.read(courtesyServiceProvider.notifier);
      String resMsg = await service.takeCoupon(ids,userId: userId);
      await loadCourtesyData();
      BotToast.showText(text: resMsg);
    } catch (e) {
      _handleError(e);
      state = state.copyWith(isLoading: false, error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false, error: null);
      LoadingDialog().hide();
    }
  }

  /// 刷新数据
  Future<void> refresh() async {
    return loadCourtesyData();
  }

  /// 处理错误
  void _handleError(final Object error) {
    state = state.copyWith(
      isLoading: false,
      error: error.toString(),
    );
  }
}
