import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/features/mine/pages/courtesy/courtesy_controller_provider.dart';
import 'package:user_app/features/mine/pages/courtesy/widgets/courtesy_header.dart';
import 'package:user_app/features/mine/pages/courtesy/widgets/my_courtesy_list.dart';
import 'package:user_app/features/mine/pages/courtesy/widgets/new_courtesy_list.dart';
import 'package:user_app/generated/l10n.dart';

/// 优惠券页面
/// 展示用户的优惠券信息，包括新的可领取优惠券和已领取的优惠券
/// 使用TabBar实现两个标签页的切换
/// 支持下拉刷新获取最新数据
class CourtesyPage extends ConsumerStatefulWidget {
  /// 构造函数
  const CourtesyPage({super.key});

  @override
  ConsumerState<CourtesyPage> createState() => _CourtesyPageState();
}

class _CourtesyPageState extends ConsumerState<CourtesyPage>
    with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
    ref.read(courtesyControllerProvider.notifier).initTabController(this);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    ref.read(courtesyControllerProvider.notifier).syncTabState();
  }

  Widget _buildCourtesyList() {

    return Column(
      children: [
        Expanded(
          child: Container(
            color: const Color(0xFFEFF0F5),
            child: TabBarView(
              controller: ref.read(courtesyControllerProvider.notifier).tabController,
              children: [

                // 我的优惠券
                Container(
                  margin: EdgeInsets.all(10.r),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: CustomScrollView(
                    slivers: [
                      MyCourtesyList(),
                    ],
                  ),
                ),

                    
                // 新的优惠券
                Container(
                  margin: EdgeInsets.all(10.r),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: CustomScrollView(
                    slivers: [
                      const NewCourtesyList(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final controller = ref.read(courtesyControllerProvider.notifier);
    final state = ref.watch(courtesyControllerProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: S.current.courtesy_page_title,
      ),
      body: Column(
          children: [
            CourtesyHeader(
              state: state,
              tabController: controller.tabController,
              onTabTap: controller.animateToTab,
            ),
            Expanded(
              child: _buildCourtesyList(),
            ),
          ],
      ),
    );
  }
}

/// A container that wraps a sliver and applies decoration
class SliverContainer extends StatelessWidget {
  const SliverContainer({
    required this.sliver,
    this.color,
    this.borderRadius,
    super.key,
  });

  final Widget sliver;
  final Color? color;
  final BorderRadius? borderRadius;

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Container(
        decoration: BoxDecoration(
          color: color,
          borderRadius: borderRadius,
        ),
        child: sliver,
      ),
    );
  }
}
