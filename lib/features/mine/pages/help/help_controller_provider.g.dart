// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'help_controller_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$helpControllerHash() => r'1a1e0526818b46f9f3a15ff6f9ad2015e3ab39c6';

/// See also [HelpController].
@ProviderFor(HelpController)
final helpControllerProvider =
    AutoDisposeNotifierProvider<HelpController, HelpState>.internal(
  HelpController.new,
  name: r'helpControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$helpControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$HelpController = AutoDisposeNotifier<HelpState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
