import 'package:user_app/data/models/mine/help_model.dart';

class HelpState {
  final List<HelpModel> helpList;
  final bool isLoading;
  final String? error;

  const HelpState({
    this.helpList = const [],
    this.isLoading = false,
    this.error,
  });

  HelpState copyWith({
    List<HelpModel>? helpList,
    bool? isLoading,
    String? error,
  }) {
    return HelpState(
      helpList: helpList ?? this.helpList,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}
