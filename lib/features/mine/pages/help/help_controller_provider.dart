import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/config/api_config.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/repositories/user/user_repository.dart';
import 'package:user_app/features/mine/pages/help/help_state.dart';
import 'package:user_app/main.dart';
import 'package:user_app/routes/app_router.dart';
import 'package:user_app/routes/paths.dart';

part 'help_controller_provider.g.dart';

@riverpod
class HelpController extends _$HelpController {
  @override
  HelpState build() {
    // 初始化时获取数据
    Future.microtask(() {
      fetchHelpList();
    });

    return const HelpState();
  }

  /// 获取帮助列表
  Future<void> fetchHelpList() async {
    state = state.copyWith(isLoading: true);
    try {
      final helpList = await ref.read(userRepositoryProvider).fetchHelpList();
      state = state.copyWith(
        helpList: helpList.data,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 打开帮助详情页面
  void openHelpDetail(final int id, final String title) {
    String lang = globalContainer.read(languageProvider);
    router.push(AppPaths.webViewPage, extra: {
      'url': '${ApiConfig.baseUrl}/$lang/v1/help/detail?help_id=$id',
      'title': title,
    });
  }
}
