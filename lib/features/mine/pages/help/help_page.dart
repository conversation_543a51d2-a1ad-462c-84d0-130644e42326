import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/data/models/mine/help_model.dart';
import 'package:user_app/features/mine/pages/help/help_controller_provider.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/core/widgets/loading_widget.dart';

/// 帮助页面
class HelpPage extends ConsumerWidget {
  /// 构造函数
  const HelpPage({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 获取帮助页面状态
    final state = ref.watch(helpControllerProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: S.current.about_help,
      ),
      body: state.isLoading
          // 加载中显示加载组件
          ? const LoadingWidget()
          // 有错误时显示错误信息
          : state.error != null
              ? Center(child: Text(state.error!))
              // 显示帮助列表
              : ListView.builder(
                  padding: EdgeInsets.all(4.w),
                  itemCount: state.helpList.length,
                  itemBuilder: (final context, final index) {
                    final item = state.helpList[index];
                    return _buildHelpItem(context, ref, item);
                  },
                ),
    );
  }

  /// 构建帮助项目卡片
  ///
  /// [context] - 构建上下文
  /// [ref] - Riverpod引用
  /// [item] - 帮助项目数据模型
  Widget _buildHelpItem(
      final BuildContext context, final WidgetRef ref, final HelpModel item) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(5.w),
      ),
      child: ListTile(
        onTap: () => ref
            .read(helpControllerProvider.notifier)
            .openHelpDetail(item.id, item.title),
        title: Text(
          item.title,
          style: TextStyle(
            fontSize: 16.sp,
            color: AppColors.textPrimaryColor,
          ),
        ),
      ),
    );
  }
}
