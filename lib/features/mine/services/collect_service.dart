import 'package:bot_toast/bot_toast.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/data/models/collect/collect_food_list.dart'
    as CollectFoodList;
import 'package:user_app/data/models/collect/collect_store_list.dart';
import 'package:user_app/data/repositories/collect/collect_repository.dart';

part 'collect_service.g.dart';

/// 收藏服务
@riverpod
class CollectService extends _$CollectService {
  @override
  void build() {}

  /// 获取餐厅收藏列表
  Future<ApiResult<CollectStoreData>> getStoreCollectList() async {
    try {
      final repository = ref.read(collectRepositoryProvider);
      final storeList = await repository.getCollectList(100);
      if (storeList.status != 200) {
        BotToast.showText(text: storeList.msg ?? '获取餐厅收藏列表失败');
      }
      return storeList;
    } catch (e) {
      rethrow;
    }
  }

  /// 获取美食收藏列表
  Future<CollectFoodList.CollectFoodData?> getFoodCollectList() async {
    try {
      final repository = ref.read(collectRepositoryProvider);
      final foodList = await repository.getFoodCollectList(100);
      if (foodList.status != 200) {
        BotToast.showText(text: foodList.msg ?? '获取美食收藏列表失败');
      }
      return foodList.data;
    } catch (e) {
      rethrow;
    }
  }
}
