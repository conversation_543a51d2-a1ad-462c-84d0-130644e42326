// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mine_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$mineServiceHash() => r'0603839c6a50a9069741c86baab6308c0b28e3e0';

/// 个人中心业务逻辑服务
///
/// Copied from [MineService].
@ProviderFor(MineService)
final mineServiceProvider =
    AutoDisposeNotifierProvider<MineService, void>.internal(
  MineService.new,
  name: r'mineServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$mineServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MineService = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
