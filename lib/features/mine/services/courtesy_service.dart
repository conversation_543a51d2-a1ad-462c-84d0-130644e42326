import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/data/models/user/courtesy_model.dart';
import 'package:user_app/data/repositories/user/courtesy_repository.dart';
part 'courtesy_service.g.dart';

/// 优惠券服务
@riverpod
class CourtesyService extends _$CourtesyService {
  @override
  void build() {}

  /// 获取优惠券列表
  Future<ApiResult<CourtesyListResponse>> getCourtesyList({
    required final int page,
    required final int pageSize,
  }) async {
    final repository = ref.read(courtesyRepositoryProvider);
    return await repository.getCourtesyList(
      page: page,
      pageSize: pageSize,
    );
  }

  /// 领取优惠券
  Future<String> takeCoupon(final String ids,{int? userId}) async {
    final repository = await ref.read(courtesyRepositoryProvider);
    ApiResult<dynamic> res = await repository.takeCoupon(ids,userId: userId);
    return res.msg;
  }

  /// 加载更多优惠券
  Future<ApiResult<CourtesyListResponse>> loadMore({
    required final int page,
    required final int pageSize,
  }) async {
    return await getCourtesyList(
      page: page,
      pageSize: pageSize,
    );
  }
}
