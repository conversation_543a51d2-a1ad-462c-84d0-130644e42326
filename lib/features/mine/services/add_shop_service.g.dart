// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_shop_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$addShopServiceHash() => r'b0564b9d0f6e9f00fdc876b2e33955d6f55dacbf';

/// 商家入驻服务
///
/// Copied from [AddShopService].
@ProviderFor(AddShopService)
final addShopServiceProvider =
    AutoDisposeAsyncNotifierProvider<AddShopService, void>.internal(
  AddShopService.new,
  name: r'addShopServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$addShopServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AddShopService = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
