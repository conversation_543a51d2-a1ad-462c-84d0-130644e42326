// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$uiServiceHash() => r'ddc73b1115bf4feb9d5e80df026d0299bb00c3cb';

/// UI服务提供者
///
/// Copied from [uiService].
@ProviderFor(uiService)
final uiServiceProvider = AutoDisposeProvider<ProfileUiService>.internal(
  uiService,
  name: r'uiServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$uiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UiServiceRef = AutoDisposeProviderRef<ProfileUiService>;
String _$profileServiceHash() => r'd9cf61ba564d20494e8d1a431f744999300fc7c8';

/// 个人资料业务逻辑
///
/// Copied from [ProfileService].
@ProviderFor(ProfileService)
final profileServiceProvider =
    AutoDisposeNotifierProvider<ProfileService, void>.internal(
  ProfileService.new,
  name: r'profileServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$profileServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProfileService = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
