// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'collect_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$collectServiceHash() => r'2c31ea567647d00b48432675900c2a7a951a8f5b';

/// 收藏服务
///
/// Copied from [CollectService].
@ProviderFor(CollectService)
final collectServiceProvider =
    AutoDisposeNotifierProvider<CollectService, void>.internal(
  CollectService.new,
  name: r'collectServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$collectServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CollectService = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
