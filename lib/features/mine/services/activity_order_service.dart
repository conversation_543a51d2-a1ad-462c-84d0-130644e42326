import 'package:flutter/foundation.dart';
import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/data/models/activity_order/lottery_detail_model.dart';
import 'package:user_app/data/models/activity_order/lottery_order_model.dart';
import 'package:user_app/data/repositories/mine/activity_order_repository.dart';

part 'activity_order_service.g.dart';

@riverpod
ActivityOrderService activityOrderService(final Ref ref) {
  return ActivityOrderService(
    repository: ref.watch(activityOrderRepositoryProvider),
  );
}

class ActivityOrderService {
  final ActivityOrderRepository _repository;

  ActivityOrderService({
    required final ActivityOrderRepository repository,
  }) : _repository = repository;

  /// 获取活动订单列表
  Future<ApiResult<LotteryOrderListModel>> getOrderList() async {
    try {
      return await _repository.getOrderList();
    } catch (e) {
      if (kDebugMode) {
        print('获取活动订单列表失败: $e');
      }
      rethrow;
    }
  }

  /// 获取抽奖详情数据
  Future<ApiResult<LotteryDetailModel>> getLotteryData(final int id) async {
    try {
      return await _repository.getLotteryData(id);
    } catch (e) {
      if (kDebugMode) {
        print('获取抽奖详情失败: $e');
      }
      rethrow;
    }
  }
}
