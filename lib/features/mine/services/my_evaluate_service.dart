import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/data/models/mine/comment_list_model.dart';
import 'package:user_app/data/repositories/mine/comment_repository.dart';

part 'my_evaluate_service.g.dart';

/// 我的评价服务
class MyEvaluateService {
  final CommentRepository _commentRepository;

  /// 构造函数
  MyEvaluateService({required final CommentRepository commentRepository})
      : _commentRepository = commentRepository;

  /// 获取评论列表
  Future<ApiResult<CommentListData>> getCommentList(
      final int page, final int limit) async {
    // 获取评论列表数据
    final result = await _commentRepository.getCommentList(page, limit);

    // 确保数据完整性，进行后处理
    if (result != null && result.data != null && result.data!.items != null) {
      for (var item in result.data!.items!) {
        // 处理删除标记
        item.deleted = false;

        // 配送员评价
        if (item.type == 1) {
          // 如果缺少配送员头像，使用默认头像
          if (item.shipperAvatar == null || item.shipperAvatar!.isEmpty) {
            item.shipperAvatar =
                "https://acdn.mulazim.com/images/default/shipper_image_default.png@400w_300h";
          }
        }

      
        // 确保评分默认值
        item.star ??= 5;
        item.foodStar ??= 5;
        item.boxStar ??= 5;

        // 确保回复和图片数组不为空
        item.replies ??= [];
        item.images ??= [];
      }
    }

    return result;
  }

  /// 删除评论
  Future<ApiResult<bool>> deleteComment(final int commentId) async {
    try {
      return await _commentRepository.deleteComment(commentId);
    } catch (e) {
      rethrow;
    }
  }
}

/// 我的评价服务提供者
@riverpod
MyEvaluateService myEvaluateService(final MyEvaluateServiceRef ref) {
  return MyEvaluateService(
    commentRepository: ref.watch(commentRepositoryProvider),
  );
}
