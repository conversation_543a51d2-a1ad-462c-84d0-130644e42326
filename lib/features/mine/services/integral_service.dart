import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/models/mine/points_model.dart';
import 'package:user_app/data/repositories/points/points_repository_impl.dart';

part 'integral_service.g.dart';

/// 积分服务
class IntegralService {
  final PointsRepository _pointsRepository;

  /// 构造函数
  IntegralService({
    required final PointsRepository pointsRepository,
  }) : _pointsRepository = pointsRepository;

  /// 获取积分记录
  Future<PointsData?> getPointsLog() async {
    final result = await _pointsRepository.getPointsLog(limit: 100);
    if (result.success) {
      return result.data;
    } else {
      throw Exception(result.msg);
    }
  }
}

/// 积分服务提供者
@riverpod
IntegralService integralService(final Ref ref) {
  return IntegralService(
    pointsRepository: ref.read(pointsRepositoryProvider),
  );
}
