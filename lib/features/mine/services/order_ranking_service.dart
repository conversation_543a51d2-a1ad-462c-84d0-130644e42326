import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/utils/image_util.dart';
import 'package:user_app/core/utils/logger.dart';
import 'package:user_app/core/utils/wechat_util.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/data/models/activity_order/order_ranking_model.dart';
import 'package:user_app/data/repositories/activity_order/order_ranking_repository.dart';
import 'package:user_app/generated/l10n.dart';

part 'order_ranking_service.g.dart';

/// 订单排名服务
@riverpod
class OrderRankingService extends _$OrderRankingService {
  @override
  Future<void> build() async {}

  /// 获取订单排名记录
  Future<OrderRankingModel?> getLuckyHistory(
      final int areaId, final int platType) async {
    final repository = ref.read(orderRankingRepositoryProvider);
    final result = await repository.getLuckyHistory(areaId, platType);

    if (result.success) {
      return result.data;
    } else {
      BotToast.showText(text: result.msg);
      return null;
    }
  }

  /// 获取订单排名页面数据
  /// [areaId] 区域ID
  /// [platType] 平台类型
  /// [prizeId] 奖品ID（可选）
  Future<Map<String, dynamic>?> getOrderRankingPage(
      final int areaId, final int platType,
      [final int? prizeId]) async {
    final repository = ref.read(orderRankingRepositoryProvider);
    final result = await repository.getActivityPage(
      areaId: areaId,
      platType: platType,
      prizeId: prizeId,
    );

    if (result.success) {
      return result.data;
    } else {
      BotToast.showText(text: result.msg);
      return null;
    }
  }

  /// 提交活动评价
  Future<bool> submitActivityComment(
    final int lotteryActivityId,
    final int type,
    final String content,
  ) async {
    final repository = ref.read(orderRankingRepositoryProvider);
    final result = await repository.submitActivityComment(
      lotteryActivityId,
      type,
      content,
    );

    if (result.success) {
      return true;
    } else {
      BotToast.showText(text: result.msg);
      return false;
    }
  }

  /// 分享活动
  Future<bool> shareActivity(final int activityId, [final int type = 6]) async {
    final repository = ref.read(orderRankingRepositoryProvider);
    final result = await repository.shareActivity(activityId, type);

    if (result.success) {
      return true;
    } else {
      BotToast.showText(text: result.msg);
      return false;
    }
  }

  /// 兼容原来代码的方法 - 分享活动预览
  Future<bool> getFavoriteShareView(final int activityId,
      [final int type = 5]) async {
    return shareActivity(activityId, type);
  }

  /// 兑换奖品
  Future<bool> exchangePrize(final int chanceId) async {
    final repository = ref.read(orderRankingRepositoryProvider);
    final result = await repository.exchangePrize(chanceId);

    if (result.success) {
      return true;
    } else {
      BotToast.showText(text: result.msg);
      return false;
    }
  }

  /// 统一分享方法
  /// 处理分享活动的所有逻辑，包括记录分享事件、下载图片和调用微信分享接口
  ///
  /// [activityId] 活动ID
  /// [context] 上下文，用于显示加载对话框
  /// [shareType] 分享类型，默认为6
  /// [prizeName] 奖品名称，可选
  /// [prizeImage] 奖品图片URL，可选
  /// [defaultTitle] 默认标题，当prizeName为空时使用
  ///
  /// 返回分享是否成功
  Future<bool> share({
    required final int activityId,
    required final BuildContext context,
    final int shareType = 6,
    final String? prizeName,
    final String? prizeImage,
    final String? defaultTitle,
  }) async {
    try {
      // 显示加载对话框
      LoadingDialog().show();

      // 记录分享事件
      await shareActivity(activityId, shareType);

      // 获取当前语言环境
      final lang = ref.read(languageProvider);

      // 分享标题
      final String title = prizeName?.isNotEmpty == true
          ? prizeName!
          : (defaultTitle?.isNotEmpty == true
              ? defaultTitle!
              : S.current.reward_level_order);

      // 分享图片URL - 如果有特定奖品图片，使用奖品图片；否则使用默认图片
      final imageUrl = prizeImage?.isNotEmpty == true
          ? prizeImage!
          : 'https://acdn.mulazim.com/wechat_mini/img/orderRanking/new_share_tag_$lang.png';

      // 下载图片
      final imageData = await ImageUtil.getImageFromUrl(imageUrl);

      // 创建缩略图
      final thumbData = await ImageUtil.createWeChatThumbnail(imageData);

      // 调用微信分享接口
      final result = await WechatUtil().shareMiniProgram(
        thumbData: thumbData,
        path: "pages/index/index",
        title: title,
        description: S.current.app_description,
      );

      // 显示分享结果提示
      if (result) {
        BotToast.showText(text: S.current.about_share_success);
      } else {
        BotToast.showText(text: S.current.about_share_failed);
      }

      return result;
    } catch (e) {
      Logger.e('OrderRankingService', '分享失败: $e');
      BotToast.showText(text: S.current.about_share_failed);
      return false;
    } finally {
      // 隐藏加载对话框
      LoadingDialog().hide();
    }
  }
}
