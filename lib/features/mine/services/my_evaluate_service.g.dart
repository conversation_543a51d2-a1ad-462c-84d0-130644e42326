// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'my_evaluate_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$myEvaluateServiceHash() => r'5a9784986a24c83cb02738888c5114e3fae27467';

/// 我的评价服务提供者
///
/// Copied from [myEvaluateService].
@ProviderFor(myEvaluateService)
final myEvaluateServiceProvider =
    AutoDisposeProvider<MyEvaluateService>.internal(
  myEvaluateService,
  name: r'myEvaluateServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$myEvaluateServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MyEvaluateServiceRef = AutoDisposeProviderRef<MyEvaluateService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
