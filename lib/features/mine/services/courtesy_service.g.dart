// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'courtesy_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$courtesyServiceHash() => r'1f68dd6f8133a056f60fd8c5555c1e2828a9960c';

/// 优惠券服务
///
/// Copied from [CourtesyService].
@ProviderFor(CourtesyService)
final courtesyServiceProvider =
    AutoDisposeNotifierProvider<CourtesyService, void>.internal(
  CourtesyService.new,
  name: r'courtesyServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$courtesyServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CourtesyService = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
