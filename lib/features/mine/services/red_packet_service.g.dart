// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'red_packet_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$redPacketServiceHash() => r'7535def4822590203cdc909ab67e354b9f7433c1';

/// 红包服务
///
/// Copied from [redPacketService].
@ProviderFor(redPacketService)
final redPacketServiceProvider = AutoDisposeProvider<RedPacketService>.internal(
  redPacketService,
  name: r'redPacketServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$redPacketServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RedPacketServiceRef = AutoDisposeProviderRef<RedPacketService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
