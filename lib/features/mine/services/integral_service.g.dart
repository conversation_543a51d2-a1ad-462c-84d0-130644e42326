// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'integral_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$integralServiceHash() => r'56302cf8aed022e5d7483cae6bb386df0daec283';

/// 积分服务提供者
///
/// Copied from [integralService].
@ProviderFor(integralService)
final integralServiceProvider = AutoDisposeProvider<IntegralService>.internal(
  integralService,
  name: r'integralServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$integralServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IntegralServiceRef = AutoDisposeProviderRef<IntegralService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
