import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/features/mine/pages/profile/widgets/image_source_dialog.dart';

/// UI服务，处理UI相关操作
class ProfileUiService {
  /// 显示图片源选择对话框
  Future<ImageSource?> showImageSourceDialog({final BuildContext? context}) {
    final currentContext = context ?? AppContext().currentContext;
    if (currentContext == null) {
      throw Exception('BuildContext is null');
    }
    return showModalBottomSheet<ImageSource>(
      context: currentContext,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (final context) => const ImageSourceDialog(),
    );
  }
}
