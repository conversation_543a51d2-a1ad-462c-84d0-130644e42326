import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/models/add_shop/license_type.dart';
import 'package:user_app/data/repositories/add_shop/add_shop_repository.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/core/network/result/api_result.dart';

part 'add_shop_service.g.dart';

/// 商家入驻服务
@riverpod
class AddShopService extends _$AddShopService {
  @override
  Future<void> build() async {}

  /// 获取证件类型列表
  Future<ApiResult<List<LicenseType>>> getLicenseTypes() async {
    final repository = ref.read(addShopRepositoryProvider);
    return repository.getLicenseTypes();
  }

  /// 获取城市区域列表
  Future<ApiResult<List<Map<String, dynamic>>>> getCityAreaList() async {
    final repository = ref.read(addShopRepositoryProvider);
    return repository.getCityAreaList();
  }

  /// 获取用户信息
  Future<Map<String, dynamic>> getUserInfo() async {
    final storage = ref.read(localStorageRepositoryProvider);
    final locationInfo = storage.getLocationInfo();

    return {
      'phone': storage.getUserInfo()?.mobile ?? '',
      'cityId': locationInfo?.cityId,
      'cityName': locationInfo?.cityName,
      'areaId': locationInfo?.areaId,
      'areaName': locationInfo?.areaName,
    };
  }

  /// 检查手机号格式
  bool checkPhone(final String phone) {
    return RegExp(r'^1[3456789]\d{9}$')
        .hasMatch(phone.replaceAll(RegExp(r'\s+'), ''));
  }

  /// 格式化手机号
  String formatPhone(final String phone) {
    final cleanPhone = phone.replaceAll(RegExp(r'\s+'), '');
    if (cleanPhone.length <= 3) return cleanPhone;
    if (cleanPhone.length <= 7) {
      return '${cleanPhone.substring(0, 3)} ${cleanPhone.substring(3)}';
    }
    return '${cleanPhone.substring(0, 3)} ${cleanPhone.substring(3, 7)} ${cleanPhone.substring(7)}';
  }

  /// 提交商家入驻
  Future<ApiResult<dynamic>> submit({
    required final String shopName,
    required final String phone,
    required final int cityId,
    required final int areaId,
  }) async {
    if (!checkPhone(phone) || shopName.isEmpty || phone.isEmpty) {
      return ApiResult.error(
        msg: S.current.addShop_all,
      );
    }

    final repository = ref.read(addShopRepositoryProvider);
    final response = await repository.submit(
      shopName: shopName,
      phone: phone.replaceAll(RegExp(r'\s+'), ''),
      cityId: cityId,
      areaId: areaId,
    );

    return response;
  }
}
