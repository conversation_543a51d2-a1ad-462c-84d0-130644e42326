import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:user_app/data/models/mine/web_item_model.dart';
import 'package:user_app/data/repositories/user/user_repository.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';

part 'mine_service.g.dart';

/// 个人中心业务逻辑服务
@riverpod
class MineService extends _$MineService {
  @override
  void build() {}

  /// 获取网页列表
  Future<List<WebItem>> fetchWebList() async {
    try {
      final result = await ref.read(userRepositoryProvider).fetchWebList();
      final data = result.data;
      final List<dynamic> items = data == null ? [] : data['items'] ?? [];
      return items.map((final item) => WebItem.fromJson(item)).toList();
    } catch (e) {
      rethrow;
    }
  }

  /// 拨打电话
  Future<void> makePhoneCall(final String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    } else {
      throw Exception('无法拨打电话');
    }
  }


  /// 获取本地客服电话
  Future<String?> getServicePhone() async {
    try {
      final storage = ref.read(localStorageRepositoryProvider);
      return storage.getServicePhone();
    } catch (e) {
      rethrow;
    }
  }
}
