// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity_order_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$activityOrderServiceHash() =>
    r'67625720a61f64ecdefbb8ed47948125a9c3775c';

/// See also [activityOrderService].
@ProviderFor(activityOrderService)
final activityOrderServiceProvider =
    AutoDisposeProvider<ActivityOrderService>.internal(
  activityOrderService,
  name: r'activityOrderServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$activityOrderServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ActivityOrderServiceRef = AutoDisposeProviderRef<ActivityOrderService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
