import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/models/user/red_packet_model.dart';
import 'package:user_app/data/repositories/mine/red_packet_repository.dart';

part 'red_packet_service.g.dart';

/// 红包服务
@riverpod
RedPacketService redPacketService(RedPacketServiceRef ref) {
  final repository = ref.watch(redPacketRepositoryProvider);
  return RedPacketService(repository);
}

/// 红包服务实现
class RedPacketService {
  /// 构造函数
  const RedPacketService(this._repository);

  /// 红包仓库
  final RedPacketRepository _repository;

  /// 获取红包列表
  Future<RedPacketListModel?> getRedPacketList() async {
    try {
      final response = await _repository.getRedPacketList();
      if (response.status != 200) {
        throw Exception(response.msg);
      }
      return response.data;
    } catch (e) {
      throw Exception('$e');
    }
  }
}
