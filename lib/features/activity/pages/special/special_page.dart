import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/config/index.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/data/models/activity/special_food_model.dart';
import 'package:user_app/features/activity/pages/seckill_page.dart';
import 'package:user_app/features/activity/pages/special/dialog/business_time_dialog.dart';
import 'package:user_app/features/activity/pages/special/dialog/special_activity_rule.dart';

import 'package:user_app/features/activity/providers/special_list_provider.dart';
import 'package:user_app/features/auth/providers/auth_provider.dart';
import 'package:user_app/features/index/pages/main_page.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/dialogs/food_detail_widget_for_special.dart';
import 'package:user_app/features/restaurant/providers/food_detail_provider.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';
import 'dart:async';

class SpecialPage extends ConsumerStatefulWidget {
  SpecialPage({super.key, required this.buildingId});
  int buildingId;

  @override
  ConsumerState createState() => _SpecialPageState();
}

class _SpecialPageState extends ConsumerState<SpecialPage>
    with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late ScrollController _horizontalScrollController; // 水平滚动控制器
  int currentIndex = 0;
  // 用来记录每个卡片的GlobalKey
  final List<GlobalKey> _cardKeys = [];

  // 防抖定时器
  Timer? _scrollTimer;

  // 控制滚动监听器的标志位
  bool _isManualScrolling = false;
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _scrollController = ScrollController();
    _horizontalScrollController = ScrollController(); // 初始化水平滚动控制器

    // 添加滚动监听器
    _scrollController.addListener(_onScroll);

    // 初始化进度条动画控制器
    _progressController = AnimationController(
      duration: const Duration(seconds: 2), // 2秒动画
      vsync: this,
    );

    // 创建从0到0.6的动画（60%）
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 0.6,
    ).animate(
      CurvedAnimation(
        parent: _progressController,
        curve: Curves.easeInOut,
      ),
    );

    // 启动动画
    _progressController.forward();

    WidgetsBinding.instance.addPostFrameCallback((final _) async {
      ref
          .read(specialListProvider.notifier)
          .fetchSpecialData(buildingId: widget.buildingId);
    });
  }

  // 滚动监听器
  void _onScroll() {
    // 如果是手动滚动，跳过自动检测
    if (_isManualScrolling) return;

    // 取消之前的定时器
    _scrollTimer?.cancel();

    // 设置新的定时器，防抖处理
    _scrollTimer = Timer(Duration(milliseconds: 100), () {
      // 再次检查是否是手动滚动
      if (_isManualScrolling || _cardKeys.isEmpty) return;

      // 获取屏幕信息
      double screenHeight = MediaQuery.of(context).size.height;
      double appBarHeight = AppBar().preferredSize.height;
      double statusBarHeight = MediaQuery.of(context).padding.top;
      double topOffset = appBarHeight + statusBarHeight;

      // 找到当前最接近顶部的卡片
      int currentVisibleIndex = 0;
      double minDistance = double.infinity;

      for (int i = 0; i < _cardKeys.length; i++) {
        if (_cardKeys[i].currentContext == null) continue;

        RenderBox? renderBox =
            _cardKeys[i].currentContext?.findRenderObject() as RenderBox?;
        if (renderBox == null) continue;

        // 计算卡片顶部到屏幕顶部的距离
        double cardTop = renderBox.localToGlobal(Offset.zero).dy;
        double cardBottom = cardTop + renderBox.size.height;
        double cardHeight = renderBox.size.height;

        // 对于最后一个元素，特殊处理
        if (i == _cardKeys.length - 1) {
          // 如果最后一个卡片内容较短，且已经在可视区域内，优先选择它
          bool isContentShort = cardHeight < (screenHeight - topOffset);
          if (isContentShort &&
              cardTop >= topOffset &&
              cardBottom <= screenHeight) {
            currentVisibleIndex = i;
            break;
          }
        }

        // 更精确的检测：如果卡片的顶部在屏幕顶部附近，且底部在屏幕内
        if (cardTop <= topOffset + 100 && cardBottom > topOffset) {
          double distance = (cardTop - topOffset).abs();
          if (distance < minDistance) {
            minDistance = distance;
            currentVisibleIndex = i;
          }
        }
      }

      // 如果当前可见的卡片索引与选中的索引不同，则更新
      if (currentVisibleIndex != currentIndex) {
        setState(() {
          currentIndex = currentVisibleIndex;
        });

        // 同时滚动标题区域到对应的标题项
        _scrollToTitleItem(currentVisibleIndex);
      }
    });
  }

  // 滚动到指定卡片
  void _scrollToCard(final int index) {
    // 检查索引是否有效
    if (index < 0 || index >= _cardKeys.length) {
      return;
    }

    // 确保卡片已经渲染
    if (_cardKeys[index].currentContext == null) {
      return;
    }

    // 如果是最后一个元素，检查是否需要滚动
    if (index == _cardKeys.length - 1) {
      RenderBox? renderBox =
          _cardKeys[index].currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        // 计算最后一个卡片的位置和大小
        double cardTop = renderBox.localToGlobal(Offset.zero).dy;
        double cardHeight = renderBox.size.height;
        double screenHeight = MediaQuery.of(context).size.height;
        double appBarHeight = AppBar().preferredSize.height;
        double statusBarHeight = MediaQuery.of(context).padding.top;
        double topOffset = appBarHeight + statusBarHeight;
        double availableHeight = screenHeight - topOffset;

        // 如果最后一个卡片内容高度短于页面显示部分的高度，直接滚动到最底部
        if (cardHeight < availableHeight) {
          // 设置手动滚动标志位
          _isManualScrolling = true;

          // 直接滚动到最底部
          _scrollController
              .animateTo(
            _scrollController.position.maxScrollExtent,
            duration: Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          )
              .then((final _) {
            // 滚动完成后，延迟重置标志位
            Future.delayed(Duration(milliseconds: 100), () {
              _isManualScrolling = false;
            });
          });
          return;
        }
      }
    }

    // 设置手动滚动标志位
    _isManualScrolling = true;

    // 使用 Scrollable.ensureVisible 方法，这是更可靠的方式
    Scrollable.ensureVisible(
      _cardKeys[index].currentContext!,
      duration: Duration(milliseconds: 500),
      curve: Curves.easeInOut,
      alignment: 0.0, // 0.0 表示滚动到顶部，1.0 表示滚动到底部
    ).then((final _) {
      // 滚动完成后，延迟重置标志位，确保滚动动画完全结束
      Future.delayed(Duration(milliseconds: 100), () {
        _isManualScrolling = false;
      });
    });
  }

  // 滚动到指定的标题项
  void _scrollToTitleItem(final int index) {
    // 检查索引是否有效
    if (index < 0) return;

    // 等待下一帧确保布局完成
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      if (!_horizontalScrollController.hasClients) return;

      // 如果是最后一个标题项，滚动到最右侧
      if (index == _cardKeys.length - 1) {
        _horizontalScrollController.animateTo(
          _horizontalScrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        return;
      }

      // 计算标题项的宽度（包括padding）
      // 根据 _titleItem 的实际布局调整：horizontal padding 15.w * 2 + 内容宽度
      double itemWidth = 30.w + 80.w; // 15.w * 2 + 80.w (大约的内容宽度)
      double scrollOffset = index * itemWidth;

      // 确保滚动偏移量不超过最大滚动范围
      double maxScrollExtent =
          _horizontalScrollController.position.maxScrollExtent;
      scrollOffset = scrollOffset.clamp(0.0, maxScrollExtent);

      // 平滑滚动到指定位置
      _horizontalScrollController.animateTo(
        scrollOffset,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  @override
  Widget build(final BuildContext context) {
    ref.listen(authProvider, (final previous, final next) {
      if (next) {
        ref
            .read(specialListProvider.notifier)
            .fetchSpecialData(buildingId: widget.buildingId);
      }
    });
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.baseGreenColor,
        foregroundColor: Colors.white,
        // centerTitle: true,
        // title: Text('تالىشىپ سېتىۋېلىش',style: TextStyle(fontSize: titleSize,color: Colors.white),),
      ),
      body: ref.watch(specialListProvider).when(
        data: (final data) {
          // 确保 _cardKeys 的长度与数据长度一致
          if (_cardKeys.length != (data ?? []).length) {
            _cardKeys.clear();
            _cardKeys.addAll(
              List.generate((data ?? []).length, (final index) => GlobalKey()),
            );
          }
          return Column(
            children: [
              Container(
                alignment: ref.watch(languageProvider) == 'ug'
                    ? Alignment.centerRight
                    : Alignment.centerLeft,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(12.w),
                    bottomLeft: Radius.circular(12.w),
                  ),
                  color: AppColors.baseGreenColor,
                ),
                child: SingleChildScrollView(
                  controller: _horizontalScrollController,
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: List.generate(
                      (data ?? []).length,
                      (final index) => _titleItem(data![index], index),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: Container(
                    margin: EdgeInsets.only(bottom: 10.w),
                    child: Column(
                      children: List.generate(
                        (data ?? []).length,
                        (final index) =>
                            _cardContentListItem(data![index], index),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
        error: (final error, final stackTrace) {
          // 数据加载失败，显示错误信息
          return Center(child: Text('address Page Error: $error'));
        },
        loading: () {
          // 正在加载，显示加载指示器
          return Center(
            child: LoadingWidget(),
          );
        },
      ),
    );
  }

  Widget _titleItem(final SpecailFoodData data, final int index) {
    return InkWell(
      onTap: () {
        // 立即更新当前索引
        setState(() {
          currentIndex = index;
        });

        // 滚动到对应卡片
        _scrollToCard(index);

        // 同时滚动标题区域到对应的标题项
        _scrollToTitleItem(index);
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10.w, horizontal: 15.w),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  '${data.price}',
                  style: TextStyle(
                    fontSize: 24.sp,
                    color: Colors.white,
                    fontFamily: 'NumberFont',
                  ),
                ),
                Text(
                  S.current.yuan_activity,
                  style: TextStyle(
                    fontSize: titleSize,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 10.w,
            ),
            index == currentIndex
                ? Container(
                    width: 80.w,
                    height: 5.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.w),
                      color: Colors.white,
                    ),
                  )
                : SizedBox(
                    height: 5,
                  ),
          ],
        ),
      ),
    );
  }

  Widget _cardContentListItem(final SpecailFoodData data, final int index) {
    final isUg = ref.watch(languageProvider) == 'ug';
    return Container(
      margin: EdgeInsets.only(top: 10.w, right: 10.w, left: 10.w),
      padding: EdgeInsets.all(10.w),
      key: _cardKeys[index], // 为每个卡片指定一个GlobalKey
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.w),
        gradient: LinearGradient(
          colors: [AppColors.secondGreenColor, Colors.white], // 渐变的颜色列表
          begin: Alignment.topCenter, // 渐变开始位置
          end: Alignment.bottomCenter, // 渐变结束位置
        ),
      ),
      child: Column(
        children: [
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        S.current.one_food,
                        style: TextStyle(
                          fontSize: titleSize,
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${data.price}',
                        style: TextStyle(
                          fontSize: 24.sp,
                          color: Colors.red,
                          fontFamily: 'NumberFont',
                        ),
                      ),
                      Text(
                        S.current.yuan,
                        style: TextStyle(
                          fontSize: titleSize,
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  // 配送费|活动规则
                  Row(
                    children: [
                      if (data.shipmentType == 1)
                        data.shipmentFee != null && data.shipmentFee! > 0
                            ? Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4.w),
                                  color: AppColors.primary,
                                  border: Border.all(
                                    color: AppColors.primary,
                                    width: 1.w,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.only(
                                          topLeft: isUg
                                              ? Radius.circular(0.w)
                                              : Radius.circular(4.w),
                                          bottomLeft: isUg
                                              ? Radius.circular(0.w)
                                              : Radius.circular(4.w),
                                          topRight: isUg
                                              ? Radius.circular(4.w)
                                              : Radius.circular(0.w),
                                          bottomRight: isUg
                                              ? Radius.circular(4.w)
                                              : Radius.circular(0.w),
                                        ),
                                      ),
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 10.w,
                                        vertical: 2.w,
                                      ),
                                      child: RichText(
                                        text: TextSpan(
                                          children: [
                                            TextSpan(
                                              text: '${data.shipmentFee}',
                                              style: TextStyle(
                                                fontSize: titleSize,
                                                color: AppColors.primary,
                                                fontWeight: FontWeight.bold,
                                                fontFamily:
                                                    AppConstants.mainFont,
                                              ),
                                            ),
                                            TextSpan(
                                              text: '¥',
                                              style: TextStyle(
                                                fontSize: titleSize - 4.sp,
                                                color: AppColors.primary,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: AppColors.primary,
                                      ),
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 10.w,
                                      ),
                                      child: Text(
                                        S.current.cart_delivery_fee,
                                        style: TextStyle(
                                          fontSize: mainSize,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : Text(
                                S.current.ask_for_shipment,
                                style: TextStyle(
                                  fontSize: mainSize,
                                  color: AppColors.textPrimaryColor,
                                ),
                              ),
                      if (data.rule != null && data.rule!.isNotEmpty)
                        SizedBox(
                          width: 10.w,
                        ),
                      if (data.rule != null && data.rule!.isNotEmpty)
                        GestureDetector(
                          onTap: () {
                            showModalBottomSheet(
                              backgroundColor: Colors.transparent,
                              useSafeArea: true,
                              context: context,
                              isScrollControlled: true,
                              builder: (final context) => SpecialActivityRule(
                                title: S.current.activity_rule,
                                content: data.rule ?? '',
                              ),
                            );
                          },
                          child: Icon(
                            Icons.info_outline,
                            size: 30.w,
                            color: AppColors.textPrimaryColor,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
              SizedBox(
                height: 5.w,
              ),
              Container(
                padding: EdgeInsets.all(6.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6.w),
                  color: Colors.white,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    data.isBegin == 1
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Transform.rotate(
                                angle: isUg ? 0 : 3.14,
                                child: Image.asset(
                                  'assets/images/notice.png',
                                  width: 14.w,
                                  height: 14.w,
                                ),
                              ),
                              SizedBox(width: 6.w),
                              Directionality(
                                textDirection: TextDirection.ltr,
                                child: Text(
                                  '${data.endTime}',
                                  style: TextStyle(
                                    fontSize: mainSize,
                                    color: Colors.orange,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Text(
                                ' ${S.current.in_end_out_sec}',
                                style: TextStyle(
                                  fontSize: mainSize,
                                  color: Colors.orange,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Transform.rotate(
                                angle: isUg ? 0 : 3.14,
                                child: Image.asset(
                                  'assets/images/notice.png',
                                  width: 14.w,
                                  height: 14.w,
                                ),
                              ),
                              SizedBox(width: 6.w),
                              Directionality(
                                textDirection: TextDirection.ltr,
                                child: Text(
                                  '${data.beginTime}',
                                  style: TextStyle(
                                    fontSize: mainSize,
                                    color: Colors.orange,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Text(
                                ' ${S.current.begin_at}',
                                style: TextStyle(
                                  fontSize: mainSize,
                                  color: Colors.orange,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                  ],
                ),
              ),
            ],
          ),
          Column(
            children: List.generate(
              (data.restaurant ?? []).length,
              (final aindex) => _cardContent(
                data.restaurant![aindex],
                data.id ?? 0,
                data.isBegin ?? 0,
                data.state ?? 0,
                data: data,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 打开食品详情
  Future<void> _openFoodDetail(
    final int specialId,
    final int restaurantId,
    final int foodId,
    final SpecialItems item,
    final SpecialDistribution? distribution,
  ) async {
    // ref.read(foodDetailProvider.notifier).fetchFoodDetailData(foodId: widget.foodItem!.id ?? 0);
    ref.read(restaurantCommentListProvider.notifier).fetchRestaurantCommentList(
          page: 1,
          id: restaurantId,
          type: 1,
          foodId: foodId,
        );
    await showFoodDetailWidget(
      context,
      specialId,
      restaurantId,
      item,
      distribution,
    );
  }

  Future<String> showFoodDetailWidget(
    final BuildContext context,
    final int specialId,
    final int restaurantId,
    final SpecialItems item,
    final SpecialDistribution? distribution,
  ) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      useSafeArea: true,
      builder: (final BuildContext bottomPanelContext) {
        return FoodDetailWidgetForSpecial(
          specialId: specialId,
          restaurantId: restaurantId,
          food: item,
          distribution: distribution,
        );
      },
    ).then((final value) {
      return value ?? '';
    });
  }

  Widget _cardContent(
    final Restaurant restaurant,
    final int specialId,
    final int isBegin,
    final int state, {
    required final SpecailFoodData data,
  }) {
    return InkWell(
      onTap: () async {
        context.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': restaurant.restaurantId ?? 0,
            'buildingId': widget.buildingId,
            'ids': [],
          },
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.w),
          color: Colors.white,
        ),
        padding: EdgeInsets.all(10.w),
        margin: EdgeInsets.only(top: 10.w),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(bottom: 10.w),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(width: 1.w, color: Color(0xffe5e5e5)),
                ),
              ),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10.w),
                    child: Image.asset(
                      'assets/images/sec_2.png',
                      fit: BoxFit.cover,
                      width: 60.w,
                      height: 60.w,
                    ),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${restaurant.restaurantName}',
                          style: TextStyle(
                            fontSize: mainSize,
                            color: AppColors.baseGreenColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(
                          height: 5.w,
                        ),
                        Text(
                          '${restaurant.distance}',
                          style: TextStyle(
                            fontSize: mainSize,
                            color: AppColors.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Column(
              children: List.generate(
                (restaurant.items ?? []).length,
                (final bindex) => _foodItem(
                  specialId,
                  restaurant.items![bindex],
                  restaurant.restaurantId ?? 0,
                  restaurant.distribution,
                  isBegin,
                  state,
                  restaurant.restaurantState ?? 0,
                  data: data,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _foodItem(
    final int specialId,
    final SpecialItems item,
    final int restaurantId,
    final SpecialDistribution? distribution,
    final int isBegin,
    final int state,
    final int restaurantState, {
    required final SpecailFoodData data,
  }) {
    final isUg = ref.watch(languageProvider) == 'ug';

    double lineWidth = MediaQuery.of(context).size.width - 115.w - 70.w;

    String label = S.current.special_btn;
    bool canBuy = true;

    // 只需要判断 customerType == 2 && userType != 2，不需要提示
    if (data.customerType == 2 && (data.userType ?? 0) != 2) {
      label = S.current.special_btn;
      canBuy = false;
    } else if (isBegin == 1) {
      if (restaurantState == 1) {
        if (state == 1) {
          if (item.totalCount == item.saledCount) {
            label = S.current.sell_done;
            canBuy = false;
          } else {
            if (item.state == 1) {
              label = S.current.special_btn;
              canBuy = true;
            } else if (item.state == 2) {
              label = S.current.resting;
              canBuy = false;
            } else {
              label = S.current.you_was_used;
              canBuy = false;
            }
          }
        } else {
          if (item.totalCount == item.saledCount) {
            label = S.current.sell_done;
            canBuy = false;
          } else {
            label = S.current.you_was_used;
            canBuy = false;
          }
        }
      } else {
        label = S.current.resting;
        canBuy = false;
      }
    } else {
      label = S.current.not_begin;
      canBuy = false;
    }

    return InkWell(
      onTap: () async {
        if (canBuy) {
          await _openFoodDetail(
            specialId,
            restaurantId,
            item.foodId ?? 0,
            item,
            distribution,
          );
        } else {
          // 只需要判断 customerType == 2 && userType != 2，弹窗提示
          if (data.customerType == 2 && (data.userType ?? 0) != 2) {
            showDialog(
              context: context,
              builder: (final context) => BusinessTimeDialog(
                title: S.current.not_new_user_title,
                content: S.current.not_new_user_tip,
                onTap: () {
                  Navigator.of(context).pop();
                },
                onDiscountTap: () {
                  Navigator.of(context).pop();
                  MainPageTabs.navigateToTab(context, MainPageTabs.discount);
                },
              ),
            );
          } else {
            BotToast.showText(text: label);
          }
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.w),
          color: Colors.white,
        ),
        margin: EdgeInsets.only(top: 10.w, bottom: 10.w),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10.w),
              child: PrefectImage(
                imageUrl: item.image ?? '',
                width: 116.w,
                height: 106.w,
                fit: BoxFit.fill,
              ),
            ),
            SizedBox(
              width: 6.w,
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Container(
                    alignment:
                        isUg ? Alignment.centerRight : Alignment.centerLeft,
                    child: Text(
                      '${item.foodName}',
                      textAlign: isUg ? TextAlign.end : TextAlign.start,
                      style: TextStyle(
                        fontSize: titleSize,
                        color: Colors.black,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // 新用户特价标签和价格在同一行 - 匹配小程序样式
                  if (data.customerType == 2)
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Color(0xFFFF8E3F), Color(0xFFFF0F0F)],
                          begin: isUg
                              ? Alignment.centerRight
                              : Alignment.centerLeft,
                          end: isUg
                              ? Alignment.centerLeft
                              : Alignment.centerRight,
                          stops: [0.0, 0.5],
                        ),
                        borderRadius: BorderRadius.circular(5.w),
                      ),
                      margin: EdgeInsets.only(top: 5.w),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        textDirection:
                            isUg ? TextDirection.rtl : TextDirection.ltr,
                        children: [
                          // 标签部分 - 匹配小程序的newUserTagtitleBox样式

                          // 背景斜切效果，使用ClipPath实现
                          ClipPath(
                            clipper: _RightSlantClipper(isUg),
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 10.w,
                                vertical: 5.w,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.center,
                                textDirection: isUg
                                    ? TextDirection.rtl
                                    : TextDirection.ltr,
                                children: [
                                  Image.asset(
                                    'assets/images/fireyellow.png',
                                    width: 14.w,
                                    height: 14.w,
                                  ),
                                  SizedBox(width: 6.w),
                                  Text(
                                    S.current.new_user,
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // 价格部分 - 匹配小程序的newUserTagPricebox样式
                          // 价格部分斜切适配
                          Expanded(
                            child: ClipPath(
                              clipper: _LeftSlantClipper(isUg),
                              child: Container(
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 1.r,
                                  ),
                                  borderRadius: isUg
                                      ? BorderRadius.horizontal(
                                          right: Radius.zero,
                                          left: Radius.circular(5.w),
                                        )
                                      : BorderRadius.horizontal(
                                          left: Radius.zero,
                                          right: Radius.circular(5.w),
                                        ),
                                  color: Colors.white,
                                ),
                                child: Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: Colors.red,
                                      width: 1.r,
                                    ),
                                    borderRadius: isUg
                                        ? BorderRadius.horizontal(
                                            right: Radius.zero,
                                            left: Radius.circular(5.w),
                                          )
                                        : BorderRadius.horizontal(
                                            left: Radius.zero,
                                            right: Radius.circular(5.w),
                                          ),
                                    color: Colors.white,
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    textDirection: isUg
                                        ? TextDirection.rtl
                                        : TextDirection.ltr,
                                    children: [
                                      RichText(
                                        text: TextSpan(
                                          children: [
                                            TextSpan(
                                              text: '￥',
                                              style: TextStyle(
                                                fontSize: 16.sp,
                                                color: const Color(0xFFFF0F0F),
                                                fontWeight: FontWeight.bold,
                                                fontFamily:
                                                    AppConstants.numberFont,
                                              ),
                                            ),
                                            TextSpan(
                                              text: '${item.price}',
                                              style: TextStyle(
                                                fontSize: 18.sp,
                                                color: const Color(0xFFFF0F0F),
                                                fontWeight: FontWeight.bold,
                                                fontFamily:
                                                    AppConstants.numberFont,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(width: 10.w),
                                      RichText(
                                        text: TextSpan(
                                          text: '￥${item.oldPrice}',
                                          style: TextStyle(
                                            fontSize: 13.sp,
                                            decoration:
                                                TextDecoration.lineThrough,
                                            color: Color(0xFFA4A4A4),
                                            fontFamily: AppConstants.numberFont,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (data.customerType == 2)
                    SizedBox(
                      height: 5.w,
                    ),

                  // 根据客户类型显示不同的进度条
                  if (data.customerType == 2)
                    // 新用户进度条
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      textDirection:
                          isUg ? TextDirection.rtl : TextDirection.ltr,
                      children: [
                        SizedBox(
                          width: lineWidth * 0.5,
                          child: AnimatedBuilder(
                            animation: _progressAnimation,
                            builder: (final context, final child) {
                              return CustomLinearProgressIndicator(
                                progress: int.parse(
                                      (item.saledCount ?? 0).toString(),
                                    ) /
                                    ((item.totalCount ?? 0)),
                                width: lineWidth * 0.5,
                                color: isBegin == 1
                                    ? AppColors.baseGreenColor
                                    : Colors.grey,
                                height: 15.w,
                              );
                            },
                          ),
                        ),
                        // 购买按钮
                        canBuy
                            ? Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 15.w,
                                  vertical: 4.h,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(30.w),
                                  color: AppColors.baseGreenColor,
                                ),
                                child: Text(
                                  label,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: mainSize,
                                  ),
                                ),
                              )
                            : Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 15.w,
                                  vertical: 4.h,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(30.w),
                                  gradient: LinearGradient(
                                    colors: [
                                      AppColors.textHintColor,
                                      AppColors.baseBackgroundColor,
                                    ], // 渐变的颜色列表
                                    begin: isUg
                                        ? Alignment.centerLeft
                                        : Alignment.centerRight, // 渐变开始位置
                                    end: isUg
                                        ? Alignment.centerRight
                                        : Alignment.centerLeft, // 渐变结束位置
                                  ),
                                ),
                                child: Text(
                                  label,
                                  style: TextStyle(
                                    color: AppColors.textSecondaryColor,
                                    fontSize: littleSize,
                                  ),
                                ),
                              ),
                      ],
                    )
                  else
                    // 普通用户价格显示
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      textDirection:
                          isUg ? TextDirection.rtl : TextDirection.ltr,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.start,
                          textDirection:
                              isUg ? TextDirection.rtl : TextDirection.ltr,
                          children: [
                            Image.asset(
                              'assets/images/huo.png',
                              fit: BoxFit.fill,
                              width: 16.w,
                              height: 18.w,
                            ),
                            SizedBox(
                              width: 5.w,
                            ),
                            Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text: '￥',
                                    style: TextStyle(
                                      fontSize: 10.sp,
                                      color: Colors.red,
                                      fontFamily: AppConstants.numberFont,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  TextSpan(
                                    text: '${item.price}',
                                    style: TextStyle(
                                      fontSize: 20.sp,
                                      color: Colors.red,
                                      fontWeight: FontWeight.bold,
                                      fontFamily: AppConstants.numberFont,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: 5.w,
                            ),
                            Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text: '￥',
                                    style: TextStyle(
                                      decoration: TextDecoration.lineThrough,
                                      decorationColor: AppColors.textSecondaryColor,
                                      fontSize: littleSize,
                                      color: AppColors.textSecondaryColor,
                                      fontFamily: AppConstants.numberFont,
                                    ),
                                  ),
                                  TextSpan(
                                    text: '${item.oldPrice}',
                                    style: TextStyle(
                                      fontSize: mainSize,
                                      fontFamily: AppConstants.numberFont,
                                      decoration: TextDecoration.lineThrough,
                                      decorationColor: AppColors.textSecondaryColor,
                                      color: AppColors.textSecondaryColor,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        // 购买按钮
                        canBuy
                            ? Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 15.w,
                                  vertical: 4.h,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(30.w),
                                  color: AppColors.baseGreenColor,
                                ),
                                child: Text(
                                  label,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: mainSize,
                                  ),
                                ),
                              )
                            : Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 15.w,
                                  vertical: 4.h,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(30.w),
                                  gradient: LinearGradient(
                                    colors: [
                                      AppColors.textHintColor,
                                      AppColors.baseBackgroundColor,
                                    ], // 渐变的颜色列表
                                    begin: isUg
                                        ? Alignment.centerLeft
                                        : Alignment.centerRight, // 渐变开始位置
                                    end: isUg
                                        ? Alignment.centerRight
                                        : Alignment.centerLeft, // 渐变结束位置
                                  ),
                                ),
                                child: Text(
                                  label,
                                  style: TextStyle(
                                    color: AppColors.textSecondaryColor,
                                    fontSize: littleSize,
                                  ),
                                ),
                              ),
                      ],
                    ),
                  if (data.customerType != 2)
                    SizedBox(
                      height: 5.h,
                    ),
                  if (data.customerType != 2)
                    // 普通用户进度条
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      textDirection:
                          isUg ? TextDirection.rtl : TextDirection.ltr,
                      children: [
                        SizedBox(
                          width: lineWidth,
                          child: AnimatedBuilder(
                            animation: _progressAnimation,
                            builder: (final context, final child) {
                              return CustomLinearProgressIndicator(
                                progress: int.parse(
                                      (item.saledCount ?? 0).toString(),
                                    ) /
                                    ((item.totalCount ?? 0)),
                                width: lineWidth,
                                color: isBegin == 1
                                    ? AppColors.baseGreenColor
                                    : Colors.grey,
                                height: 15.w,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  if (data.customerType != 2)
                    SizedBox(
                      height: 5.h,
                    ),
                  // 配送时间
                  if (item.takeTime != null)
                    Row(
                      textDirection:
                          isUg ? TextDirection.rtl : TextDirection.ltr,
                      children: [
                        Text(
                          S.current.distribution_time,
                          style: TextStyle(
                            color: AppColors.textSecondaryColor,
                            fontSize: 14.sp,
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 3.w),
                          child: Text(
                            item.takeTime.toString(),
                            style: TextStyle(
                              color: AppColors.textSecondaryColor,
                              fontSize: 14.sp,
                            ),
                          ),
                        ),
                        Text(
                          S.current.seckill_minute,
                          style: TextStyle(
                            color: AppColors.textSecondaryColor,
                            fontSize: 14.sp,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _scrollTimer?.cancel();
    _scrollController.dispose();
    _horizontalScrollController.dispose(); // 清理水平滚动控制器
    _progressController.dispose();
    super.dispose();
  }
}

/// 背景右边斜切效果，使用ClipPath实现
class _RightSlantClipper extends CustomClipper<Path> {
  final bool isUg;

  _RightSlantClipper(this.isUg);

  @override
  Path getClip(final Size size) {
    final path = Path();
    // 默认ltr方向，右上角往左斜切12.w
    path.moveTo(0, 0);
    path.lineTo(size.width - 12.w, 0); // 右上角往左斜切12.w
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant final CustomClipper<Path> oldClipper) => false;
}

/// 背景左边斜切效果，使用ClipPath实现
/// 背景左边斜切效果，使用ClipPath实现
/// 让左上角斜切12.w，和右边对称
class _LeftSlantClipper extends CustomClipper<Path> {
  final bool isUg;

  _LeftSlantClipper(this.isUg);

  @override
  Path getClip(final Size size) {
    final path = Path();
    if (isUg) {
      // ug语种，rtl方向，右上角往左斜切12.w
      path.moveTo(size.width - 2.w, 0);
      path.lineTo(size.width - 12.w, size.height);
      path.lineTo(-size.width, size.height);
      path.lineTo(-size.width, 0);
      path.close();
    } else {
      // 默认ltr方向，左上角往右斜切12.w
      path.moveTo(2.w, 0);
      path.lineTo(size.width, 0);
      path.lineTo(size.width, size.height);
      path.lineTo(12.w, size.height);
      path.close();
    }
    return path;
  }

  @override
  bool shouldReclip(covariant final CustomClipper<Path> oldClipper) => false;
}
