import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/repositories/activity/user_ranking_repository.dart';
import 'package:user_app/features/activity/pages/user_ranking/user_ranking_state.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/features/activity/pages/user_ranking/prize_exchange_page.dart';
import 'package:user_app/features/activity/pages/user_ranking/widgets/common_widget.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/data/models/activity/ranking_model.dart';

part 'user_ranking_controller.g.dart';

/// 用户排行榜控制器
@riverpod
class UserRankingController extends _$UserRankingController {
  /// 倒计时定时器
  Timer? _countdownTimer;

  /// 页面参数
  Map<String, String>? _pageParams;

  @override
  UserRankingState build() {
    // 监听页面销毁，清理定时器
    ref.onDispose(() {
      _countdownTimer?.cancel();
      _countdownTimer = null;
    });

    return const UserRankingState();
  }

  /// 初始化数据
  /// 根据页面参数获取排行榜数据
  Future<void> initializeData({
    String? rankingId,
    String? areaId,
    String? restaurantId,
    String? type,
  }) async {
    // 保存页面参数
    _pageParams = {
      if (rankingId != null) 'ranking_id': rankingId,
      if (areaId != null) 'area_id': areaId,
      if (restaurantId != null) 'restaurant_id': restaurantId,
      if (type != null) 'type': type,
    };

    await getData();
  }

  /// 获取排行榜数据
  Future<void> getData() async {
    if (_pageParams == null) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final repository = ref.read(userRankingRepositoryProvider);
      final result = await repository.getRankingInfo(
        rankingId: _pageParams!['ranking_id'] ?? '',
        areaId: _pageParams!['area_id'],
        restaurantId: _pageParams!['restaurant_id'],
        type: _pageParams!['type'],
      );

      if (result.success && result.data != null) {
        final data = result.data!;

        // 计算状态
        final status = UserRankingState.calculateStatus(data);

        // 更新状态
        state = state.copyWith(
          isLoading: false,
          rankingData: data,
          status: status,
          remainingSeconds: data.remainderTime ?? 0,
        );

        // 启动倒计时
        if (data.remainderTime != null && data.remainderTime! > 0) {
          _startCountdown(data.remainderTime!);
        }

        // 计算进度条宽度
        if (status == RankingStatus.inProgress ||
            status == RankingStatus.winner) {
          _calculateProgressBarWidth();
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.msg,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 启动倒计时
  void _startCountdown(int initialSeconds) {
    _countdownTimer?.cancel();

    int remainingSeconds = initialSeconds;

    // 立即更新一次倒计时显示
    _updateCountdown(remainingSeconds);

    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      remainingSeconds--;

      if (remainingSeconds <= 0) {
        timer.cancel();
        _countdownTimer = null;
        // 倒计时结束，重新获取数据
        getData();
        return;
      }

      _updateCountdown(remainingSeconds);
    });
  }

  /// 更新倒计时显示
  void _updateCountdown(int seconds) {
    final countdown = CountdownData.fromSeconds(seconds);
    state = state.copyWith(
      countdown: countdown,
      remainingSeconds: seconds,
    );
  }

  /// 计算进度条宽度
  void _calculateProgressBarWidth() {
    // 进度条宽度计算移至UI组件中进行，因为需要获取实际的容器尺寸
    // 这里只是触发状态更新，实际宽度在MiddleContent组件中计算
    state = state.copyWith(progressBarWidth: null);
  }

  /// 设置进度条宽度（由UI组件调用）
  void setProgressBarWidth(double? width) {
    state = state.copyWith(progressBarWidth: width);
  }

  /// 参加排行榜活动
  Future<void> joinRanking() async {
    if (state.isJoining || _pageParams == null) return;

    final rankingId = _pageParams!['ranking_id'];
    if (rankingId == null || rankingId.isEmpty) return;

    state = state.copyWith(isJoining: true);

    try {
      final repository = ref.read(userRankingRepositoryProvider);
      final result = await repository.joinRanking(rankingId: rankingId);

      if (result.success) {
        // 参加成功，重新获取数据
        await getData();
      } else {
        state = state.copyWith(
          isJoining: false,
          error: result.msg,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isJoining: false,
        error: e.toString(),
      );
    }
  }

  /// 显示活动规则
  void showRankingRule({required final WidgetRef ref}) {
    final context = AppContext().currentContext;
    if (context == null || state.rankingData == null) return;

    rankingbottomSheet(
        context: context,
        child: Container(
          width: double.infinity,
          color: Color(0xffEFF1F6),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              cardWidget(
                child: ruleContent(
                    state.rankingData?.ranking ?? RankingActivity(), ref),
              ),
              cardWidget(
                child: rulePrizeContent(
                    state.rankingData?.ranking ?? RankingActivity(), ref),
              ),
            ],
          ),
        ),
        title: S.current.ranking_rule_title);
  }

  /// 跳转到获奖记录页面
  void goToRankingHistory() {
    final context = AppContext().currentContext;
    if (context == null) return;

    // 这里需要根据实际的获奖记录页面路径进行跳转
    context.push(AppPaths.rankingHistoryPage);
  }

  /// 跳转到兑换奖品页面
  void goToRankingGive() {
    final context = AppContext().currentContext;
    if (context == null || state.rankingData == null) return;

    final user = state.rankingData!.user;
    if (user?.isWinner == 1 && user?.winnerGift != null) {
      // 这里需要根据实际的兑换奖品页面路径进行跳转
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (final context) => PrizeExchangePage(
                  rankingData: state.rankingData ?? UserRankingModel()))).then((_) => getData());
    }
  }

  /// 跳转到相应页面
  void goToPage() {
    final context = AppContext().currentContext;
    if (context == null || _pageParams == null) return;

    final type = _pageParams!['type'];

    if (type == '1') {
      // 平台活动，跳转到首页
      context.go(AppPaths.mainPage);
    } else {
      // 餐厅活动，跳转到餐厅详情
      final restaurantId = _pageParams!['restaurant_id'];
      final rankingId = _pageParams!['ranking_id'];

      if (restaurantId != null && rankingId != null) {
        context.pop();
      }
    }
  }

  /// 跳转到首页
  void goToHomePage() {
    final context = AppContext().currentContext;
    if (context == null) return;

    context.go(AppPaths.mainPage);
  }

  /// 返回上一页
  void goBack() {
    final context = AppContext().currentContext;
    if (context == null) return;

    // 检查是否有上一页
    if (context.canPop()) {
      context.pop();
    } else {
      // 没有上一页，跳转到首页
      context.go(AppPaths.mainPage);
    }
  }

  /// 切换选项卡
  void switchTab(String tabType) {
    // 这个功能在当前设计中可能不需要，因为我们使用了不同的布局
    // 如果需要可以在这里实现
  }

  /// 清除错误信息
  void clearError() {
    state = state.clearError();
  }

  /// 刷新数据
  Future<void> refresh() async {
    await getData();
  }

  /// 获取当前用户信息
  RankingUser? get currentUser => state.rankingData?.user;

  /// 获取当前排行榜信息
  RankingActivity? get currentRanking => state.rankingData?.ranking;

  /// 获取是否可以参加活动
  bool get canJoinActivity {
    return state.status == RankingStatus.notStarted &&
        !state.isJoining &&
        currentUser?.userState != 1;
  }

  /// 获取是否可以去下单
  bool get canGoOrder {
    return state.status == RankingStatus.inProgress ||
        state.status == RankingStatus.ended ||
        state.status == RankingStatus.loser;
  }

  /// 获取是否可以填写收货地址
  bool get canFillAddress {
    return state.status == RankingStatus.winner &&
        currentUser?.winnerGift != null;
  }

  /// 获取是否可以返回首页
  bool get canGoHome {
    return state.status == RankingStatus.userError;
  }
}
