import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/activity/rankin_history_model.dart';
import 'package:user_app/features/activity/providers/user_ranking_provider.dart';
import 'package:user_app/features/activity/pages/user_ranking/widgets/common_widget.dart';
import 'package:user_app/generated/l10n.dart';

class RankingHistoryPage extends ConsumerStatefulWidget {
  const RankingHistoryPage({super.key});

  @override
  ConsumerState<RankingHistoryPage> createState() => _RankingHistoryPageState();
}

class _RankingHistoryPageState extends ConsumerState<RankingHistoryPage> {

  List<RankingHistoryData>? rankingHistoryList;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _getRankingHistory();
  }

  void _getRankingHistory() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      final repository = ref.read(activityRepositoryProvider);
      final result = await repository.getRankingHistory();
      if (result.success && result.data != null) {
        // 过滤掉 ranking_id 为 null 的项目
        final validItems = result.data!.where((item) => item.rankingId != null && item.level != null && item.rewardImage != null).toList();
        setState(() {
          rankingHistoryList = validItems;
          isLoading = false;
        });
      } else {
        setState(() {
          errorMessage = result.msg;
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = S.current.network_error;
        isLoading = false;
      });
      print('获取排行榜历史失败: $e');
    }
  }

  @override
  Widget build(final BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffEFF1F6),
      appBar: AppBar(
        title: Text(S.current.prize_xchange),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: isLoading ? null : _getRankingHistory,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16.h),
            Text(
              S.current.loading_wait,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline_outlined,
              size: 64.sp,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              errorMessage!,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: _getRankingHistory,
              child: Text('重试'),
            ),
          ],
        ),
      );
    }

    if (rankingHistoryList == null || rankingHistoryList!.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64.sp,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              S.current.ranking_history_no_data,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _getRankingHistory();
        return Future.value();
      },
      child: SingleChildScrollView(
        physics: AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            ...rankingHistoryList!.map((e) => _prizeItem(e)),
            SizedBox(height: 20.h), // 底部留白
          ],
        ),
      ),
    );
  }

  Widget _prizeItem(final RankingHistoryData data) {
    final language = ref.watch(languageProvider);
    return cardWidget(
      child: Column(
        children: [
          rankingPrizeItem(
            imageUrl:
                data.rewardImage ?? "",
            title: language == 'ug' ? data.rewardNameUg ?? "" : data.rewardNameZh ?? "",
            subtitle: "${S.current.ranking_count} ${data.rewardCount ?? 0}",
            price: data.rewardOldPrice ?? "",
            rank: data.rewardRank?.toString() ?? "",
          ),
          SizedBox(height: 10.w,),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Text("${S.current.ranking_time} : ", style: TextStyle(fontSize: 14.sp,color: Color(0xff9DA0C2)),),
                  Directionality(
                    textDirection: TextDirection.ltr,
                    child: Text(data.time ?? "", style: TextStyle(fontSize: 14.sp,color: Color(0xff9DA0C2)),),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
