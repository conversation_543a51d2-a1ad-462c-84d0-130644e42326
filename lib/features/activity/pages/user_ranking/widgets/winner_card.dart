import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/generated/l10n.dart';

import '../user_ranking_controller.dart';

/// 获奖卡片组件
class WinnerCard extends ConsumerWidget {
  const WinnerCard({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final languageId = ref
        .watch(languageProvider.select((final lang) => lang == 'ug' ? 1 : 2));
    final winnerGift = ref.watch(
      userRankingControllerProvider.select(
        (final state) => state.winnerGift,
      ),
    );

    if (winnerGift == null) return const SizedBox();

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      child: Column(
        children: [
          // 获奖背景容器
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [
                  Color(0xFFFFC385),
                  Color(0xFFFF824D),
                  Color(0xFFFFC782),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            padding: EdgeInsets.fromLTRB(15.w, 15.h, 15.w, 0),
            child: Column(
              children: [
                // 获奖标题
                Text(
                  S.current.winning_award,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 25.sp,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        color: const Color(0xFFC22C0C),
                        offset: Offset(0, 2.h),
                        blurRadius: 2.r,
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 5.h),

                // 白色卡片容器
                Container(
                  width: double.infinity,
                  margin: EdgeInsets.only(top: 10.h),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12.r),
                      topRight: Radius.circular(12.r),
                    ),
                  ),
                  padding:
                      EdgeInsets.symmetric(vertical: 20.w, horizontal: 10.w),
                  child: _buildPrizeItem(winnerGift, languageId),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建奖品项目
  Widget _buildPrizeItem(final dynamic winnerGift, final int languageId) {
    return Row(
      children: [
        // 排名部分
        Column(
          children: [
            // 排名数字
            Text(
              '${winnerGift.rewardRank ?? 1}',
              style: TextStyle(
                fontSize: 40.sp,
                fontWeight: FontWeight.bold,
                fontStyle: FontStyle.italic,
                foreground: Paint()
                  ..shader = const LinearGradient(
                    colors: [Color(0xFFF87754), Color(0xFFFF4B44)],
                  ).createShader(const Rect.fromLTWH(0.0, 0.0, 200.0, 70.0)),
              ),
              textAlign: TextAlign.center,
            ),
            // 排名文字
            Text(
              S.current.ranking_rank,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        SizedBox(width: 15.w),
        // 奖品信息部分
        Expanded(
          child: Row(
            children: [
              // 奖品图片
              SizedBox(
                height: 70.h,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.r),
                  child: CachedNetworkImage(
                    imageUrl: winnerGift.rewardImage ?? '',
                    fit: BoxFit.fitWidth, // 使用cover避免拉伸，保持图片比例
                  ),
                ),
              ),
              SizedBox(width: 10.w),
              // 文本信息部分
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 奖品名称
                    Text(
                      languageId == 1
                          ? (winnerGift.rewardNameUg ?? '')
                          : (winnerGift.rewardNameZh ?? ''),
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.end,
                    ),
                    SizedBox(height: 4.h),

                    // 数量
                    Text(
                      '${S.current.ranking_count}${winnerGift.rewardCount ?? 1}',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.black,
                      ),
                    ),

                    // 原价（如果存在）
                    if (winnerGift.rewardOldPrice != null) ...[
                      SizedBox(height: 4.h),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            S.current.ranking_old_price,
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: const Color(0xFF9DA0C2),
                            ),
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            '${winnerGift.rewardOldPrice}',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: const Color(0xFF9DA0C2),
                              decoration: TextDecoration.lineThrough,
                              decorationColor: const Color(0xFF9DA0C2),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
