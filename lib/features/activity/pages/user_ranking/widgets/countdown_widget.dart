import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import '../user_ranking_controller.dart';

/// 倒计时组件
class CountdownWidget extends ConsumerWidget {
  const CountdownWidget({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final languageId = ref
        .watch(languageProvider.select((final lang) => lang == 'ug' ? 1 : 2));
    final countdown = ref.watch(
        userRankingControllerProvider.select((final state) => state.countdown));

    return Directionality(
      textDirection: TextDirection.ltr,
      child: Column(
        children: [
          // // 倒计时标题
          // Text(
          //   languageId == 1 ? 'قالغان ۋاقىت' : '剩余时间',
          //   style: TextStyle(
          //     color: Colors.white.withOpacity(0.9),
          //     fontSize: 14.sp,
          //     fontWeight: FontWeight.w500,
          //   ),
          // ),
          // SizedBox(height: 8.h),

          // 倒计时数字
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildTimeUnit(countdown.days, languageId == 1 ? 'كۈن' : '天'),
              _buildSeparator(),
              _buildTimeUnit(countdown.hours, languageId == 1 ? 'سائەت' : '时'),
              _buildSeparator(),
              _buildTimeUnit(
                  countdown.minutes, languageId == 1 ? 'مىنۇت' : '分'),
              _buildSeparator(),
              _buildTimeUnit(
                  countdown.seconds, languageId == 1 ? 'سېكۇنت' : '秒'),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建时间单位
  Widget _buildTimeUnit(String value, String unit) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: Text(
            value,
            style: TextStyle(
              color: Colors.red,
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              fontFamily: 'monospace',
            ),
          ),
        ),
        // SizedBox(height: 4.h),
        // Text(
        //   unit,
        //   style: TextStyle(
        //     color: Colors.white.withOpacity(0.8),
        //     fontSize: 12.sp,
        //   ),
        // ),
      ],
    );
  }

  /// 构建分隔符
  Widget _buildSeparator() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      child: Text(
        ':',
        style: TextStyle(
          color: Colors.white,
          fontSize: 16.sp,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
