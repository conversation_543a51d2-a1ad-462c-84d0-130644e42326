import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';

/// 排行榜背景组件
class RankingBackground extends ConsumerWidget {
  const RankingBackground({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final languageId =
        ref.watch(languageProvider.select((final lang) => lang == 'ug' ? 1 : 2));

    return Stack(
      children: [
        // 背景图片层 - 对应微信小程序的 .bg-image
        Positioned(
          left: 0,
          top: 0,
          right: 0,
          bottom: 0,
          child: Container(
            color: const Color(0xFFFF3023), // 确保背景色
            child: Image.asset(
              'assets/images/ranking/ranking-bg.jpeg',
              width: double.infinity,
              fit: BoxFit.fitWidth, // 对应微信小程序的 mode="widthFix"
              alignment: Alignment.topCenter,
            ),
          ),
        ),

        // 标题图片层 - 对应微信小程序的 .title-image
        Positioned(
          left: 0,
          top: 0,
          right: 0,
          child: Image.asset(
            languageId == 1
                ? 'assets/images/ranking/ranking-title-ug.png'
                : 'assets/images/ranking/ranking-title-zh.png',
            width: double.infinity,
            fit: BoxFit.contain, // 对应微信小程序的 mode="aspectFit"
            alignment: Alignment.center,
          ),
        ),
      ],
    );
  }
}
