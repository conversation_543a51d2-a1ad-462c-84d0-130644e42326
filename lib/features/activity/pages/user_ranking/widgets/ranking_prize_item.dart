import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/data/models/activity/ranking_model.dart';
import 'package:user_app/generated/l10n.dart';

/// 奖品项目组件
class RankingPrizeItem extends StatelessWidget {
  final RankingGift gift;
  final int languageId;

  const RankingPrizeItem({
    super.key,
    required this.gift,
    required this.languageId,
  });

  @override
  Widget build(final BuildContext context) {
    return Directionality(
      textDirection: languageId == 2 ? TextDirection.rtl : TextDirection.ltr,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 5.h),
        child: Row(
          children: [
            // 排名区域
            SizedBox(
              width: 50.w,
              child: Column(
                children: [
                  Text(
                    '${gift.rewardRank ?? 1}',
                    style: TextStyle(
                      fontSize: 45.sp,
                      fontWeight: FontWeight.bold,
                      fontStyle: FontStyle.italic,
                      foreground: Paint()
                        ..shader = const LinearGradient(
                          colors: [Color(0xFFF87754), Color(0xFFFF4B44)],
                        ).createShader(
                          const Rect.fromLTWH(0.0, 0.0, 200.0, 70.0),
                        ),
                    ),
                  ),
                  Text(
                    S.current.ranking_rank,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(width: 10.w),

            // 奖品信息区域
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    languageId == 1
                        ? (gift.rewardNameUg ?? '')
                        : (gift.rewardNameZh ?? ''),
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                    textAlign:
                        languageId == 1 ? TextAlign.right : TextAlign.left,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    '${S.current.ranking_count}: ${gift.rewardCount ?? 1}',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.black,
                    ),
                  ),
                  if (gift.rewardOldPrice != null) ...[
                    SizedBox(height: 4.h),
                    Text.rich(
                      TextSpan(
                        text: '${S.current.ranking_old_price}: ',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: const Color(0xFF9DA0C2),
                        ),
                        children: [
                          TextSpan(
                            text: '¥${gift.rewardOldPrice}',
                            style: const TextStyle(
                              decoration: TextDecoration.lineThrough,
                              decorationColor: Color(0xFF9DA0C2),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),

            SizedBox(width: 10.w),

            // 奖品图片
            Container(
              height: 70.h,
              padding: EdgeInsets.all(5.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                // color: Colors.grey[200], // 背景色，防止加载时显示空白
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8.r),
                child: CachedNetworkImage(
                  imageUrl: gift.rewardImage ?? '',
                  fit: BoxFit.fitWidth,
                  placeholder: (final context, final url) =>  SizedBox(
                    width: 70.w,
                    height: 70.h,
                    child: Container(
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: 10.w),
          ],
        ),
      ),
    );
  }
}
