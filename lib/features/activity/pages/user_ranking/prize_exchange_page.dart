import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/storage/storage_service.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/icon_font.dart';
import 'package:user_app/data/models/activity/ranking_model.dart';
import 'package:user_app/data/models/activity/ranking_user_address_model.dart';
import 'package:user_app/features/activity/providers/user_ranking_provider.dart';
import 'package:user_app/features/activity/pages/user_ranking/widgets/common_widget.dart';
import 'package:user_app/features/address/pages/add_address_page.dart';
import 'package:user_app/generated/l10n.dart';

class PrizeExchangePage extends ConsumerStatefulWidget {
  final UserRankingModel rankingData;
  const PrizeExchangePage({super.key, required this.rankingData});

  @override
  ConsumerState<PrizeExchangePage> createState() => _PrizeExchangePageState();
}

class _PrizeExchangePageState extends ConsumerState<PrizeExchangePage> {

  RankingUserAddressData? rankingUserAddressData;
  AddressList? selectedAddress;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    fetchRankingUserAddress(widget.rankingData.ranking?.id ?? 0);
  }

  void fetchRankingUserAddress(int rankingId) {
    setState(() {
      isLoading = true;
    });
    
    ref.read(activityRepositoryProvider).getRankingUserAddress({'ranking_id': rankingId}).then((result) {
      setState(() {
        rankingUserAddressData = result.data;
        isLoading = false;
      });
    }).catchError((final error) {
      setState(() {
        isLoading = false;
      });
      BotToast.showText(text: error.toString());
    });
  }

  void _showAddressListDialog() {
    if (rankingUserAddressData?.addressList == null || rankingUserAddressData!.addressList!.isEmpty) {
      BotToast.showText(text: S.current.my_addr_not);
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // 标题栏
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.grey.shade200, width: 1),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      S.current.home_select_address,
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(Icons.close, size: 24.w),
                    ),
                  ],
                ),
              ),
              // 地址列表
              Expanded(
                child: ListView.builder(
                  itemCount: rankingUserAddressData!.addressList!.length,
                  itemBuilder: (context, index) {
                    final address = rankingUserAddressData!.addressList![index];
                    final isSelected = address.id == selectedAddress?.id;
                    final isLast = index == rankingUserAddressData!.addressList!.length - 1;
                    
                    return _buildAddressItem(
                      address: address,
                      isSelected: isSelected,
                      isLast: isLast,
                      onTap: () {
                        setState(() {
                          selectedAddress = address;
                        });
                        Navigator.of(context).pop();
                      },
                      onEdit: () {
                        // 导航到编辑地址页面
                        Navigator.of(context).pop();
                        _navigateToEditAddress(address);
                      },
                    );
                  },
                ),
              ),
              // 添加地址按钮
              SafeArea(
                child: GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                    _navigateToAddAddress();
                  },
                  child: Container(
                    margin: EdgeInsets.only(
                        left: 16.w, right: 16.w, top: 5.h, bottom: 16.h),
                    padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
                    alignment: Alignment.center,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: AppColors.baseGreenColor,
                      borderRadius: BorderRadius.circular(45.r),
                    ),
                    child: Text(
                      S.current.add_address,
                      style: TextStyle(color: Colors.white, fontSize: 16.sp),
                    ),
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  Widget _buildAddressItem({
    required AddressList address,
    required bool isSelected,
    required bool isLast,
    required VoidCallback onTap,
    required VoidCallback onEdit,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: isSelected ? AppColors.baseGreenColor.withAlpha(20) : Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        border: Border(
          bottom: isLast
              ? BorderSide.none
              : BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 36.w,
                child: isSelected
                    ? Icon(
                        Icons.check_circle,
                        color: AppColors.baseGreenColor,
                        size: 20.sp,
                      )
                    : Container(),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: onTap,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${address.cityName ?? ''} ${address.areaName ?? ''} ${address.streetName ?? ''} ${address.address ?? ''}',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Color(0xff333333),
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 6.h),
                      Text(
                        '${address.tel ?? ''} ${address.name ?? ''}',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.textSecondaryColor,
                          fontWeight: FontWeight.w400,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(width: 15.w),
              // 编辑按钮
              GestureDetector(
                onTap: onEdit,
                child: Icon(
                  IconFont.bianji,
                  color: AppColors.textHintColor,
                  size: 20.sp,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(final BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffEFF1F6),
      appBar: AppBar(
        title: Text(S.current.prize_xchange),
      ),
      body: SingleChildScrollView(
        child: Container(
          child: Column(
            children: [
              Container(
                width: double.infinity,
                margin: EdgeInsets.all(10.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.w),
                ),
                padding: EdgeInsets.all(10.w),
                child: Column(
                  children: [
                    Text(S.current.ranking_give_card_description,
                        style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                            color: Color(0xff9DA0C2))),
                    SizedBox(height: 10.w),
                    rankingPrizeItem(
                      imageUrl:
                          widget.rankingData.user?.winnerGift?.rewardImage ?? '',
                      title: widget.rankingData.user?.winnerGift?.rewardNameUg ??
                          '',
                      subtitle:
                          '${S.current.count} : ${widget.rankingData.user?.winnerGift?.rewardCount}',
                      price:
                          widget.rankingData.user?.winnerGift?.rewardOldPrice ??
                              '',
                      rank: widget.rankingData.user?.winnerGift?.rewardRank?.toString() ??
                          '',
                    ),
                  ],
                ),
              ),

              _addressItem(),

              SizedBox(height: 150.w),

              _rankingConfrimAddressButton(),

              SizedBox(height: 100.w),

              _prizeTips(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _addressItem() {
    return cardWidget(
      child: InkWell(
        onTap: isLoading ? null : _showAddressListDialog,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 10.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: AppColors.baseGreenColor,
                      size: 32.w,
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (selectedAddress == null) ...[
                            Text(
                              S.current.ranking_choice,
                              style: TextStyle(
                                fontSize: 16.sp, 
                                fontWeight: FontWeight.w500, 
                                color: Color(0xff333333)
                              ),
                            ),
                          ] else ...[
                            // 地址信息 - 主要地址
                            Text(
                              "${S.current.ranking_give_address}:${selectedAddress!.cityName ?? ''} ${selectedAddress!.areaName ?? ''} ${selectedAddress!.streetName ?? ''} ${selectedAddress!.buildingName ?? ''} ${selectedAddress!.address ?? ''}",
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Color(0xff333333),
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(height: 4.w),
                            // 联系人姓名
                            Text(
                              "${S.current.ranking_give_name}: ${selectedAddress!.name ?? ''}",
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Color(0xff666666),
                              ),
                            ),
                            SizedBox(height: 2.w),
                            // 联系电话
                            Text(
                              "${S.current.ranking_give_mobile}: ${selectedAddress!.tel ?? ''}",
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Color(0xff666666),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              if (isLoading)
                SizedBox(
                  width: 20.w,
                  height: 20.w,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.black,
                  size: 22.w,
                )
            ],
          ),
        ),
      ),
    );
  }

  Widget _rankingConfrimAddressButton() {
    final bool canConfirm = selectedAddress != null;
    
    return Container(
      height: 50.w,
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 30.w),
      decoration: BoxDecoration(
        color: canConfirm ? null : Colors.grey,
        borderRadius: BorderRadius.circular(50.w),
        gradient: canConfirm ? LinearGradient(
          colors: [
            Color(0xffFF4C45),
            Color(0xffFF6F47),
            Color(0xffFF4B44),
          ],
        ) : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(50.w),
          onTap: canConfirm ? _confirmExchange : null,
          child: Center(
            child: Text(
              S.current.ranking_give_btn,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.white
              )
            ),
          ),
        ),
      ),
    );
  }

  void _confirmExchange() {
    if (selectedAddress == null) {
      BotToast.showText(text: S.current.home_select_address);
      return;
    }

    // 显示简化的确认对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Container(
            padding: EdgeInsets.all(24.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 顶部图标
                Container(
                  width: 80.w,
                  height: 80.w,
                  decoration: BoxDecoration(
                    color: AppColors.baseGreenColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.card_giftcard,
                    size: 40.w,
                    color: AppColors.baseGreenColor,
                  ),
                ),
                SizedBox(height: 20.h),
                
                // 标题
                Text(
                  S.current.confrim_address,
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: Color(0xff333333),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 12.h),
                
                // 描述文字
                Text(
                  S.current.confrim_address_tips,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Color(0xff666666),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 24.h),
                
                // 按钮区域
                Row(
                  children: [
                    // 取消按钮
                    Expanded(
                      child: Container(
                        height: 48.h,
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: Colors.grey.shade300),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10.r),
                            ),
                          ),
                          child: Text(
                            S.current.cancel,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                              color: Color(0xff666666),
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    // 确认按钮
                    Expanded(
                      child: Container(
                        height: 48.h,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            _performExchange();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.baseGreenColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10.r),
                            ),
                            elevation: 0,
                          ),
                          child: Text(
                            S.current.confirm,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _performExchange() async {
    if (selectedAddress == null) {
      BotToast.showText(text: S.current.ranking_choice);
      return;
    }

    try {
      // 显示加载状态
      setState(() {
        isLoading = true;
      });

      // 先确认地址
      final confirmResult = await ref.read(activityRepositoryProvider).confirmRankingAddress({
        'ranking_id': widget.rankingData.ranking?.id ?? 0,
        'address_id': selectedAddress!.id ?? 0,
      });

      if (!confirmResult.success) {
        setState(() {
          isLoading = false;
        });
        BotToast.showText(text: confirmResult.msg);
        return;
      }

      setState(() {
        isLoading = false;
      });

      BotToast.showText(text: confirmResult.msg);
      // 兑换成功后返回上一页
      Navigator.of(context).pop();
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      BotToast.showText(text: '$e');
    }
  }

  Widget _prizeTips(){
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 30.w),
      alignment: Alignment.center,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(S.current.ranking_give_description1,textAlign: TextAlign.center, style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500, color: Color(0xff9DA0C2)),),
          SizedBox(height: 10.w),
          Text(S.current.ranking_give_description2,textAlign: TextAlign.center, style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500, color: Color(0xff9DA0C2)),),
        ],
      ),
    );
  }

  /// 导航到编辑地址页面
  void _navigateToEditAddress(final AddressList address) {
    // 获取当前定位信息
    final StorageService _storageService = StorageService();
    int buildingId = int.tryParse((_storageService.read('buildingId') ?? '0')) ?? 0;
    String areaName = '${_storageService.read('areaName')}';
    String buildingName = '${_storageService.read('buildingName')}';
    String buildingNameZh = '${_storageService.read('buildingNameZh')}';

    // 导航到编辑地址页面
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (final context) => AddAddressPage(
          buildingName: buildingName,
          buildingNameZh: buildingNameZh,
          buildingId: buildingId,
          address: address.address ?? '',
          name: address.name ?? '',
          tel: address.tel ?? '',
          addressId: address.id ?? 0,
          areaName: areaName,
        ),
      ),
    ).then((final value) {
      // 编辑完成后刷新地址列表
      fetchRankingUserAddress(widget.rankingData.ranking?.id ?? 0);
    });
  }

  /// 导航到添加地址页面
  void _navigateToAddAddress() async {
    // 获取当前定位信息
    final StorageService _storageService = StorageService();
    int buildingId = int.tryParse((_storageService.read('buildingId') ?? '0')) ?? 0;
    String areaName = '${_storageService.read('areaName')}';
    String buildingName = '${_storageService.read('buildingName')}';
    String buildingNameZh = '${_storageService.read('buildingNameZh')}';

    // 导航到添加地址页面
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddAddressPage(
          buildingName: buildingName,
          buildingNameZh: buildingNameZh,
          buildingId: buildingId,
          address: '',
          name: '',
          tel: '',
          addressId: 0,
          areaName: areaName,
        ),
      ),
    ).then((final value) {
      // 添加完成后刷新地址列表
      fetchRankingUserAddress(widget.rankingData.ranking?.id ?? 0);
    });
  }
}
