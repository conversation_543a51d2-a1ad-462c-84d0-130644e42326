import 'package:user_app/data/models/activity/ranking_model.dart';

/// 排行榜状态枚举
enum RankingStatus {
  /// 未开始
  notStarted(1),

  /// 进行中且已参加
  inProgress(2),

  /// 已结束未获奖
  ended(3),

  /// 已结束且获奖
  winner(4),

  /// 已结束未获奖(备选)
  loser(5),

  /// 用户状态异常
  userError(6);

  const RankingStatus(this.value);
  final int value;
}

/// 倒计时数据
class CountdownData {
  /// 天数
  final String days;

  /// 小时
  final String hours;

  /// 分钟
  final String minutes;

  /// 秒数
  final String seconds;

  const CountdownData({
    required this.days,
    required this.hours,
    required this.minutes,
    required this.seconds,
  });

  /// 创建零时间倒计时
  factory CountdownData.zero() {
    return const CountdownData(
      days: '00',
      hours: '00',
      minutes: '00',
      seconds: '00',
    );
  }

  /// 从剩余秒数创建倒计时
  factory CountdownData.fromSeconds(int totalSeconds) {
    if (totalSeconds <= 0) {
      return CountdownData.zero();
    }

    final days = totalSeconds ~/ (24 * 60 * 60);
    final hours = (totalSeconds % (24 * 60 * 60)) ~/ (60 * 60);
    final minutes = (totalSeconds % (60 * 60)) ~/ 60;
    final seconds = totalSeconds % 60;

    return CountdownData(
      days: days.toString().padLeft(2, '0'),
      hours: hours.toString().padLeft(2, '0'),
      minutes: minutes.toString().padLeft(2, '0'),
      seconds: seconds.toString().padLeft(2, '0'),
    );
  }
}

/// 用户排行榜页面状态
class UserRankingState {
  /// 是否加载中
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 排行榜数据
  final UserRankingModel? rankingData;

  /// 当前状态
  final RankingStatus status;

  /// 倒计时数据
  final CountdownData countdown;

  /// 进度条宽度（像素）
  final double? progressBarWidth;

  /// 是否正在参加活动
  final bool isJoining;

  /// 剩余时间（秒）
  final int remainingSeconds;

  const UserRankingState({
    this.isLoading = false,
    this.error,
    this.rankingData,
    this.status = RankingStatus.notStarted,
    this.countdown = const CountdownData(
      days: '00',
      hours: '00',
      minutes: '00',
      seconds: '00',
    ),
    this.progressBarWidth,
    this.isJoining = false,
    this.remainingSeconds = 0,
  });

  /// 拷贝状态
  UserRankingState copyWith({
    bool? isLoading,
    String? error,
    UserRankingModel? rankingData,
    RankingStatus? status,
    CountdownData? countdown,
    double? progressBarWidth,
    bool? isJoining,
    int? remainingSeconds,
  }) {
    return UserRankingState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      rankingData: rankingData ?? this.rankingData,
      status: status ?? this.status,
      countdown: countdown ?? this.countdown,
      progressBarWidth: progressBarWidth ?? this.progressBarWidth,
      isJoining: isJoining ?? this.isJoining,
      remainingSeconds: remainingSeconds ?? this.remainingSeconds,
    );
  }

  /// 清除错误
  UserRankingState clearError() {
    return copyWith(error: null);
  }

  /// 计算排行榜状态
  /// 根据微信小程序的逻辑计算当前状态
  static RankingStatus calculateStatus(UserRankingModel? data) {
    if (data == null) return RankingStatus.notStarted;

    final user = data.user;
    final ranking = data.ranking;

    if (user == null || ranking == null) return RankingStatus.notStarted;

    // 用户状态异常
    if (user.userState == 1) {
      return RankingStatus.userError;
    }

    // 活动进行中且已参加
    if (ranking.rankingState == 1 && user.isJoined == true) {
      return RankingStatus.inProgress;
    }

    // 活动已结束
    if (ranking.rankingState == 2) {
      return RankingStatus.ended;
    }

    // 活动已完成且中奖
    if (ranking.rankingState == 3 && user.isWinner == 1) {
      return RankingStatus.winner;
    }

    // 活动已完成未中奖
    if (ranking.rankingState == 3 && user.isWinner == 0) {
      return RankingStatus.loser;
    }

    // 活动已确认且中奖
    if (ranking.rankingState == 4 && user.isWinner == 1) {
      return RankingStatus.winner;
    }

    // 活动已确认未中奖
    if (ranking.rankingState == 4 && user.isWinner == 0) {
      return RankingStatus.loser;
    }

    // 默认未开始
    return RankingStatus.notStarted;
  }

  /// 计算进度条宽度
  /// 根据用户消费金额和剩余金额计算进度条宽度，与微信小程序逻辑保持一致
  static double? calculateProgressBarWidth(
    UserRankingModel? data,
    double containerWidth,
  ) {
    if (data?.user == null) return null;

    final user = data!.user!;
    final amount = (user.amount ?? 0).toDouble();
    final remainingAmount = (user.remainingAmount ?? 0).toDouble();

    if (amount + remainingAmount <= 0) return null;

    // 与微信小程序保持一致的计算逻辑
    final percent = (amount + remainingAmount) / 100;
    final widthPercent = amount / percent;
    double width = (containerWidth / 100) * widthPercent;

    // 小于50时不设置宽度（与微信小程序保持一致）
    if (width < 50) return null;

    // 最大宽度（留出6像素边距）
    if (containerWidth - width < 6) {
      width = containerWidth - 6;
    }

    return width;
  }

  /// 是否需要显示倒计时
  bool get shouldShowCountdown {
    return status == RankingStatus.notStarted ||
        status == RankingStatus.inProgress;
  }

  /// 是否需要显示进度条
  bool get shouldShowProgressBar {
    return status == RankingStatus.inProgress || status == RankingStatus.winner;
  }

  /// 是否需要显示获奖卡片
  bool get shouldShowWinnerCard {
    return status == RankingStatus.winner &&
        rankingData?.user?.winnerGift != null;
  }

  /// 获取当前用户排名
  int get currentRanking {
    return rankingData?.user?.currentRanking ?? 0;
  }

  /// 获取用户消费金额
  String get userAmount {
    return rankingData?.user?.amount?.toString() ?? '0';
  }

  /// 获取用户剩余金额
  String get userRemainingAmount {
    return rankingData?.user?.remainingAmount?.toString() ?? '0';
  }

  /// 获取排行榜用户列表
  List<RankingUserInfo> get rankingUsers {
    return rankingData?.ranking?.users ?? [];
  }

  /// 获取奖品列表
  List<RankingGift> get giftList {
    return rankingData?.ranking?.gift ?? [];
  }

  /// 获取中奖礼品信息
  WinnerGift? get winnerGift {
    return rankingData?.user?.winnerGift;
  }
}
