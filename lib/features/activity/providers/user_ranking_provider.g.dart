// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_ranking_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$activityRepositoryHash() =>
    r'8c062d48397f2adb08a211aee5f37a8494ab34c4';

/// 排行榜数据仓库提供者
///
/// Copied from [activityRepository].
@ProviderFor(activityRepository)
final activityRepositoryProvider =
    AutoDisposeProvider<ActivityRepository>.internal(
  activityRepository,
  name: r'activityRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$activityRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ActivityRepositoryRef = AutoDisposeProviderRef<ActivityRepository>;
String _$userRankingNotifierHash() =>
    r'c4c886145b80d5cde1e178b01a4b9074ff07c664';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$UserRankingNotifier
    extends BuildlessAutoDisposeAsyncNotifier<RankingInfoData?> {
  late final int rankingId;
  late final int areaId;
  late final int restaurantId;
  late final int type;

  FutureOr<RankingInfoData?> build({
    required int rankingId,
    required int areaId,
    required int restaurantId,
    required int type,
  });
}

/// 排行榜信息提供者
///
/// Copied from [UserRankingNotifier].
@ProviderFor(UserRankingNotifier)
const userRankingNotifierProvider = UserRankingNotifierFamily();

/// 排行榜信息提供者
///
/// Copied from [UserRankingNotifier].
class UserRankingNotifierFamily extends Family<AsyncValue<RankingInfoData?>> {
  /// 排行榜信息提供者
  ///
  /// Copied from [UserRankingNotifier].
  const UserRankingNotifierFamily();

  /// 排行榜信息提供者
  ///
  /// Copied from [UserRankingNotifier].
  UserRankingNotifierProvider call({
    required int rankingId,
    required int areaId,
    required int restaurantId,
    required int type,
  }) {
    return UserRankingNotifierProvider(
      rankingId: rankingId,
      areaId: areaId,
      restaurantId: restaurantId,
      type: type,
    );
  }

  @override
  UserRankingNotifierProvider getProviderOverride(
    covariant UserRankingNotifierProvider provider,
  ) {
    return call(
      rankingId: provider.rankingId,
      areaId: provider.areaId,
      restaurantId: provider.restaurantId,
      type: provider.type,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userRankingNotifierProvider';
}

/// 排行榜信息提供者
///
/// Copied from [UserRankingNotifier].
class UserRankingNotifierProvider extends AutoDisposeAsyncNotifierProviderImpl<
    UserRankingNotifier, RankingInfoData?> {
  /// 排行榜信息提供者
  ///
  /// Copied from [UserRankingNotifier].
  UserRankingNotifierProvider({
    required int rankingId,
    required int areaId,
    required int restaurantId,
    required int type,
  }) : this._internal(
          () => UserRankingNotifier()
            ..rankingId = rankingId
            ..areaId = areaId
            ..restaurantId = restaurantId
            ..type = type,
          from: userRankingNotifierProvider,
          name: r'userRankingNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$userRankingNotifierHash,
          dependencies: UserRankingNotifierFamily._dependencies,
          allTransitiveDependencies:
              UserRankingNotifierFamily._allTransitiveDependencies,
          rankingId: rankingId,
          areaId: areaId,
          restaurantId: restaurantId,
          type: type,
        );

  UserRankingNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.rankingId,
    required this.areaId,
    required this.restaurantId,
    required this.type,
  }) : super.internal();

  final int rankingId;
  final int areaId;
  final int restaurantId;
  final int type;

  @override
  FutureOr<RankingInfoData?> runNotifierBuild(
    covariant UserRankingNotifier notifier,
  ) {
    return notifier.build(
      rankingId: rankingId,
      areaId: areaId,
      restaurantId: restaurantId,
      type: type,
    );
  }

  @override
  Override overrideWith(UserRankingNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: UserRankingNotifierProvider._internal(
        () => create()
          ..rankingId = rankingId
          ..areaId = areaId
          ..restaurantId = restaurantId
          ..type = type,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        rankingId: rankingId,
        areaId: areaId,
        restaurantId: restaurantId,
        type: type,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<UserRankingNotifier, RankingInfoData?>
      createElement() {
    return _UserRankingNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserRankingNotifierProvider &&
        other.rankingId == rankingId &&
        other.areaId == areaId &&
        other.restaurantId == restaurantId &&
        other.type == type;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, rankingId.hashCode);
    hash = _SystemHash.combine(hash, areaId.hashCode);
    hash = _SystemHash.combine(hash, restaurantId.hashCode);
    hash = _SystemHash.combine(hash, type.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserRankingNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<RankingInfoData?> {
  /// The parameter `rankingId` of this provider.
  int get rankingId;

  /// The parameter `areaId` of this provider.
  int get areaId;

  /// The parameter `restaurantId` of this provider.
  int get restaurantId;

  /// The parameter `type` of this provider.
  int get type;
}

class _UserRankingNotifierProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<UserRankingNotifier,
        RankingInfoData?> with UserRankingNotifierRef {
  _UserRankingNotifierProviderElement(super.provider);

  @override
  int get rankingId => (origin as UserRankingNotifierProvider).rankingId;
  @override
  int get areaId => (origin as UserRankingNotifierProvider).areaId;
  @override
  int get restaurantId => (origin as UserRankingNotifierProvider).restaurantId;
  @override
  int get type => (origin as UserRankingNotifierProvider).type;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
