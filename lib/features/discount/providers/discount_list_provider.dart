import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/repositories/home/<USER>';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/generated/l10n.dart';

///获取地址列表按定位数据
Future<ApiResult<DiscountFoodsData>> getDiscountData(Ref ref,
    {required int buildingId,required int page}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'building_id': buildingId,
    'page': page,
    'limit': 15,
  };
  // 秒杀活动数据
  final homeRepository = HomeRepository(apiClient: ref.read(apiClientProvider));
  return await homeRepository.discountFoods(param);
}

///地址列表按定位数据提供者类
class DiscountListProvider extends StateNotifier<AsyncValue<DiscountFoodsData>?> {
  DiscountListProvider(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> fetchDiscountData({required int buildingId,required int page}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final buildingId = ref.read(localStorageRepositoryProvider).getLocationInfo()?.id;
      if(buildingId == null) {
        state = AsyncValue.error(S.current.no_data, StackTrace.empty);
        return;
      }
      final response = await getDiscountData(ref, buildingId: buildingId ,page: page);
      final discountInfo = response.data;
      
      if(!response.success || discountInfo == null) {
        state = AsyncValue.error(response.msg, StackTrace.empty);
        return;
      }
      
      state = AsyncValue.data(discountInfo);

      if ((discountInfo.items ?? []).length < 15) {
        ref.watch(canDiscountDataProvider.notifier).state = false;
      } else {
        ref.watch(canDiscountDataProvider.notifier).state = true;
      }

      if (page > 1) {
        ref
            .watch(discountItemsProvider.notifier)
            .state
            .addAll(state?.value?.items ?? []);
      } else {
        ref.watch(discountItemsProvider.notifier).state =
            state?.value?.items ?? [];
      }

    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final discountListProvider = StateNotifierProvider.autoDispose<
    DiscountListProvider, AsyncValue<DiscountFoodsData>?>(
  (ref) => DiscountListProvider(ref),
);

final canDiscountDataProvider = StateProvider.autoDispose<bool>((final ref) => true);

final discountItemsProvider = StateProvider.autoDispose<List<Items>>((final ref) => []);

