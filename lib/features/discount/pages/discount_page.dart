import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/widgets/image_viewer.dart';
import 'package:user_app/core/widgets/index.dart';
import 'package:user_app/core/widgets/custom_refresh_indicator.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/features/discount/providers/discount_list_provider.dart';
import 'package:user_app/features/home/<USER>/parts/discount_code_page.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';
import 'package:user_app/features/order/widgets/spec_selected_options_widget.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';

class DiscountPage extends ConsumerStatefulWidget {
  const DiscountPage({super.key});

  @override
  ConsumerState createState() => _DiscountPageState();
}

class _DiscountPageState extends ConsumerState<DiscountPage> {
  int _currentPage = 1;
  final int _buildingId = 112297;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      _fetchInitialData();
    });
  }

  Future<void> _fetchInitialData() async {
    _currentPage = 1;
    // 这里假设 fetchDiscountData 内部会处理加载状态，不需要手动检查 isMounted
    await ref
        .read(discountListProvider.notifier)
        .fetchDiscountData(buildingId: _buildingId, page: _currentPage);
  }

  Future<void> _onRefresh() async {
    _currentPage = 1;
    // 移除 isRefreshing 参数，假设 notifier 内部处理或不需要
    await ref
        .read(discountListProvider.notifier)
        .fetchDiscountData(buildingId: _buildingId, page: _currentPage);
  }

  Future<void> _onLoadMore() async {
    // 检查 ref 是否还在 mounted 状态，以及是否有更多数据
    if (!mounted) return;
    if (ref.read(canDiscountDataProvider)) {
      _currentPage++;
      await ref
          .read(discountListProvider.notifier)
          .fetchDiscountData(buildingId: _buildingId, page: _currentPage);
      print("加载更多完成，当前页: $_currentPage");
    }
  }

  @override
  Widget build(final BuildContext context) {
    final discountListState = ref.watch(discountListProvider);
    final discountItems = ref.watch(discountItemsProvider); // 已缓存或派生，通常重建较少
    final bool canLoadMore = ref.watch(canDiscountDataProvider);
    final homeNoticeLocationId = ref.watch(
        homeNoticeProvider.select((final value) => value.value?.location?.id));
    final currentLanguage = ref.watch(languageProvider);

    ref.listen(homeNoticeProvider, (final _, final __) {
      _onRefresh();
    });

    Widget bodyContent;

    if (discountListState == null) {
      bodyContent = const Center(child: LoadingWidget(isFullScreen: false));
    } else {
      bodyContent = discountListState.when(
        data: (final data) {
          final bool isLoading = discountListState.isLoading;
          final bool isRefreshing = (discountListState is AsyncLoading &&
              discountListState.isRefreshing == true);

          if (discountItems.isEmpty &&
              !isLoading &&
              !isRefreshing &&
              data?.items?.isEmpty == true) {
            return EmptyView(
              message: S.current.no_more_data, // 使用一个通用的无数据提示
              onRetry: _fetchInitialData,
              retryMessage: S.current.retry,
            );
          }
          // 即使在加载中，如果已有数据，也构建列表，让 CustomRefreshIndicator 处理头部动画
          return CustomRefreshIndicator(
            onRefresh: _onRefresh,
            onLoading: _onLoadMore,
            enablePullUp: true,
            hasMoreData: canLoadMore,
            child: ListView.builder(
              itemCount: discountItems.length + 1,
              itemBuilder: (final context, final index) {
                if (index == 0) {
                  return _discountQRCode(context, currentLanguage);
                }
                final itemIndex = index - 1;
                // 防御性检查，虽然理论上不应该超出范围
                if (itemIndex >= 0 && itemIndex < discountItems.length) {
                  return _foodItem(
                    discountItems[itemIndex],
                    homeNoticeLocationId ?? 0,
                  );
                }
                return const SizedBox.shrink(); // 或者 null，但SizedBox.shrink更明确
              },
            ),
          );
        },
        error: (final error, final stackTrace) {
          print('stackTrace $stackTrace');
          return EmptyView(
            message: error.toString(),
            retryMessage: S.current.retry,
            onRetry: _fetchInitialData,
          );
        },
        loading: () {
          // 如果 discountItems 为空，显示中心加载动画 (首次加载)
          if (discountItems.isEmpty) {
            return const Center(
              child: LoadingWidget(isFullScreen: false),
            );
          }
          // 如果已有数据 (例如下拉刷新时)，则 CustomRefreshIndicator 会处理加载动画
          // 返回当前的列表，避免UI跳闪。CustomRefreshIndicator 会在其之上显示刷新动画
          return CustomRefreshIndicator(
            onRefresh: _onRefresh,
            onLoading: _onLoadMore,
            enablePullUp: canLoadMore,
            hasMoreData: canLoadMore,
            child: ListView.builder(
              itemCount: discountItems.length + 1,
              itemBuilder: (final context, final index) {
                if (index == 0) {
                  return _discountQRCode(context, currentLanguage);
                }
                final itemIndex = index - 1;
                if (itemIndex >= 0 && itemIndex < discountItems.length) {
                  return _foodItem(
                    discountItems[itemIndex],
                    homeNoticeLocationId ?? 0,
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          );
        },
      );
    }

    return Scaffold(
      body: Stack(
        children: [
          Container(
            alignment: Alignment.center,
            color: AppColors.baseBackgroundColor,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: AppColors.baseGreenColor,
                        gradient: LinearGradient(
                          colors: [
                            AppColors.baseGreenColor,
                            AppColors.baseBackgroundColor,
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          stops: [
                            0.6,
                            1,
                          ],
                        ),
                        image: DecorationImage(
                          image: AssetImage(
                            'assets/images/header_img.png',
                          ), // 本地图片
                          fit: BoxFit.contain, // 让图片覆盖整个容器
                        ),
                      ),
                      height: 200.w,
                    ),
                    Positioned(
                      top: 56.w,
                      right: 0,
                      left: 0,
                      child: Container(
                        alignment: Alignment.center,
                        child: Text(
                          S.current.discount,
                          style: TextStyle(
                            fontSize: soBigSize,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Positioned(
            left: 0,
            right: 0,
            top: 86.w,
            bottom: 0,
            child: bodyContent,
          ),
        ],
      ),
    );
  }

  String reverseSortDateRange(final String input) {
    if (input.isEmpty || !input.contains('-')) return '';
    // 1. 拆分
    List<String> parts = input.split('-');
    // 2. 排序（降序）
    parts.sort((final a, final b) => b.compareTo(a));
    // 3. 拼接
    return parts.join('-');
  }

  Widget _foodItem(final Items items, final int noticeLocationId) {
    String thisTime = ref.watch(languageProvider) == 'ug'
        ? reverseSortDateRange(items.time ?? '')
        : (items.time ?? '');
    String thisDate = ref.watch(languageProvider) == 'ug'
        ? reverseSortDateRange(items.date ?? '')
        : (items.date ?? '');

    return InkWell(
      onTap: () {
        context.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': items.restaurantId ?? 0,
            'buildingId': noticeLocationId, // 使用传递进来的 noticeLocationId
            'ids': [items.foodId],
          },
        );
      },
      child: Container(
        margin: EdgeInsets.only(top: 10.w, right: 10.w, left: 10.w),
        padding: EdgeInsets.all(10.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.w),
          color: Colors.white,
        ),
        child: Column(
          children: [
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10.w),
                  child: PrefectImage(
                    imageUrl: items.image ?? '',
                    width: 135.w,
                    height: 115.w,
                    fit: BoxFit.fill,
                  ),
                ),
                SizedBox(
                  width: 10.w,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 4.w,
                      ),
                      Text(
                        items.foodName ?? '',
                        style: TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: titleSize,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                      ),
                      SizedBox(
                        height: 5.w,
                      ),
                      Text(
                        items.restaurantName ?? '',
                        style: TextStyle(
                          color: AppColors.textSecondColor,
                          fontWeight: FontWeight.bold,
                          fontSize: mainSize,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                      ),
                      SizedBox(
                        height: 5.w,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            '${S.current.hours}:',
                            style: TextStyle(
                              color: AppColors.textSecondColor,
                              fontWeight: FontWeight.bold,
                              fontSize: littleSize,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                          ),
                          Text(
                            thisTime,
                            style: TextStyle(
                              color: AppColors.textSecondColor,
                              fontWeight: FontWeight.bold,
                              fontSize: littleSize,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                          ),
                          SizedBox(
                            width: 10.w,
                          ),
                          Text(
                            '${S.current.time}:',
                            style: TextStyle(
                              color: AppColors.textSecondColor,
                              fontWeight: FontWeight.bold,
                              fontSize: littleSize,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                          ),
                          Text(
                            thisDate,
                            style: TextStyle(
                              color: AppColors.textSecondColor,
                              fontWeight: FontWeight.bold,
                              fontSize: littleSize,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 4.w,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: '¥',
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16.sp,
                                    fontFamily: AppConstants.numberFont,
                                  ),
                                ),
                                ...FormatUtil.buildPriceTextSpans(
                                  items.price.toString(),
                                  20.sp,
                                  Colors.red,
                                  AppConstants.numberFont,
                                ),

                                // 空距离
                                TextSpan(
                                  text: ' ',
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 10.sp,
                                    fontFamily: AppConstants.numberFont,
                                  ),
                                ),

                                //原价，划线
                                TextSpan(
                                  text: '¥',
                                  style: TextStyle(
                                    color: AppColors.textSecondaryColor,
                                    decoration: TextDecoration.lineThrough,
                                    decorationColor:
                                        AppColors.textSecondaryColor,
                                    fontSize: 12.sp,
                                  ),
                                ),
                                TextSpan(
                                  text: '${items.originPrice}',
                                  style: TextStyle(
                                    decoration: TextDecoration.lineThrough,
                                    decorationColor:
                                        AppColors.textSecondaryColor,
                                    color: AppColors.textSecondaryColor,
                                    fontSize: 16.sp,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          StarRating(
                            rating: 5.0,
                            size: 18.sp, // 32rpx / 2
                            gap: 4.0, // 设置星星间隔
                            hideTip: true,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // 显示规格信息 - 与小程序 spec-container 逻辑一致
            if ((items.foodType ?? 0) == 1 &&
                (items.selectedSpec?.specOptions?.isNotEmpty ?? false))
              Container(
                margin: EdgeInsets.only(top: 8.h),
                child: SpecSelectedOptionsWidget.withBackground(
                  specSelectedOptions: items.selectedSpec!.specOptions!
                      .map(
                        (final option) => {
                          'spec_type_id': 0, // 这里可以根据需要设置
                          'spec_option_id': option.id ?? 0,
                          'name': option.name ?? '',
                          'price': option.price ?? 0,
                        },
                      )
                      .toList(),
                  fontSize: 12.sp, // 对应小程序 24rpx
                  textColor: const Color(0xFF8D8C8C),
                  backgroundColor: const Color(0xFFEAECF0), // 对应小程序 #eaecf0
                  padding: EdgeInsets.symmetric(
                    horizontal: 8.w, // 对应小程序 10rpx
                    vertical: 4.h, // 对应小程序 8rpx
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _discountQRCode(final BuildContext context, final String language) {
    final appConfig = ref.read(localStorageRepositoryProvider).getAppConfig();
    final isShowEnterpriseWechatQrCode =
        appConfig?.app_show_enterprise_wechat_qr_code == 1;
    if (!isShowEnterpriseWechatQrCode) {
      return const SizedBox.shrink();
    }
    return Container(
      margin: EdgeInsets.only(top: 10.w, right: 10.w, left: 10.w),
      padding: EdgeInsets.all(10.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.w),
        color: Colors.white,
      ),
      child: Row(
        children: [
          InkWell(
            onTap: () {
              showImageViewer(
                context,
                imageUrls: [
                  'https://acdn.mulazim.com/upload/work-wechat/202402/29/5ff29640a2bbc193486fa175889ca864.png',
                ],
                heroTagPrefix: 'chat_image',
              );
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10.w),
              child: PrefectImage(
                imageUrl:
                    'https://acdn.mulazim.com/upload/work-wechat/202402/29/5ff29640a2bbc193486fa175889ca864.png',
                fit: BoxFit.cover,
                width: 108.w,
                height: 108.w,
              ),
            ),
          ),
          SizedBox(
            width: 10.w,
          ),
          InkWell(
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (final context) => DiscountCodePage(),
                ),
              );
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10.w),
              child: PrefectImage(
                imageUrl:
                    'https://acdn.mulazim.com/wechat_mini/img/work/work-$language.png',
                fit: BoxFit.fill,
                height: 108.w,
                width: MediaQuery.of(context).size.width - 108.w - 50.w,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
