import 'package:user_app/data/models/order_evaluate/order_evaluate_model.dart';

/// 订单评价页面状态
class OrderEvaluateState {
  /// 构造函数
  const OrderEvaluateState({
    this.isLoading = false,
    this.error,
    this.evaluateData,
    this.isSubmitting = false,
    this.showSuccessPopup = false,
    this.isUploading = false,
    this.isSuccess = false,
  });

  /// 是否加载中
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 评价数据
  final OrderEvaluateModel? evaluateData;

  /// 是否正在提交
  final bool isSubmitting;

  /// 是否显示成功弹窗
  final bool showSuccessPopup;

  /// 是否正在上传图片
  final bool isUploading;

  /// 是否提交成功（用于页面退出逻辑）
  final bool isSuccess;

  /// 拷贝方法
  OrderEvaluateState copyWith({
    bool? isLoading,
    String? error,
    OrderEvaluateModel? evaluateData,
    bool? isSubmitting,
    bool? showSuccessPopup,
    bool? isUploading,
    bool? isSuccess,
  }) {
    return OrderEvaluateState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      evaluateData: evaluateData ?? this.evaluateData,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      showSuccessPopup: showSuccessPopup ?? this.showSuccessPopup,
      isUploading: isUploading ?? this.isUploading,
      isSuccess: isSuccess ?? this.isSuccess,
    );
  }

  /// 清除错误信息
  OrderEvaluateState clearError() {
    return copyWith(error: null);
  }
}
