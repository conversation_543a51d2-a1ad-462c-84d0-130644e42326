import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/order_evaluate_controller.dart';

/// 美食列表组件
class FoodListWidget extends ConsumerWidget {
  const FoodListWidget({final Key? key}) : super(key: key);

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 精确监听美食列表
    final foods = ref.watch(
      orderEvaluateControllerProvider.select(
        (final state) => state.evaluateData?.orderDetail.foods ?? [],
      ),
    );

    // 将美食列表分组为两列
    final List<List<dynamic>> pairs = [];
    for (int i = 0; i < foods.length; i += 2) {
      if (i + 1 < foods.length) {
        pairs.add([
          {'index': i, 'food': foods[i]},
          {'index': i + 1, 'food': foods[i + 1]}
        ]);
      } else {
        pairs.add([
          {'index': i, 'food': foods[i]},
          null // 占位符
        ]);
      }
    }

    return Column(
      children: pairs.map((final pair) {
        return Container(
          margin: EdgeInsets.only(bottom: 10.h),
          child: Row(
            children: [
              // 左侧美食项
              Expanded(
                child: _buildFoodItem(
                  ref,
                  pair[0]['index'],
                  pair[0]['food'],
                ),
              ),
              SizedBox(width: 10.w),
              // 右侧美食项
              Expanded(
                child: pair[1] != null
                    ? _buildFoodItem(
                        ref,
                        pair[1]['index'],
                        pair[1]['food'],
                      )
                    : const SizedBox(), // 空占位符
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// 构建单个美食项
  Widget _buildFoodItem(final WidgetRef ref, final int index, final food) {
    return GestureDetector(
      onTap: () {
        ref
            .read(orderEvaluateControllerProvider.notifier)
            .selectFood(index, food.foodName ?? '');
      },
      child: Container(
        decoration: BoxDecoration(
          color:
              food.active ? const Color(0xFFFFF8E3) : const Color(0xFFF8F9FB),
          borderRadius: BorderRadius.circular(8.w),
          border: Border.all(
            color: food.active ? const Color(0xFFFFA30E) : Colors.transparent,
            width: 1.w,
          ),
        ),
        child: Row(
          children: [
            // 美食图片和名称，占用权重2
            Expanded(
              flex: 8,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(4.w),
                    child: Image.network(
                      food.foodImage ?? '',
                      width: 40.w,
                      height: 40.w,
                      fit: BoxFit.cover,
                      errorBuilder:
                          (final context, final error, final stackTrace) {
                        return Container(
                          width: 40.w,
                          height: 40.w,
                          color: const Color(0xFFF0F0F0),
                          child: Icon(
                            Icons.image,
                            size: 20.sp,
                            color: const Color(0xFF8C8C8C),
                          ),
                        );
                      },
                    ),
                  ),
                  SizedBox(width: 4.w),
                  // 美食名称
                  Expanded(
                    child: Text(
                      food.foodName ?? '',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: food.active
                            ? const Color(0xFFFFA30E)
                            : AppColors.textPrimaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 美食数量，占用权重1
            Expanded(
              flex: 1,
              child: Text(
                'X${food.foodCount ?? 1}',
                textAlign: TextAlign.end,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: food.active
                      ? const Color(0xFFFFA30E)
                      : const Color(0xFF666666),
                ),
              ),
            ),
            SizedBox(width: 5.w,)
          ],
        ),
      ),
    );
  }
}
