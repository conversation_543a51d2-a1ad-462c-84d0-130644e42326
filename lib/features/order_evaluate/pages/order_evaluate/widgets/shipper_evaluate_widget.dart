import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/widgets/dash_painter.dart';
import 'package:user_app/data/models/order_evaluate/order_evaluate_model.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/order_evaluate_controller.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/widgets/anonymous_checkbox_widget.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/widgets/satisfaction_card_widget.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/widgets/suggest_tags_widget.dart';
import 'package:user_app/generated/l10n.dart';

/// 配送员评价组件
class ShipperEvaluateWidget extends ConsumerWidget {
  /// 构造函数
  const ShipperEvaluateWidget({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 监听配送员数据
    final shipperData = ref.watch(
      orderEvaluateControllerProvider
          .select((final state) => state.evaluateData?.shipper),
    );

    // 如果没有配送员数据，不显示
    if (shipperData == null) {
      return const SizedBox.shrink();
    }

    final lang = ref.watch(languageProvider);
    final isRtl = lang == 'ug';

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.w),
      ),
      padding: EdgeInsets.all(10.w),
      child: Directionality(
        textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题和匿名选项
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.current.evaluate_shipper,
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                AnonymousCheckboxWidget(
                  isAnonymous: shipperData.isAnonymous,
                  onChanged: (final value) {
                    ref
                        .read(orderEvaluateControllerProvider.notifier)
                        .updateShipperAnonymous(value);
                  },
                ),
              ],
            ),
            //虚线
            CustomPaint(
              painter: DashPainter(
                color: Colors.grey.shade300,
                strokeWidth: 1.h,
                borderRadius: 0,
                dashPattern: [1, 2], // 实线模式：线段长度1，间隔0
              ),
              size: Size(double.infinity, 1.h),
            ),
            SizedBox(height: 10.h),

            // 配送员信息
            _buildShipperInfo(shipperData),

            SizedBox(height: 15.h),

            // 满意度选择
            const SatisfactionCardWidget(),

            SizedBox(height: 10.h),

            // 建议标签
            const SuggestTagsWidget(),
          ],
        ),
      ),
    );
  }

  /// 构建配送员信息
  Widget _buildShipperInfo(final ShipperEvaluate shipperData) {
    return Row(
      children: [
        // 配送员头像
        CircleAvatar(
          radius: 25.w,
          backgroundImage: shipperData.shipperAvatar != null
              ? NetworkImage(shipperData.shipperAvatar!)
              : null,
          backgroundColor: Colors.grey[300],
          child: shipperData.shipperAvatar == null
              ? Icon(Icons.person, size: 30.sp, color: Colors.grey[600])
              : null,
        ),
        SizedBox(width: 10.w),
        // 配送员信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                shipperData.shipperName ?? '配送员',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (shipperData.areaName != null) ...[
                SizedBox(height: 2.h),
                Text(
                  shipperData.areaName!,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
