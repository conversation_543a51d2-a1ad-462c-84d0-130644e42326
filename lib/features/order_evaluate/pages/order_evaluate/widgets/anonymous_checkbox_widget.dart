import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 匿名复选框组件
class AnonymousCheckboxWidget extends StatelessWidget {
  final bool isAnonymous;
  final Function(bool) onChanged;

  const AnonymousCheckboxWidget({
    final Key? key,
    required this.isAnonymous,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(final BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Checkbox(
          value: isAnonymous,
          onChanged: (final value) => onChanged(value ?? false),
          activeColor: AppColors.baseGreenColor,
          shape: const CircleBorder(),
          side: BorderSide(
            color: Colors.grey,
            width: 1.0,
          ),
        ),
        GestureDetector(
          onTap: () => onChanged(!isAnonymous),
          child: Text(
            S.current.evaluate_anonymous,
            style: TextStyle(
              fontSize: 18.sp,
              color: Colors.black54,
            ),
          ),
        ),
      ],
    );
  }
}
