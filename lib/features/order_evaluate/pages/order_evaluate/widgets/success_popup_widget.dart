import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/config/config.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/order_evaluate/order_evaluate_model.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';

/// 评价成功弹窗组件（完全按照小程序设计）
class SuccessPopupWidget extends ConsumerStatefulWidget {
  final bool isVisible;
  final VoidCallback onClose;
  final ShipperEvaluate? shipper;

  const SuccessPopupWidget({
    final Key? key,
    required this.isVisible,
    required this.onClose,
    this.shipper,
  }) : super(key: key);

  @override
  ConsumerState<SuccessPopupWidget> createState() => _SuccessPopupWidgetState();
}

class _SuccessPopupWidgetState extends ConsumerState<SuccessPopupWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.ease,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.ease,
    ));

    if (widget.isVisible) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(final SuccessPopupWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible && !oldWidget.isVisible) {
      _animationController.forward();
    } else if (!widget.isVisible && oldWidget.isVisible) {
      _animationController.reverse();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 跳转到配送员详情页面
  void _navigateToShipperDetail() {
    if (widget.shipper != null && widget.shipper!.id != null) {
      context.push(
        AppPaths.shipperDetailPage,
        extra: {
          'shipperId': widget.shipper!.id!,
          'orderId': widget.shipper!.orderId,
        },
      );
    }
  }

  @override
  Widget build(final BuildContext context) {
    if (!widget.isVisible) return const SizedBox.shrink();

    final lang = ref.watch(languageProvider);
    final isRtl = lang == 'ug';

    return Material(
      color: Colors.transparent,
      child: Stack(
        children: [
          // 背景遮罩
          Positioned.fill(
            child: GestureDetector(
              onTap: widget.onClose,
              child: Container(
                color: Colors.black.withOpacity(0.4),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
                  child: Container(),
                ),
              ),
            ),
          ),

          // 弹窗内容
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Center(
                child: AnimatedBuilder(
                  animation: _animationController,
                  builder: (final context, final child) {
                    return Transform.scale(
                      scale: _scaleAnimation.value,
                      child: Opacity(
                        opacity: _opacityAnimation.value,
                        child: _buildPopupContent(isRtl),
                      ),
                    );
                  },
                ),
              ),

              // 关闭按钮
              Center(
                child: GestureDetector(
                  onTap: widget.onClose,
                  child: Container(
                    margin: EdgeInsets.only(top: 10.h),
                    width: 35.w,
                    height: 35.w,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.9),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.close,
                      size: 20.sp,
                      color: Colors.black54,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建弹窗内容
  Widget _buildPopupContent(final bool isRtl) {
    return Container(
      width: 0.9.sw,
      decoration: BoxDecoration(
        color: const Color(0xFF3CC873), // 绿色背景
        borderRadius: BorderRadius.circular(8.w),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 头部区域
          _buildHeader(),

          // 内容区域
          _buildContent(isRtl),
        ],
      ),
    );
  }

  /// 构建头部区域
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(15.w),
      child: Column(
        children: [
          // 成功图标
          Image.network(
            'https://acdn.mulazim.com/wechat_mini/img/evaluate/tips_success.png',
            width: 124.w, // 248rpx / 2
            height: 94.w, // 188rpx / 2
            fit: BoxFit.contain,
            errorBuilder: (final context, final error, final stackTrace) {
              return Container(
                width: 124.w,
                height: 94.w,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8.w),
                ),
                child: Icon(
                  Icons.check_circle,
                  size: 50.sp,
                  color: Colors.white,
                ),
              );
            },
          ),

          SizedBox(height: 10.w),

          // 成功标题
          Text(
            S.current.evaluate_submit_success,
            style: TextStyle(
              color: Colors.white,
              fontSize: 22.5.sp, // 45rpx / 2
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.mainFont,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 10.w),

          // 成功描述
          Text(
            S.current.evaluate_submit_success_desc,
            style: TextStyle(
              color: const Color(0xFF9DFFC5),
              fontSize: 16.sp, // 32rpx / 2
              fontFamily: AppConstants.mainFont,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent(final bool isRtl) {
    return Container(
      margin: EdgeInsets.all(10.w),
      padding: EdgeInsets.all(10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.w),
      ),
      child: Directionality(
        textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
        child: Column(
          children: [
            // 打赏配送员标题
            Text(
              S.current.evaluate_popup_header_shipper_reward,
              style: TextStyle(
                fontSize: 19.sp, // 38rpx / 2
                color: Colors.black,
                fontWeight: FontWeight.bold,
                fontFamily: AppConstants.mainFont,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 20.w),

            // 配送员信息
            _buildShipperInfo(),

            SizedBox(height: 15.w),

            // 鼓励文字
            Text(
              S.current.evaluate_popup_header_shipper,
              style: TextStyle(
                color: const Color(0xFFA0ABC1),
                fontSize: 12.sp, // 24rpx / 2
                fontFamily: AppConstants.mainFont,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 17.5.w),

            // 打赏按钮
            _buildRewardButton(),
          ],
        ),
      ),
    );
  }

  /// 构建配送员信息
  Widget _buildShipperInfo() {
    return Column(
      children: [
        // 配送员头像
        Image.network(
          'https://acdn.mulazim.com/wechat_mini/img/evaluate/shipper.png',
          width: 55.w, // 110rpx / 2
          height: 55.w, // 110rpx / 2
          fit: BoxFit.cover,
          errorBuilder: (final context, final error, final stackTrace) {
            return Container(
              width: 55.w,
              height: 55.w,
              decoration: BoxDecoration(
                color: AppColors.baseGreenColor,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.person,
                size: 30.sp,
                color: Colors.white,
              ),
            );
          },
        ),

        SizedBox(height: 12.5.w),

        // 配送员名称
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: 29.w, // 58rpx / 2
            vertical: 6.w, // 12rpx / 2
          ),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F9FB),
            borderRadius: BorderRadius.circular(20.w), // 40rpx / 2
          ),
          child: Text(
            widget.shipper?.shipperName ?? 'rozijan',
            style: TextStyle(
              color: const Color(0xFFA0ABC1),
              fontSize: 17.sp, // 34rpx / 2
              fontFamily: AppConstants.mainFont,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建打赏按钮
  Widget _buildRewardButton() {
    return GestureDetector(
      onTap: _navigateToShipperDetail,
      child: Container(
        padding: EdgeInsets.all(10.w),
        decoration: BoxDecoration(
          border: Border.all(
            color: const Color(0xFF15C45B),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(7.w), // 14rpx / 2
          color: const Color(0xFFDCFFEA),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 打赏图标
            Image.network(
              'https://acdn.mulazim.com/wechat_mini/img/evaluate/tpis_pay.png',
              width: 25.w, // 50rpx / 2
              height: 25.w, // 50rpx / 2
              fit: BoxFit.contain,
              errorBuilder: (final context, final error, final stackTrace) {
                return Icon(
                  Icons.monetization_on,
                  size: 25.sp,
                  color: const Color(0xFF15C45B),
                );
              },
            ),

            SizedBox(width: 5.w),

            // 打赏文字
            Text(
              S.current.order_shipper_reward,
              style: TextStyle(
                color: const Color(0xFF15C45B),
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                fontFamily: AppConstants.mainFont,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
