import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/order_evaluate_controller.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/widgets/anonymous_checkbox_widget.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/widgets/rating_row_widget.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/widgets/comment_input_widget.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/widgets/food_list_widget.dart';
import 'package:user_app/generated/l10n.dart';

/// 美食评价组件
class FoodEvaluateWidget extends ConsumerWidget {
  const FoodEvaluateWidget({final Key? key}) : super(key: key);

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 监听订单详情数据
    final orderDetail = ref.watch(
      orderEvaluateControllerProvider.select(
        (final state) => state.evaluateData?.orderDetail,
      ),
    );

    // 监听餐厅数据
    final restaurant = ref.watch(
      orderEvaluateControllerProvider.select(
        (final state) => state.evaluateData?.restaurant,
      ),
    );

    if (orderDetail == null) {
      return const SizedBox.shrink();
    }

    final lang = ref.watch(languageProvider);
    final isRtl = lang == 'ug';

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.w),
      ),
      padding: EdgeInsets.all(10.w),
      child: Directionality(
        textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题和匿名选项
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.current.evaluate_foods,
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                AnonymousCheckboxWidget(
                  isAnonymous: orderDetail.isAnonymous,
                  onChanged: (final value) {
                    ref
                        .read(orderEvaluateControllerProvider.notifier)
                        .updateFoodAnonymous(value);
                  },
                ),
              ],
            ),

            SizedBox(height: 15.h),

            // 餐厅信息
            if (restaurant != null) ...[
              _buildRestaurantInfo(restaurant),
              SizedBox(height: 15.h),
            ],

            // 评分行
            RatingRowWidget(
              label: S.current.evaluate_in_general,
              rating: orderDetail.star,
              type: 'general',
              size: 30.w,
              gap: 8.w,
            ),
            SizedBox(height: 4.h),

            RatingRowWidget(
              label: S.current.evaluate_taste,
              rating: orderDetail.foodStar,
              type: 'taste',
              size: 20.w,
              gap: 18.w,
            ),
            SizedBox(height: 4.h),

            RatingRowWidget(
              label: S.current.evaluate_packing,
              rating: orderDetail.boxStar,
              type: 'packing',
              size: 20.w,
              gap: 18.w,
            ),
            SizedBox(height: 15.h),

            // 评论输入框
            CommentInputWidget(
              comment: orderDetail.comment,
              images: orderDetail.images,
              onChanged: (final value) {
                ref
                    .read(orderEvaluateControllerProvider.notifier)
                    .updateFoodComment(value);
              },
              onImagesChanged: (final images) {
                // 这个回调现在不需要了，图片状态由控制器直接管理
                // 保留是为了兼容组件接口
              },
            ),

            SizedBox(height: 15.h),

            // 美食列表
            if (orderDetail.foods.isNotEmpty) ...[
              const FoodListWidget(),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建餐厅信息
  Widget _buildRestaurantInfo(final restaurant) {
    return Row(
      children: [
        CircleAvatar(
          radius: 25.w,
          backgroundImage:
              restaurant.logo != null ? NetworkImage(restaurant.logo!) : null,
          backgroundColor: Colors.grey[300],
        ),
        SizedBox(width: 7.5.w),
        Text(
          restaurant.name ?? '',
          style: TextStyle(
            fontSize: 18.sp,
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
