import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/theme/icon_font.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/data/models/search/search_page_model.dart';
import 'package:user_app/data/models/search/search_restaurant_model.dart';
import 'package:user_app/data/models/search/search_word_panel.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';
import 'package:user_app/features/search/providers/search_by_word_provider.dart';
import 'package:user_app/features/search/providers/search_info_provider.dart';
import 'package:user_app/features/search/providers/search_restaurant_provider.dart';
import 'package:user_app/features/search/widgets/xm_start_rating.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/routes/paths.dart';

class SearchPage extends ConsumerStatefulWidget {
  const SearchPage({super.key});

  @override
  ConsumerState createState() => _SearchPageState();
}

class _SearchPageState extends ConsumerState<SearchPage> {

  AsyncValue<SearchWordData?>? searchWord;
  /// 搜索关键词控制器
  final TextEditingController _keyWord = TextEditingController();
  final FocusNode _keyWordNode = FocusNode(); // 创建 FocusNode
  List<String> keyWords = [];
  SearchRestaurantData? searchRestaurantData;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _keyWordNode.addListener(_onFocusChange);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      ref.read(searchPanelHeightProvider.notifier).state = 70;
      final storageService = ref.read(storageServiceProvider);
      keyWords = storageService.readStringList('keyWords') ?? [];
      print('keyWords.length ${keyWords.length}');
      ref.watch(searchPanelHeightProvider);
      // ref.watch(searchRestaurantProvider);
    });

  }


  void _onFocusChange() {
    if (_keyWordNode.hasFocus) {
      print("TextField 获取焦点");
      ref.watch(searchSelectProvider.notifier).state = '';
      searchRestaurantData = null;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final buildingId = ref.watch(homeNoticeProvider).value?.location?.id ?? 0; // 替换为动态获取的值
    final searchHomeData = ref.watch(searchFutureProvider(buildingId));
    // ref.read(searchWordProvider);
    return Scaffold(
        backgroundColor: AppColors.baseBackgroundColor,
        appBar: AppBar(
          centerTitle:true,
          backgroundColor: AppColors.baseGreenColor,
          foregroundColor: Colors.white,
          title: Text(
            S.current.app_name,
            style: TextStyle(
              fontSize: soBigSize,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        body: searchHomeData.when(
          data: (items) {
            return Column(
              children: [
                AnimatedContainer(
                    height: ref.watch(searchPanelHeightProvider).w,
                    padding: EdgeInsets.symmetric(vertical: 15.w),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          bottomRight:Radius.circular(15.w),
                          bottomLeft:Radius.circular(15.w),
                        ),
                        color: Colors.white
                    ),
                    duration: Duration(milliseconds: 300),  // 动画时长为1秒
                    curve: Curves.easeInOut,  // 动画曲线
                    child: Column(
                      children: [

                        Directionality(
                          textDirection: TextDirection.rtl,
                            child: Row(
                              children: [
                                SizedBox(width: 12.w,),
                                InkWell(
                                  onTap: () async {
                                    // _panelHeight = 70.w;
                                    _keyWordNode.unfocus(); // 使 TextField 失去焦点
                                    print(_keyWord.text);
                                    ref.read(searchPanelHeightProvider.notifier).state = 70;
                                    searchRestaurantData = await searchRestaurant(ref, buildingId: buildingId,keyWord: _keyWord.text);
                                    setState(() {});
                                    // await searchRestaurant(keyWord: _keyWord.text);
                                  },
                                  child: Container(
                                    height: 40.w, // 恢复固定高度
                                    alignment: Alignment.center,
                                    padding: EdgeInsets.symmetric(horizontal: 20.w), // 恢复原来的内边距
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(20.w),
                                        color: AppColors.baseGreenColor
                                    ),
                                    child: Text(S.current.search,style: TextStyle(color: Colors.white,fontSize: titleSize),),
                                  ),
                                ),
                                SizedBox(width: 12.w,),
                                Expanded(
                                  child: Container(
                                    // padding: EdgeInsets.symmetric(vertical: 4.w),
                                      height: 40.w, // 恢复固定高度
                                      decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(30.w),
                                          color: AppColors.searchBackColor
                                      ),
                                      child: Directionality(
                                          textDirection: ref.watch(languageProvider) == 'ug' ? TextDirection.rtl : TextDirection.ltr,
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              Container(
                                                  padding: ref.watch(languageProvider) == 'ug' ? EdgeInsets.only(right: 12.w,left: 6.w) : EdgeInsets.only(right: 8.w,left: 12.w),
                                                  child: Icon(IconFont.search,color: AppColors.textSecondColor,size: 18.sp,)
                                              ),
                                              Container(height: 16.w,width: 1.w,color: AppColors.homeSearchPlaceHolderColor,),
                                              SizedBox(width: 5.w,),
                                              Expanded(
                                                child: TextField(
                                                  focusNode: _keyWordNode,
                                                  controller: _keyWord,
                                                  textAlignVertical: TextAlignVertical.center, // 光标居中
                                                  maxLines: 1, // 保持单行
                                                  onChanged: (text) async { // 这里的 text 是输入框中的当前文本
                                                    // await getSearchByWord(keyWord: text);
                                                    print('text $text');
                                                    print(text.length);
                                                    ref.read(triggerSearchProvider.notifier).state = text;
                                                    ref.read(searchWordProvider.notifier).fetchData(key: text,buildingId: ref.watch(homeNoticeProvider).value?.location?.id ?? 0);
                                                    // ref.watch(searchPanelHeightProvider.notifier).state = 70;
                                                    // searchWord = ref.watch(searchWordFutureProvider(RequestParams(buildingId: null,keyWord: text)));
                                                  },
                                                  cursorColor: Colors.black,  // 设置光标颜色为红色
                                                  style: TextStyle(fontSize: 16.sp,color: Colors.black),
                                                  decoration: InputDecoration(
                                                    border: InputBorder.none, // 去掉底部横线
                                                    // contentPadding: EdgeInsets.symmetric(vertical: 10.w, horizontal: 8.w),  // 调整上下左右的间距
                                                    contentPadding: EdgeInsets.only(right: 6.w,left: 6.w,bottom: 14.w),  // 调整上下左右的间距
                                                    hintText: S.current.search_place_holder_text,
                                                    hintStyle: TextStyle(
                                                        color: AppColors.homeSearchPlaceHolderColor,
                                                        fontSize: 16.sp
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          )
                                      ),


                                  ),
                                ),
                                SizedBox(width: 12.w,),
                              ],
                            ),
                        ),


                        if(ref.watch(triggerSearchProvider).isNotEmpty)
                          ref.watch(searchWordProvider).when(
                          data: (data) {
                            print('data?.words ?? [] ${data?.words ?? []}');
                            // if((data?.words ?? []).length >8){
                            //   ref.read(searchPanelHeightProvider.notifier).state = 400;
                            // }else{
                            //   ref.read(searchPanelHeightProvider.notifier).state = 70 + ((data?.words ?? []).length * 40);
                            // }
                            // 显示获取到的数据
                            return Expanded(
                              child: Container(
                                color: Colors.white,
                                alignment: ref.watch(languageProvider) == 'ug' ? Alignment.centerRight : Alignment.centerLeft,
                                margin: EdgeInsets.only(top: 10.w),
                                child: SingleChildScrollView(
                                  child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: List.generate((data?.words ?? []).length, (index)=>
                                          SingleChildScrollView(
                                              scrollDirection: Axis.horizontal,  // 水平滚动
                                              child: InkWell(
                                                onTap: () async {
                                                  keyWords.add((data?.words ?? [])[index]);
                                                  final storageService = ref.read(storageServiceProvider);
                                                  await storageService.writeStringList("keyWords",keyWords);
                                                  _keyWordNode.unfocus(); // 使 TextField 失去焦点
                                                  ref.read(triggerSearchProvider.notifier).state = '';
                                                  ref.read(searchPanelHeightProvider.notifier).state = 70;
                                                  _keyWord.text = (data?.words ?? [])[index];
                                                  ref.read(searchSelectProvider.notifier).state = (data?.words ?? [])[index];

                                                  print('(data?.words ?? [])[index] ${(data?.words ?? [])[index]}');

                                                  searchRestaurantData = await searchRestaurant(ref, buildingId: buildingId,keyWord: (data?.words ?? [])[index]);
                                                  setState(() {});
                                                  // await ref.read(searchRestaurantProvider.notifier).getSearchData(buildingId: buildingId, keyWord:  (data?.words ?? [])[index]);
                                                },
                                                child: Container(
                                                  alignment: Alignment.center,
                                                  height: 40.w,
                                                  // color: Colors.yellow,
                                                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                                                  child: Text(data?.words?[index] ?? '',style: TextStyle(fontSize: mainSize,color: Colors.black)),
                                                ),
                                              )
                                          ))
                                  ),
                                ),
                              ),
                            );
                          },
                          loading: () {
                            return SizedBox();
                          },
                          error: (error, stackTrace) {
                            return Column(
                              children: [
                                Text('Error: $error'),
                                Text('stackTrace: $stackTrace')
                              ],
                            );
                          },
                        )
                      ],
                    )
                ),

                (searchRestaurantData != null && searchRestaurantData!.searchData != null && (searchRestaurantData!.searchData!.items ?? []).isNotEmpty)?
                  _searchedRestaurant(searchRestaurantData,buildingId):
                _originalRestaurant(items,buildingId)
                //
                // ref.watch(searchSelectProvider).isNotEmpty ?
                // ref.watch(searchRestaurantProvider).when(data: (data){
                //   print('data-->$data');
                //   // print('data.searchData.items.length-->${data!.searchData!.items!.length}');
                //   return _searchedRestaurant(data,buildingId);
                // }, error: (error, stackTrace) {
                //   // 数据加载失败，显示错误信息
                //   return Text('Search Page Error: $error');
                // }, loading: () {
                //   // 正在加载，显示加载指示器
                //   return SizedBox();
                // },):_originalRestaurant(items,buildingId)
              ],
            );
          },
          loading: () {
            // 正在加载，显示加载指示器
            return Center(child: LoadingWidget());
          },
          error: (error, stackTrace) {
            // 数据加载失败，显示错误信息
            return Text('Search Page Error: $error');
          },
        ),
    );
  }

  Widget _originalRestaurant(SearchPageData? searchPageData, int buildingId){
    List<SearchPageFoods> searchPageFoods = searchPageData?.foods ?? [];
    List<SearchPageRestaurants> searchPageRestaurants = searchPageData?.restaurants ?? [];
    List<SearchPageFoods> searchPageFoodsFirst = [];
    List<SearchPageFoods> searchPageFoodsSecond = [];
    if(searchPageFoods.length > 1){
      searchPageFoodsFirst = searchPageFoods.sublist(0, searchPageFoods.length ~/ 2);  // 从0到中间
      searchPageFoodsSecond = searchPageFoods.sublist(searchPageFoods.length ~/ 2);  // 从0到中间
    }
    return Expanded(
      child: SingleChildScrollView(
        child: Container(
          margin: EdgeInsets.all(10.w),
          padding: EdgeInsets.all(10.w),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15.w),
              color: Colors.white
          ),
          child: Column(
            children: [
              if(keyWords.isNotEmpty) _titleWidget(S.current.my_search,'assets/images/record.png',true),
              if(keyWords.isNotEmpty)SizedBox(height: 8.w,),
              if(keyWords.isNotEmpty)Container(
                width: MediaQuery.of(context).size.width - 40.w,
                child: Wrap(
                  spacing: 10.w,
                  runSpacing: 10.w,
                  children: List.generate(keyWords.length, (index)=>_searchedItem(index,buildingId)),
                ),
              ),
              if(keyWords.isNotEmpty)SizedBox(height: 25.w,),
              _titleWidget(S.current.hot_food,'assets/images/huo.png',false),
              SizedBox(height: 8.w,),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                    children: List.generate((searchPageFoodsFirst ?? []).length, (index)=>_borderContainerByFood(searchPageFoodsFirst[index],buildingId))
                ),
              ),

              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                    children: List.generate((searchPageFoodsSecond ?? []).length, (index)=>_borderContainerByFood(searchPageFoodsSecond[index],buildingId))
                ),
              ),
              SizedBox(height: 15.w,),
              _titleWidget(S.current.recommit_restaurant,'assets/images/recommend.png',false),
              SizedBox(height: 8.w,),
              Column(
                  children: List.generate(searchPageRestaurants.length, (index)=>_restaurantItem(searchPageRestaurants[index]))
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _searchedItem(int index, int buildingId){
    return InkWell(
      onTap: () async {
        _keyWord.text = keyWords[index];
        searchRestaurantData = await searchRestaurant(ref, buildingId: buildingId,keyWord: keyWords[index]);
        setState(() {});
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w,vertical: 5.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.w),
            color: AppColors.baseBackgroundColor,
        ),
        child: Text(keyWords[index],style: TextStyle(fontSize: mainSize,color: AppColors.textSecondaryColor),),
      ),
    );
  }

  Widget _titleWidget(String title,String imageAsset,bool deleteIcon){
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.w),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.only(bottom: 4.w),
                child: Image.asset(
                  imageAsset,
                  fit: BoxFit.fill,
                  width: deleteIcon ? 18.w : 22.w,
                  height: deleteIcon ? 18.w : 22.w,
                ),
              ),
              SizedBox(width: 10.w,),
              Text(title,style: TextStyle(fontSize: titleSize,color: Colors.black),)
            ],
          ),
          if(deleteIcon)InkWell(
            onTap: () async {
              final storageService = ref.read(storageServiceProvider);
              await storageService.writeStringList("keyWords",[]);
              keyWords.clear();
              setState(() {});
            },
            child: Icon(Icons.delete_outline,size: 24.sp,)
          )
        ],
      )
    );
  }

  Widget _borderContainerByFood(SearchPageFoods searchPageFoods,buildingId){
    return InkWell(
      onTap: (){
        context.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': searchPageFoods.restaurantId ?? 0,
            'buildingId': buildingId,
            'ids': [searchPageFoods.id ?? 0],
          },
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 4.w,horizontal: 12.w),
        margin: EdgeInsets.only(left: 10.w,bottom: 10.w),
        decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.baseGreenColor,
              width: 0.5.w,
            ),
            borderRadius: BorderRadius.circular(20.w),
            color: Colors.white
        ),
        child: Text('${searchPageFoods.name}',style: TextStyle(color: AppColors.hotTextColor,fontSize: mainSize),),
      ),
    );
  }


  ///餐厅元素
  Widget _restaurantItem(SearchPageRestaurants searchPageRestaurant){
    List<TakeTag> takeTag = searchPageRestaurant.takeTag ?? [];
    return InkWell(
      onTap: (){
        context.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': searchPageRestaurant.id ?? 0,
            'buildingId': ref.watch(homeNoticeProvider).value?.location?.id ?? 0,
            'ids': [],
          },
        );
      },
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.w),
            color: Colors.white
        ),
        margin: EdgeInsets.only(bottom: 25.w),
        child: Row(
          children: [
            Container(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10.w),
                  child: PrefectImage(
                    imageUrl: searchPageRestaurant.logo ?? '',
                    width: 90.w,
                    height: 90.w,
                    fit: BoxFit.fill,
                  ),
                )
            ),
            SizedBox(width: 10.w,),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(searchPageRestaurant.name ?? '',style: TextStyle(fontSize: mainSize),),
                  SizedBox(height: 5.h,),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text('${searchPageRestaurant.starAvg ?? ''}',style: TextStyle(fontSize: secondSize,color: AppColors.baseOrangeColor,fontWeight: FontWeight.bold),),
                          Icon(Icons.star,color: AppColors.baseOrangeColor,size: mainSize,),
                          SizedBox(width: 10.w,),
                          Text('${S.current.month_order_count}:${searchPageRestaurant.monthOrderCount ?? ''}',style: TextStyle(fontSize: secondSize,color: AppColors.textSecondColor),),
                        ],
                      ),
                      Text(searchPageRestaurant.distance ?? '',style: TextStyle(fontSize: secondSize,color: AppColors.textSecondColor),),
                    ],
                  ),
                  SizedBox(height: 10.h,),
                  Wrap(
                    spacing: 5.w,
                    runSpacing: 4.w,
                    children: List.generate(takeTag.length, (tagIndex)=>_borderContainerForTag(takeTag[tagIndex])),
                  )
                ],
              ),
            ),
            // SizedBox(width: 10.w,)
          ],
        ),
      ),
    );
  }

  Widget _borderContainerForTag(takeTag){
    return Container(
      padding: EdgeInsets.symmetric(vertical: 2.w,horizontal: 5.w),
      decoration: BoxDecoration(
          border: Border.all(
            color: FormatUtil.parseColor(takeTag?.color ?? '00FF00'),
            width: 0.5.w,
          ),
          borderRadius: BorderRadius.circular(4.w),
          color: Colors.white
      ),
      child: Text(takeTag?.title ?? '',style: TextStyle(color: FormatUtil.parseColor(takeTag?.color ?? '00FF00'),fontSize: littleSize),),
    );
  }


  Widget _searchedRestaurant(SearchRestaurantData? data,int buildingId){

    print('(data?.searchData?.items ?? []).length ${(data?.searchData?.items ?? []).length}');

    return Expanded(
      child: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.all(10.w),
          child: Column(
              children: List.generate((data?.searchData?.items ?? []).length, (index)=>_searchedRestaurantItem(data!.searchData!.items![index],buildingId))
          ),
        ),
      ),
    );
  }

  ///餐厅元素
  Widget _searchedRestaurantItem(SearchedRestaurantItems searchedRestaurantItems,int buildingId){
    return InkWell(
      onTap: (){
        context.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': searchedRestaurantItems.id ?? 0,
            'buildingId': buildingId,
            'ids': [],
          },
        );
      },
      child: Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.w),
              color: Colors.white
          ),
          margin: EdgeInsets.only(bottom: 10.w),
          padding: EdgeInsets.all(10.w),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10.w),
                        child: PrefectImage(
                          imageUrl: searchedRestaurantItems.logo ?? '',
                          width: 90.w,
                          height: 90.w,
                          fit: BoxFit.fitWidth,
                        ),
                      )
                  ),
                  SizedBox(width: 10.w,),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(searchedRestaurantItems.name ?? '',style: TextStyle(fontSize: titleSize),),
                        SizedBox(height: 5.h,),
                        Text('${S.current.month_order_count}:${searchedRestaurantItems.monthOrderCount ?? ''}',style: TextStyle(fontSize: secondSize,color: AppColors.textSecondColor),),
                        SizedBox(height: 5.h,),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 10.w,vertical: 5.w),
                          height: 30.w,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20.w),
                              color: AppColors.starBgColor
                          ),
                          child: XMStartRating(rating: searchedRestaurantItems.starAvg ?? 0.0,size: titleSize,spacing: 1,totalRating: 5,selectColor: AppColors.baseOrangeColor,),
                        )
                      ],
                    ),
                  ),
                  // SizedBox(width: 10.w,)
                ],
              ),
              if((searchedRestaurantItems.foods ?? []).isNotEmpty)
                ...List.generate((searchedRestaurantItems.foods ?? []).length, (foodIndex)=>_searchedRestaurantFoodItem(searchedRestaurantItems.foods![foodIndex],searchedRestaurantItems.id ?? 0,buildingId))
            ],
          )
      ),
    );
  }


  Widget _searchedRestaurantFoodItem(SearchedRestaurantFoods food,int restaurantId,int builingId){
    return InkWell(
      onTap: (){
        context.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': restaurantId,
            'buildingId': builingId,
            'ids': [food.id ?? 0],
          },
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10.w),
        child: Row(
          children: [
            Container(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10.w),
                  child: PrefectImage(
                    imageUrl: food.image ?? '',
                    width: 120.w,
                    height: 90.w,
                    fit: BoxFit.fill,
                  ),
                )
            ),
            SizedBox(width: 10.w,),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(food.name ?? '',style: TextStyle(fontSize: mainSize),),
                  SizedBox(height: 5.h,),
                  Text('${S.current.month_order_count}:${food.monthOrderCount ?? ''}',style: TextStyle(fontSize: secondSize,color: AppColors.textSecondColor),),
                  SizedBox(height: 5.h,),
                  Text('${food.price ?? '0'}￥'),
                ],
              ),
            ),
            // SizedBox(width: 10.w,)
          ],
        ),
      ),
    );
  }



}
