// 自定义页面切换动画
import 'package:flutter/material.dart';

class CustomPageRoute extends PageRouteBuilder {
  final Widget page;

  CustomPageRoute(this.page)
      : super(
    // 页面构建器，返回目标页面
    pageBuilder: (context, animation, secondaryAnimation) => page,
    // 设置动画过渡效果
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      // 这里使用的是滑动动画，从右边滑入
      const begin = Offset(0.0,1.0); // 从右侧进入
      const end = Offset.zero;
      const curve = Curves.ease;

      var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
      var offsetAnimation = animation.drive(tween);

      return SlideTransition(position: offsetAnimation, child: child);
    },
  );
}