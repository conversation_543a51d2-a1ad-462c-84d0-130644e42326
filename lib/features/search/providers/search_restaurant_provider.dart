
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/search/search_restaurant_model.dart';
import 'package:user_app/data/repositories/search/search_repository.dart';


///获取查找页面数据
Future<SearchRestaurantData?> searchRestaurant(WidgetRef ref, {required int buildingId,required String keyWord}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'category_id': 1,
    'page': 1,
    'limit': 25,
    'key': keyWord,
    'building_id': buildingId
  };
  final searchRepository = SearchRepository(apiClient: ref.read(apiClientProvider));
  final searchRestaurant = await searchRepository.searchRestaurant(param);
  return searchRestaurant.data;
}

///searchFutureProvider，支持可选的 buildingId 参数


// class SearchRestaurantProvider extends StateNotifier<AsyncValue<SearchRestaurantData?>> {
//   SearchRestaurantProvider(this.ref) : super(const AsyncValue.loading());
//   final Ref ref; // 添加一个 ref 字段
//   Future<void> getSearchData({required int buildingId, required String keyWord}) async {
//     try {
//       // 进行异步请求前先检查是否 mounted
//       if (!mounted) return;
//       final response = await searchRestaurant(ref, buildingId: buildingId,keyWord: keyWord);
//       print('rs --- > $response');
//       state = AsyncValue.data(response);
//     } catch (error, stackTrace) {
//       state = AsyncValue.error(error, stackTrace);
//     }
//   }
// }


// final searchRestaurantProvider = StateNotifierProvider<
//     SearchRestaurantProvider, AsyncValue<SearchRestaurantData?>>(
//       (ref) => SearchRestaurantProvider(ref),
// );


final searchSelectProvider = StateProvider<String>((ref) => '');