import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/search/search_page_model.dart';
import 'package:user_app/data/repositories/search/search_repository.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';

///获取查找页面数据
Future<SearchPageData?> getSearchInfo(Ref ref, {required int buildingId}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'building_id': buildingId,
  };
  final searchRepository = SearchRepository(apiClient: ref.read(apiClientProvider));
  final searchInfo = await searchRepository.getSearchInfo(param);
  return searchInfo?.data ?? SearchPageData();
}

///searchFutureProvider，支持可选的 buildingId 参数
final searchFutureProvider = FutureProvider.autoDispose.family<SearchPageData?, int?>((ref, buildingId) async {
  if (buildingId == null || buildingId <= 0) {
    // 如果 buildingId 无效，尝试从缓存或当前定位获取
    final currentLocation = ref.read(homeNoticeProvider).value?.location;
    if (currentLocation != null && currentLocation.id != null && currentLocation.id! > 0) {
      return await getSearchInfo(ref, buildingId: currentLocation.id!);
    }
    // 如果无法获取位置，返回空数据
    return SearchPageData();
  }
  return await getSearchInfo(ref, buildingId: buildingId);
});
