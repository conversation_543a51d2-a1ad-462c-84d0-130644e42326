import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/search/search_word_panel.dart';
import 'package:user_app/data/repositories/search/search_repository.dart';


///按关键词查找
Future<SearchWordData?> getSearchByWord(Ref ref,{int? buildingId,required String key}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'page': 1,
    'limit': 5,
    'key': key,
    'building_id': buildingId,
  };
  final searchRepository = SearchRepository(apiClient: ref.read(apiClientProvider));
  final searchByWord = await searchRepository.getSearchByWord(param);
  return searchByWord?.data ?? SearchWordData();
}

///searchFutureProvider，支持可选的 buildingId 参数
class SearchWordNotifier extends StateNotifier<AsyncValue<SearchWordData?>> {
  SearchWordNotifier(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段

  Future<void> fetchData({int? buildingId,required String key}) async {
    try {
      SearchWordData? searchWordData = await getSearchByWord(ref, buildingId: buildingId, key:key);
      state = AsyncValue.data(searchWordData);
      if(searchWordData != null && searchWordData.words != null){
        if(searchWordData.words!.length >8){
          ref.read(searchPanelHeightProvider.notifier).state = 400;
        }else{
          ref.read(searchPanelHeightProvider.notifier).state = 70 + (searchWordData.words!.length * 40);
        }
      }
    } catch (error,stackTrace) {
      state = AsyncValue.error(error,stackTrace);
    }
  }
}

// 使用 StateNotifierProvider 来创建 Provider
final searchWordProvider = StateNotifierProvider<SearchWordNotifier, AsyncValue<SearchWordData?>>(
      (ref) => SearchWordNotifier(ref),
);

final triggerSearchProvider = StateProvider.autoDispose<String>((ref) => '');

final searchPanelHeightProvider = StateProvider.autoDispose<double>((ref) => 70);






