import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/generated/l10n.dart';

class PrivacyConfirmDialog {
  ///动画对话框
  void Function()? callBack;
  PrivacyConfirmDialog(
      BuildContext context, double screenWidth,String lang, this.callBack) {
    showGeneralDialog(
      transitionDuration: const Duration(milliseconds: 100),
      transitionBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation, Widget child) {
        return ScaleTransition(scale: animation, child: child);
      },
      context: context,
      pageBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return ConfirmDialog(
          screenWidth: screenWidth,
          lang: lang,
          callBack: callBack,
        );
      },
    );
  }
}

class ConfirmDialog extends Dialog {
  final String id;
  final double screenWidth;
  final String lang;
  void Function()? callBack;

  // 构造函数赋值
  ConfirmDialog(
      {Key? key,
      this.screenWidth = 0.0,
      this.lang = 'ug',
      this.callBack,
      this.id = '0'})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 调用方法
    // _showTimer(context);
    return Material(
        type: MaterialType.transparency,
        child: Center(
          child: Container(
            width: screenWidth - 80.w,
            // height:Adapt.setPx(140),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.w),
              color: Colors.white,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[

                Directionality(
                  textDirection: lang == 'ug' ? TextDirection.rtl : TextDirection.ltr,
                  child:Padding(
                    padding:
                    EdgeInsets.symmetric(vertical: 25.w, horizontal: 20.w),
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        S.current.privacy_content,
                        style: TextStyle(fontSize: mainSize, height: 1.6,fontFamily: "UkijTuzTom",),
                        textAlign: TextAlign.justify,
                      ),
                    ),
                  ),
                ),
                Directionality(
                  textDirection: TextDirection.rtl,
                  child: SizedBox(
                    width: double.infinity,
                    child: Row(
                      children: [
                        Expanded(
                          child: InkWell(
                              child: Container(
                                  height: 50.w,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    border: Border(
                                      left: BorderSide(
                                          width: 0.4, color: Colors.grey),
                                      top: BorderSide(
                                          width: 0.4, color: Colors.grey),
                                    ),
                                  ),
                                  // height: double.infinity,
                                  child: Text(
                                    S.current.see_again,
                                    style: TextStyle(
                                        fontSize: mainSize,
                                        fontFamily: "UkijTuzTom",
                                        color: AppColors.baseGreenColor),
                                    textAlign: TextAlign.center,
                                  )),
                              onTap: () {
                                Navigator.pop(context);
                                // callBack!.call();
                              }),
                        ),
                        Expanded(
                          child: InkWell(
                            child: Container(
                              height: 50.w,
                              decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(
                                      width: 0.4, color: Colors.grey),
                                ),
                              ),
                              child: Center(
                                  child: Text(
                                S.current.exit,
                                style: TextStyle(
                                    fontSize: mainSize,
                                    fontFamily: "UkijTuzTom",
                                    color: Colors.black),
                                textAlign: TextAlign.center,
                              )),
                            ),
                            onTap: () {
                              SystemNavigator.pop();
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  // onTap: (){
                  //   Navigator.pop(context);
                  // },
                ),
              ],
            ),
          ),
        ));
  }
}
