import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/generated/l10n.dart';

class AnimateConfirmDialog {
  ///动画对话框
  void Function()? callBack;
  AnimateConfirmDialog(
      BuildContext context, double screenWidth, String content, this.callBack) {
    showGeneralDialog(
      transitionDuration: const Duration(milliseconds: 100),
      transitionBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation, Widget child) {
        return ScaleTransition(scale: animation, child: child);
      },
      context: context,
      pageBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return ConfirmDialog(
          content: content,
          screenWidth: screenWidth,
          callBack: callBack,
        );
      },
    );
  }
}

class ConfirmDialog extends Dialog {
  final String content;
  final String id;
  final double screenWidth;
  void Function()? callBack;

  // 构造函数赋值
  ConfirmDialog(
      {Key? key,
      this.content = "",
      this.screenWidth = 0.0,
      this.callBack,
      this.id = '0'})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 调用方法
    // _showTimer(context);
    return Material(
        type: MaterialType.transparency,
        child: Center(
          child: Container(
            width: screenWidth - 80.w,
            // height:Adapt.setPx(140),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.w),
              color: Colors.white,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Padding(
                  padding:
                      EdgeInsets.symmetric(vertical: 25.w, horizontal: 20.w),
                  child: Container(
                    alignment: Alignment.center,
                    child: Text(
                      content,
                      style: TextStyle(fontSize: titleSize, height: 1.5,fontFamily: "UkijTuzTom",),
                      textAlign: TextAlign.justify,
                    ),
                  ),
                ),
                Directionality(
                  textDirection: TextDirection.rtl,
                  child: SizedBox(
                    width: double.infinity,
                    child: Row(
                      children: [
                        Expanded(
                          child: InkWell(
                              child: Container(
                                  height: 50.w,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    border: Border(
                                      left: BorderSide(
                                          width: 0.4, color: Colors.grey),
                                      top: BorderSide(
                                          width: 0.4, color: Colors.grey),
                                    ),
                                  ),
                                  // height: double.infinity,
                                  child: Text(
                                    S.current.yes,
                                    style: TextStyle(
                                        fontSize: mainSize,
                                        fontFamily: "UkijTuzTom",
                                        color: Colors.black),
                                    textAlign: TextAlign.center,
                                  )),
                              onTap: () {
                                Navigator.pop(context);
                                callBack!.call();
                              }),
                        ),
                        Expanded(
                          child: InkWell(
                            child: Container(
                              height: 50.w,
                              decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(
                                      width: 0.4, color: Colors.grey),
                                ),
                              ),
                              child: Center(
                                  child: Text(
                                S.current.no,
                                style: TextStyle(
                                    fontSize: mainSize,
                                    fontFamily: "UkijTuzTom",
                                    color: AppColors.baseGreenColor),
                                textAlign: TextAlign.center,
                              )),
                            ),
                            onTap: () {
                              Navigator.pop(context);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  // onTap: (){
                  //   Navigator.pop(context);
                  // },
                ),
              ],
            ),
          ),
        ));
  }
}
