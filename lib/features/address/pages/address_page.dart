import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/storage/storage_service.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/icon_font.dart';
import 'package:user_app/core/widgets/dialogs/login_confirm_dialog.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/address/pages/select_address_page.dart';
import 'package:user_app/features/address/providers/address_city_provider.dart';
import 'package:user_app/features/address/providers/address_info_provider.dart';
import 'package:user_app/features/address/providers/address_provider.dart';
import 'package:user_app/features/address/providers/address_search_provider.dart';
import 'package:user_app/data/models/order/history_address_model.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart';
import 'package:user_app/routes/paths.dart';

import 'add_address_page.dart';

class AddressPage extends ConsumerStatefulWidget {
  AddressPage({
    super.key,
    required this.lat,
    required this.lng,
    required this.areaName,
  });
  String lat;
  String lng;
  String areaName;
  @override
  ConsumerState createState() => _AddressPageState();
}

class _AddressPageState extends ConsumerState<AddressPage> {
  final TextEditingController _keyWord = TextEditingController();

  bool hideNearItem = true;
  bool hideMyItem = true;
  bool isLtr = false;
  bool isRefresh = false;

  HistoryAddressData? currentAddress;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((final timeStamp) async {
      isLtr = ref.watch(languageProvider) == 'ug' ? false : true;
      ref
          .read(addressInfoProvider.notifier)
          .fetchAddressData(lng: widget.lng, lat: widget.lat);
      ref.read(historyAddressListProvider.notifier).fetchHistoryAddressData();
    });
  }

  @override
  Widget build(final BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.baseBackgroundColor,
      appBar: AppBar(
        title: Text(
          S.current.select_address,
          style: TextStyle(fontSize: soBigSize),
        ),
        centerTitle: true,
      ),
      body: ref.watch(addressInfoProvider).when(
        data: (final data) {
          currentAddress = data?.items?.first;
          return _page();
        },
        error: (final error, final stackTrace) {
          return Directionality(
            textDirection: TextDirection.ltr,
            child: Container(
              padding: EdgeInsets.only(top: 100.w),
              child: Column(
                children: [
                  Text(
                    'error --> $error',
                    style: TextStyle(color: Colors.red),
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  Text(
                    'stackTrace --> $stackTrace',
                    style: TextStyle(color: Colors.blue),
                  ),
                ],
              ),
            ),
          );
        },
        loading: () {
          return Center(
            child: LoadingWidget(),
          );
        },
      ),
      bottomNavigationBar: InkWell(
        onTap: () async {
          final isLogin = ref.read(isLoggedInProvider);
          if (isLogin) {
            final StorageService _storageService = StorageService();
            int buildingId =
                int.tryParse((_storageService.read('buildingId') ?? '0')) ?? 0;
            int areaId =
                int.tryParse((_storageService.read('areaId') ?? '0')) ?? 0;
            String areaName = '${_storageService.read('areaName')}';
            String buildingName = '${_storageService.read('buildingName')}';
            String buildingNameZh = '${_storageService.read('buildingNameZh')}';

            Navigator.of(context)
                .push(MaterialPageRoute(
              builder: (final context) => AddAddressPage(
                  buildingName: buildingName,
                  buildingNameZh: buildingNameZh,
                  buildingId: buildingId,
                  address: '',
                  name: '',
                  tel: '',
                  addressId: 0,
                  areaName: areaName),
            ))
                .then((final value) async {
              await ref
                  .read(historyAddressListProvider.notifier)
                  .fetchHistoryAddressData();
              setState(() {});
            });
          } else {
            LoginConfirmDialog.show(context,
                onConfirm: () => context.push(AppPaths.courtesyPage));
          }
        },
        child: Container(
          alignment: Alignment.center,
          height: 60.w,
          color: Colors.white,
          child: Text(
            S.current.add_address,
            style:
                TextStyle(color: AppColors.baseGreenColor, fontSize: titleSize),
          ),
        ),
      ),
    );
  }

  Widget _page() {
    final StorageService _storageService = StorageService();
    return Column(
      children: [
        Container(
          color: Colors.white,
          child: Row(
            children: [
              InkWell(
                onTap: () {
                  ref.read(selectedAddressProvider.notifier).state.clear();
                  Navigator.of(context)
                      .push(
                        MaterialPageRoute(
                          builder: (final context) => SelectAddressPage(),
                        ),
                      )
                      .then((final value) => Navigator.of(context).pop(value));
                },
                child: Row(
                  children: [
                    SizedBox(
                      width: 8.w,
                    ),
                    Icon(
                      Icons.keyboard_arrow_down,
                      size: soBigSize,
                      color: AppColors.baseGreenColor,
                    ),
                    Container(
                      alignment: Alignment.center,
                      width: 80.w,
                      color: Colors.white,
                      child: Text(
                        widget.areaName.isEmpty
                            ? S.current.select_area
                            : widget.areaName,
                        style: TextStyle(
                          fontSize: titleSize,
                          color: AppColors.baseGreenColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  constraints: BoxConstraints(
                    maxHeight: 50.w,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.w),
                    color: Colors.red,
                  ),
                  margin: EdgeInsets.symmetric(vertical: 10.w),
                  child: TextField(
                    // focusNode: _keyWordNode,
                    controller: _keyWord,
                    style: TextStyle(fontSize: 15.sp),
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.all(10.0),
                      hoverColor: AppColors.baseBackgroundColor,
                      fillColor: AppColors.baseBackgroundColor,
                      filled: true,
                      hintText: S.current.input_address,
                      hintStyle: TextStyle(
                        color: AppColors.textSecondColor,
                        fontSize: mainSize,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: AppColors.baseBackgroundColor,
                        ),
                      ),
                      prefixIcon: Icon(
                        IconFont.search,
                        size: 20.w,
                        color: AppColors.textSecondColor,
                      ),
                      focusedBorder: OutlineInputBorder(
                        //选中时外边框颜色
                        borderSide: BorderSide(
                          color: AppColors.baseBackgroundColor,
                        ),
                      ),
                    ),
                    maxLines: 1, //不限制行数
                    // autofocus: true,
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.text,
                    onChanged: (final val) async {
                      print('val----->$val');
                      // await getListByInput(keyWord: val);
                      // ref.watch(locationInputProvider.notifier).state = val;

                      ref
                          .watch(addressSearchProvider.notifier)
                          .fetchAddressData(keyWord: val);
                    },
                    // controller: Provider.of<LoginViewmodel>(context).getUser,
                  ),
                ),
              ),
              SizedBox(
                width: 10.w,
              ),
            ],
          ),
        ),
        SizedBox(
          height: 1.w,
        ),
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  // height: 200.w,
                  color: Colors.white,
                  margin: EdgeInsets.only(bottom: 10.w),
                  child: Column(
                    children: [
                      Container(
                        padding:
                            EdgeInsets.only(right: 10.w, left: 10.w, top: 15.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Text(
                              S.current.location_picker_current_location,
                              style: TextStyle(
                                color: AppColors.textSecondColor,
                                fontSize: titleSize,
                              ),
                            ),
                            SizedBox(),
                          ],
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          Map<String, dynamic> addressParam = {
                            'building_id': int.tryParse(
                                    (currentAddress?.buildingId ?? 0).toString()) ??
                                0,
                            'area_id': int.tryParse(
                                    (currentAddress?.areaId ?? 0).toString()) ??
                                0,
                          };
                          Navigator.of(context).pop(addressParam); // 返回并传递参数
                        },
                        child: Container(
                          padding: EdgeInsets.only(
                            right: 10.w,
                            left: 10.w,
                            top: 10.w,
                            bottom: 15.w,
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(
                                    width: MediaQuery.of(context).size.width -
                                        50.w,
                                    child: Text(
                                      '${currentAddress?.buildingName?.isEmpty ?? true ? 'ئورۇن بەلگىلەش مەغلۇپ بولدى' : currentAddress?.buildingName}',
                                      style: TextStyle(
                                        fontSize: titleSize,
                                        color: Colors.black,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  Icon(
                                    Icons.filter_center_focus,
                                    color: AppColors.baseGreenColor,
                                    size: 26.w,
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 15.w,
                              ),
                              if (ref.watch(languageProvider) == 'ug')
                                Directionality(
                                  textDirection: TextDirection.ltr,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        '当前位置：',
                                        style: TextStyle(
                                          fontSize: titleSize,
                                          color: Colors.black,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          '${currentAddress?.buildingNameZh?.isEmpty ?? true ? '无法获取您的地理位置' : currentAddress?.buildingNameZh}',
                                          style: TextStyle(
                                            fontSize: titleSize,
                                            color: Colors.black,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                if ((ref.watch(addressSearchProvider).value?.items ?? [])
                        .isNotEmpty &&
                    _keyWord.text != '')
                  Container(
                    color: Colors.white,
                    padding: EdgeInsets.all(10.w),
                    margin: EdgeInsets.only(bottom: 10.w),
                    child: Column(
                      children: [
                        Container(
                          padding: EdgeInsets.all(10.w),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                S.current.search_res,
                                style: TextStyle(
                                  color: AppColors.textSecondColor,
                                  fontSize: titleSize,
                                ),
                              ),
                              SizedBox(),
                            ],
                          ),
                        ),
                        Column(
                          children: List.generate(
                            (ref.watch(addressSearchProvider).value?.items ??
                                    [])
                                .length,
                            (final index) => _addressItemSearch(ref
                                .watch(addressSearchProvider)
                                .value!
                                .items![index]),
                          ),
                        ),
                      ],
                    ),
                  ),
                ref.watch(historyAddressListProvider).when(
                  data: (final data) {
                    List<HistoryAddressData> historyAddress = data ?? [];
                    return ((historyAddress).isNotEmpty)
                        ? Container(
                            color: Colors.white,
                            padding: EdgeInsets.all(10.w),
                            margin: EdgeInsets.only(bottom: 10.w),
                            child: Column(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(10.w),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        S.current.my_address,
                                        style: TextStyle(
                                          color: AppColors.textSecondColor,
                                          fontSize: titleSize,
                                        ),
                                      ),
                                      SizedBox(),
                                    ],
                                  ),
                                ),
                                Column(
                                  children: [
                                    if ((historyAddress ?? []).length < 4)
                                      ...List.generate(
                                        (historyAddress ?? []).length,
                                        (final index) => _addressItem(
                                          historyAddress![index],
                                          false,
                                        ),
                                      ),
                                    if ((historyAddress ?? []).length > 3)
                                      ...List.generate(
                                        hideMyItem
                                            ? 3
                                            : (historyAddress ?? []).length,
                                        (final index) => _addressItem(
                                          historyAddress![index],
                                          false,
                                        ),
                                      ),
                                    if ((historyAddress ?? []).length > 3)
                                      InkWell(
                                        onTap: () {
                                          setState(() {
                                            hideMyItem = !hideMyItem;
                                          });
                                        },
                                        child: Container(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 12.w),
                                          child: Row(
                                            children: [
                                              Icon(
                                                hideMyItem
                                                    ? Icons.keyboard_arrow_down
                                                    : Icons.keyboard_arrow_up,
                                                color:
                                                    AppColors.textSecondColor,
                                              ),
                                              SizedBox(
                                                width: 10.w,
                                              ),
                                              Text(
                                                hideMyItem
                                                    ? S.current.see_all
                                                    : S.current.see_little,
                                                style: TextStyle(
                                                    color: AppColors
                                                        .textSecondColor,
                                                    fontSize: titleSize),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ],
                            ),
                          )
                        : SizedBox();
                  },
                  error: (final error, final stackTrace) {
                    return SizedBox();
                  },
                  loading: () {
                    return SizedBox();
                  },
                ),
                Container(
                  color: Colors.white,
                  padding: EdgeInsets.all(10.w),
                  child: Column(
                    children: [
                      Container(
                        padding: EdgeInsets.all(10.w),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.location_on,
                                  color: AppColors.baseGreenColor,
                                  size: 22.w,
                                ),
                                Text(
                                  S.current.recent_address,
                                  style: TextStyle(
                                    color: AppColors.textSecondColor,
                                    fontSize: titleSize,
                                  ),
                                ),
                              ],
                            ),
                            if (ref.watch(languageProvider) == 'ug')
                              InkWell(
                                onTap: () {
                                  setState(() {
                                    isLtr = !isLtr;
                                  });
                                },
                                child: Container(
                                  width: 70.w,
                                  height: 32.w,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(30.w),
                                    color: AppColors.grayColor,
                                  ),
                                  child: Stack(
                                    children: [
                                      if (!isLtr)
                                        Positioned(
                                          left: 0,
                                          child: Container(
                                            margin: EdgeInsets.all(2.8.w),
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 10.w,
                                                vertical: 2.w),
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(30.w),
                                              color: isLtr
                                                  ? AppColors.baseGreenColor
                                                  : AppColors.grayColor,
                                            ),
                                            child: Text(
                                              '汉',
                                              style: TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                        ),
                                      Positioned(
                                        right: 0,
                                        child: Container(
                                          margin: EdgeInsets.all(2.8.w),
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 10.w, vertical: 2.w),
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(30.w),
                                            color: isLtr
                                                ? AppColors.grayColor
                                                : AppColors.baseGreenColor,
                                          ),
                                          child: Text(
                                            'ئۇ',
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontWeight: FontWeight.bold),
                                          ),
                                        ),
                                      ),
                                      if (isLtr)
                                        Positioned(
                                          left: 0,
                                          child: Container(
                                            margin: EdgeInsets.all(2.8.w),
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 10.w,
                                                vertical: 2.w),
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(30.w),
                                              color: isLtr
                                                  ? AppColors.baseGreenColor
                                                  : AppColors.grayColor,
                                            ),
                                            child: Text(
                                              '汉',
                                              style: TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                      Column(
                        children: [
                          if ((ref.watch(addressInfoProvider).value?.items ??
                                      [])
                                  .length <
                              4)
                            ...List.generate(
                              (ref.watch(addressInfoProvider).value?.items ??
                                      [])
                                  .length,
                              (final index) => _addressItem(
                                  ref
                                      .watch(addressInfoProvider)
                                      .value!
                                      .items![index],
                                  true),
                            ),
                          if ((ref.watch(addressInfoProvider).value?.items ??
                                      [])
                                  .length >
                              3)
                            ...List.generate(
                              hideNearItem
                                  ? 3
                                  : (ref
                                              .watch(addressInfoProvider)
                                              .value
                                              ?.items ??
                                          [])
                                      .length,
                              (final index) => _addressItem(
                                  ref
                                      .watch(addressInfoProvider)
                                      .value!
                                      .items![index],
                                  true),
                            ),
                          if ((ref.watch(addressInfoProvider).value?.items ??
                                      [])
                                  .length >
                              3)
                            InkWell(
                              onTap: () {
                                setState(() {
                                  hideNearItem = !hideNearItem;
                                });
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(vertical: 12.w),
                                child: Directionality(
                                  textDirection: isLtr
                                      ? TextDirection.ltr
                                      : TextDirection.rtl,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Icon(
                                        hideNearItem
                                            ? Icons.keyboard_arrow_down
                                            : Icons.keyboard_arrow_up,
                                        color: AppColors.textSecondColor,
                                      ),
                                      SizedBox(
                                        width: 10.w,
                                      ),
                                      Text(
                                        hideNearItem
                                            ? (isLtr
                                                ? '查看所有地址'
                                                : 'ھەممە ئادرېسنى كۆرۈش')
                                            : (isLtr ? '收回' : 'يىغىش'),
                                        style: TextStyle(
                                            color: AppColors.textSecondColor,
                                            fontSize: titleSize),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        SizedBox(
          height: 6.w,
        ),
      ],
    );
  }

  Widget _addressItemSearch(final HistoryAddressData addressItem) {
    return InkWell(
      onTap: () {
        Map<String, dynamic> addressParam = {
          'building_id': addressItem.buildingId ?? addressItem.id,
          'area_id': addressItem.areaId,
        };
        Navigator.of(context).pop(addressParam); // 返回并传递参数
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 15.w),
        // margin: EdgeInsets.only(bottom: 10.w),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade100, width: 1.0),
          ),
          // color: Colors.red
        ),
        child: Directionality(
          textDirection: isLtr ? TextDirection.ltr : TextDirection.rtl,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              SizedBox(
                width: MediaQuery.of(context).size.width - 30.w,
                child: Text(
                  '${(addressItem.buildingName)}',
                  style: TextStyle(color: Colors.black, fontSize: titleSize),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _addressItem(final HistoryAddressData addressItem, final bool isNear) {
    return InkWell(
      onTap: () {
        Map<String, dynamic> addressParam = {
          'building_id': addressItem.buildingId ?? addressItem.id,
          'area_id': addressItem.areaId,
        };
        Navigator.of(context).pop(addressParam); // 返回并传递参数
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 15.w),
        // margin: EdgeInsets.only(bottom: 10.w),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade100, width: 1.0),
          ),
          // color: Colors.red
        ),
        child: isNear
            ? Directionality(
                textDirection: isLtr ? TextDirection.ltr : TextDirection.rtl,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width - 30.w,
                      child: Text(
                        '${(isLtr ? addressItem.buildingNameZh : addressItem.buildingName)}',
                        style:
                            TextStyle(color: Colors.black, fontSize: titleSize),
                      ),
                    ),
                  ],
                ),
              )
            : Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width - 30.w,
                        child: Text(
                          '${addressItem.buildingName} ${addressItem.address}',
                          style: TextStyle(
                              color: Colors.black, fontSize: titleSize),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Text(
                        '${addressItem.name}',
                        style: TextStyle(
                            color: AppColors.textSecondaryColor,
                            fontSize: titleSize),
                      ),
                      SizedBox(
                        width: 18.w,
                      ),
                      Text(
                        '${addressItem.tel}',
                        style: TextStyle(
                            color: AppColors.textSecondaryColor,
                            fontSize: titleSize),
                      ),
                    ],
                  ),
                ],
              ),
      ),
    );
  }
}
