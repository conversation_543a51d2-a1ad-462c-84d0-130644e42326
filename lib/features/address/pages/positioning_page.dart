import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/icon_font.dart';
import 'package:user_app/features/address/pages/select_address_page.dart';
import 'package:user_app/features/address/providers/address_city_provider.dart';
import 'package:user_app/features/address/providers/address_info_provider.dart';
import 'package:user_app/features/address/providers/address_search_provider.dart';
import 'package:user_app/data/models/order/history_address_model.dart';
import 'package:user_app/generated/l10n.dart';

class PositioningPage extends ConsumerStatefulWidget {
  const PositioningPage(
      {super.key,
      required this.initialLatitude,
      required this.initialLongitude,
      required this.name,
      required this.address,
      required this.areaName});
  final num initialLatitude;
  final num initialLongitude;
  final String name;
  final String address;
  final String areaName;

  @override
  ConsumerState createState() => _PositioningPageState();
}

class _PositioningPageState extends ConsumerState<PositioningPage> {
  final TextEditingController _keyWord = TextEditingController();
  FocusNode _keyWordNode = FocusNode();
  final GlobalKey _mapKey = GlobalKey();
  bool hasFocus = false;
  Map<String, dynamic> creationParams = {};
  MethodChannel? _channel;
  bool _isClosing = false;

  // 控制地图显示的状态
  bool _showMap = false;

  @override
  void initState() {
    super.initState();

    // 监听焦点变化
    _keyWordNode.addListener(() {
      if (mounted) {
        setState(() {
          hasFocus = _keyWordNode.hasFocus;
        });
      }
    });

    // 初次加载页面时，直接传递初始定位数据
    creationParams = {
      'latitude': widget.initialLatitude,
      'longitude': widget.initialLongitude,
    };

    // 延迟初始化地图，避免加载过快导致的问题
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      if (!mounted) return;

      try {
        // 先加载地址数据
        ref.read(addressInfoProvider.notifier).fetchAddressData(
            lng: widget.initialLongitude.toString(),
            lat: widget.initialLatitude.toString());

        // 再初始化通道和显示地图
        _initMapChannel();
      } catch (e) {
        print("Error initializing: $e");
      }
    });
  }

  // 初始化地图通道
  void _initMapChannel() {
    if (!mounted) return;

    _channel = const MethodChannel('position_channel');

    // 监听地图中心位置变化
    _channel?.setMethodCallHandler((call) async {
      if (!mounted) return;

      if (call.method.toString() == 'updateCenterPosition') {
        try {
          var positionEncodeVal = jsonEncode(call.arguments);
          Map<String, dynamic> position = jsonDecode(positionEncodeVal);
          _onCenterPositionUpdated(position);
        } catch (e) {
          print("Error handling position update: $e");
        }
      } else {
        throw MissingPluginException('未实现的方法: ${call.method}');
      }
    });

    // 显示地图
    setState(() {
      _showMap = true;
    });
  }

  void _onCenterPositionUpdated(Map<String, dynamic> position) {
    if (!mounted) return;

    double latitude = position['latitude'];
    double longitude = position['longitude'];
    ref
        .read(addressInfoProvider.notifier)
        .fetchAddressData(lng: longitude.toString(), lat: latitude.toString());
    log('地图中心位置更新：纬度: $latitude, 经度: $longitude');
  }

  // 清理资源
  void _cleanupResources() {
    try {
      // 清除方法通道处理程序
      _channel?.setMethodCallHandler(null);
      _channel = null;
    } catch (e) {
      print("Error cleaning up resources: $e");
    }
  }

  @override
  void dispose() {
    _cleanupResources();
    _keyWordNode.dispose();
    _keyWord.dispose();
    super.dispose();
  }

  // 安全关闭页面，并返回结果
  Future<void> _safePopWithResult(Map<String, dynamic> result) async {
    if (_isClosing) return;
    _isClosing = true;

    // 取消焦点
    FocusScope.of(context).unfocus();

    // 先隐藏地图，断开连接
    setState(() {
      _showMap = false;
    });

    // 清理资源
    _cleanupResources();

    // 等待框架完成布局
    await Future.delayed(const Duration(milliseconds: 50));

    if (!mounted) return;

    // 使用 Future.microtask 确保在页面已经完全更新后再导航
    Future.microtask(() {
      if (mounted && !Navigator.of(context).canPop()) return;
      if (mounted) Navigator.of(context).pop(result);
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      // 处理系统返回按钮
      onWillPop: () async {
        await _safePopWithResult({});
        return false;
      },
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: AppColors.baseBackgroundColor,
        appBar: AppBar(
          title: Text(
            S.current.choice_address,
            style: TextStyle(fontSize: soBigSize),
          ),
          centerTitle: true,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => _safePopWithResult({}),
          ),
        ),
        body: Column(
          children: [
            Container(
              color: Colors.white,
              child: Row(
                children: [
                  InkWell(
                    onTap: () {
                      // 先隐藏地图，防止内存泄漏
                      setState(() {
                        _showMap = false;
                      });

                      // 清理资源
                      _cleanupResources();

                      // 等待一帧
                      Future.microtask(() {
                        if (!mounted) return;

                        ref
                            .read(selectedAddressProvider.notifier)
                            .state
                            .clear();
                        Navigator.of(context)
                            .push(MaterialPageRoute(
                          builder: (context) => SelectAddressPage(),
                        ))
                            .then((value) {
                          print('value ---> $value');

                          // 重新初始化地图
                          if (mounted) {
                            _initMapChannel();

                            if (value != null) {
                              _safePopWithResult(value);
                            }
                          }
                        });
                      });
                    },
                    child: Row(
                      children: [
                        SizedBox(
                          width: 8.w,
                        ),
                        Icon(
                          Icons.keyboard_arrow_down,
                          size: soBigSize,
                          color: Colors.black,
                        ),
                        Container(
                            alignment: Alignment.center,
                            width: 100.w,
                            color: Colors.white,
                            child: Text(
                              widget.areaName,
                              style: TextStyle(
                                  fontSize: titleSize,
                                  color: AppColors.baseGreenColor),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ))
                      ],
                    ),
                  ),
                  Expanded(
                    child: Container(
                      constraints: BoxConstraints(
                        maxHeight: 50.w,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.w),
                        color: Colors.red,
                      ),
                      margin: EdgeInsets.symmetric(vertical: 10.w),
                      child: TextField(
                        focusNode: _keyWordNode,
                        controller: _keyWord,
                        style: TextStyle(fontSize: 15.sp),
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.all(10.0),
                          hoverColor: AppColors.baseBackgroundColor,
                          fillColor: AppColors.baseBackgroundColor,
                          filled: true,
                          hintText: S.current.input_address,
                          hintStyle: TextStyle(
                              color: AppColors.textSecondColor,
                              fontSize: mainSize),
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: AppColors.baseBackgroundColor,
                            ),
                          ),
                          prefixIcon: Icon(
                            IconFont.search,
                            size: 26.w,
                            color: AppColors.textSecondColor,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: AppColors.baseBackgroundColor,
                            ),
                          ),
                        ),
                        maxLines: 1,
                        textInputAction: TextInputAction.next,
                        keyboardType: TextInputType.text,
                        onChanged: (val) async {
                          ref
                              .watch(addressSearchProvider.notifier)
                              .fetchAddressData(keyWord: val);
                        },
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 10.w,
                  )
                ],
              ),
            ),
            SizedBox(
              height: 1.w,
            ),
            if (!hasFocus) Expanded(child: _mapPart()),
            _labelPart(),
          ],
        ),
      ),
    );
  }

  ///标签部分
  Widget _labelPart() {
    List<HistoryAddressData> addressDataList =
        ref.watch(addressSearchProvider).value?.items ?? [];
    if (addressDataList.isEmpty) {
      addressDataList = ref.watch(addressInfoProvider).value?.items ?? [];
    }
    return Expanded(
      child: Container(
        // padding: EdgeInsets.symmetric(horizontal: 10.w),
        width: MediaQuery.of(context).size.width,
        color: Colors.white,
        child: Column(mainAxisSize: MainAxisSize.max, children: [
          Expanded(
              child: SingleChildScrollView(
            child: Column(
              children: List.generate(
                addressDataList.length,
                (index) => _addressItem(addressDataList[index]),
              ),
            ),
          ))
        ]),
      ),
    );
  }

  ///规划图元素
  Widget _addressItem(HistoryAddressData item) {
    return InkWell(
      onTap: () {
        Map<String, dynamic> positionParam = {
          'building_id': item.id,
          'building_name': item.buildingName,
          'building_name_zh': item.buildingNameZh,
        };
        _safePopWithResult(positionParam);
      },
      child: Container(
        decoration: BoxDecoration(
          border:
              Border(bottom: BorderSide(width: 1.w, color: Color(0xffe5e5e5))),
        ),
        padding: EdgeInsets.all(10.w),
        margin: EdgeInsets.symmetric(vertical: 1.w),
        child: Column(
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width,
              child: Text(
                item.buildingName ?? '',
                style:
                    TextStyle(fontSize: titleSize, fontWeight: FontWeight.bold),
              ),
            ),
            SizedBox(
              height: 2.w,
            ),
            SizedBox(
              width: MediaQuery.of(context).size.width,
              child: Text(
                '${item.cityName} ${item.areaName} ${item.streetName}',
                style: TextStyle(
                    fontSize: secondSize, color: AppColors.textSecondColor),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///地图部分
  Widget _mapPart() {
    if (!_showMap) {
      return Container(
        key: _mapKey,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.w),
          color: AppColors.baseBackgroundColor,
        ),
        height: MediaQuery.of(context).size.height - 500.w,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    const String viewType = '<position-view>';
    return Container(
        key: _mapKey,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.w),
          color: AppColors.baseBackgroundColor,
        ),
        height: MediaQuery.of(context).size.height - 500.w,
        child: Stack(
          children: [
            Center(
              child: Platform.isIOS
                  ? UiKitView(
                    viewType: "plugin/shipper-location",
                  )
                  : AndroidView(
                      viewType: viewType,
                      creationParams: creationParams,
                      creationParamsCodec: const StandardMessageCodec(),
                    ),
            ),
            _centerMarker(),
          ],
        ));
  }

  /// 固定在地图中心的 Marker 图标
  Widget _centerMarker() {
    return Center(
      child: Container(
        margin: EdgeInsets.only(bottom: 50.w),
        child: Icon(
          Icons.location_on,
          size: 40,
          color: Colors.red,
        ),
      ),
    );
  }
}
