
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/utils/location_util.dart';
import 'package:user_app/data/models/address/location_list_model.dart';
import 'package:user_app/data/repositories/address/address_repository.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

///获取地址列表按定位数据
Future<LocationListData?> getListByLocation(Ref ref,
    {required String lat, required String lng}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'limit': 20,
    'lat': lat,
    'lng': lng,
  };
  if (lat.isEmpty || lng.isEmpty || lat == '0' || lng == '0' || lat == '0.0' || lng == '0.0') {
    final location = LocationUtil.getLatestLocationData();
    if (location != null && location['latitude'] != null && location['longitude'] != null) {
      param['lat'] = location['latitude'] ?? '0';
      param['lng'] = location['longitude'] ?? '0';
    } else {
      return null;
    }
  }
  // 获取首页数据
  final addressRepository = AddressRepository(apiClient: ref.read(apiClientProvider));
  final addressInfo = await addressRepository.getListByLocation(param);
  return addressInfo?.data;
}

///地址列表按定位数据提供者类
class AddressInfoNotifier extends StateNotifier<AsyncValue<LocationListData?>> {
  AddressInfoNotifier(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> fetchAddressData({required String lat, required String lng}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final response = await getListByLocation(ref, lng: lng, lat: lat);
      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      if (!mounted) return;
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final addressInfoProvider = StateNotifierProvider.autoDispose<
    AddressInfoNotifier, AsyncValue<LocationListData?>>(
  (ref) => AddressInfoNotifier(ref),
);

final locationInputProvider = StateProvider.autoDispose<String>((ref) => '');
