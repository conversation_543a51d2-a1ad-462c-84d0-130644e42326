import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/address/street_list_model.dart';
import 'package:user_app/data/repositories/address/address_repository.dart';
import 'package:user_app/core/utils/formatter.dart';

///获取对应街头提供者
class AddressStreetNotifier
    extends StateNotifier<AsyncValue<List<StreetListData>?>> {
  AddressStreetNotifier(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> fetchAddressData({required int areaId}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final addressRepository =
          AddressRepository(apiClient: ref.read(apiClientProvider));
      final streetList = await addressRepository.getStreetList(areaId);
      state = AsyncValue.data(streetList.data);
      if ((streetList.data ?? []).isNotEmpty) {
        int langNum = ref.read(languageProvider) == 'zh' ? 2 : 1;

        var formatter = Formatter(langNum);
        ref.watch(newStreetProvider.notifier).state =
            formatter.formatList(streetList.data ?? []);
      } else {
        ref.read(newStreetProvider.notifier).state = [];
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

///提供者实列
final addressStreetProvider = StateNotifierProvider<AddressStreetNotifier,
    AsyncValue<List<StreetListData>?>>(
  (ref) => AddressStreetNotifier(ref),
);

final newStreetProvider =
    StateProvider.autoDispose<List<Map<String, dynamic>>>((ref) => []);
