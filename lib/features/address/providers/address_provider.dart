import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/order/current_address_model.dart';
import 'package:user_app/data/models/order/history_address_model.dart';
import 'package:user_app/features/order/providers/order_provider.dart';
import 'package:user_app/data/repositories/address/address_repository.dart';
import 'package:user_app/main.dart';

part 'address_provider.g.dart';


///获取我的地址列表
Future<List<HistoryAddressData>?> getHistoryAddress(Ref ref) async {

  // 秒杀活动数据
  final addressProvider = AddressRepository(apiClient: ref.read(apiClientProvider));
  final addressInfo = await addressProvider.getHistoryAddress();
  return addressInfo.data;
}




///地址列表按定位数据提供者类
class HistoryAddressListProvider extends StateNotifier<AsyncValue<List<HistoryAddressData>?>> {
  HistoryAddressListProvider(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> fetchHistoryAddressData() async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;

      final isLogin = ref.watch(isLoggedInProvider);
      if(!isLogin) {
        state = AsyncValue.data(null);
        return;
      };
      final response = await getHistoryAddress(ref);
      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final historyAddressListProvider = StateNotifierProvider<
    HistoryAddressListProvider, AsyncValue<List<HistoryAddressData>?>>(
      (ref) => HistoryAddressListProvider(ref),
);


/// 地址仓库
final addressRepositoryProvider = Provider<AddressRepository>(
  ((final ref) {
    return AddressRepository(apiClient: ref.read(apiClientProvider));
  }),
);

/// 地址提供者
final addressProvider = FutureProvider.autoDispose.family<List<HistoryAddressData>?, Map<String, dynamic>?>(
        (final ref, final params) async {
  // 添加缓存机制，利用keepAlive保持数据
  // ref.keepAlive();

  final addressRepository = ref.watch(addressRepositoryProvider);
  final address = await addressRepository.getHistoryAddress(params: params);
  return Future<List<HistoryAddressData>?>.value(address.data);
});

/// 选中地址提供者
@riverpod
class SetAddress extends _$SetAddress {
  @override
  HistoryAddressData? build() {
    return null;
  }

  /// 设置地址
  void setAddress(final HistoryAddressData address) {
    state = address;
    // 更新当前地址
    ref.read(currentAddressProvider.notifier).setAddress(CurrentSelectAddress(
          id: address.id,
          name: address.name,
          tel: address.tel,
          address: address.address,
          buildingName: address.buildingName,
          buildingId: address.buildingId,
        ),);
    ref.invalidate(submitOrderInfoProvider);
  }

  /// 获取当前选中得地址、如果没有则返回null
  HistoryAddressData? get address => state;
}
