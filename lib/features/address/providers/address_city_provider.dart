import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/address/city_area_model.dart';
import 'package:user_app/data/repositories/address/address_repository.dart';
import 'package:user_app/generated/l10n.dart';

///获取各城市
class AddressCityNotifier
    extends StateNotifier<AsyncValue<List<CityAreaData>?>> {
  AddressCityNotifier(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> fetchAddressData() async {
    final addressRepository =
        AddressRepository(apiClient: ref.read(apiClientProvider));
    final cityInfo = await addressRepository.getCityArea();
    List<CityArea> marketingArea = [];
    for (int i = 0; i < (cityInfo.data ?? []).length; i++) {
      for (int j = 0; j < (cityInfo.data?[i].area ?? []).length; j++) {
        if (cityInfo.data?[i].area?[j] != null &&
            cityInfo.data?[i].area![j].state == 1) {
          marketingArea.add(cityInfo.data![i].area![j]);
        }
      }
    }
    CityAreaData areaData =CityAreaData(area: marketingArea, name: S.current.marketing_area);
    // 插入数据到最前面
    (cityInfo.data ?? []).insert(0, areaData);
    state = AsyncValue.data(cityInfo.data);
  }
}

///提供者实列
final addressCityProvider =
    StateNotifierProvider<AddressCityNotifier, AsyncValue<List<CityAreaData>?>>(
  (final ref) => AddressCityNotifier(ref),
);

final selectedAddressProvider = StateProvider<List<String>>((final ref) => []);
