// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'address_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$setAddressHash() => r'07a34a630def5e674d0b2c41de64578273376700';

/// 选中地址提供者
///
/// Copied from [SetAddress].
@ProviderFor(SetAddress)
final setAddressProvider =
    AutoDisposeNotifierProvider<SetAddress, HistoryAddressData?>.internal(
  SetAddress.new,
  name: r'setAddressProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$setAddressHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SetAddress = AutoDisposeNotifier<HistoryAddressData?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
