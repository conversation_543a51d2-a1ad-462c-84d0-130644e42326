import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/address/add_address_response_model.dart';
import 'package:user_app/data/repositories/address/address_repository.dart';
import 'package:user_app/features/address/providers/address_provider.dart';

///获取地址列表按定位数据
Future<AddAddressData?> addAddress(Ref ref,
    {required String name,
    required String tel,
    required String address,
    required String buildingId}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'name': name,
    'tel': tel,
    'address': address,
    'building_id': buildingId,
  };
  // 获取首页数据
  final addressRepository =
      AddressRepository(apiClient: ref.read(apiClientProvider));
  final addressInfo = await addressRepository.addAddress(param);
  return addressInfo?.data;
}

///地址列表按定位数据提供者类
class AddAddressNotifier extends StateNotifier<AsyncValue<AddAddressData?>> {
  AddAddressNotifier(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> addAddressData(
      {required String name,
      required String tel,
      required String address,
      required String buildingId}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final response = await addAddress(ref,
          name: name, tel: tel, address: address, buildingId: buildingId);
      state = AsyncValue.data(response);
      ref.invalidate(addressProvider);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final addAddressProvider =
    StateNotifierProvider<AddAddressNotifier, AsyncValue<AddAddressData?>>(
  (ref) => AddAddressNotifier(ref),
);
