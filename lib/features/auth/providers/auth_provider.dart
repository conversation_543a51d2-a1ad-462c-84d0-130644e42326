import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';

import 'package:user_app/data/repositories/auth/auth_repository.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/data/repositories/user/user_repository.dart';
import 'package:user_app/data/models/auth/auth_model.dart';
import 'package:user_app/main.dart';
import 'package:user_app/generated/l10n.dart';

part 'auth_provider.g.dart';

/// 认证服务提供者
///
/// 负责处理认证相关的业务逻辑，如登录、登出、验证码发送等
@Riverpod(keepAlive: true)
class Auth extends _$Auth {
  @override
  bool build() {
    // 默认未登录状态
    return false;
  }

  /// 验证手机号
  bool validatePhoneNumber(final String phone) {
    return phone.isNotEmpty && phone.length == 11;
  }

  /// 验证验证码
  bool validateVerifyCode(final String code) {
    return code.isNotEmpty && code.length == 6;
  }

  /// 发送短信验证码
  Future<ApiResult<void>> sendSmsCode(final String phone) async {
    if (!validatePhoneNumber(phone)) {
      BotToast.showText(text: S.current.login_tel_yz);
      return ApiResult.error(msg: S.current.login_tel_yz);
    }

    try {
      final repository = ref.read(authRepositoryProvider);
      final result = await repository.sendSmsCode(phone);

      if (result.success) {
        BotToast.showText(text: result.msg ?? S.current.sms_code_send_success);
      } else {
        BotToast.showText(text: result.msg ?? S.current.sms_code_send_failed);
      }

      return result;
    } catch (e) {
      log("Error sending SMS code: $e");
      BotToast.showText(text: S.current.sms_code_send_retry);
      return ApiResult.error(msg: S.current.sms_code_send_retry);
    }
  }

  /// 验证码登录
  Future<ApiResult<AuthData>> smsLogin(
    final String phone,
    final String code,
  ) async {
    if (!validatePhoneNumber(phone)) {
      BotToast.showText(text: S.current.login_tel_yz);
      return ApiResult.error(msg: S.current.login_tel_yz);
    }

    if (!validateVerifyCode(code)) {
      BotToast.showText(text: S.current.login_code_yz);
      return ApiResult.error(msg: S.current.login_code_yz);
    }

    try {
      final authRepository = ref.read(authRepositoryProvider);
      final userRepository = ref.read(userRepositoryProvider);


      // String lang = ref.read(languageProvider);
      // /// 获取本地存储库实例
      // final localStorageRepository = globalContainer.read(localStorageRepositoryProvider);
      // final regId = localStorageRepository.getRegId() ?? '';
      // authRepository.registerJPush(regId, lang);


      final result = await authRepository.smsLogin(phone, code);

      if (result.success && result.data != null) {
        final token = result.data!.tokens?.accessToken ?? "";
        final userInfo = result.data!.user;

        if (userInfo != null) {
          // 使用UserRepository保存用户信息和令牌
          await userRepository.saveUserInfo(userInfo, token);
        }

        // 更新全局登录状态
        state = true;
        globalContainer.read(isLoggedInProvider.notifier).state = true;

        BotToast.showText(text: result.msg ?? S.current.login_success);


        String lang = ref.read(languageProvider);
        /// 获取本地存储库实例
        final localStorageRepository = globalContainer.read(localStorageRepositoryProvider);
        final regId = localStorageRepository.getRegId() ?? '';
        authRepository.registerJPush(regId, lang);
      } else {
        BotToast.showText(text: result.msg ?? S.current.login_failed);
      }

      return result;
    } catch (e) {
      log("Error during login: $e");
      BotToast.showText(text: S.current.login_retry);
      return ApiResult.error(msg: S.current.login_retry);
    }
  }

  /// 登出
  Future<void> logout() async {
    try {
      // 使用UserRepository清除用户信息和令牌
      final userRepository = ref.read(userRepositoryProvider);
      await userRepository.clearUserInfo();
      // 更新全局登录状态
      state = false;
      globalContainer.read(isLoggedInProvider.notifier).state = false;


      final authRepository = ref.read(authRepositoryProvider);
      final localStorageRepository = globalContainer.read(localStorageRepositoryProvider);
      final regId = localStorageRepository.getRegId() ?? '';
      authRepository.unRegisterJPush(regId);
    } catch (e) {
      log("Error during logout: $e");
      BotToast.showText(text: S.current.logout_failed);
    }
  }

  /// 检查登录状态
  Future<UserInfo?> checkLoginStatus() async {
    try {
      if (kDebugMode) print("正在检查登录状态...");

      final userRepository = ref.read(userRepositoryProvider);
      final token = userRepository.getToken();

      if (kDebugMode) print("获取到的token: $token");

      if (token != null && token.isNotEmpty) {
        // 从UserRepository获取用户信息
        final userInfo = userRepository.getUserInfo();

        if (kDebugMode) {
          print("用户ID: ${userInfo?.id}, 用户名: ${userInfo?.name}");
        }

        // 更新全局登录状态
        state = true;
        globalContainer.read(isLoggedInProvider.notifier).state = true;

        if (kDebugMode) {
          print("用户已登录，token: $token, isLoggedIn: $state");
        }

        return userInfo;
      } else {
        if (kDebugMode) print("用户未登录");

        // 更新全局登录状态
        state = false;
        globalContainer.read(isLoggedInProvider.notifier).state = false;

        return null;
      }
    } catch (e) {
      log("Error checking login status: $e");
      return null;
    }
  }
}
