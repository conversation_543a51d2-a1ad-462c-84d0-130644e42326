// 登录页面状态类
import 'package:user_app/data/models/auth/auth_model.dart';

/// 登录页面状态
class LoginState {
  /// 是否已登录
  final bool isLoggedIn;

  /// 是否正在加载中
  final bool isLoading;

  /// 是否正在发送验证码
  final bool isSending;

  /// 验证码倒计时
  final int countDown;

  /// 是否已经发送过验证码
  final bool hasSentCode;

  /// 认证令牌
  final String token;

  /// 用户信息
  final UserInfo? user;

  /// 错误信息
  final String? errorMessage;

  bool? isAgreed;

  /// 创建登录页面状态
  LoginState({
    this.isLoggedIn = false,
    this.isLoading = false,
    this.isSending = false,
    this.countDown = 60,
    this.hasSentCode = false,
    this.token = '',
    this.user,
    this.errorMessage,
    this.isAgreed = false,
  });

  /// 复制当前状态并更新部分字段
  LoginState copyWith({
    bool? isLoggedIn,
    bool? isLoading,
    bool? isSending,
    int? countDown,
    bool? hasSentCode,
    String? token,
    UserInfo? user,
    String? errorMessage,
    bool? isAgreed,
  }) {
    return LoginState(
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
      isLoading: isLoading ?? this.isLoading,
      isSending: isSending ?? this.isSending,
      countDown: countDown ?? this.countDown,
      hasSentCode: hasSentCode ?? this.hasSentCode,
      token: token ?? this.token,
      user: user ?? this.user,
      isAgreed: isAgreed ?? this.isAgreed,
      errorMessage: errorMessage,
    );
  }
}
