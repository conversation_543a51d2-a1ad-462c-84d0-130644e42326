import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/auth/pages/login/login_controller_provider.dart';

/// 用户信息组件
class UserInfoWidget extends ConsumerWidget {
  final String? fromPath;

  const UserInfoWidget({
    super.key,
    this.fromPath,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 只监听用户信息
    final user =
        ref.watch(loginControllerProvider.select((state) => state.user));
    final loginController = ref.read(loginControllerProvider.notifier);

    if (user == null) return const SizedBox.shrink();

    return Column(
      children: [
        SizedBox(height: 30.h),

        // 用户头像
        CircleAvatar(
          radius: 50.r,
          backgroundImage: user.avatar != null
              ? NetworkImage(user.avatar!)
              : const AssetImage("assets/images/basic/default_avatar.png")
                  as ImageProvider,
        ),

        SizedBox(height: 20.h),

        // 用户名
        Text(
          user.name ?? "用户${user.id}",
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
          ),
        ),

        SizedBox(height: 10.h),

        // 手机号
        if (user.mobile != null)
          Text(
            user.mobile!,
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey,
            ),
          ),

        SizedBox(height: 40.h),

        // 继续按钮（如果有来源页面）
        if (fromPath != null && fromPath!.isNotEmpty)
          InkWell(
            onTap: () => context.go(fromPath!),
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 30.w),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                "继续",
                style: TextStyle(fontSize: 18.sp, color: Colors.white),
              ),
            ),
          ),

        SizedBox(height: 20.h),

        // 退出登录按钮
        InkWell(
          onTap: () => loginController.logout(),
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 30.w),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.red.shade400,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              "退出登录",
              style: TextStyle(fontSize: 18.sp, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }
}
