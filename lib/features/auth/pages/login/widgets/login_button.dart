import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 登录按钮组件
class LoginButton extends StatelessWidget {
  final bool isLoading;
  final VoidCallback onLogin;

  const LoginButton({
    super.key,
    required this.isLoading,
    required this.onLogin,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: isLoading ? null : onLogin,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 30.w),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: isLoading ? Colors.grey : AppColors.primary,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          S.current.please_login,
          style: TextStyle(fontSize: 18.sp, color: Colors.white),
        ),
      ),
    );
  }
}
