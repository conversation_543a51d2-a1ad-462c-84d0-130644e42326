import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/auth/pages/login/login_controller_provider.dart';
import 'package:user_app/generated/l10n.dart';

class LoginForm extends ConsumerStatefulWidget {
  final TextEditingController phoneController;
  final TextEditingController codeController;
  final VoidCallback onSendCode;

  const LoginForm({
    super.key,
    required this.phoneController,
    required this.codeController,
    required this.onSendCode,
  });

  @override
  ConsumerState<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends ConsumerState<LoginForm> {
  // Cache for TextField widgets
  late final Widget _phoneTextField;
  late final Widget _codeTextField;

  // 验证码输入框的焦点节点
  late final FocusNode _codeFocusNode;

  @override
  void initState() {
    super.initState();
    // Initialize cached widgets
    _phoneTextField = _buildTextField(
      controller: widget.phoneController,
      hintText: S.current.phone_number_placeholder,
      prefixIcon: const Icon(Icons.phone_android),
      maxLength: 11,
    );

    _codeTextField = _buildTextField(
      controller: widget.codeController,
      hintText: S.current.verification_code,
      prefixIcon: const Icon(Icons.lock_outline),
      maxLength: 6,
    );

    _codeFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _codeFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return Column(
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 手机号标签
            Container(
              padding: EdgeInsets.symmetric(vertical: 10.w, horizontal: 5.h),
              child: Text(
                S.current.phone_number_label,
                style: TextStyle(fontSize: 18.sp, color: Colors.black54),
              ),
            ),

            // 手机号输入框
            _phoneTextField,

            SizedBox(height: 10.h),

            // 验证码标签
            Container(
              padding: EdgeInsets.symmetric(vertical: 10.w, horizontal: 5.h),
              child: Text(
                S.current.verification_code,
                style: TextStyle(fontSize: 16.sp, color: Colors.black54),
              ),
            ),

            // 验证码输入框和发送按钮
            _buildVerificationCodeField(),
          ],
        ),
      ],
    );
  }

  Widget _buildTextField({
    required final TextEditingController controller,
    required final String hintText,
    final Widget? prefixIcon,
    final int maxLength = 50,
    final FocusNode? focusNode,
  }) {
    return TextField(
      controller: controller,
      focusNode: focusNode,
      style: TextStyle(fontSize: 16.sp),
      textDirection: TextDirection.ltr,
      cursorColor: AppColors.primary,
      maxLength: maxLength,
      decoration: InputDecoration(
        prefixIcon: prefixIcon,
        contentPadding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 20.h),
        hintText: hintText,
        counterText: "",
        hintStyle: TextStyle(fontSize: 14.sp, color: Colors.grey),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: AppColors.primary),
        ),
      ),
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
      ],
    );
  }

  Widget _buildVerificationCodeField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.w),
      ),
      child: Row(
        children: [
          // 使用缓存的验证码输入框
          Expanded(
            child: _buildTextField(
              controller: widget.codeController,
              hintText: S.current.verification_code_placeholder,
              prefixIcon: const Icon(Icons.lock_outline),
              maxLength: 6,
              focusNode: _codeFocusNode,
            ),
          ),
          SizedBox(width: 15.w),
          // 使用Consumer包装按钮，只有状态变化时才会重建按钮部分
          Consumer(
            builder: (final context, final ref, final child) {
              // 局部监听状态
              final isCurrentlySending = ref.watch(
                  loginControllerProvider.select((final s) => s.isSending));
              final currentCountDown = ref.watch(
                  loginControllerProvider.select((final s) => s.countDown));
              final hasSentCode = ref.watch(
                  loginControllerProvider.select((final s) => s.hasSentCode));

              return InkWell(
                onTap: isCurrentlySending
                    ? null
                    : () {
                        widget.onSendCode();
                        // 发送验证码后，将焦点移动到验证码输入框
                        Future.delayed(const Duration(milliseconds: 100), () {
                          _codeFocusNode.requestFocus();
                        });
                      },
                child: Container(
                  padding:
                      EdgeInsets.symmetric(vertical: 14.h, horizontal: 45.w),
                  decoration: BoxDecoration(
                    color: isCurrentlySending ? Colors.grey : AppColors.primary,
                    // borderRadius: BorderRadius.circular(44.r),
                    borderRadius: BorderRadius.circular(8),

                  ),
                  child: Text(
                    isCurrentlySending
                        ? "${currentCountDown}s"
                        : (hasSentCode ? S.current.resend : S.current.send),
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16.sp,
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
