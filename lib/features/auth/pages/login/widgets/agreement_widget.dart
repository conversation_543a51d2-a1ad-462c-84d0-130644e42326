/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Email: <EMAIL>
 * @Date: 2023-10-17 12:32:19
 * @Descripttion: 
 */

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:roundcheckbox/roundcheckbox.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/app_router.dart';
import 'package:user_app/routes/paths.dart';

/// 协议封装
/// checkSubmit: 选择出发
/// agreement: 是否选择状态
class AgreementWidget extends StatefulWidget {
  final Function checkSubmit;
  final bool agreement;
  final String lang;
  const AgreementWidget({Key? key, required this.checkSubmit, required this.agreement,required this.lang}) : super(key: key);

  @override
  State<AgreementWidget> createState() => AagreementWidgetState();
}

class AagreementWidgetState extends State<AgreementWidget>{
  bool _agreement = false;
  @override
  void initState() {
    super.initState();
    _agreement = widget.agreement;
  }

  @override
  Widget build(BuildContext context) {
    return agreementCheck();
  }

  Widget agreementCheck(){
    return Wrap(
      alignment: WrapAlignment.start,
      runAlignment: WrapAlignment.center,
      children: [
        InkWell(
          onTap: (){
            setState(() {_agreement = !_agreement;});
            widget.checkSubmit(_agreement);
          },
          child: Container(
            height: 40.w,
            width: 40.w,
            alignment: Alignment.center,
            child: RoundCheckBox(
              size: 15,
              checkedWidget: const Icon(Icons.check, color: Colors.white, size: 10),
              uncheckedWidget: Icon(Icons.check, color: AppColors.textSecondaryColor, size: 10),
              checkedColor: Theme.of(context).primaryColor,
              border: Border.all(color: _agreement ? AppColors.baseGreenColor : AppColors.textSecondaryColor, width: 1.3.w),
              isChecked: _agreement,
              onTap: (selected) {
                setState(() {_agreement = !_agreement;});
                widget.checkSubmit(selected);
              }
            ),
          ),
        ),
        GestureDetector(
          onTap: (){
            setState(() {_agreement = !_agreement;});
            widget.checkSubmit(_agreement);
          },
          child: Container(
            padding: EdgeInsets.only(top: 10.w),
            child: RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: TextStyle(fontFamily: Theme.of(context).textTheme.bodySmall?.fontFamily),
                children: [
                  widget.lang == 'zh'  ? TextSpan(text: S.of(context).agree_privacy, style: TextStyle(color: AppColors.textPrimaryColor, fontSize: 14.sp)):TextSpan(),
                  TextSpan(text: S.of(context).app_name, style: TextStyle(color: AppColors.textPrimaryColor, fontSize: 14.sp)),
                  TextSpan(
                    text: S.of(context).mulazim_privacy,
                    recognizer: TapGestureRecognizer()..onTap = (){
                      router.push(AppPaths.webViewPage, extra: {
                        'url': 'https://cer.mulazim.com/privacy',
                        'title': S.current.app_name,
                      });
                      // Navigator.of(context).push(MaterialPageRoute(builder:(context) => WebviewPage(url: Config.privacyUrl, title: S.of(context).agreement, webviewLoadEnum: WebviewLoadEnum.url)));
                    },
                    style: TextStyle(color: AppColors.baseGreenColor, fontSize: 14.sp)
                  ),
                  WidgetSpan(child: SizedBox(width: 4.w)),
                  widget.lang == 'ug'  ? TextSpan(text: S.of(context).agree_privacy, style: TextStyle(color: AppColors.textPrimaryColor, fontSize: 14.sp)):TextSpan()
                ]
              ),
            ),
          )
        )
      ],
    );
  }
}