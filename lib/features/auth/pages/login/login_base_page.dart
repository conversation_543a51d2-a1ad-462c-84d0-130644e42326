
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/app_router.dart';
import 'package:user_app/routes/paths.dart';

class LoginBasePage extends ConsumerStatefulWidget {
  const LoginBasePage({super.key});

  @override
  ConsumerState createState() => _LoginBasePageState();
}

class _LoginBasePageState extends ConsumerState<LoginBasePage> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            margin: EdgeInsets.all(30.w),
            child: Column(
              children: [
                SizedBox(height: 100.w,),
                // Logo
                SizedBox(
                  width: double.infinity,
                  child: Transform.scale(
                    scale: 1.2,
                    child: Image.asset(
                      "assets/images/basic/mulazim-login-tag.png",
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            top: 354.w,
            right: 0,
            left: 0,
            child: Column(
              children: [
                InkWell(
                  onTap:(){
                    // _fluwx.sendWeChatAuth(scope: "snsapi_userinfo", state: "wechat_sdk_demo_test");
                  },
                  child: Container(
                    alignment: Alignment.center,
                    padding: EdgeInsets.symmetric(vertical: 15.w),
                    margin: EdgeInsets.symmetric(horizontal: 24.w),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.w),
                        color: AppColors.baseGreenColor),
                    child: Text('手机号登录',style: TextStyle(color: Colors.white,fontSize: titleSize),),
                  ),
                ),
                SizedBox(height: 10.w,),
                InkWell(
                  onTap: (){
                    context.push(AppPaths.login);
                  },
                  child: Container(
                    // color: Colors.yellow,
                    alignment: Alignment.center,
                    margin: EdgeInsets.symmetric(horizontal: 24.w),
                    padding: EdgeInsets.symmetric(vertical: 12.w),
                    child: Text('手机号登录/注册',style: TextStyle(color: AppColors.baseGreenColor,fontSize: titleSize),),
                  ),
                ),
                SizedBox(height: 25.w,),
                Container(
                  // color: Colors.yellow,
                  alignment: Alignment.center,
                  margin: EdgeInsets.symmetric(horizontal: 24.w),
                  padding: EdgeInsets.symmetric(vertical: 12.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.check_circle,color: AppColors.baseGreenColor,size: 26.sp,),
                      SizedBox(width: 5.w,),
                      Text('我已阅读并同意',style: TextStyle(color: Colors.black,fontSize: mainSize),),
                      InkWell(
                        onTap: (){
                          router.push(AppPaths.webViewPage, extra: {
                            'url': 'https://cer.mulazim.com/privacy',
                            'title': S.current.app_name,
                          });
                        },
                        child: Text('美滋来用户协议，隐私协议',style: TextStyle(color: AppColors.baseGreenColor,fontSize: mainSize),)
                      )
                    ],
                  )
                ),
              ],
            )
          ),


        ],
      )
    );
  }
}
