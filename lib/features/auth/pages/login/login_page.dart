import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/utils/status_bar_util.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/main.dart';
import 'package:user_app/features/auth/pages/login/login_controller_provider.dart';
import 'package:user_app/features/auth/pages/login/widgets/user_info_widget.dart';
import 'package:user_app/features/auth/pages/login/widgets/login_form_widget.dart';


class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  ConsumerState createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage> {

  static bool _isNavigating = false;
  final ScrollController _scrollController = ScrollController();
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Future.delayed(Duration(milliseconds: 200));
      StatusBarUtil.setDarkMode();
      await Future.delayed(Duration(milliseconds: 800));
      StatusBarUtil.setDarkMode();
    });
  }


  @override
  Widget build(final BuildContext context) {

    // 只监听需要在此组件使用的状态
    final isLoggedIn = ref.watch(isLoggedInProvider);

    // 获取来源页面路径（如果有）
    final fromPath = GoRouterState.of(context).uri.queryParameters['from'];

    // 如果用户已登录，直接返回来源页面
    if (isLoggedIn &&
        fromPath != null &&
        fromPath.isNotEmpty &&
        !_isNavigating) {
      // 使用addPostFrameCallback确保在构建完成后执行导航
      WidgetsBinding.instance.addPostFrameCallback((final _) {
        if (!_isNavigating) {
          _isNavigating = true;
          // 可以选择使用replace或pop，这里使用pop更安全
          context.pop();
          // 导航完成后重置标志
          Future.delayed(const Duration(milliseconds: 500), () {
            _isNavigating = false;
          });
        }
      });
    }

    // 监听登录状态变化，登录成功后返回来源页面
    ref.listen(isLoggedInProvider, (final previous, final current) {
      if (previous == false && current == true && !_isNavigating) {
        _isNavigating = true;
        try {
          context.pop();
        } finally {
          // 确保标志能被重置
          Future.delayed(const Duration(milliseconds: 500), () {
            _isNavigating = false;
          });
        }
      }
    });

    return PopScope(
      // 拦截返回按钮，如果有来源页面且用户已登录，则导航到来源页面
      canPop: !(fromPath != null && isLoggedIn),
      onPopInvokedWithResult: (final bool didPop, final bool? result) {
        if (!didPop && fromPath != null && isLoggedIn) {
          context.replace(fromPath);
        }
      },
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Scaffold(
          backgroundColor: Colors.white,
          body: Stack(
            children: [
              SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  children: [
                    // Logo
                    SizedBox(
                      width: double.infinity,
                      height: 250.h,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10.w),
                        child: Transform.scale(
                          scale: 1.4,
                          child: Transform.translate(
                            offset: Offset(0, 50.h),
                            child: Image.asset(
                              "assets/images/basic/mulazim-login-tag.png",
                              width: double.infinity,
                              height: 400.h,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                    ),

                    // 已登录状态显示
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 30.w),
                      child: Consumer(
                          builder: (final context, final ref, final child) {
                            final hasUser = ref.watch(loginControllerProvider
                                .select((final s) => s.user != null));
                            return (isLoggedIn && hasUser)
                                ? UserInfoWidget(fromPath: fromPath)
                                : LoginFormWidget(scrollController: _scrollController);
                          }),
                    ),
                  ],
                ),
              ),

              // 关闭按钮
              Positioned(
                  top: kTextTabBarHeight + 20,
                  left: 30.w,
                  child: InkWell(
                      onTap: () {
                        // 如果有来源页面且用户已登录，则导航到来源页面
                        if (fromPath != null && isLoggedIn) {
                          context.go(fromPath);
                        } else {
                          context.pop();
                        }
                      },
                      child: Icon(Icons.close,
                          size: 32.w, color: Color(0xff666666)))),

              // 加载指示器
              Consumer(
                builder: (final context, final ref, final child) {
                  final isLoading = ref.watch(
                      loginControllerProvider.select((final s) => s.isLoading));
                  return isLoading
                      ? const Center(
                    child: LoadingWidget(),
                  )
                      : const SizedBox.shrink();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

