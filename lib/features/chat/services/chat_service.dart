import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:user_app/data/models/chat/chat_model.dart';
import 'package:user_app/data/repositories/chat/chat_repository.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart';

/// 聊天服务
class ChatService {
  /// 构造函数
  ChatService({
    required this.chatRepository,
    required this.localStorageRepository,
  });

  /// 聊天仓库
  final ChatRepository chatRepository;

  /// 本地存储仓库
  final LocalStorageRepository localStorageRepository;

  /// 获取聊天列表
  Future<List<ChatItem>> getChatList() async {
    final result = await chatRepository.getChatList();

    if (!result.success) {
      if (kDebugMode) {
        print('获取聊天列表失败: ${result.msg}');
      }
      throw Exception(result.msg ?? '获取聊天列表失败');
    }

    return result.data ?? [];
  }

  /// 获取聊天室详情
  Future<List<ChatMessage>> getChatDetail(final String orderId) async {
    final result = await chatRepository.getChatDetail(orderId);

    if (!result.success) {
      if (kDebugMode) {
        print('获取聊天室详情失败: ${result.msg}');
      }
      throw Exception(result.msg ?? '获取聊天室详情失败');
    }

    return formatChatMessageList(result.data ?? [], isWorkWechatEnabled: true);
  }

  /// 发送聊天消息
  Future<void> sendChatMessage({
    required final String orderId,
    required final int senderType,
    required final int contentType,
    required final String content,
    final String image = '',
  }) async {
    final userId = globalContainer.read(localStorageRepositoryProvider).getUserInfo()?.id;
    
    final result = await chatRepository.sendChatMessage(
      orderId: orderId,
      senderType: senderType,
      userId: userId ?? 0,
      contentType: contentType,
      content: content,
      image: image,
    );

    if (!result.success) {
      if (kDebugMode) {
        print('发送消息失败: ${result.msg}');
      }
      throw Exception(result.msg ?? '发送消息失败');
    }
  }

  /// 上传图片
  Future<Map<String, dynamic>> uploadImage(final String filePath) async {
    final result = await chatRepository.uploadImage(filePath);

    if (!result.success) {
      if (kDebugMode) {
        print('上传图片失败: ${result.msg}');
      }
      throw Exception(result.msg ?? '上传图片失败');
    }

    return result.data ?? {};
  }

  /// 格式化聊天消息列表
  List<ChatMessage> formatChatMessageList(final List<ChatMessage> messages,
      {final bool? isWorkWechatEnabled}) {
    DateTime? lastMessageTime;
    final List<ChatMessage> formattedMessages = [];

    for (final message in messages) {
      // 格式化消息时间
      final DateTime messageTime = DateTime.parse(message.msgDatetime);
      final int timestamp = messageTime.millisecondsSinceEpoch ~/ 1000;

      bool displayTime = true;
      if (lastMessageTime != null) {
        final int timeDiff =
            timestamp - (lastMessageTime.millisecondsSinceEpoch ~/ 1000);
        if (timeDiff <= 300) {
          // 5分钟内的消息不显示时间
          displayTime = false;
        }
      }

      if (displayTime) {
        message.relativeTime = _getRelativeTime(messageTime);
        lastMessageTime = messageTime;
      }

      // 处理地址类型消息
      if (message.type == 'address' && message.content.isNotEmpty) {
        try {
          final Map<String, dynamic> locationData = jsonDecode(message.content);
          message.content = jsonEncode(locationData); // 确保是有效的JSON
        } catch (e) {
          debugPrint('Error parsing address content: $e');
        }
      }

      // 处理卡片类型消息
      if (message.cardContent?.isNotEmpty == true) {
        try {
          final Map<String, dynamic> cardData =
              jsonDecode(message.cardContent!);
          message.cardContent = jsonEncode(cardData); // 确保是有效的JSON
          formattedMessages.add(message);
          final workWechatPoster =
              localStorageRepository.getLocationInfo()?.workWechatPoster ?? '';
          // 如果订单状态是4且启用了企业微信广告，添加一条广告消息
          if (cardData['order_state'] == 4 && isWorkWechatEnabled == true) {
            formattedMessages.add(
              ChatMessage(
                avatar: message.avatar,
                content: workWechatPoster, // 这里需要从配置中获取广告图片URL
                type: 'work_wechat_ad',
                msgDatetime: message.msgDatetime,
                rule: message.rule,
                orderId: message.orderId,
                cardType: message.cardType,
              ),
            );
          }
        } catch (e) {
          debugPrint('Error parsing card content: $e');
          formattedMessages.add(message);
        }
      } else {
        formattedMessages.add(message);
      }
    }

    return formattedMessages;
  }

  /// 获取相对时间描述
  String _getRelativeTime(final DateTime messageTime) {
    final now = DateTime.now();
    final difference = now.difference(messageTime);

    if (difference.inMinutes < 1) {
      return S.current.just_now;
    }

    return DateFormat('yyyy-MM-dd HH:mm:ss').format(messageTime);
  }
}

/// 聊天服务提供者
final chatServiceProvider = Provider<ChatService>((final ref) {
  return ChatService(
    chatRepository: ref.watch(chatRepositoryProvider),
    localStorageRepository: ref.watch(localStorageRepositoryProvider),
  );
});
