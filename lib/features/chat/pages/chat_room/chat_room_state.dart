import 'package:user_app/data/models/chat/chat_model.dart';

/// 聊天室页面状态
class ChatRoomState {
  /// 构造函数
  const ChatRoomState({
    this.isLoading = false,
    this.error,
    this.messages = const [],
    this.messageValue = '',
    this.isShowingOptions = false,
    this.isKeyboardVisible = false,
    this.messageOptionsHeight = 125,
    this.isWebSocketListening = false,
    this.currentOrderId,
  });

  /// 是否正在加载
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 聊天消息列表
  final List<ChatMessage> messages;

  /// 输入框文本
  final String messageValue;

  /// 是否显示消息选项
  final bool isShowingOptions;

  /// 键盘是否可见
  final bool isKeyboardVisible;

  /// 消息选项高度
  final double messageOptionsHeight;

  /// 是否已设置WebSocket监听器
  final bool isWebSocketListening;

  /// 当前订单ID
  final String? currentOrderId;

  /// 复制状态
  ChatRoomState copyWith({
    bool? isLoading,
    String? error,
    List<ChatMessage>? messages,
    String? messageValue,
    bool? isShowingOptions,
    bool? isKeyboardVisible,
    double? messageOptionsHeight,
    bool? isWebSocketListening,
    String? currentOrderId,
  }) {
    return ChatRoomState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      messages: messages ?? this.messages,
      messageValue: messageValue ?? this.messageValue,
      isShowingOptions: isShowingOptions ?? this.isShowingOptions,
      isKeyboardVisible: isKeyboardVisible ?? this.isKeyboardVisible,
      messageOptionsHeight: messageOptionsHeight ?? this.messageOptionsHeight,
      isWebSocketListening: isWebSocketListening ?? this.isWebSocketListening,
      currentOrderId: currentOrderId ?? this.currentOrderId,
    );
  }
}
