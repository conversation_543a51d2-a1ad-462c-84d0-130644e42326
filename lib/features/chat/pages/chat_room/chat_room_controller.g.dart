// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_room_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$chatRoomControllerHash() =>
    r'6323ca739fc182b02554d87cbcfec5100a695a4f';

/// 聊天室控制器
///
/// Copied from [ChatRoomController].
@ProviderFor(ChatRoomController)
final chatRoomControllerProvider =
    AutoDisposeNotifierProvider<ChatRoomController, ChatRoomState>.internal(
  ChatRoomController.new,
  name: r'chatRoomControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$chatRoomControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ChatRoomController = AutoDisposeNotifier<ChatRoomState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
