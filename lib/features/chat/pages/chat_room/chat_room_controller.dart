import 'dart:async';
import 'dart:convert';
import 'package:bot_toast/bot_toast.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/websocket_service.dart';
import 'package:user_app/core/utils/permission_helper.dart';
import 'package:user_app/data/models/chat/chat_model.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/features/chat/pages/chat_room/chat_room_state.dart';
import 'package:user_app/features/chat/services/chat_service.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';

part 'chat_room_controller.g.dart';

/// 聊天室控制器
@riverpod
class ChatRoomController extends _$ChatRoomController {
  /// 消息输入控制器
  final messageController = TextEditingController();

  /// 滚动控制器
  final scrollController = ScrollController();

  /// 消息焦点
  final messageFocusNode = FocusNode();

  /// 上一条消息时间
  int? _lastMessageTime;

  /// WebSocket服务引用
  late final WebSocketService _webSocketService;

  /// 消息监听器订阅
  StreamSubscription? _messageSubscription;

  @override
  ChatRoomState build() {
    // 获取WebSocket服务单例
    _webSocketService = WebSocketService.instance;

    // 监听输入框变化
    messageController.addListener(_onMessageChanged);

    // 监听焦点变化
    messageFocusNode.addListener(() {
      setKeyboardVisible(messageFocusNode.hasFocus);
    });

    // 在控制器被销毁时清理资源
    ref.onDispose(() {
      if (kDebugMode) {
        print('开始销毁聊天室控制器...');
      }

      // 取消消息监听器
      _messageSubscription?.cancel();

      messageController.removeListener(_onMessageChanged);
      messageController.dispose();
      scrollController.dispose();
      messageFocusNode.dispose();

      // 离开当前聊天室并关闭WebSocket（与小程序一致）
      if (state.currentOrderId != null) {
        if (kDebugMode) {
          print('离开聊天室: ${state.currentOrderId}');
        }
        _webSocketService.leaveRoom(state.currentOrderId!);
      }

      // 立即关闭WebSocket连接（与小程序onUnload一致）
      _webSocketService.dispose();

      if (kDebugMode) {
        print('聊天室控制器销毁完成');
      }
    });

    return const ChatRoomState();
  }

  /// 初始化聊天室
  Future<void> initChatRoom(final String orderId) async {
    if (state.isLoading) return;

    state = state.copyWith(
      isLoading: true,
      currentOrderId: orderId,
    );
    _lastMessageTime = null;

    try {
      // 获取聊天消息
      final service = ref.read(chatServiceProvider);
      final messages = await service.getChatDetail(orderId);

      // 只在第一次初始化时设置WebSocket消息监听器
      if (!state.isWebSocketListening) {
        if (kDebugMode) {
          print('首次初始化聊天室，设置消息监听器');
        }

        // 监听WebSocket消息流
        _messageSubscription =
            _webSocketService.messageStream.listen((final data) {
          _handleWebSocketMessage(data);
        });

        state = state.copyWith(isWebSocketListening: true);
      } else {
        if (kDebugMode) {
          print('聊天室已初始化过，消息监听器已存在');
        }
      }

      // 加入聊天室（这会自动初始化和连接WebSocket）
      _webSocketService.joinRoom(orderId);

      state = state.copyWith(
        isLoading: false,
        messages: messages,
      );
      _scrollToBottom();

      if (kDebugMode) {
        print('聊天室初始化成功，订单ID: $orderId，消息数量: ${messages.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('初始化聊天室失败: $e');
      }

      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 输入框文本变化处理
  void _onMessageChanged() {
    updateMessageValue(messageController.text);
  }

  /// 处理WebSocket消息
  void _handleWebSocketMessage(final Map<String, dynamic> data) {
    try {
      if (kDebugMode) {
        print('处理WebSocket消息: $data');
      }

      // 检查事件类型，应该是 'chat_result' 而不是 'chat_message'
      if (data['event'] != 'chat_result') {
        if (kDebugMode) {
          print('忽略非聊天结果消息: ${data['event']}');
        }
        return;
      }

      final messageData = data['message'] as Map<String, dynamic>?;
      if (messageData == null) {
        if (kDebugMode) {
          print('消息数据为空');
        }
        return;
      }

      // 过滤自己发送的消息（rule == 1 表示是自己发送的消息）
      if (messageData['rule'] == 1) {
        if (kDebugMode) {
          print('忽略自己发送的消息');
        }
        return;
      }

      final message = ChatMessage.fromJson(messageData);

      // 处理消息时间
      final timestamp =
          DateTime.parse(message.msgDatetime).millisecondsSinceEpoch ~/ 1000;
      if (_lastMessageTime == null || timestamp - _lastMessageTime! > 300) {
        // 如果是刚发送的消息，显示"刚刚"
        final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
        if (now - timestamp < 60) {
          message.relativeTime = S.current.just_now;
        } else {
          // 否则直接使用接口返回的时间
          message.relativeTime = message.msgDatetime;
        }
        _lastMessageTime = timestamp;
      }

      final updatedMessages = [...state.messages, message];
      state = state.copyWith(messages: updatedMessages);
      _scrollToBottom();

      if (kDebugMode) {
        print('成功添加新消息: ${message.content}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('处理WebSocket消息失败: $e');
      }
    }
  }

  /// 更新消息值
  void updateMessageValue(final String value) {
    state = state.copyWith(messageValue: value);
  }

  /// 切换选项显示状态
  void toggleOptions() {
    state = state.copyWith(
      isShowingOptions: !state.isShowingOptions,
      isKeyboardVisible: false,
    );
  }

  /// 关闭选项
  void closeOptions() {
    if (state.isShowingOptions) {
      state = state.copyWith(isShowingOptions: false);
    }
  }

  /// 设置键盘可见状态
  void setKeyboardVisible(final bool visible) {
    state = state.copyWith(isKeyboardVisible: visible);
  }

  /// 发送文本消息
  Future<void> sendTextMessage(final String orderId) async {
    final text = messageController.text.trim();
    if (text.isEmpty) return;

    try {
      // 先在本地添加消息，立即显示给用户（与小程序逻辑一致）
      final userInfo = ref.read(localStorageRepositoryProvider).getUserInfo();
      final newMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        orderId: orderId,
        msgDatetime: DateTime.now().toString(),
        rule: 1, // 表示是自己发送的消息
        type: 'text',
        content: text,
        avatar: userInfo?.avatar ??
            'https://filev1.almas.biz/images/default/chat_default_user.png',
        cardType: 0,
        relativeTime: S.current.just_now,
      );

      // 立即更新界面
      final updatedMessages = [...state.messages, newMessage];
      state = state.copyWith(messages: updatedMessages);

      // 清空输入框
      messageController.clear();
      _scrollToBottom();

      // 发送到服务器
      await ref.read(chatServiceProvider).sendChatMessage(
            orderId: orderId,
            senderType: 1,
            contentType: 1,
            content: text,
          );

      if (kDebugMode) {
        print('消息发送成功: $text');
      }
    } catch (e) {
      if (kDebugMode) {
        print('发送消息失败: $e');
      }
      state = state.copyWith(error: e.toString());
    }
  }

  /// 发送位置信息
  Future<void> sendLocation(
      final String orderId, final BuildContext context) async {
    try {
      // 打开位置选择页面
      final result = await context.push<Map<String, dynamic>>(
        AppPaths.locationPickerPage,
        extra: {
          'latitude': 0.0,
          'longitude': 0.0,
          'name': '',
          'address': '',
        },
      );

      // 如果用户选择了位置
      if (result != null) {
        // 将位置信息转换为JSON字符串
        final locationString = jsonEncode(result);

        // 先将本地消息添加到列表中，用于立即显示
        final newMessage = ChatMessage(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          orderId: orderId,
          msgDatetime: DateTime.now().toString(),
          rule: 1,
          type: 'address',
          content: locationString,
          avatar: ref
                  .read(localStorageRepositoryProvider)
                  .getUserInfo()
                  ?.avatar ??
              'https://filev1.almas.biz/images/default/chat_default_user.png', // 默认头像，应从用户信息获取
          cardType: 0, // 非卡片消息
          relativeTime: S.current.just_now,
        );

        // 更新界面
        state = state.copyWith(
          messages: [...state.messages, newMessage],
        );
        _scrollToBottom();

        // 发送位置消息到服务器
        await ref.read(chatServiceProvider).sendChatMessage(
              orderId: orderId,
              senderType: 1,
              contentType: 6, // 位置消息类型
              content: locationString,
            );

        // 刷新消息列表以获取服务器返回的正确消息格式
        // await initChatRoom(orderId);
      }
    } catch (e) {
      if (kDebugMode) {
        print('发送位置失败: $e');
      }
      state = state.copyWith(error: e.toString());
    }
  }

  /// 发送图片消息
  Future<void> sendImageMessage(
      final String orderId, final String imagePath) async {
    try {
      // 先上传图片
      final response =
          await ref.read(chatServiceProvider).uploadImage(imagePath);
      final imageUrl = response['url'] as String;

      // 先在本地添加消息，立即显示给用户
      final userInfo = ref.read(localStorageRepositoryProvider).getUserInfo();
      final newMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        orderId: orderId,
        msgDatetime: DateTime.now().toString(),
        rule: 1, // 表示是自己发送的消息
        type: 'image',
        content: imageUrl,
        avatar: userInfo?.avatar ??
            'https://filev1.almas.biz/images/default/chat_default_user.png',
        cardType: 0,
        relativeTime: S.current.just_now,
      );

      // 立即更新界面
      final updatedMessages = [...state.messages, newMessage];
      state = state.copyWith(messages: updatedMessages);
      _scrollToBottom();

      // 发送到服务器
      await ref.read(chatServiceProvider).sendChatMessage(
            orderId: orderId,
            senderType: 1,
            contentType: 2,
            content: '',
            image: imageUrl,
          );

      if (kDebugMode) {
        print('图片消息发送成功: $imageUrl');
      }
    } catch (e) {
      if (kDebugMode) {
        print('发送图片失败: $e');
      }
      state = state.copyWith(error: e.toString());
    }
  }

  /// 拍照
  Future<void> takePhoto(final String orderId) async {
    final permissionResults = await PermissionHelper.requestPermission(
      permission: Permission.camera,
      explanation: S.current.camera_permission_explanation,
      title: S.current.profile_photo_permissions,
    );

    // 检查是否获得了所有权限
    if (!permissionResults) {
      BotToast.showText(
          text: S.current.permissions_required
              .replaceAll('%s', S.current.profile_photo_permissions));
      return;
    }
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.camera);

    if (image != null) {
      await sendImageMessage(orderId, image.path);
    }
  }

  /// 从相册选择图片
  Future<void> pickImage(final String orderId) async {
    final deviceInfo = DeviceInfoPlugin();
    final androidInfo = await deviceInfo.androidInfo;
    final permission = androidInfo.version.sdkInt <= 32
        ? Permission.storage
        : Permission.photos;
    final permissionResults = await PermissionHelper.requestPermission(
      permission: permission,
      explanation: S.current.photos_permission_explanation,
      title: S.current.profile_photo_permissions,
    );

    // 检查是否获得了所有权限
    if (!permissionResults) {
      BotToast.showText(
          text: S.current.permissions_required
              .replaceAll('%s', S.current.profile_photo_permissions));
      return;
    }
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);

    if (image != null) {
      await sendImageMessage(orderId, image.path);
    }
  }

  /// 添加快捷回复
  void appendQuickReply(final String text) {
    messageController.text = '${messageController.text}$text ';
    messageFocusNode.requestFocus();
  }

  /// 滚动到底部
  void _scrollToBottom() {
    if (scrollController.hasClients) {
      Future.delayed(const Duration(milliseconds: 100), () {
        scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    }
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }
}
