import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/chat/pages/chat_room/chat_room_controller.dart';
import 'package:user_app/features/chat/pages/chat_room/widgets/chat_input_area.dart';
import 'package:user_app/features/chat/pages/chat_room/widgets/chat_message_list.dart';
import 'package:user_app/features/chat/pages/chat_room/widgets/chat_options_area.dart';

/// 聊天室页面
class ChatRoomPage extends ConsumerStatefulWidget {
  /// 构造函数
  const ChatRoomPage({
    super.key,
    required this.orderId,
    required this.name,
  });

  /// 订单ID
  final String orderId;

  /// 餐厅名称
  final String name;

  @override
  ConsumerState<ChatRoomPage> createState() => _ChatRoomPageState();
}

class _ChatRoomPageState extends ConsumerState<ChatRoomPage> {
  @override
  void initState() {
    super.initState();
    // 初始化聊天室
    Future.microtask(() {
      ref
          .read(chatRoomControllerProvider.notifier)
          .initChatRoom(widget.orderId);
    });
  }

  @override
  Widget build(final BuildContext context) {
    final optionsArea = ChatOptionsArea(orderId: widget.orderId);
    return SafeArea(
      top: false,
      child: Scaffold(
        appBar: CustomAppBar(
          title: widget.name,
          backgroundColor: AppColors.primary,
          titleColor: Colors.white,
        ),
        body: GestureDetector(
          onTap: () {
            // 点击页面时关闭键盘和选项
            FocusScope.of(context).unfocus();
            ref.read(chatRoomControllerProvider.notifier).closeOptions();
          },
          child: Directionality(
            textDirection: TextDirection.rtl,
            child: Column(
              children: [
                // 聊天消息列表
                Expanded(
                  child: Consumer(
                    builder: (final context, final ref, final child) {
                      final isLoading = ref.watch(chatRoomControllerProvider
                          .select((final value) => value.isLoading));
                      final messages = ref.watch(chatRoomControllerProvider
                          .select((final value) => value.messages));
                      return isLoading && messages.isEmpty
                          ? const Center(child: LoadingWidget())
                          : ChatMessageList();
                    },
                  ),
                ),

                // 输入区域
                ChatInputArea(
                  orderId: widget.orderId,
                ),

                // 选项区域
                Consumer(
                  builder: (final context, final ref, final child) {
                    final isShowingOptions = ref.watch(
                      chatRoomControllerProvider
                          .select((final value) => value.isShowingOptions),
                    );
                    return AnimatedCrossFade(
                      firstChild: optionsArea,
                      secondChild: const SizedBox.shrink(),
                      crossFadeState: isShowingOptions
                          ? CrossFadeState.showFirst
                          : CrossFadeState.showSecond,
                      duration: const Duration(milliseconds: 100),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
