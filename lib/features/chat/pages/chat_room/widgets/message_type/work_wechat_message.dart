import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/prefect_image_width.dart';
import 'package:user_app/core/widgets/image_viewer.dart';

/// 企业微信广告消息组件
class WorkWechatMessage extends StatelessWidget {
  /// 构造函数
  const WorkWechatMessage({
    super.key,
    required this.imageUrl,
    this.onTap,
  });

  /// 图片URL
  final String imageUrl;

  /// 点击回调
  final VoidCallback? onTap;

  @override
  Widget build(final BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 打开图片查看器
        showImageViewer(
          context,
          imageUrls: [imageUrl],
          heroTagPrefix: 'work_wechat',
        );
        onTap?.call();
      },
      child: <PERSON>(
        tag: 'work_wechat_${imageUrl.hashCode}',
        child: ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: PrefectImageWidth(
            imageUrl: imageUrl,
            width: 400.w,
          ),
        ),
      ),
    );
  }
}
