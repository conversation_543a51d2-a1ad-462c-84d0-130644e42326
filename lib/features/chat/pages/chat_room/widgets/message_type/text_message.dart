import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 文本消息组件
class TextMessage extends StatelessWidget {
  /// 构造函数
  const TextMessage({
    super.key,
    required this.content,
    required this.isUserMessage,
  });

  /// 消息内容
  final String content;

  /// 是否是用户消息
  final bool isUserMessage;

  @override
  Widget build(final BuildContext context) {
    // 根据当前语言环境判断文字方向
    final isUyghur = Localizations.localeOf(context).languageCode == 'en';

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: isUserMessage ? Theme.of(context).primaryColor : Colors.white,
        borderRadius: BorderRadius.circular(5.r),
      ),
      child: Text(
        content,
        style: TextStyle(
          fontSize: 16.sp,
          color: isUserMessage ? Colors.white : Colors.black,
        ),
        textDirection: isUyghur ? TextDirection.rtl : TextDirection.ltr,
      ),
    );
  }
}
