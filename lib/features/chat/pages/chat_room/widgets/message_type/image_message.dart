import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/image_viewer.dart';

/// 图片消息组件
class ImageMessage extends StatelessWidget {
  /// 构造函数
  const ImageMessage({
    super.key,
    required this.imageUrl,
    this.onTap,
  });

  /// 图片URL
  final String imageUrl;

  /// 点击回调
  final VoidCallback? onTap;

  @override
  Widget build(final BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 打开图片查看器
        showImageViewer(
          context,
          imageUrls: [imageUrl],
          heroTagPrefix: 'chat_image',
        );
        onTap?.call();
      },
      child: Hero(
        tag: 'chat_image_${imageUrl.hashCode}',
        child: ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            width: 200.w,
            height: 200.h,
            fit: BoxFit.cover,
            placeholder: (final context, final url) => Container(
              width: 200.w,
              height: 200.h,
              color: Colors.grey[200],
              child: Center(
                child: CircularProgressIndicator(
                  strokeWidth: 2.r,
                ),
              ),
            ),
            errorWidget: (final context, final url, final error) => Container(
              width: 200.w,
              height: 200.h,
              color: Colors.grey[200],
              child: Icon(
                Icons.error,
                size: 30.r,
                color: Colors.grey,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
