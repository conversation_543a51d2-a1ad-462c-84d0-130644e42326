import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 订单卡片消息组件
class CardMessage extends StatelessWidget {
  /// 构造函数
  const CardMessage({
    super.key,
    required this.cardType,
    required this.cardContent,
  });

  /// 卡片类型
  final int cardType;

  /// 卡片内容JSON字符串
  final String cardContent;

  @override
  Widget build(final BuildContext context) {
    try {
      final data = jsonDecode(cardContent) as Map<String, dynamic>;
      final orderState = data['order_state'] as int;
      final isUyghur = Localizations.localeOf(context).languageCode == 'en';
      final orderStateMsg = isUyghur
          ? data['order_state_msg_ug'] as String
          : data['order_state_msg_zh'] as String;
      final foodsList = data['foods_list'] as List<dynamic>;
      final remark = data['remark'] as String;
      final createdTime = data['created_time'] as String;
      final deliveryTime = data['delivery_time'] as String;

      return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          textDirection: isUyghur ? TextDirection.rtl : TextDirection.ltr,
          children: [
            // 标题
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(14.w),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: AppColors.borderColor,
                    width: 1.w,
                  ),
                ),
              ),
              child: Text(
                S.of(context).chat_room_order_title,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
                textDirection: isUyghur ? TextDirection.rtl : TextDirection.ltr,
              ),
            ),

            // 订单状态
            Container(
              padding: EdgeInsets.symmetric(vertical: 15.w),
              alignment: Alignment.center,
              child: Text(
                orderStateMsg,
                style: TextStyle(
                  fontSize: 19.sp,
                  fontWeight: FontWeight.bold,
                  color: _getStateColor(orderState),
                ),
                textDirection: isUyghur ? TextDirection.rtl : TextDirection.ltr,
              ),
            ),

            // 菜品列表
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                textDirection: isUyghur ? TextDirection.rtl : TextDirection.ltr,
                children: [
                  Text(
                    S.of(context).chat_room_res_food,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondColor,
                    ),
                    textDirection:
                        isUyghur ? TextDirection.rtl : TextDirection.ltr,
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: foodsList.map((final food) {
                        final foodName = isUyghur
                            ? food['food_name_ug'] as String
                            : food['food_name_zh'] as String;
                        final number = food['number'] as int;

                        return Padding(
                          padding: EdgeInsets.only(bottom: 3.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            textDirection: isUyghur
                                ? TextDirection.rtl
                                : TextDirection.ltr,
                            children: [
                              Expanded(
                                child: Text(
                                  foodName,
                                  style: TextStyle(fontSize: 14.sp),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  textDirection: isUyghur
                                      ? TextDirection.rtl
                                      : TextDirection.ltr,
                                ),
                              ),
                              Text(
                                'x$number',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: Colors.grey,
                                ),
                                textDirection: isUyghur
                                    ? TextDirection.rtl
                                    : TextDirection.ltr,
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),

            // 备注
            if (remark.isNotEmpty)
              Padding(
                padding: EdgeInsets.fromLTRB(15.w, 10.h, 15.w, 0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  textDirection:
                      isUyghur ? TextDirection.rtl : TextDirection.ltr,
                  children: [
                    Text(
                      S.of(context).mother_day_remark,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondColor,
                      ),
                      textDirection:
                          isUyghur ? TextDirection.rtl : TextDirection.ltr,
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      child: Text(
                        remark,
                        style: TextStyle(fontSize: 14.sp),
                        textDirection:
                            isUyghur ? TextDirection.rtl : TextDirection.ltr,
                      ),
                    ),
                  ],
                ),
              ),

            // 时间信息
            Padding(
              padding: EdgeInsets.all(15.w),
              child: Column(
                textDirection: isUyghur ? TextDirection.rtl : TextDirection.ltr,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    textDirection:
                        isUyghur ? TextDirection.rtl : TextDirection.ltr,
                    children: [
                      Text(
                        S.of(context).order_time,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.textSecondColor,
                        ),
                        textDirection:
                            isUyghur ? TextDirection.rtl : TextDirection.ltr,
                      ),
                      Text(
                        createdTime,
                        style: TextStyle(fontSize: 14.sp),
                        textDirection:
                            isUyghur ? TextDirection.rtl : TextDirection.ltr,
                      ),
                    ],
                  ),
                  SizedBox(height: 5.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    textDirection:
                        isUyghur ? TextDirection.rtl : TextDirection.ltr,
                    children: [
                      Text(
                        S.of(context).distribution_time,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.textSecondColor,
                        ),
                        textDirection:
                            isUyghur ? TextDirection.rtl : TextDirection.ltr,
                      ),
                      Text(
                        deliveryTime,
                        style: TextStyle(fontSize: 14.sp),
                        textDirection:
                            isUyghur ? TextDirection.rtl : TextDirection.ltr,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    } catch (e) {
      return const SizedBox();
    }
  }

  /// 获取状态颜色
  Color _getStateColor(final int state) {
    switch (state) {
      case 3: // 等待接收订单
        return Colors.orange;
      case 4: // 订单已接受
        return const Color(0xFF2D7AFF);
      case 5: // 已备餐
      case 200: // 到餐厅
      case 300: // 已取餐
      case 6: // 配送中
        return Colors.green;
      case 7: // 已完成
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }
}
