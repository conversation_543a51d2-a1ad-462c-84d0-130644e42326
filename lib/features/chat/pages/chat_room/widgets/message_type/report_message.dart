import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/prefect_image_width.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/core/widgets/image_viewer.dart';

/// 上报情况消息组件
class ReportMessage extends StatelessWidget {
  /// 构造函数
  const ReportMessage({
    super.key,
    required this.cardContent,
    this.onImageTap,
  });

  /// 卡片内容JSON字符串
  final String cardContent;

  /// 图片点击回调
  final void Function(String url, List<String> urls)? onImageTap;

  @override
  Widget build(final BuildContext context) {
    try {
      final data = jsonDecode(cardContent) as Map<String, dynamic>;
      final isUyghur = Localizations.localeOf(context).languageCode == 'en';
      final title =
          isUyghur ? data['title_ug'] as String : data['title_zh'] as String;
      final type =
          isUyghur ? data['type_ug'] as String : data['type_zh'] as String;
      final content = data['content'] as String;
      final images = List<String>.from(data['image'] as List);

      return Container(
        padding: EdgeInsets.all(10.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          textDirection: isUyghur ? TextDirection.rtl : TextDirection.ltr,
          children: [
            // 标题
            Container(
              padding: EdgeInsets.all(14.w),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: AppColors.borderColor,
                    width: 1.w,
                  ),
                ),
              ),
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
                textDirection: isUyghur ? TextDirection.rtl : TextDirection.ltr,
              ),
            ),

            // 状态
            Container(
              padding: EdgeInsets.symmetric(vertical: 15.h),
              alignment: Alignment.center,
              child: Text(
                type,
                style: TextStyle(
                  fontSize: 19.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.baseBlueColor,
                ),
                textDirection: isUyghur ? TextDirection.rtl : TextDirection.ltr,
              ),
            ),

            // 详细内容
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                textDirection: isUyghur ? TextDirection.rtl : TextDirection.ltr,
                children: [
                  Text(
                    S.of(context).chat_room_order_detail,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                    textDirection:
                        isUyghur ? TextDirection.rtl : TextDirection.ltr,
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: Text(
                      content,
                      style: TextStyle(fontSize: 14.sp),
                      textDirection:
                          isUyghur ? TextDirection.rtl : TextDirection.ltr,
                    ),
                  ),
                ],
              ),
            ),

            // 图片列表
            if (images.isNotEmpty)
              Padding(
                padding: EdgeInsets.all(15.w),
                child: LayoutBuilder(
                  builder: (final context, final constraints) {
                    final imageWidth = _calculateImageWidth(
                        images.length, constraints.maxWidth);
                    return Wrap(
                      spacing: 5.w,
                      runSpacing: 5.h,
                      textDirection:
                          isUyghur ? TextDirection.rtl : TextDirection.ltr,
                      children: images.map((final url) {
                        return GestureDetector(
                          onTap: () {
                            // 打开图片查看器
                            showImageViewer(
                              context,
                              imageUrls: images,
                              initialIndex: images.indexOf(url),
                              heroTagPrefix: 'report',
                            );
                            onImageTap?.call(url, images);
                          },
                          child: Hero(
                            tag: 'report_${url.hashCode}',
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(4.r),
                              child: PrefectImageWidth(
                                imageUrl: url,
                                width: imageWidth,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    );
                  },
                ),
              ),
          ],
        ),
      );
    } catch (e) {
      return const SizedBox();
    }
  }

  /// 计算图片宽度
  double _calculateImageWidth(
      final int imageCount, final double containerWidth) {
    final spacing = 5.w;
    if (imageCount == 1) {
      return containerWidth;
    } else if (imageCount == 2) {
      return (containerWidth - spacing) / 2;
    } else {
      return (containerWidth - spacing * 2) / 3;
    }
  }
}
