import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 消息头像组件
class MessageAvatar extends StatelessWidget {
  /// 构造函数
  const MessageAvatar({
    super.key,
    required this.avatarUrl,
    this.isLeft = true,
  });

  /// 头像URL
  final String avatarUrl;

  /// 是否在左侧
  final bool isLeft;

  @override
  Widget build(final BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: isLeft ? 0 : 10.w,
        right: isLeft ? 10.w : 0,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25.r),
        child: CachedNetworkImage(
          imageUrl: avatarUrl,
          width: 50.w,
          height: 50.w,
          fit: BoxFit.cover,
          placeholder: (final context, final url) => Container(
            color: Colors.grey[200],
          ),
          errorWidget: (final context, final url, final error) => Container(
            color: Colors.grey[200],
            child: Icon(Icons.error, size: 25.w),
          ),
        ),
      ),
    );
  }
}
