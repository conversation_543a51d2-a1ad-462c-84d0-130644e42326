import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/detail/map_list_widget.dart';
import 'package:user_app/generated/l10n.dart';

/// 位置消息组件
class LocationMessage extends StatelessWidget {
  /// 构造函数
  const LocationMessage({
    super.key,
    required this.locationJson,
    this.onTap,
  });

  /// 位置信息JSON字符串
  final String locationJson;

  /// 点击回调
  final VoidCallback? onTap;

  @override
  // ignore: prefer_final_parameters
  Widget build(BuildContext context) {
    try {
      final locationData = jsonDecode(locationJson) as Map<String, dynamic>;
      final addr = locationData['addr'] as String;
      final fullAddr = locationData['full_addr'] as String;
      final lat = locationData['lat'] as double;
      final lng = locationData['lng'] as double;

      return GestureDetector(
        onTap: onTap ??
            (() {
               try {
                showModalBottomSheet(
                    backgroundColor: Colors.transparent,
                    context: context,
                    builder: (_)=>mapCallWidget(context,
                        title: S.current.select_navigation,
                        startLatitude:0.0,
                        startLongitude:0.0,
                        endLatitude:lat,
                        endLongitude:lng,
                    )
                );
              } catch (e) {
                print('elfa Failed to open native page: $e');
              }
            }),
        child: Container(
          padding: EdgeInsets.all(10.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(5.r),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.location_on, size: 20.w, color: Colors.red),
                  SizedBox(width: 5.w),
                  Expanded(
                    child: Text(
                      addr,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 5.h),
              Text(
                fullAddr,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      );
    } catch (e) {
      return const SizedBox();
    }
  }
}
