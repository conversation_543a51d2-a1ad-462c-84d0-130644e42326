import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/data/models/chat/chat_model.dart';
import 'package:user_app/features/chat/pages/chat_room/widgets/message_type/index.dart';

/// 聊天消息项组件
class ChatMessageItem extends ConsumerWidget {
  /// 构造函数
  const ChatMessageItem({
    super.key,
    required this.message,
  });

  /// 消息
  final ChatMessage message;

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 判断是否是用户发送的消息（根据rule判断）
    final isUserMessage = message.rule == 1;

    return Padding(
      padding: EdgeInsets.only(bottom: 15.r),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        textDirection: isUserMessage ? TextDirection.rtl : TextDirection.ltr,
        children: [
          // 头像
          MessageAvatar(
            avatarUrl: message.avatar,
            isLeft: !isUserMessage,
          ),

          // 消息内容
          Flexible(
            child: Container(
              margin: EdgeInsets.only(
                left: 10.r,
                right: 10.r,
              ),
              child: _buildMessageContent(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建消息内容
  Widget _buildMessageContent(final BuildContext context) {
    switch (message.type) {
      case 'text':
        return TextMessage(
          content: message.content,
          isUserMessage: message.rule == 1,
        );
      case 'image':
        return ImageMessage(
          imageUrl: message.content,
        );
      case 'address':
        return LocationMessage(
          locationJson: message.content,
        );
      case 'card':
        return CardMessage(
          cardType: message.cardType,
          cardContent: message.cardContent ?? '',
        );
      case 'work_wechat_ad':
        return WorkWechatMessage(
          imageUrl: message.content,
        );
      case 'report':
        return ReportMessage(
          cardContent: message.cardContent ?? '',
        );
      default:
        return const SizedBox();
    }
  }
}
