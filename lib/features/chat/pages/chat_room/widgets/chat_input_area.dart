import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/chat/pages/chat_room/chat_room_controller.dart';
import 'package:user_app/features/chat/pages/chat_room/widgets/quick_reply_button.dart';
import 'package:user_app/generated/l10n.dart';

/// 聊天输入区域组件
class ChatInputArea extends ConsumerWidget {
  /// 构造函数
  const ChatInputArea({
    super.key,
    required this.orderId,
  });

  /// 订单ID
  final String orderId;

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final controller = ref.read(chatRoomControllerProvider.notifier);
    final isUyghur = Localizations.localeOf(context).languageCode == 'en';

    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // 快捷回复选项
          Padding(
            padding: EdgeInsets.all(10.r),
            child: Row(
              children: [
                QuickReplyButton(
                  text: S.of(context).chat_room_et_res,
                  onTap: () => controller
                      .appendQuickReply(S.of(context).chat_room_et_res),
                ),
                QuickReplyButton(
                  text: S.of(context).chat_room_et_shipment,
                  onTap: () => controller
                      .appendQuickReply(S.of(context).chat_room_et_shipment),
                ),
                QuickReplyButton(
                  text: S.of(context).chat_room_location,
                  onTap: () => controller.sendLocation(orderId, context),
                ),
              ],
            ),
          ),

          // 输入框和发送按钮
          Padding(
            padding: EdgeInsets.only(left: 15.r, right: 5.r, bottom: 10.r),
            child: Row(
              children: [
                // 发送按钮
                Consumer(
                  builder: (final context, final ref, final child) {
                    final messageValue = ref.watch(chatRoomControllerProvider
                        .select((final value) => value.messageValue));
                    return messageValue.isNotEmpty
                        ? IconButton(
                            icon: Image.asset(
                              'assets/images/chat-room/sending.png',
                              width: 30.w,
                              height: 30.w,
                            ),
                            onPressed: () =>
                                controller.sendTextMessage(orderId),
                          )
                        : const SizedBox.shrink();
                  },
                ),

                // 选项按钮
                Consumer(
                  builder: (final context, final ref, final child) {
                    final isShowingOptions = ref.watch(
                      chatRoomControllerProvider
                          .select((final value) => value.isShowingOptions),
                    );
                    return IconButton(
                      icon: Image.asset(
                        isShowingOptions
                            ? 'assets/images/chat-room/top.png'
                            : 'assets/images/chat-room/add.png',
                        width: 30.w,
                        height: 30.w,
                      ),
                      onPressed: () {
                        controller.toggleOptions();
                      },
                    );
                  },
                ),

                // 输入框
                Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.r),
                    decoration: BoxDecoration(
                      color: const Color(0xFFEFF1F6),
                      borderRadius: BorderRadius.circular(5.r),
                    ),
                    child: TextField(
                      controller: controller.messageController,
                      focusNode: controller.messageFocusNode,
                      maxLines: 1,
                      style: TextStyle(fontSize: 16.sp),
                      textDirection:
                          isUyghur ? TextDirection.rtl : TextDirection.ltr,
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: S.of(context).chat_room_input_hint,
                        hintStyle: TextStyle(
                          fontSize: 16.sp,
                          textBaseline: TextBaseline.alphabetic,
                        ),
                        hintTextDirection:
                            isUyghur ? TextDirection.rtl : TextDirection.ltr,
                      ),
                      onSubmitted: (final _) =>
                          controller.sendTextMessage(orderId),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
