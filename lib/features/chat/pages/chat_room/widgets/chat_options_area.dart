import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/chat/pages/chat_room/chat_room_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 聊天选项区域组件
class ChatOptionsArea extends ConsumerWidget {
  /// 构造函数
  const ChatOptionsArea({
    super.key,
    required this.orderId,
  });

  /// 订单ID
  final String orderId;

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final controller = ref.read(chatRoomControllerProvider.notifier);

    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(vertical: 10.r),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 相机选项
          _OptionItem(
            iconPath: 'assets/images/chat-room/camera.png',
            title: S.of(context).chat_room_camera,
            onTap: () => controller.takePhoto(orderId),
          ),

          // 相册选项
          _OptionItem(
            iconPath: 'assets/images/chat-room/album.png',
            title: S.of(context).chat_room_albom,
            onTap: () => controller.pickImage(orderId),
          ),
        ],
      ),
    );
  }
}

/// 选项项组件
class _OptionItem extends StatelessWidget {
  /// 构造函数
  const _OptionItem({
    required this.iconPath,
    required this.title,
    required this.onTap,
  });

  /// 图标路径
  final String iconPath;

  /// 标题
  final String title;

  /// 点击回调
  final VoidCallback onTap;

  @override
  Widget build(final BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 55.r,
            height: 55.r,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Image.asset(
              iconPath,
              width: 30.r,
              height: 30.r,
            ),
          ),
          SizedBox(height: 5.r),
          Text(
            title,
            style: TextStyle(fontSize: 15.sp),
          ),
        ],
      ),
    );
  }
}
