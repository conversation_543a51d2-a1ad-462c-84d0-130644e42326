import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 快捷回复按钮组件
class QuickReplyButton extends ConsumerWidget {
  /// 构造函数
  const QuickReplyButton({
    super.key,
    required this.text,
    required this.onTap,
  });

  /// 按钮文本
  final String text;

  /// 点击回调
  final VoidCallback onTap;

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.r),
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 12.r, vertical: 6.r),
          decoration: BoxDecoration(
            color: const Color(0xFFEFF1F6),
            borderRadius: BorderRadius.circular(50.r),
          ),
          child: Text(
            text,
            style: TextStyle(fontSize: 15.sp),
          ),
        ),
      ),
    );
  }
}
