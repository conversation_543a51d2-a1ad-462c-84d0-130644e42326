import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/chat/pages/chat_room/chat_room_controller.dart';
import 'package:user_app/features/chat/pages/chat_room/widgets/chat_message_item.dart';

/// 聊天消息列表组件
class ChatMessageList extends ConsumerWidget {
  /// 构造函数
  const ChatMessageList({
    super.key,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final messages = ref.watch(chatRoomControllerProvider.select((final value) => value.messages));
    final controller = ref.read(chatRoomControllerProvider.notifier);

    return Container(
      color: AppColors.backgroundColor,
      child: ListView.builder(
        controller: controller.scrollController,
        reverse: true, // 反转列表显示
        padding: EdgeInsets.only(
          left: 15.r,
          right: 15.r,
          top: 15.r,
          bottom: 5.r,
        ),
        itemCount: messages.length,
        itemBuilder: (final context, final index) {
          // 由于列表反转，我们需要反转索引
          final message = messages[messages.length - 1 - index];

          return Column(
            children: [
              // 相对时间显示
              if (message.relativeTime != null)
                Padding(
                  padding: EdgeInsets.only(bottom: 7.5.r),
                  child: Text(
                    message.relativeTime!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondColor,
                    ),
                    textDirection: TextDirection.ltr,
                  ),
                ),

              // 消息内容
              ChatMessageItem(
                message: message,
              ),
            ],
          );
        },
      ),
    );
  }
}
