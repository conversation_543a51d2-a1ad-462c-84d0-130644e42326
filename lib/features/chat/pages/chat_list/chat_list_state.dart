import 'package:user_app/data/models/chat/chat_model.dart';

/// 聊天列表页面状态
class ChatListState {
  /// 构造函数
  const ChatListState({
    this.isLoading = false,
    this.error,
    this.chatList = const [],
  });

  /// 是否正在加载
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 聊天列表
  final List<ChatItem> chatList;

  /// 复制状态
  ChatListState copyWith({
    bool? isLoading,
    String? error,
    List<ChatItem>? chatList,
  }) {
    return ChatListState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      chatList: chatList ?? this.chatList,
    );
  }
}
