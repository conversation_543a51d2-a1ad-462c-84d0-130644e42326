import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/empty_view.dart';
import 'package:user_app/data/models/chat/chat_model.dart';
import 'package:user_app/features/chat/pages/chat_list/widgets/chat_item.dart';
import 'package:user_app/generated/l10n.dart';

/// 聊天列表视图组件
class ChatListView extends ConsumerWidget {
  /// 构造函数
  const ChatListView({
    super.key,
    required this.chatList,
  });

  /// 聊天列表数据
  final List<ChatItem> chatList;

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    if (chatList.isEmpty) {
      return EmptyView(
        message: S.current.search_result_no,
      );
    }

    return ListView.builder(
      padding: EdgeInsets.only(bottom: 10.r),
      itemCount: chatList.length,
      itemBuilder: (final context, final index) {
        return ChatItemWidget(
          item: chatList[index],
        );
      },
    );
  }
}
