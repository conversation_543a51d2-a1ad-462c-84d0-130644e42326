import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/features/chat/pages/chat_list/chat_list_state.dart';
import 'package:user_app/features/chat/services/chat_service.dart';
import 'package:user_app/main.dart';

part 'chat_list_controller.g.dart';

/// 聊天列表控制器
@riverpod
class ChatListController extends _$ChatListController {
  @override
  ChatListState build() {
    Future.microtask(() {
      loadChatList();
    });

    /// 监听是否已登录
    ref.listen(isLoggedInProvider, (previous, next) {
      if (next) {
        Future.microtask(() => loadChatList());
      }
    });
    return const ChatListState();
  }

  /// 加载聊天列表
  Future<void> loadChatList() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true);

    try {
      final service = ref.read(chatServiceProvider);
      final chatList = await service.getChatList();

      state = state.copyWith(
        isLoading: false,
        chatList: chatList,
      );
    } catch (e) {
      if (kDebugMode) {
        print('获取聊天列表失败: $e');
      }

      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }
}
