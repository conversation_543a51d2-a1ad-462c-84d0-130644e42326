import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/data/models/home/<USER>';

/// 位置列表组件
class LocationListWidget extends ConsumerWidget {
  const LocationListWidget({
    Key? key,
    required this.pois,
    required this.onPoiSelected,
  }) : super(key: key);

  final List<PoiModel> pois;
  final Function(PoiModel) onPoiSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          children: List.generate(
            pois.length,
            (index) => _addressItem(index, pois),
          ),
        ),
      ),
    );
  }

  /// 地址列表项
  Widget _addressItem(int index, List<PoiModel> pois) {
    String distanceStr = Platform.isIOS
        ? (pois[index].distance / 1000).toStringAsFixed(3)
        : pois[index].distance.toStringAsFixed(3);

    return InkWell(
      onTap: () => onPoiSelected(pois[index]),
      child: Container(
        decoration: BoxDecoration(
          border:
              Border(bottom: BorderSide(width: 1, color: Color(0xffe5e5e5))),
        ),
        padding: EdgeInsets.all(10.h),
        margin: EdgeInsets.only(bottom: 10.h),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                    child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Text(
                    pois[index].name,
                    style: TextStyle(
                        fontSize: mainSize, fontWeight: FontWeight.bold),
                  ),
                )),
                SizedBox(),
              ],
            ),
            SizedBox(height: 6.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  '${distanceStr}km',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(' | '),
                Expanded(
                    child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Text(pois[index].address),
                ))
              ],
            )
          ],
        ),
      ),
    );
  }
}
