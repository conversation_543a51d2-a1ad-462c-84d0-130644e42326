import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/generated/l10n.dart';

/// 顶部操作栏组件
class AppBarWidget extends ConsumerWidget {
  const AppBarWidget({
    Key? key,
    required this.onBack,
    required this.onSend,
  }) : super(key: key);

  final VoidCallback onBack;
  final VoidCallback onSend;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Positioned(
      top: 40,
      right: 0,
      left: 0,
      child: Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 60,
              margin: EdgeInsets.symmetric(horizontal: 15.h),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    child: _buttonItemBack(Icon(Icons.arrow_back_ios)),
                    onTap: onBack,
                  ),
                  SizedBox.shrink(),
                ],
              ),
            ),
            InkWell(
              onTap: onSend,
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
                padding: EdgeInsets.symmetric(horizontal: 12.h, vertical: 5.h),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: AppColors.baseGreenColor,
                  borderRadius: BorderRadius.circular(5.r),
                ),
                child: Text(
                  S.current.send,
                  style: TextStyle(color: Colors.white, fontSize: mainSize),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  /// 返回按钮样式
  Widget _buttonItemBack(Widget widget) {
    return ClipOval(
      child: Container(
        width: 42.h,
        height: 42.h,
        alignment: Alignment.center,
        padding: EdgeInsets.only(right: 9.h, left: 1.h, top: 5.h, bottom: 5.h),
        child: widget,
      ),
    );
  }
}
