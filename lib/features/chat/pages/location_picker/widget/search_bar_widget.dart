import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/theme/icon_font.dart';

/// 搜索栏组件
class SearchBarWidget extends ConsumerWidget {
  const SearchBarWidget({
    Key? key,
    required this.controller,
    required this.isKeyboardVisible,
    required this.onSearch,
    required this.onClear,
    required this.onCancel,
  }) : super(key: key);

  final TextEditingController controller;
  final bool isKeyboardVisible;
  final Function(String) onSearch;
  final VoidCallback onClear;
  final VoidCallback onCancel;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        Expanded(
          child: Container(
            constraints: BoxConstraints(
              maxHeight: 150.h,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.r),
              color: AppColors.baseBackgroundColor,
            ),
            child: TextField(
              controller: controller,
              style: TextStyle(fontSize: 15.sp),
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.all(10.0),
                hoverColor: AppColors.baseBackgroundColor,
                fillColor: AppColors.baseBackgroundColor,
                filled: true,
                hintText: '搜索地点',
                hintStyle: TextStyle(
                    color: AppColors.textSecondaryColor, fontSize: 14.sp),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: AppColors.baseBackgroundColor,
                  ),
                ),
                prefixIcon: IconButton(
                  onPressed: () {},
                  icon: Icon(
                    IconFont.search,
                    size: 24.h,
                    color: AppColors.textSecondaryColor,
                  ),
                ),
                suffixIcon: IconButton(
                  onPressed: onClear,
                  icon: Icon(
                    Icons.cancel,
                    size: 18.h,
                    color: AppColors.textSecondaryColor,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: AppColors.baseGreenColor,
                  ),
                ),
              ),
              maxLines: 1,
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.text,
              onChanged: onSearch,
            ),
          ),
        ),
        if (isKeyboardVisible)
          InkWell(
            onTap: onCancel,
            child: Row(
              children: [
                SizedBox(width: 8.h),
                Container(
                    alignment: Alignment.center,
                    width: 40.h,
                    height: 40.h,
                    child: Text(
                      '取消',
                      style: TextStyle(fontSize: mainSize, color: Colors.blue),
                    ))
              ],
            ),
          )
      ],
    );
  }
}
