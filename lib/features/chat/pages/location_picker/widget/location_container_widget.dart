import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/chat/pages/location_picker/widget/location_list_widget.dart';
import 'package:user_app/features/chat/pages/location_picker/widget/search_bar_widget.dart';
import 'package:user_app/features/chat/pages/location_picker/location_picker_controller.dart';

/// 位置容器组件
class LocationContainerWidget extends ConsumerWidget {
  const LocationContainerWidget({
    Key? key,
    required this.searchController,
    required this.isKeyboardVisible,
    required this.pois,
    required this.onSearch,
    required this.onClear,
    required this.onCancel,
    required this.onPoiSelected,
  }) : super(key: key);

  final TextEditingController searchController;
  final bool isKeyboardVisible;
  final List<PoiModel> pois;
  final Function(String) onSearch;
  final VoidCallback onClear;
  final VoidCallback onCancel;
  final Function(PoiModel) onPoiSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 获取当前选中的地址
    final state = ref.watch(locationPickerControllerProvider);
    final selectedPoi = state.selectedPoi;
    
    // 决定显示哪个地址：优先显示选中的，其次显示列表第一个
    final displayPoi = selectedPoi ?? (pois.isNotEmpty ? pois.first : null);
    
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.h, vertical: 10.h),
        width: MediaQuery.of(context).size.width,
        height: 300.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            /// 当前选中的地址
            Container(
              height: 40.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.all(7.r),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(36.r),
                    ),
                    child: Icon(Icons.location_on, color: Colors.white,size: 20,)),
                  SizedBox(width: 10),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          displayPoi?.name ?? "选择位置",
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          displayPoi?.address ?? "请在下方列表中选择地址",
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey[600],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
            // 搜索栏
            SearchBarWidget(
              controller: searchController,
              isKeyboardVisible: isKeyboardVisible,
              onSearch: onSearch,
              onClear: onClear,
              onCancel: onCancel,
            ),

            SizedBox(height: 10.h),

            // POI列表
            LocationListWidget(
              pois: pois,
              onPoiSelected: onPoiSelected,
            ),
          ],
        ),
      ),
    );
  }
}
