import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 地图视图组件
class MapViewWidget extends ConsumerWidget {
  const MapViewWidget({
    Key? key,
    required this.currentLatitude,
    required this.currentLongitude,
    required this.isKeyboardVisible,
    required this.keyboardHeight,
    required this.locationInitialized,
    required this.onLocationButtonPressed,
  }) : super(key: key);

  final double currentLatitude;
  final double currentLongitude;
  final bool isKeyboardVisible;
  final double keyboardHeight;
  final bool locationInitialized;
  final VoidCallback onLocationButtonPressed;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 创建参数
    final creationParams = {
      'initial_latitude': currentLatitude,
      'initial_longitude': currentLongitude,
    };

    return Container(
      height: isKeyboardVisible
          ? MediaQuery.of(context).size.height - 300.h - keyboardHeight
          : MediaQuery.of(context).size.height - 300.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        color: Colors.grey[200],
      ),
      child: Stack(
        children: [
          // 地图视图
          Center(
            child: Platform.isIOS
                ? UiKitView(
                    viewType: "plugin/choice-position-view",
                    key: const ValueKey('location_picker_map'),
                  )
                : AndroidView(
                    viewType: '<choice-position-view>',
                    creationParams: creationParams,
                    creationParamsCodec: const StandardMessageCodec(),
                    key: const ValueKey('location_picker_map'),
                  ),
          ),

          // 中心标记图标
          Center(
            child: Container(
              margin: EdgeInsets.only(bottom: 50.h),
              child: Icon(
                Icons.location_on,
                size: 40,
                color: Colors.red,
              ),
            ),
          ),

          // 我的位置按钮
          // Positioned(
          //   bottom: 20.h,
          //   right: 15.h,
          //   child: InkWell(
          //     child: Container(
          //       width: 45.h,
          //       height: 45.h,
          //       decoration: BoxDecoration(
          //         color: Colors.white,
          //         borderRadius: BorderRadius.circular(8.r),
          //       ),
          //       child: Icon(
          //         Icons.my_location,
          //         color: Colors.blue,
          //         size: 26.sp,
          //       ),
          //     ),
          //     onTap: onLocationButtonPressed,
          //   ),
          // ),
        ],
      ),
    );
  }
}
