import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/chat/pages/location_picker/location_picker_controller.dart';
import 'package:user_app/features/chat/pages/location_picker/widget/app_bar_widget.dart';
import 'package:user_app/features/chat/pages/location_picker/widget/location_container_widget.dart';
import 'package:user_app/features/chat/pages/location_picker/widget/map_view_widget.dart';

class LocationPickerPage extends ConsumerStatefulWidget {
  const LocationPickerPage({
    super.key,
    required this.initialLatitude,
    required this.initialLongitude,
    required this.name,
    required this.address,
  });

  final num initialLatitude;
  final num initialLongitude;
  final String name;
  final String address;

  @override
  ConsumerState<LocationPickerPage> createState() => _ChoicePositionPageState();
}

class _ChoicePositionPageState extends ConsumerState<LocationPickerPage>
    with WidgetsBindingObserver {
  bool _isControllerInitialized = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // 使用 SchedulerBinding 延迟初始化，避免在构建过程中修改状态
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _initializeController();
    });
  }

  /// 初始化Controller
  Future<void> _initializeController() async {
    final controller = ref.read(locationPickerControllerProvider.notifier);
    await controller.initialize(
      context: context,
      initialLatitude: widget.initialLatitude,
      initialLongitude: widget.initialLongitude,
      name: widget.name,
      address: widget.address,
    );

    // 标记控制器已初始化
    if (mounted) {
      setState(() {
        _isControllerInitialized = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取状态
    final state = ref.watch(locationPickerControllerProvider);
    final controller = ref.read(locationPickerControllerProvider.notifier);

    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: AppColors.baseBackgroundColor,
      body: Stack(
        children: [
          // 主体内容
          Column(
            children: [
              // 地图部分 - 只有在控制器初始化完成后才显示
              if (_isControllerInitialized)
                MapViewWidget(
                  currentLatitude: state.currentLatitude,
                  currentLongitude: state.currentLongitude,
                  isKeyboardVisible: state.isKeyboardVisible,
                  keyboardHeight: state.keyboardHeight,
                  locationInitialized: state.locationInitialized,
                  onLocationButtonPressed: () {
                    if (state.locationInitialized) {
                      controller.goToMyPosition();
                    } else {
                      controller.getCurrentLocation(context);
                    }
                  },
                )
              else
                // 显示占位符，等待初始化完成
                Container(
                  height: MediaQuery.of(context).size.height - 300,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.grey[200],
                  ),
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),

              // 位置列表部分
              LocationContainerWidget(
                searchController: controller.searchController,
                isKeyboardVisible: state.isKeyboardVisible,
                pois: state.pois,
                onSearch: (val) {
                  controller.searchPOIs(val);
                },
                onClear: () {
                  controller.searchController.clear();
                },
                onCancel: () {
                  controller.searchController.clear();
                  FocusScope.of(context).unfocus();
                },
                onPoiSelected: (poi) {
                  controller.selectPoi(poi);
                  FocusScope.of(context).unfocus();
                },
              ),
            ],
          ),

          // 顶部操作栏
          AppBarWidget(
            onBack: () {
              Navigator.of(context).pop();
            },
            onSend: () {
              final result = controller.getLocationResult();
              debugPrint('=== Sending location result ===');
              debugPrint('Result: $result');
              debugPrint('==============================');
              Navigator.of(context).pop(result);
            },
          ),

          // 加载指示器
          if (state.isLoading) LoadingWidget(),
        ],
      ),
    );
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();

    if (!mounted) return;

    // 获取键盘高度
    final bottomInset = WidgetsBinding.instance.window.viewInsets.bottom;
    final devicePixelRatio = WidgetsBinding.instance.window.devicePixelRatio;
    final keyboardHeight = bottomInset / devicePixelRatio;

    // 更新控制器中的键盘高度
    ref
        .read(locationPickerControllerProvider.notifier)
        .updateKeyboardHeight(keyboardHeight);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
