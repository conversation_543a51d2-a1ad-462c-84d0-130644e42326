import 'package:user_app/data/models/home/<USER>';

/// 位置选择器状态类
class LocationPickerState {
  /// 构造函数
  const LocationPickerState({
    this.isLoading = false,
    this.error,
    this.pois = const [],
    this.currentLatitude = 0.0,
    this.currentLongitude = 0.0,
    this.selectedPoi,
    this.isKeyboardVisible = false,
    this.keyboardHeight = 0.0,
    this.locationInitialized = false,
    this.userManuallySelectedPoi = false,
  });

  /// 是否正在加载
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// POI列表
  final List<PoiModel> pois;

  /// 当前纬度
  final double currentLatitude;

  /// 当前经度
  final double currentLongitude;

  /// 选中的POI
  final PoiModel? selectedPoi;

  /// 键盘是否可见
  final bool isKeyboardVisible;

  /// 键盘高度
  final double keyboardHeight;

  /// 位置是否已初始化
  final bool locationInitialized;

  /// 用户是否手动选择了POI
  final bool userManuallySelectedPoi;

  /// 复制方法，创建新的状态实例
  LocationPickerState copyWith({
    bool? isLoading,
    String? error,
    List<PoiModel>? pois,
    double? currentLatitude,
    double? currentLongitude,
    PoiModel? selectedPoi,
    bool? isKeyboardVisible,
    double? keyboardHeight,
    bool? locationInitialized,
    bool? userManuallySelectedPoi,
  }) {
    return LocationPickerState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      pois: pois ?? this.pois,
      currentLatitude: currentLatitude ?? this.currentLatitude,
      currentLongitude: currentLongitude ?? this.currentLongitude,
      selectedPoi: selectedPoi ?? this.selectedPoi,
      isKeyboardVisible: isKeyboardVisible ?? this.isKeyboardVisible,
      keyboardHeight: keyboardHeight ?? this.keyboardHeight,
      locationInitialized: locationInitialized ?? this.locationInitialized,
      userManuallySelectedPoi: userManuallySelectedPoi ?? this.userManuallySelectedPoi,
    );
  }
}
