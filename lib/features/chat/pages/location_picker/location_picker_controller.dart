import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/utils/event_bus.dart';
import 'package:user_app/core/utils/location_util.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/chat/pages/location_picker/location_picker_state.dart';

/// 地图通信服务，处理与原生地图的通信
class MapCommunicationService {
  /// 平台通道
  final MethodChannel _channel;

  /// 构造函数
  MapCommunicationService({String channelName = 'choice_position_channel'})
      : _channel = MethodChannel(channelName);

  /// 获取通道
  MethodChannel get channel => _channel;

  /// 搜索POI
  Future<void> searchPOIs(String keyword) async {
    if (keyword.isEmpty) return;

    try {
      await _channel.invokeMethod('searchPOIs', keyword);
    } catch (e) {
      print("搜索POI异常: $e");
    }
  }

  /// 更新地图位置
  Future<void> changeMyPosition(Map<String, dynamic> positionData) async {
    try {
      await _channel.invokeMethod('myPosition', positionData);
    } catch (e) {
      print("我的位置异常: $e");
    }
  }

  /// 跳转到我的位置
  Future<void> goToMyPosition() async {
    try {
      await _channel.invokeMethod('goToMyPosition');
    } catch (e) {
      print("跳转到我的位置异常: $e");
    }
  }

  /// 切换位置项
  Future<void> changePositionItem(Map<String, dynamic> positionData) async {
    try {
      await _channel.invokeMethod('changePositionItem', positionData);
    } catch (e) {
      print("位置替换异常: $e");
    }
  }
}

/// 位置服务，处理位置获取和事件
class LocationService {
  /// 事件总线订阅
  StreamSubscription? _locationSubscription;

  /// 位置获取结果回调
  final Function(PoiModel) onLocationReceived;

  /// 位置错误回调
  final Function(String) onLocationError;

  /// 加载状态设置回调
  final Function(bool) setLoading;

  /// 构造函数
  LocationService({
    required this.onLocationReceived,
    required this.onLocationError,
    required this.setLoading,
  });

  /// 获取当前位置
  Future<void> getCurrentLocation(BuildContext context) async {
    try {
      // 取消之前的监听，避免重复监听
      cancelLocationSubscription();

      // 显示加载中
      setLoading(true);

      // 初始化位置服务
      final locationInstance = await LocationUtil.getInstance(context: context);

      // 只获取一次位置，不使用持续监听
      final completer = Completer<bool>();

      // 设置超时（10秒后如果没有获取到位置就超时）
      final timeoutTimer = Timer(const Duration(seconds: 10), () {
        if (!completer.isCompleted) {
          completer.complete(false);
          print("位置获取超时");
        }
      });

      // 检查位置权限
      final hasPermission = await locationInstance.checkLocationPermission();
      if (!hasPermission) {
        timeoutTimer.cancel();
        onLocationError("位置权限被拒绝");
        setLoading(false);
        return;
      }

      // 监听位置更新
      _locationSubscription =
          eventBus.on<EventLocationMap>().listen((final event) {
        try {
          final locationData = event.obj as Map<String, dynamic>;

          // 检查位置数据是否有效
          if (locationData['errorCode'] != null &&
              locationData['errorCode'] != 0) {
            final errorInfo = locationData['errorInfo'] ?? "未知位置错误";
            print("位置服务错误: ${locationData['errorCode']} - $errorInfo");

            // 只在第一次收到错误时完成completer
            if (!completer.isCompleted) {
              completer.complete(false);
            }
            return;
          }

          // 检查是否包含所需属性并转换为正确的类型
          final latitude = locationData['latitude'] != null
              ? double.tryParse(locationData['latitude'].toString())
              : null;
          final longitude = locationData['longitude'] != null
              ? double.tryParse(locationData['longitude'].toString())
              : null;
          final address = locationData['address']?.toString() ?? '';
          final name = locationData['name']?.toString() ?? '';

          // 检查坐标是否有效
          if (latitude == null ||
              longitude == null ||
              latitude == 0 ||
              longitude == 0) {
            print("位置坐标无效: lat=$latitude, lng=$longitude");
            return;
          }

          // 创建POI
          final poi = PoiModel(
            name: name.isNotEmpty ? name : "当前位置",
            address: address.isNotEmpty ? address : "未知地址",
            latitude: latitude,
            longitude: longitude,
            distance: 0,
          );

          // 更新位置
          onLocationReceived(poi);

          // 获取到位置后标记完成，只完成一次
          if (!completer.isCompleted) {
            completer.complete(true);
            // 获取到位置数据后，取消监听以防止持续更新
            timeoutTimer.cancel();
            // 立即取消位置监听，防止后续覆盖用户选择
            cancelLocationSubscription();
          }
        } catch (e) {
          debugPrint("位置数据处理错误: $e");
          if (!completer.isCompleted) {
            completer.complete(false);
          }
        }
      });

      // 确保位置服务已正确初始化
      await LocationUtil.getInstance(context: context);
      await LocationUtil.refreshLocation();
      // 等待位置获取结果或超时
      final result = await completer.future;
      if (!result) {
        print("未能获取到有效位置");
        onLocationError("未能获取到位置信息，请检查应用权限设置和网络连接");
      }

      // 隐藏加载中
      setLoading(false);
    } catch (e) {
      print("获取当前位置失败: $e");
      onLocationError("获取位置失败: $e");
      setLoading(false);
    }
  }

  /// 取消位置更新订阅
  void cancelLocationSubscription() {
    _locationSubscription?.cancel();
    _locationSubscription = null;
    print("位置订阅已取消");
  }

  /// 清理资源
  void dispose() {
    cancelLocationSubscription();
    LocationUtil.stopLocation();
  }
}

/// 位置选择器控制器
class LocationPickerController extends StateNotifier<LocationPickerState> {
  /// 地图通信服务
  final MapCommunicationService _mapService;

  /// 位置服务
  late LocationService _locationService;

  /// 搜索控制器
  final TextEditingController searchController = TextEditingController();

  /// 搜索框焦点
  final FocusNode searchFocusNode = FocusNode();

  /// 构造函数
  LocationPickerController()
      : _mapService = MapCommunicationService(),
        super(const LocationPickerState()) {
    // 初始化位置服务
    _locationService = LocationService(
      onLocationReceived: _handleLocationReceived,
      onLocationError: _handleLocationError,
      setLoading: _setLoading,
    );
  }

  /// 初始化方法
  Future<void> initialize({
    required BuildContext context,
    required num initialLatitude,
    required num initialLongitude,
    required String name,
    required String address,
  }) async {
    // 清理搜索框内容
    searchController.clear();
    
    // 检查是否需要获取当前位置
    final needsCurrentLocation =
        initialLatitude == 0.0 || initialLongitude == 0.0;

    // 初始化位置参数
    final currentLatitude = initialLatitude.toDouble();
    final currentLongitude = initialLongitude.toDouble();

    // 设置初始PoI
    final selectedPoi = PoiModel(
      name: name,
      address: address,
      latitude: currentLatitude,
      longitude: currentLongitude,
      distance: 0,
    );

    // 使用 Future.microtask 延迟状态更新，避免在构建过程中修改状态
    await Future.microtask(() {
      // 更新状态
      state = state.copyWith(
        currentLatitude: currentLatitude,
        currentLongitude: currentLongitude,
        selectedPoi: selectedPoi,
      );
    });

    // 设置通道处理程序
    setupChannelHandler();

    // 给地图组件一些时间来初始化
    await Future.delayed(const Duration(milliseconds: 500));

    // 如果需要获取当前位置
    if (needsCurrentLocation) {
      await _locationService.getCurrentLocation(context);
    } else {
      // 如果已有坐标，直接搜索该位置的POI
      await _mapService.changePositionItem({
        'latitude': currentLatitude,
        'longitude': currentLongitude,
      });
    }
  }

  /// 设置通道处理方法
  void setupChannelHandler() {
    try {
      // 监听地图中心位置变化
      _mapService.channel.setMethodCallHandler((call) async {
        if (call.method.toString() == 'updateCenterPosition') {
          //在这里注意json解码，有点坑，因为java返回的是Map<Object?,Object?>,不能直接用
          var positionEncodeVal = jsonEncode(call.arguments);
          Map<String, dynamic> position = jsonDecode(positionEncodeVal);
          _onCenterPositionUpdated(position);
        } else if (call.method.toString() == 'updatePOIs') {
          List<dynamic> addressList = call.arguments;
          final pois = addressList
              .map((data) => PoiModel.fromMap(Map<String, dynamic>.from(data)))
              .toList();

          updatePois(pois);
        } else {
          throw MissingPluginException('未实现的方法: ${call.method}');
        }
      });
    } catch (e) {
      // Handle errors gracefully
      print("Error setting up channel handler: $e");
    }
  }

  /// 更新加载状态
  void _setLoading(bool isLoading) {
    // 使用 Future.microtask 延迟状态更新，确保在正确的时机修改状态
    Future.microtask(() {
      state = state.copyWith(isLoading: isLoading);
    });
  }

  /// 处理位置错误
  void _handleLocationError(String error) {
    // 使用 Future.microtask 延迟状态更新，确保在正确的时机修改状态
    Future.microtask(() {
      state = state.copyWith(error: error);
    });
  }

  /// 处理位置接收
  void _handleLocationReceived(PoiModel poi) {
    debugPrint('=== _handleLocationReceived ===');
    debugPrint('Received POI: ${poi.name}');
    debugPrint('POI address: ${poi.address}');
    debugPrint('Current selectedPoi before update: ${state.selectedPoi?.name}');
    debugPrint('locationInitialized: ${state.locationInitialized}');
    debugPrint('userManuallySelectedPoi: ${state.userManuallySelectedPoi}');
    debugPrint('==============================');
    
    // 使用 Future.microtask 延迟状态更新，确保在正确的时机修改状态
    Future.microtask(() {
      // 只在位置未初始化或者用户没有手动选择POI时才更新selectedPoi
      if (!state.locationInitialized || !state.userManuallySelectedPoi) {
        state = state.copyWith(
          currentLatitude: poi.latitude,
          currentLongitude: poi.longitude,
          selectedPoi: poi,
          locationInitialized: true,
        );
        debugPrint('Updated selectedPoi to: ${poi.name}');
      } else {
        // 只更新坐标，不覆盖用户选择的POI
        state = state.copyWith(
          currentLatitude: poi.latitude,
          currentLongitude: poi.longitude,
          locationInitialized: true,
        );
        debugPrint('Kept existing selectedPoi: ${state.selectedPoi?.name}');
      }
    });

    // 不调用updateMapPosition，避免触发自动返回用户位置
    // iOS端会自动处理首次位置显示和POI搜索
  }

  /// 获取当前位置
  Future<void> getCurrentLocation(BuildContext context) async {
    await _locationService.getCurrentLocation(context);
  }

  /// 中心位置更新处理
  void _onCenterPositionUpdated(Map<String, dynamic> position) {
    double latitude = position['latitude'];
    double longitude = position['longitude'];
    debugPrint('地图中心位置更新：纬度: $latitude, 经度: $longitude');

    // 检查位置是否真的发生了显著变化（超过100米）
    final currentLat = state.currentLatitude;
    final currentLng = state.currentLongitude;
    final distance = _calculateDistance(currentLat, currentLng, latitude, longitude);
    
    debugPrint('Position change distance: ${distance}m');
    debugPrint('Current selectedPoi before position update: ${state.selectedPoi?.name}');
    
    // 使用 Future.microtask 延迟状态更新，确保在正确的时机修改状态
    Future.microtask(() {
      // 只有当位置发生显著变化时才重置手动选择状态
      if (distance > 200) { // 超过200米认为是用户拖动地图
        debugPrint('Significant position change detected, resetting manual selection');
        state = state.copyWith(
          currentLatitude: latitude,
          currentLongitude: longitude,
          userManuallySelectedPoi: false, // 重置手动选择状态
        );
      } else {
        // 小幅度变化，只更新坐标，但不影响任何选择状态
        debugPrint('Small position change, keeping current selection');
        state = state.copyWith(
          currentLatitude: latitude,
          currentLongitude: longitude,
          // 注意：不改变 userManuallySelectedPoi 和 selectedPoi
        );
      }
      
      debugPrint('selectedPoi after position update: ${state.selectedPoi?.name}');
    });
  }
  
  /// 计算两点间距离（米）
  double _calculateDistance(double lat1, double lng1, double lat2, double lng2) {
    if (lat1 == 0 && lng1 == 0) return double.infinity; // 初始状态
    
    const double earthRadius = 6371000; // 地球半径（米）
    final double dLat = (lat2 - lat1) * (3.14159 / 180);
    final double dLng = (lng2 - lng1) * (3.14159 / 180);
    final double a = (math.sin(dLat / 2) * math.sin(dLat / 2)) +
        (math.cos(lat1 * (3.14159 / 180)) * math.cos(lat2 * (3.14159 / 180)) *
         math.sin(dLng / 2) * math.sin(dLng / 2));
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }

  /// 更新POI列表
  void updatePois(List<PoiModel> pois) {
    debugPrint('=== updatePois START ===');
    debugPrint('POI count: ${pois.length}');
    debugPrint('userManuallySelectedPoi BEFORE: ${state.userManuallySelectedPoi}');
    debugPrint('selectedPoi BEFORE: ${state.selectedPoi?.name}');
    
    // 使用 Future.microtask 延迟状态更新，确保在正确的时机修改状态
    Future.microtask(() {
      state = state.copyWith(pois: pois);

      // 只有在用户没有手动选择POI且位置未初始化时，才自动选择第一个POI
      if (!state.userManuallySelectedPoi && !state.locationInitialized) {
        debugPrint('Auto-selecting POI because user has not manually selected and location not initialized');
        if (pois.isNotEmpty) {
          state = state.copyWith(
            selectedPoi: PoiModel(
              name: pois[0].name,
              address: pois[0].address,
              latitude: pois[0].latitude,
              longitude: pois[0].longitude,
              distance: pois[0].distance,
            ),
            locationInitialized: true,
          );
          debugPrint('Auto-selected POI: ${pois[0].name}');
        } else {
          // 如果没有POI，使用当前地图中心位置
          final centerPoi = PoiModel(
            name: "选中位置",
            address: "纬度: ${state.currentLatitude.toStringAsFixed(6)}, 经度: ${state.currentLongitude.toStringAsFixed(6)}",
            latitude: state.currentLatitude,
            longitude: state.currentLongitude,
            distance: 0,
          );
          
          state = state.copyWith(
            selectedPoi: centerPoi,
            locationInitialized: true,
          );
          debugPrint('Used center POI: ${centerPoi.name}');
        }
      } else {
        debugPrint('Keeping existing selectedPoi: ${state.selectedPoi?.name} (userManuallySelectedPoi: ${state.userManuallySelectedPoi}, locationInitialized: ${state.locationInitialized})');
      }
      
      debugPrint('=== updatePois END ===');
      debugPrint('POI count: ${pois.length}');
      debugPrint('userManuallySelectedPoi AFTER: ${state.userManuallySelectedPoi}');
      debugPrint('selectedPoi AFTER: ${state.selectedPoi?.name}');
      debugPrint('==================');
    });
  }

  /// 选择POI
  Future<void> selectPoi(PoiModel poi) async {
    debugPrint('=== selectPoi ===');
    debugPrint('User selected POI: ${poi.name}');
    debugPrint('POI address: ${poi.address}');
    debugPrint('POI coordinates: ${poi.latitude}, ${poi.longitude}');
    debugPrint('=================');
    
    try {
      // 立即停止位置服务，防止覆盖用户选择
      _locationService.cancelLocationSubscription();
      
      // 使用 Future.microtask 延迟状态更新，确保在正确的时机修改状态
      await Future.microtask(() {
        // 更新选中的POI，并标记为用户手动选择
        state = state.copyWith(
          selectedPoi: poi,
          userManuallySelectedPoi: true,
        );
      });

      // 更新地图位置
      await _mapService.changePositionItem({
        'latitude': poi.latitude,
        'longitude': poi.longitude,
      });
      
      debugPrint('POI selected and location service stopped. Final selectedPoi: ${state.selectedPoi?.name}');
    } catch (e) {
      debugPrint('Error selecting POI: $e');
      // 即使地图更新失败，也要保持用户的选择
    }
  }

  /// 更新地图位置
  Future<void> updateMapPosition() async {
    final params = {
      'initial_latitude': state.currentLatitude,
      'initial_longitude': state.currentLongitude,
    };

    await _mapService.changeMyPosition(params);
  }

  /// 跳转到我的位置
  Future<void> goToMyPosition() async {
    // 使用 Future.microtask 延迟状态更新，确保在正确的时机修改状态
    await Future.microtask(() {
      // 重置手动选择状态，允许自动选择新的POI
      state = state.copyWith(userManuallySelectedPoi: false);
    });
    await _mapService.goToMyPosition();
  }

  /// 处理位置回调操作
  Map<String, dynamic> getLocationResult() {
    debugPrint('=== getLocationResult ===');
    debugPrint('selectedPoi: ${state.selectedPoi?.name}');
    debugPrint('selectedPoi address: ${state.selectedPoi?.address}');
    debugPrint('selectedPoi lat: ${state.selectedPoi?.latitude}');
    debugPrint('selectedPoi lng: ${state.selectedPoi?.longitude}');
    debugPrint('userManuallySelectedPoi: ${state.userManuallySelectedPoi}');
    debugPrint('========================');
    
    return {
      'addr': state.selectedPoi?.name ?? '',
      'full_addr': state.selectedPoi?.address ?? '',
      'lat': state.selectedPoi?.latitude ?? '',
      'lng': state.selectedPoi?.longitude ?? '',
      'distance': 0
    };
  }

  /// 搜索POI
  Future<void> searchPOIs(String keyword) async {
    await _mapService.searchPOIs(keyword);
  }

  /// 更新键盘高度
  void updateKeyboardHeight(double height) {
    // 使用 Future.microtask 延迟状态更新，确保在正确的时机修改状态
    Future.microtask(() {
      state = state.copyWith(
        keyboardHeight: height,
        isKeyboardVisible: height > 0,
      );
    });
  }

  /// 取消位置更新订阅
  void cancelLocationSubscription() {
    _locationService.cancelLocationSubscription();
  }

  /// 清理资源
  @override
  void dispose() {
    _locationService.dispose();
    searchController.dispose();
    searchFocusNode.dispose();
    super.dispose();
  }
}

/// 位置选择器控制器提供者
final locationPickerControllerProvider =
    StateNotifierProvider<LocationPickerController, LocationPickerState>((ref) {
  return LocationPickerController();
});
