import 'dart:async';
// import 'dart:developer' as dev;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/app/app.dart';
import 'package:user_app/core/app/app_init.dart';
import 'package:user_app/core/config/environment_config.dart';
import 'package:user_app/core/utils/aliyun_sls.dart';
import 'package:user_app/core/utils/status_bar_util.dart';
import 'package:user_app/features/auth/pages/login/login_controller_provider.dart';

/// 创建一个全局的ProviderContainer，用于在整个应用中共享状态
final globalContainer = ProviderContainer(
  overrides: [],
);

/// 创建一个简单的全局登录状态提供者
final isLoggedInProvider = StateProvider<bool>((final ref) => false);

void main() async {
  // 处理命令行参数中的环境设置
  const envArg = String.fromEnvironment('ENV');
  if (envArg.isNotEmpty) {
    EnvironmentConfig.setForcedEnvironment(envArg);
  }

  // 设置全局错误处理
  FlutterError.onError = (final FlutterErrorDetails details) {
    // 初始化阿里云日志服务
    FlutterError.presentError(details);
    debugPrint('Flutter Error: ${details.exception}\n${details.stack}');
    if (kReleaseMode) {
      final errorMessage =
          'Error: $details.exception\nStackTrace: $details.stack';
      AliyunSls().sendLog(
        'user_flutter异常捕获',
        {'exception_content': errorMessage},
        'error',
      );
    }
  };

  // 在同一个Zone中初始化Flutter绑定和运行应用程序
  runZonedGuarded(
    () async {
      // 确保Flutter初始化完成
      WidgetsFlutterBinding.ensureInitialized();

      // 设置全局状态栏样式 - 必须在runApp之前调用
      StatusBarUtil.setDarkMode();


      // 设置屏幕方向为竖屏
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
      ]);

      // 初始化应用所需的服务和配置
      await AppInit.init();

      // 预取登录状态
      await globalContainer
          .read(loginControllerProvider.notifier)
          .checkLoginStatus();

      // 获取登录状态并设置到全局状态提供者
      final loginState = globalContainer.read(loginControllerProvider);
      globalContainer.read(isLoggedInProvider.notifier).state =
          loginState.isLoggedIn;

      debugPrint(
        "应用启动 - 登录状态: ${loginState.isLoggedIn}, 全局状态: ${globalContainer.read(isLoggedInProvider)}, Token: ${loginState.token}",
      );

      // 运行应用
      runApp(
        UncontrolledProviderScope(
          container: globalContainer,
          child: const App(),
        ),
      );
    },
    (final error, final stack) {
      final errorMessage = 'Error: $error\nStackTrace: $stack';
      debugPrint('errorMessage ----》 $errorMessage');
      if (kReleaseMode) {
        AliyunSls().sendLog(
          'user_flutter异常捕获',
          {'exception_content': errorMessage},
          'error',
        );
      }
      // 显示友好的错误提示
    },
  );
}
