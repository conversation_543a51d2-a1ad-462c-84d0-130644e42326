import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/widgets/empty_view.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/features/activity/pages/user_ranking/ranking_history_page.dart';
import 'package:user_app/features/address/pages/add_address_page.dart';
import 'package:user_app/features/address/pages/my_address_page.dart';
import 'package:user_app/features/chat/pages/location_picker/location_picker_page.dart';
import 'package:user_app/features/home/<USER>/privacy_page.dart';
import 'package:user_app/features/home/<USER>/select_language_page.dart';
import 'package:user_app/features/mine/pages/add_shop/add_shop_page.dart';
import 'package:user_app/features/mine/pages/collect/collect_page.dart';
import 'package:user_app/features/mine/pages/courtesy/courtesy_page.dart';
import 'package:user_app/features/mine/pages/help/help_page.dart';
import 'package:user_app/features/mine/pages/integral/integral_page.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/order_ranking_my_page.dart';
import 'package:user_app/features/mine/pages/order_ranking_prize/order_ranking_prize_page.dart';
import 'package:user_app/features/mine/pages/profile/profile_page.dart';
import 'package:user_app/features/mine/pages/red_packet/red_packet_page.dart';
import 'package:user_app/features/mine/pages/url_config/url_config_page.dart';
import 'package:user_app/features/mine/pages/video/video_page.dart';
import 'package:user_app/features/my_order/pages/agent_pay/agent_pay_page.dart';
import 'package:user_app/features/my_order/pages/index/my_order_page.dart';
import 'package:user_app/features/restaurant/pages/license/business_license_page.dart';
import 'package:user_app/features/web_view/pages/web/web_view_page.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart'; // 导入main.dart以访问全局状态提供者
import 'package:user_app/routes/index.dart';
import 'package:user_app/features/mine/pages/license/agent_license_page.dart';
import 'package:user_app/features/mine/pages/my_evaluate/my_evaluate_page.dart';
import 'package:user_app/features/mine/pages/activity_order_list/activity_order_list_page.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/order_evaluate_page.dart';
import 'package:user_app/features/shipper/pages/shipper_detail/shipper_detail_page.dart';
import 'package:user_app/features/shipper/pages/shipper_tips_history/shipper_tips_history_page.dart';
import 'package:user_app/features/chat/pages/chat_list/chat_list_page.dart';
import 'package:user_app/features/chat/pages/chat_room/chat_room_page.dart';
import 'package:user_app/features/restaurant/pages/poster/poster_page.dart';
import 'package:user_app/features/mine/pages/order_ranking_preview/order_ranking_preview_page.dart';
import 'package:user_app/features/activity/pages/user_ranking/user_ranking_page.dart';

// 定义需要登录才能访问的路径
final List<String> _protectedPaths = [
  AppPaths.submitOrderPage,
  AppPaths.profilePage,
  AppPaths.integralPage,
  AppPaths.collectPage,
  AppPaths.redPacketPage, // 添加红包页面到需要登录的路径列表
  AppPaths.courtesyPage,
  AppPaths.myEvaluatePage,
  AppPaths.orderEvaluatePage,
  AppPaths.activityOrderList,
  AppPaths.orderRankingMy,
  AppPaths.chatListPage, // 聊天列表需要登录
  AppPaths.chatRoomPage, // 聊天室需要登录
  AppPaths.myAddressPage,
  AppPaths.addAddressPage,
  AppPaths.myOrdersPage,
  AppPaths.agentPay,
  AppPaths.orderRankingPrizePage,
  AppPaths.submitOrderPage,
  AppPaths.businessLicensePage,
  AppPaths.userRankingPage, // 用户排行榜页面需要登录

  // 其他需要登录才能访问的路径...
];

// 获取初始路由
String _getInitialRoute() {
  // 检查是否已经选择过语言
  final storageService = globalContainer.read(localStorageRepositoryProvider);
  final hasSelectedLanguage = storageService.getLang() != null;
  // 检查是否同意隐私政策
  final isAgreePrivacyPolicy = storageService.getIsAgreePrivacyPolicy();
  if (!isAgreePrivacyPolicy) {
    return AppPaths.selectLanguagePage;
  }

  // 如果已经选择过语言，返回主页面路径，否则返回语言选择页面路径
  return hasSelectedLanguage ? AppPaths.mainPage : AppPaths.selectLanguagePage;
}

/// 路由监听器类 - 用于跟踪当前路由状态
class AppRouteObserver extends NavigatorObserver {
  static final AppRouteObserver _instance = AppRouteObserver._();
  factory AppRouteObserver() => _instance;
  AppRouteObserver._();

  String _currentRouteName = '';
  Map<String, dynamic>? _currentRouteArguments;

  /// 获取当前路由名称
  String get currentRouteName => _currentRouteName;

  /// 获取当前路由参数
  Map<String, dynamic>? get currentRouteArguments => _currentRouteArguments;

  @override
  void didPush(
      final Route<dynamic> route, final Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    _updateCurrentRoute(route);
  }

  @override
  void didReplace(
      {final Route<dynamic>? newRoute, final Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    if (newRoute != null) {
      _updateCurrentRoute(newRoute);
    }
  }

  @override
  void didPop(final Route<dynamic> route, final Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    if (previousRoute != null) {
      _updateCurrentRoute(previousRoute);
    }
  }

  void _updateCurrentRoute(final Route<dynamic> route) {
    // 获取路由名称
    final routeName = route.settings.name ?? '';

    // 获取路由参数
    final arguments = route.settings.arguments;
    Map<String, dynamic>? routeArgs;

    if (arguments is Map<String, dynamic>) {
      routeArgs = arguments;
    } else if (arguments != null) {
      // 如果参数不是 Map，尝试转换
      routeArgs = {'data': arguments};
    }

    _currentRouteName = routeName;
    _currentRouteArguments = routeArgs;

    if (kDebugMode) {
      print('路由变化: $routeName, 参数: $routeArgs');
    }
  }
}

/// 创建路由器函数
GoRouter createRouter({final List<NavigatorObserver>? observers}) {
  // 创建包含AppContext routeObserver和BotToastNavigatorObserver的观察者列表
  final List<NavigatorObserver> allObservers = [
    AppContext().routeObserver,
    BotToastNavigatorObserver(), // 添加BotToast导航观察者
    AppRouteObserver(), // 添加自定义路由观察者
    ...(observers ?? []),
  ];

  return GoRouter(
    navigatorKey: AppContext().navigatorKey,
    debugLogDiagnostics: true,
    initialLocation: _getInitialRoute(),
    observers: allObservers,
    routes: [
      GoRoute(
        path: AppPaths.selectLanguagePage,
        builder: (final context, final state) => const SelectLanguagePage(),
      ),
      GoRoute(
        path: AppPaths.mainPage,
        builder: (final context, final state) => const MainPage(),
      ),
      GoRoute(
        path: AppPaths.restaurantDetailPage,
        builder: (final context, final state) {
          // 获取传递的参数
          final params = state.extra as Map<String, dynamic>?;
          return RestaurantDetailPage(
            restaurantId: params?['restaurantId'] as int,
            buildingId: params?['buildingId'] as int,
            ids: params?['ids'] as List<dynamic>? ?? [],
            comeAgainItems: params?['comeAgainItems'] as List<dynamic>? ?? [],
          );
        },
      ),
      GoRoute(
        path: AppPaths.submitOrderPage,
        builder: (final context, final state) {
          /// 获取传递的参数
          final params = state.extra as Map<String, dynamic>?;

          /// 订单提交页面参数
          return SubmitOrderPage(
            restaurantId: params?['restaurantId'] as int,
            buildingId: params?['buildingId'] as int,
            homeBuildingId: params?['homeBuildingId'] as int,
          );
        },
      ),
      GoRoute(
        path: AppPaths.paymentPage,
        builder: (final context, final state) {
          final extra = state.extra as Map<String, dynamic>?;
          return PaymentPage(orderData: extra);
        },
      ),
      GoRoute(
        path: AppPaths.login,
        builder: (final context, final state) => const LoginPage(),
      ),
      GoRoute(
        path: AppPaths.searchPage,
        builder: (final context, final state) => const SearchPage(),
      ),
      GoRoute(
        path: AppPaths.agentPay,
        builder: (final context, final state) {
          final extra = state.extra as Map<String, dynamic>?;
          return AgentPayPage(param: extra);
        },
      ),

      /// 个人中心页
      GoRoute(
        path: AppPaths.profilePage,
        builder: (final context, final state) => const ProfilePage(),
      ),

      /// 积分页
      GoRoute(
        path: AppPaths.integralPage,
        builder: (final context, final state) => const IntegralPage(),
      ),

      /// Web页面
      GoRoute(
        path: AppPaths.webViewPage,
        builder: (final context, final state) {
          final extra = state.extra as Map<String, dynamic>?;
          return WebPage(
            title: extra?['title']?.toString() ?? S.current.about_info,
            url: Uri.decodeComponent(extra?['url']?.toString() ?? ''),
          );
        },
      ),

      /// 收藏页
      GoRoute(
        path: AppPaths.collectPage,
        builder: (final context, final state) => const CollectPage(),
      ),

      /// 我的地址页
      GoRoute(
        path: AppPaths.myAddressPage,
        builder: (final context, final state) => const MyAddressPage(),
      ),

      /// 我的地址页
      GoRoute(
        path: AppPaths.addAddressPage,
        builder: (final context, final state) {
          final extra = state.extra as Map<String, dynamic>?;
          return AddAddressPage(
            buildingName: extra?['buildingName'] ?? '',
            buildingNameZh: extra?['buildingNameZh'] ?? '',
            buildingId: extra?['buildingId'] ?? 0,
            address: extra?['address'] ?? '',
            name: extra?['name'] ?? '',
            tel: extra?['tel'] ?? '',
            addressId: extra?['addressId'] ?? 0,
            areaName: extra?['areaName'] ?? '',
          );
        },
      ),

      ///视频播放页
      GoRoute(
        path: AppPaths.videoPage,
        builder: (final context, final state) => const VideoPage(),
      ),

      ///帮助页
      GoRoute(
        path: AppPaths.helpPage,
        builder: (final context, final state) => const HelpPage(),
      ),

      /// 代理资质页面
      GoRoute(
        path: AppPaths.licensePage,
        builder: (final context, final state) => const AgentLicensePage(),
      ),

      /// 红包页面
      GoRoute(
        path: AppPaths.redPacketPage,
        builder: (final context, final state) => const RedPacketPage(),
      ),

      /// 优惠券页面
      GoRoute(
        path: AppPaths.courtesyPage,
        builder: (final context, final state) => const CourtesyPage(),
      ),

      /// 商家入驻
      GoRoute(
        path: AppPaths.addShopPage,
        builder: (final context, final state) => const AddShopPage(),
      ),

      /// 我的评价页面
      GoRoute(
        path: AppPaths.myEvaluatePage,
        builder: (final context, final state) => const MyEvaluatePage(),
      ),

      /// 订单评价页面
      GoRoute(
        path: AppPaths.orderEvaluatePage,
        builder: (final context, final state) {
          final extra = state.extra as Map<String, dynamic>?;
          return OrderEvaluatePage(
            orderId: extra?['orderId'] as int,
            fromPage: extra?['fromPage'] as String? ?? '',
          );
        },
      ),

      /// 活动订单列表页面
      GoRoute(
        path: AppPaths.activityOrderList,
        builder: (final context, final state) => const ActivityOrderListPage(),
      ),

      ///视频播放页
      GoRoute(
        path: AppPaths.myOrdersPage,
        builder: (final context, final state) => MyOrderPage(),
      ),

      /// 配送员详情页面
      GoRoute(
        path: AppPaths.shipperDetailPage,
        builder: (final context, final state) {
          final extra = state.extra as Map<String, dynamic>?;
          return ShipperDetailPage(
            shipperId: extra?['shipperId'] as int,
            orderId: extra?['orderId'] as int?,
          );
        },
      ),

      /// 配送员打赏历史页面
      GoRoute(
        path: AppPaths.shipperTipsHistoryPage,
        builder: (final context, final state) {
          final shipperId = int.parse(state.pathParameters['shipperId'] ?? '0');
          return ShipperTipsHistoryPage(shipperId: shipperId);
        },
      ),

      /// 聊天列表页面
      GoRoute(
        path: AppPaths.chatListPage,
        builder: (final context, final state) => const ChatListPage(),
      ),

      /// 聊天室页面
      GoRoute(
        path: AppPaths.chatRoomPage,
        builder: (final context, final state) {
          final extra = state.extra as Map<String, dynamic>?;
          return ChatRoomPage(
            orderId: extra?['order_id'] as String? ?? '',
            name: extra?['name'] as String? ?? '',
          );
        },
      ),

      /// 位置选择页面
      GoRoute(
        path: AppPaths.locationPickerPage,
        builder: (final context, final state) {
          final extra = state.extra as Map<String, dynamic>?;
          return LocationPickerPage(
            initialLatitude: extra?['latitude'] as num,
            initialLongitude: extra?['longitude'] as num,
            name: extra?['name'] as String,
            address: extra?['address'] as String,
          );
        },
      ),

      // 海报页面
      GoRoute(
        path: AppPaths.posterPage,
        builder: (final context, final state) {
          final resId = state.extra != null
              ? (state.extra as Map)['resId'] as String
              : '';
          final type =
              state.extra != null ? (state.extra as Map)['type'] as int : 1;
          return PosterPage(resId: resId, type: type);
        },
      ),

      /// 订单排名活动（3.8妇女节）
      GoRoute(
        path: AppPaths.orderRankingMy,
        builder: (final context, final state) => const OrderRankingMyPage(),
      ),

      /// 奖品列表页
      GoRoute(
        path: AppPaths.orderRankingPrizePage,
        builder: (final context, final state) =>
            const OrderRankingPrizePage(), // 临时使用同一页面
      ),

      /// 规则预览页
      GoRoute(
        path: AppPaths.orderRankingPreviewPage,
        builder: (final context, final state) =>
            const OrderRankingPreviewPage(),
      ),

      /// 订单排名活动（3.8妇女节）
      GoRoute(
        path: AppPaths.privacyPage,
        builder: (final context, final state) => const PrivacyPage(),
      ),

      /// 设置页面
      GoRoute(
        path: AppPaths.urlConfigPage,
        builder: (final context, final state) => const UrlConfigPage(),
      ),

      /// 资质证书页面
      GoRoute(
        path: AppPaths.businessLicensePage,
        builder: (final context, final state) {
          final extra = state.extra as Map<String, dynamic>?;
          return BusinessLicensePage(
            restaurantId: extra?['restaurant_id'],
          );
        },
      ),

      /// 用户排行榜页面
      GoRoute(
        path: AppPaths.userRankingPage,
        builder: (final context, final state) {
          final extra = state.extra as Map<String, dynamic>?;
          return UserRankingPage(
            params: extra ?? {},
          );
        },
      ),

      /// 获奖记录页面
      GoRoute(
        path: AppPaths.rankingHistoryPage,
        builder: (final context, final state) => const RankingHistoryPage(),
      ),
    ],

    // 添加重定向逻辑
    redirect: (final BuildContext context, final GoRouterState state) {
      // 获取当前路径
      final String currentPath = state.matchedLocation;

      // 检查是否是登录页面
      final bool isLoginPage = currentPath.startsWith(AppPaths.login);

      // 如果已经在登录页面，直接返回null，避免重复导航
      if (isLoginPage) {
        if (kDebugMode) {
          print("已在登录页面，跳过重定向检查");
        }
        return null;
      }

      // 检查是否是受保护的路径
      final bool requiresAuth = _protectedPaths.contains(currentPath);

      // 如果不需要认证，直接返回null（不重定向）
      if (!requiresAuth) {
        return null;
      }

      // 直接从全局状态提供者获取登录状态
      final bool isLoggedIn = globalContainer.read(isLoggedInProvider);

      if (kDebugMode) {
        // 打印调试信息
        print(
            "路由重定向检查 - 路径: $currentPath, 是否需要认证: $requiresAuth, 是否已登录: $isLoggedIn");
      }

      // 如果需要认证但用户未登录，重定向到登录页面
      if (requiresAuth && !isLoggedIn) {
        // 将当前路径作为参数传递给登录页面，以便登录成功后返回
        return '${AppPaths.login}?from=$currentPath';
      }

      // 用户已登录或不需要认证，不重定向
      return null;
    },

    // 错误页面
    errorBuilder: (final context, final state) => Scaffold(
      body: EmptyView(
        message: S.current.page_not_found,
        retryMessage: S.current.go_home,
        onRetry: () {
          context.go(AppPaths.mainPage);
        },
      ),
    ),
  );
}

/// 默认路由器实例
final router = createRouter();
