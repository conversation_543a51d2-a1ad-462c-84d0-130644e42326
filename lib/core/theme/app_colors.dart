import 'package:flutter/material.dart';

/// 应用颜色
class AppColors {
  /// 红色主题
  static const Color redColor = Color(0xFFFF4B56);

  /// 文本主色
  static const Color textPrimaryColor = Color(0xFF000000);

  /// 文本次色
  static const Color textSecondColor = Color(0xFF666666);
  static const Color homeSearchIconColor = Color(0xFFBDBDBD);
  static const Color homeSearchPlaceHolderColor = Color(0xFF8D8C8C);
  static const Color hotTextColor = Color(0xFF2F2F2F);

  /// 背景色
  static const Color baseBackgroundColor = Color(0xFFEFF1F6);

  /// 分隔线颜色
  static const Color dividerColor = Color(0xFFEEEEEE);

  static const primary = Color(0xff15C45B);
  static const backgroundColor = Color(0xFFF5F5F5);
  static const textHintColor = Color(0xFF999999); // 提示文字颜色
  // static const baseGreenColor = Color(0xFF15c45b);
  static const baseGreenColor = Color(0xFF15C45B);
  static const searchBackColor = Color(0xFFd8e1f4);
  static const textSecondaryColor = Color(0xFF666666); // 次要文字颜色
  static const baseOrangeColor = Color(0xFFff632f);
  static const starBgColor = Color(0xFFfff2e8);
  static const primaryGreenColor = Color(0xFF10C35A);
  static const baseYellowColor = Color(0xFFffd200);
  static const secOrangeColor = Color(0xFFfc532d);
  static const littleGreenColor = Color(0xff86edb3);
  static const basePinkColor = Color(0xFFfb3455);
  static const restaurantBgStartColor = Color(0xFFff893d);
  static const restaurantBgEndColor = Color(0xFFff1210);
  static const languageBgColor = Color(0xFF13b253);
  static const languagePageBgColor = Color(0xFFEFF1F6);
  static const secondGreenColor = Color(0xFFd2ffd8);
  static const couponBackground = Color(0xFFCFF6E0); // 优惠券背景颜色
  static const foodPriceColor = Color(0xFFFF9371B);
  static const grayColor = Color(0xFFC5C5C5);
  static const dialogDividerColor = Color(0xFFE0E0E0); // 对话框分割线颜色
  static const dialogTextColor = Color(0xDD000000); // 对话框文本颜色
  static const dialogCancelColor = Color(0xFF757575); // 对话框取消按钮颜色
  static const discountRedColor = Color(0xFFFA3024); // 对话框取消按钮颜色
  static const discountRedSeColor = Color(0xfff6875a); // 对话框取消按钮颜色
  static const borderColor = Color(0xFFEFF1F6);
  static const baseBlueColor = Color(0xFF2D7AFF);
  static const baseOrangeColor2 = Color(0xFFff7f00);
  static const btnBackGroundOrange = Color(0xFFFFF7EE);
  static const panelBackGroundOrange = Color(0xFFFFECD1);
  static const panelTextOrange = Color(0xFFFF6B03);
  static const couponPriceColor = Color(0xFFffdba8);
  static const couponbtnColor1 = Color(0xFFff8829);
  static const couponbtnColor2 = Color(0xFFffa411);
}
