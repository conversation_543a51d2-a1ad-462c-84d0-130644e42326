import 'package:flutter/widgets.dart';

class IconFont {
  static const String _fontFamily = 'IconFont';

  static const IconData pay = IconData(0xe630, fontFamily: _fontFamily);
  static const IconData commentW = IconData(0xe62b, fontFamily: _fontFamily);
  static const IconData xiadan = IconData(0xe62f, fontFamily: _fontFamily);
  static const IconData quxiaodingdan = IconData(0xe625, fontFamily: _fontFamily);
  static const IconData dashang = IconData(0xe6c7, fontFamily: _fontFamily);
  static const IconData ziyuan = IconData(0xe6a4, fontFamily: _fontFamily);
  static const IconData deleteFilling = IconData(0xe68a, fontFamily: _fontFamily);
  static const IconData shanchu = IconData(0xe626, fontFamily: _fontFamily);
  static const IconData xiangji = IconData(0xe619, fontFamily: _fontFamily);
  static const IconData emm = IconData(0xe678, fontFamily: _fontFamily);
  static const IconData weixiao = IconData(0xe679, fontFamily: _fontFamily);
  static const IconData daxiao = IconData(0xe67b, fontFamily: _fontFamily);
  static const IconData pingguo1 = IconData(0xe61b, fontFamily: _fontFamily);
  static const IconData roupai = IconData(0xe6b3, fontFamily: _fontFamily);
  static const IconData shinshopliangyoufushi = IconData(0xe62e, fontFamily: _fontFamily);
  static const IconData cai = IconData(0xe62d, fontFamily: _fontFamily);
  static const IconData jiaozi = IconData(0xe603, fontFamily: _fontFamily);
  static const IconData gouwulan1 = IconData(0xe61a, fontFamily: _fontFamily);
  static const IconData liangyou1 = IconData(0xe60d, fontFamily: _fontFamily);
  static const IconData chaoshi = IconData(0xe602, fontFamily: _fontFamily);
  static const IconData rou = IconData(0xe60f, fontFamily: _fontFamily);
  static const IconData shucai = IconData(0xe606, fontFamily: _fontFamily);
  static const IconData jiaozi2 = IconData(0xe610, fontFamily: _fontFamily);
  static const IconData liangyou = IconData(0xe613, fontFamily: _fontFamily);
  static const IconData erweima1 = IconData(0xe614, fontFamily: _fontFamily);
  static const IconData basketFill = IconData(0xe612, fontFamily: _fontFamily);
  static const IconData gouwuche = IconData(0xe665, fontFamily: _fontFamily);
  static const IconData jian2 = IconData(0xe70a, fontFamily: _fontFamily);
  static const IconData deleteFill = IconData(0xe6f5, fontFamily: _fontFamily);
  static const IconData duihao = IconData(0xe66d, fontFamily: _fontFamily);
  static const IconData collect = IconData(0xe604, fontFamily: _fontFamily);
  static const IconData starHollow = IconData(0xe813, fontFamily: _fontFamily);
  static const IconData refresh = IconData(0xe650, fontFamily: _fontFamily);
  static const IconData star = IconData(0xe688, fontFamily: _fontFamily);
  static const IconData sea = IconData(0xe64d, fontFamily: _fontFamily);
  static const IconData dian = IconData(0xe7a5, fontFamily: _fontFamily);
  static const IconData erweima = IconData(0xea7e, fontFamily: _fontFamily);
  static const IconData weizhi = IconData(0xe709, fontFamily: _fontFamily);
  static const IconData fiftyOne = IconData(0xe69f, fontFamily: _fontFamily);
  static const IconData bianji = IconData(0xe620, fontFamily: _fontFamily);
  static const IconData tongyi = IconData(0xe706, fontFamily: _fontFamily);
  static const IconData foldUp = IconData(0xea80, fontFamily: _fontFamily);
  static const IconData fanhuiXia = IconData(0xe705, fontFamily: _fontFamily);
  static const IconData quzheli = IconData(0xe704, fontFamily: _fontFamily);
  static const IconData circle = IconData(0xe6b7, fontFamily: _fontFamily);
  static const IconData gouwuche2 = IconData(0xe677, fontFamily: _fontFamily);
  static const IconData wodeDizhi = IconData(0xe698, fontFamily: _fontFamily);
  static const IconData dingwei = IconData(0xe702, fontFamily: _fontFamily);
  static const IconData qingkong = IconData(0xe67a, fontFamily: _fontFamily);
  static const IconData jian1 = IconData(0xe6ff, fontFamily: _fontFamily);
  static const IconData jia1 = IconData(0xe700, fontFamily: _fontFamily);
  static const IconData tuige = IconData(0xe601, fontFamily: _fontFamily);
  static const IconData jiaCopy = IconData(0xe743, fontFamily: _fontFamily);
  static const IconData backRightCopy = IconData(0xe73f, fontFamily: _fontFamily);
  static const IconData select = IconData(0xe6b0, fontFamily: _fontFamily);
  static const IconData dingcan = IconData(0xe6b5, fontFamily: _fontFamily);
  static const IconData jian = IconData(0xe6f7, fontFamily: _fontFamily);
  static const IconData zhuozi = IconData(0xe6f9, fontFamily: _fontFamily);
  static const IconData wifi = IconData(0xe6fa, fontFamily: _fontFamily);
  static const IconData gengduo1 = IconData(0xe63d, fontFamily: _fontFamily);
  static const IconData wangluocuowu = IconData(0xe62c, fontFamily: _fontFamily);
  static const IconData guanbi = IconData(0xe636, fontFamily: _fontFamily);
  static const IconData xingxing = IconData(0xe65e, fontFamily: _fontFamily);
  static const IconData up = IconData(0xe666, fontFamily: _fontFamily);
  static const IconData ziquDianpu = IconData(0xe687, fontFamily: _fontFamily);
  static const IconData wodeFankui = IconData(0xe69d, fontFamily: _fontFamily);
  static const IconData mlzdianhua = IconData(0xe6a0, fontFamily: _fontFamily);
  static const IconData jiudian = IconData(0xe6f2, fontFamily: _fontFamily);
  static const IconData yiyuan = IconData(0xe6f3, fontFamily: _fontFamily);
  static const IconData chihe = IconData(0xe6f4, fontFamily: _fontFamily);
  static const IconData shouye = IconData(0xe651, fontFamily: _fontFamily);
  static const IconData wodedingdan = IconData(0xe652, fontFamily: _fontFamily);
  static const IconData wode = IconData(0xe654, fontFamily: _fontFamily);
  static const IconData diancai = IconData(0xe6ef, fontFamily: _fontFamily);
  static const IconData ziqudingdan = IconData(0xe689, fontFamily: _fontFamily);
  static const IconData waimaidingdan = IconData(0xe68b, fontFamily: _fontFamily);
  static const IconData qingtieDingzuo = IconData(0xe68f, fontFamily: _fontFamily);
  static const IconData search = IconData(0xe656, fontFamily: _fontFamily);
  static const IconData gengduo = IconData(0xe741, fontFamily: _fontFamily);
  static const IconData arrowTop2 = IconData(0xe740, fontFamily: _fontFamily);
  static const IconData arrowTop3 = IconData(0xea7f, fontFamily: _fontFamily);
  static const IconData gengduoCopy = IconData(0xe742, fontFamily: _fontFamily);
  static const IconData quanbu = IconData(0xe627, fontFamily: _fontFamily);
  static const IconData add = IconData(0xe6ee, fontFamily: _fontFamily);
}