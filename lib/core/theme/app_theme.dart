import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/core/config/index.dart';
import 'package:user_app/core/theme/app_colors.dart';

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      fontFamily: AppConstants.mainFont,
      platform: TargetPlatform.iOS,
      colorScheme: ColorScheme.light(
        primary: AppColors.primary,
      ),
      scaffoldBackgroundColor: AppColors.backgroundColor,
      splashFactory: NoSplash.splashFactory,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      appBarTheme: AppBarTheme(
        centerTitle: true,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontFamily: AppConstants.mainFont,
          fontSize: 18,
          // fontWeight: FontWeight.bold,
        ),
        // 为每个AppBar设置状态栏样式
        systemOverlayStyle: const SystemUiOverlayStyle(
          // 状态栏背景色透明，让AppBar颜色延伸到状态栏
          statusBarColor: Colors.transparent,
          // iOS状态栏内容为白色（因为AppBar是深色）
          statusBarIconBrightness: Brightness.light, // Android
          statusBarBrightness: Brightness.dark, // iOS
          // 导航栏设置
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      ),
    );
  }
  // 更多主题  ...
}
