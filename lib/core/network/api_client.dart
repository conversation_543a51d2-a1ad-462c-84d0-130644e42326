import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:user_app/core/config/api_config.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/core/network/interceptors/auth_interceptor.dart';
import 'package:user_app/core/network/interceptors/error_interceptor.dart';
import 'package:user_app/core/network/interceptors/response_interceptor.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/network/result/api_error_code.dart';

/// API客户端类，负责处理所有网络请求
///
/// 该类实现了单例模式，确保整个应用中只有一个API客户端实例。
/// 它封装了基于Dio的HTTP请求，并提供了统一的错误处理和响应解析。
/// 所有请求都返回[ApiResult]类型，包含了请求结果或错误信息。
class ApiClient {
  static final ApiClient _instance = ApiClient._internal();
  late final Dio _dio;
  bool _initialized = false;

  /// 工厂构造函数，返回单例实例
  factory ApiClient() => _instance;

  /// 私有构造函数
  ApiClient._internal();

  /// 初始化API客户端
  ///
  /// 配置基础URL、超时时间、请求头和拦截器。
  /// 只有第一次调用时才会执行初始化，后续调用会被忽略。
  Future<void> init() async {
    if (_initialized) return;

    _dio = Dio(BaseOptions(
      baseUrl: ApiConfig.baseUrl,
      connectTimeout: ApiConfig.connectTimeout,
      receiveTimeout: ApiConfig.receiveTimeout,
      headers: ApiConfig.defaultHeaders,
    ));

    _setupInterceptors();
    _initialized = true;
  }

  /// 设置请求拦截器
  ///
  /// 添加认证拦截器、响应处理拦截器和错误处理拦截器
  void _setupInterceptors() {
    _dio.interceptors.addAll([
      AuthInterceptor(),
      ResponseInterceptor(),
      ErrorInterceptor(),
    ]);
  }

  /// 执行GET请求
  ///
  /// [path] - API路径，不包含基础URL
  /// [params] - 查询参数，会附加到URL后
  /// [options] - Dio请求选项，可以自定义请求头等
  /// [fromJson] - 数据转换回调函数，用于将API响应转换为具体的模型对象
  ///   - 第一个参数[response]是完整的响应对象
  ///   - 第二个参数[data]是响应中的data字段内容
  ///   - 可以根据需要选择使用哪个参数或同时使用两者
  ///
  /// 返回一个包含请求结果或错误信息的[ApiResult]对象
  ///
  /// 示例:
  /// ```dart
  /// // 只使用data部分
  /// apiClient.get('/users', fromJson: (response, data) => User.fromJson(data));
  ///
  /// // 使用完整响应
  /// apiClient.get('/users', fromJson: (response, data) {
  ///   final status = response['status'];
  ///   if (status == 200) {
  ///     return User.fromJson(data);
  ///   }
  ///   return null;
  /// });
  /// ```
  Future<ApiResult<T>> get<T>(
    final String path, {
    final Map<String, dynamic>? params,
    final Options? options,
    required final T Function(dynamic response, dynamic data) fromJson,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: params,
        options: options,
      );

      return _processResponse(response, fromJson);
    } catch (e) {
      return ApiResult.fromException(e);
    }
  }

  /// 执行POST请求
  ///
  /// [path] - API路径，不包含基础URL
  /// [data] - 请求体数据
  /// [queryParameters] - 查询参数，会附加到URL后
  /// [options] - Dio请求选项，可以自定义请求头等
  /// [fromJson] - 数据转换回调函数，用于将API响应转换为具体的模型对象
  ///   - 第一个参数[response]是完整的响应对象
  ///   - 第二个参数[data]是响应中的data字段内容
  ///
  /// 返回一个包含请求结果或错误信息的[ApiResult]对象
  Future<ApiResult<T>> post<T>(
    final String path, {
    final dynamic data,
    final Map<String, dynamic>? queryParameters,
    final Options? options,
    required final T Function(dynamic response, dynamic data) fromJson,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );

      return _processResponse(response, fromJson);
    } catch (e) {
      return ApiResult.fromException(e);
    }
  }

  /// 执行PUT请求
  ///
  /// [path] - API路径，不包含基础URL
  /// [data] - 请求体数据
  /// [queryParameters] - 查询参数，会附加到URL后
  /// [options] - Dio请求选项，可以自定义请求头等
  /// [fromJson] - 数据转换回调函数，用于将API响应转换为具体的模型对象
  ///
  /// 返回一个包含请求结果或错误信息的[ApiResult]对象
  Future<ApiResult<T>> put<T>(
    final String path, {
    final dynamic data,
    final Map<String, dynamic>? queryParameters,
    final Options? options,
    required final T Function(dynamic response, dynamic data) fromJson,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );

      return _processResponse(response, fromJson);
    } catch (e) {
      return ApiResult.fromException(e);
    }
  }

  /// 执行DELETE请求
  ///
  /// [path] - API路径，不包含基础URL
  /// [data] - 请求体数据
  /// [queryParameters] - 查询参数，会附加到URL后
  /// [options] - Dio请求选项，可以自定义请求头等
  /// [fromJson] - 数据转换回调函数，用于将API响应转换为具体的模型对象
  ///
  /// 返回一个包含请求结果或错误信息的[ApiResult]对象
  Future<ApiResult<T>> delete<T>(
    final String path, {
    final dynamic data,
    final Map<String, dynamic>? queryParameters,
    final Options? options,
    required final T Function(dynamic response, dynamic data) fromJson,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );

      return _processResponse(response, fromJson);
    } catch (e) {
      return ApiResult.fromException(e);
    }
  }

  /// 下载文件
  ///
  /// [path] - 文件URL路径
  /// [savePath] - 保存文件的本地路径
  /// [cancelToken] - 用于取消下载的token
  /// [onReceiveProgress] - 下载进度回调函数
  ///
  /// 返回下载结果的[ApiResult]对象
  Future<ApiResult<T>> downloadFile<T>(
    final String path,
    final String savePath, {
    final CancelToken? cancelToken,
    final void Function(int received, int total)? onReceiveProgress,
    required final T Function(dynamic response, dynamic data) fromJson,
  }) async {
    try {
      final response = await _dio.download(
        path,
        savePath,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      return _processResponse(response, fromJson);
    } catch (e) {
      return ApiResult.fromException(e);
    }
  }

  /// 上传文件
  ///
  /// [path] - API路径
  /// [filePath] - 要上传的文件路径
  /// [name] - 文件字段名
  /// [formData] - 附加的表单数据
  /// [onSendProgress] - 上传进度回调函数
  ///
  /// 返回上传结果的[ApiResult]对象，通常包含服务器返回的文件URL或ID
  Future<ApiResult<T>> upload<T>(
    final String path, {
    required final String filePath,
    required final String name,
    final Map<String, dynamic>? formData,
    final void Function(int sent, int total)? onSendProgress,
    required final T Function(dynamic response, dynamic data) fromJson,
  }) async {
    try {
      final formDataMap = Map<String, dynamic>.from(formData ?? {});
      formDataMap[name] = await MultipartFile.fromFile(filePath);

      final response = await _dio.post(
        path,
        data: FormData.fromMap(formDataMap),
        onSendProgress: onSendProgress,
      );

      return _processResponse(response, fromJson);
    } catch (e) {
      return ApiResult.fromException(e);
    }
  }

  /// 处理API响应
  ///
  /// 该方法提取响应数据，检查状态码，并使用fromJson函数转换数据。
  ///
  /// [response] - Dio响应对象
  /// [fromJson] - 数据转换回调函数，接收两个参数:
  ///   - 完整响应(response)
  ///   - data字段内容(data)
  ///
  /// 处理逻辑:
  /// 1. 如果HTTP状态码是200，进一步处理业务逻辑
  /// 2. 检查响应中的业务状态码(status字段)
  /// 3. 如果业务状态码成功，则调用fromJson函数转换数据
  /// 4. 否则，返回带有错误信息的ApiResult
  ApiResult<T> _processResponse<T>(
    final Response response,
    final T Function(dynamic response, dynamic data) fromJson,
  ) {
    try {
      if (response.statusCode == ApiErrorCode.SUCCESS) {
        if (response.data is Map<String, dynamic>) {
          final Map<String, dynamic> responseData = response.data;
          final status = responseData['status'] ?? ApiErrorCode.SUCCESS;

          // 检查业务状态码
          if (status == ApiErrorCode.SUCCESS) {
            try {
              // 传递完整响应和 data 字段给 fromJson 函数
              final dynamic dataField = responseData['data'];
              final T result = fromJson(responseData, dataField);

              return ApiResult.success(
                result,
                msg: responseData['msg'] ?? '',
                time: responseData['time'] ?? '',
                lang: responseData['lang'] ?? 'ug',
              );
            } catch (e,stack) {
              if(kDebugMode){
                print('${S.current.err_msg}: $e, $stack');

              }
              return ApiResult.error(
                msg: '${S.current.err_msg}: $e',
                status: ApiErrorCode.PARSING_ERROR,
              );
            }
          } else {
            // 业务错误
            return ApiResult.error(
              msg: responseData['msg'] ?? S.current.network_err_response,
              status: status,
              time: responseData['time'] ?? '',
              lang: responseData['lang'] ?? 'ug',
            );
          }
        } else {
          // 如果响应不是Map类型
          try {
            // 传递完整响应，data为null
            final T result = fromJson(response.data, null);
            return ApiResult.success(result);
          } catch (e,stack) {
            if(kDebugMode){
              print('${S.current.err_msg}: $e, $stack');

            }
            return ApiResult.error(
              msg: '${S.current.err_msg}: $e',
              status: ApiErrorCode.PARSING_ERROR,
            );
          }
        }
      }

      // 处理非200状态码
      return ApiResult.error(
        msg: _getErrorMessage(response.statusCode ?? 0),
        status: response.statusCode ?? 0,
      );
    } catch (e,stack) {
      if(kDebugMode){
        print('${S.current.err_msg}: $e, $stack');
      }
      return ApiResult.error(
        msg: '${S.current.err_msg}: $e',
        status: ApiErrorCode.PARSING_ERROR,
      );
    }
  }

  /// 获取错误消息
  ///
  /// 根据HTTP状态码返回对应的错误消息，使用国际化字符串
  ///
  /// [statusCode] - HTTP状态码
  String _getErrorMessage(final int statusCode) {
    switch (statusCode) {
      case ApiErrorCode.UNAUTHORIZED:
        return S.current.network_err_not_auth;
      case ApiErrorCode.NOT_FOUND:
        return S.current.network_err_cat_not_find;
      case ApiErrorCode.SERVER_ERROR:
        return S.current.network_err_server_error;
      default:
        return '${S.current.err_msg} ($statusCode)';
    }
  }
}
