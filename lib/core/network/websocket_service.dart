import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:user_app/core/config/api_config.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/main.dart';
import 'package:web_socket_client/web_socket_client.dart';

/// WebSocket服务
/// 基于web_socket_client包，提供聊天室功能
/// 使用单例模式，页面级生命周期管理
class WebSocketService {
  /// 单例实例
  static WebSocketService? _instance;

  /// 获取单例实例
  static WebSocketService get instance {
    _instance ??= WebSocketService._internal();
    return _instance!;
  }

  /// 私有构造函数
  WebSocketService._internal();

  /// WebSocket客户端
  WebSocket? _webSocket;

  /// 当前房间ID
  String? _currentRoomId;

  /// 是否已初始化
  bool _isInitialized = false;

  /// 连接状态订阅
  StreamSubscription? _connectionSubscription;

  /// 获取消息流（直接暴露web_socket_client的消息流）
  Stream<Map<String, dynamic>> get messageStream {
    _ensureInitialized();
    return _webSocket!.messages.map((final message) {
      try {
        return jsonDecode(message as String) as Map<String, dynamic>;
      } catch (e) {
        if (kDebugMode) {
          print('解析WebSocket消息失败: $e');
        }
        return <String, dynamic>{};
      }
    }).where((final data) => data.isNotEmpty);
  }

  /// 获取连接状态流（直接暴露web_socket_client的连接状态）
  Stream<ConnectionState> get connectionStateStream {
    _ensureInitialized();
    return _webSocket!.connection;
  }

  /// 获取当前连接状态
  ConnectionState get connectionState {
    _ensureInitialized();
    return _webSocket!.connection.state;
  }

  /// 获取是否已连接
  bool get isConnected => connectionState is Connected;

  /// 确保WebSocket已初始化
  void _ensureInitialized() {
    if (_isInitialized) return;

    if (kDebugMode) {
      print('初始化WebSocket连接: ${ApiConfig.socketUrl}');
    }

    // 创建WebSocket客户端，web_socket_client会自动处理连接和重连
    _webSocket = WebSocket(
      Uri.parse(ApiConfig.socketUrl),
      backoff: BinaryExponentialBackoff(
        initial: const Duration(seconds: 1),
        maximumStep: 3, // 最大等待时间: 1, 2, 4, 4, 4...秒
      ),
      timeout: const Duration(seconds: 10),
    );

    // 监听连接状态，实现房间重连逻辑
    _connectionSubscription = _webSocket!.connection.listen(
      (final state) {
        if (kDebugMode) {
          print('WebSocket连接状态: ${state.runtimeType}');
        }

        // 连接成功后自动重新加入房间
        if ((state is Connected || state is Reconnected) &&
            _currentRoomId != null) {
          Future.delayed(const Duration(milliseconds: 500), () {
            if (_currentRoomId != null) {
              _sendJoinMessage(_currentRoomId!);
            }
          });
        }
      },
      onError: (final error) {
        if (kDebugMode) {
          print('WebSocket连接状态错误: $error');
        }
      },
    );

    _isInitialized = true;
  }

  /// 加入聊天室
  void joinRoom(final String roomId) {
    _currentRoomId = roomId;
    _ensureInitialized();

    if (kDebugMode) {
      print('加入聊天室: $roomId');
    }

    // 检查当前连接状态，如果已连接则立即发送消息
    // 如果未连接，全局连接监听器会在连接建立后自动发送
    final currentState = _webSocket!.connection.state;
    if (currentState is Connected) {
      _sendJoinMessage(roomId);
      if (kDebugMode) {
        print('WebSocket已连接，直接发送加入消息');
      }
    } else {
      if (kDebugMode) {
        print('WebSocket未连接，等待连接建立...');
      }
    }
  }

  /// 发送加入房间消息
  void _sendJoinMessage(final String roomId) {
    if (_webSocket == null) return;
    final userId =
        globalContainer.read(localStorageRepositoryProvider).getUserInfo()?.id;

    final message = {
      'event': 'join_room',
      'data': {'order_id': roomId, 'sender_type': '1', 'user_id': userId},
    };

    try {
      _webSocket!.send(jsonEncode(message));
      if (kDebugMode) {
        print('发送加入房间消息: $message');
      }
    } catch (e) {
      if (kDebugMode) {
        print('发送加入房间消息失败: $e');
      }
    }
  }

  /// 离开聊天室
  void leaveRoom(final String roomId) {
    if (_webSocket == null) return;

    final userId =
        globalContainer.read(localStorageRepositoryProvider).getUserInfo()?.id;

    final message = {
      'event': 'leave_room',
      'data': {'order_id': roomId, 'sender_type': '1', 'user_id': userId},
    };

    try {
      _webSocket!.send(jsonEncode(message));
      if (kDebugMode) {
        print('发送离开房间消息: $message');
      }
    } catch (e) {
      if (kDebugMode) {
        print('发送离开房间消息失败: $e');
      }
    }

    // 清除当前房间
    if (_currentRoomId == roomId) {
      _currentRoomId = null;
    }
  }

  /// 发送消息
  void sendMessage(final String message) {
    if (_webSocket == null) {
      if (kDebugMode) {
        print('WebSocket未初始化，无法发送消息');
      }
      return;
    }

    try {
      _webSocket!.send(message);
      if (kDebugMode) {
        print('发送消息: $message');
      }
    } catch (e) {
      if (kDebugMode) {
        print('发送消息失败: $e');
      }
    }
  }

  /// 关闭资源
  void dispose() {
    if (kDebugMode) {
      print('关闭WebSocket服务');
    }

    _connectionSubscription?.cancel();
    _webSocket?.close();

    _webSocket = null;
    _currentRoomId = null;
    _isInitialized = false;

    // 重置单例实例，下次使用时重新创建（与小程序页面级生命周期一致）
    _instance = null;
  }
}
