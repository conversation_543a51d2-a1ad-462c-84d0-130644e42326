// 定义错误码常量
class ApiErrorCode {
  // 成功状态码
  static const int SUCCESS = 200;

  // 错误状态码
  static const int BUSINESS_ERROR = -1000; // 业务错误
  static const int NETWORK_ERROR = 1001; // 网络连接错误
  static const int TIMEOUT_ERROR = 1002; // 请求超时
  static const int PARSING_ERROR = 1003; // 数据解析错误

  // 常用HTTP状态码
  static const int UNAUTHORIZED = 401;
  static const int NOT_FOUND = 404;
  static const int SERVER_ERROR = 500;
}
