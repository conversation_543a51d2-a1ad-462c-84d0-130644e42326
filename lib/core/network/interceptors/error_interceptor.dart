import 'package:dio/dio.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:user_app/core/network/result/api_result.dart';

class ErrorInterceptor extends Interceptor {
  @override
  void onError(final DioError err, final ErrorInterceptorHandler handler) {
    // 使用 ApiResult.fromException 处理错误
    final apiResult = ApiResult.fromException(err);

    // 显示错误提示
    BotToast.showText(text: apiResult.msg);

    // 传递错误
    handler.next(err);
  }
}
