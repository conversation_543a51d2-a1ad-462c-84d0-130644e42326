import 'dart:developer';
import 'package:bot_toast/bot_toast.dart';
import 'package:dio/dio.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/network/result/api_error_code.dart';

// 响应拦截器
class ResponseInterceptor extends Interceptor {
  @override
  void onResponse(final Response response, final ResponseInterceptorHandler handler) {
    // 处理业务错误（非200状态码）
    if (response.statusCode == 200 &&
        response.data['status'] != ApiErrorCode.SUCCESS &&
        response.data['status'] != ApiErrorCode.UNAUTHORIZED) {
      // 排除授权错误，由AuthInterceptor处理
      BotToast.showText(text: response.data['msg'] ?? '获取数据失败');
    }

    // 继续处理响应
    handler.next(response);
  }

  @override
  void onError(final DioError err, final ErrorInterceptorHandler handler) {
    log('error: ${err.message}');
    // 显示错误提示
    final apiResult = ApiResult.fromException(err);

    // 如果不是授权错误，则显示提示（授权错误由AuthInterceptor处理）
    if (apiResult.status != ApiErrorCode.UNAUTHORIZED) {
      BotToast.showText(text: apiResult.msg);
    }

    handler.next(err);
  }
}
