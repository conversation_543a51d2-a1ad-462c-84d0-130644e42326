import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluwx/fluwx.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/config/environment_config.dart';
import 'package:user_app/core/utils/image_util.dart';
import 'package:user_app/generated/l10n.dart';

/// 微信工具类
class WechatUtil with ChangeNotifier {
  /// 支付微信小程序原始ID
  static const String originalId = 'gh_4c15504e80b3';

  /// 分享微信小程序原始ID
  static const String shareOriginalId = 'gh_871557eec307';

  /// 微信客服ID（企业ID）
  static const String customerServiceId = 'ww837e7167e04beec9';

  /// 微信客服URL（根据企业ID和客服账号生成）
  static const String customerServiceUrl =
      'https://work.weixin.qq.com/kfid/kfc250273f90810f715';

  final Fluwx _fluwx = Fluwx();
  static final WechatUtil _instance = WechatUtil._internal();
  WechatUtil._internal();
  factory WechatUtil() {
    _instance.init();
    return _instance;
  }

  /// 微信支付响应监听器
  late Function(WeChatResponse) responseListener;

  // 初始化微信SDK
  Future<void> init() async {
    await _fluwx.registerApi(
      appId: "wxf3c60530648d4ddd",
      universalLink: "https://cer.mulazim.com/",
      doOnAndroid: true,
      doOnIOS: true,
    );
  }

  /// 打开微信客服聊天
  /// 返回是否成功跳转微信
  Future<bool> openCustomerServiceChat() async {
    try {
      final result = await _fluwx.open(
        target: CustomerServiceChat(
          corpId: customerServiceId,
          url: customerServiceUrl,
        ),
      );
      return result;
    } catch (e) {
      debugPrint('打开微信客服失败: $e');
      BotToast.showText(text: S.current.open_costumet_faield);
      return false;
    }
  }

  /// 跳转到微信小程序
  Future<bool> payWithWechatMiniProgram(
      {required final String orderId,
      required final String path,
      required final String type,
      required final String originalId}) async {
    return await _fluwx.open(
        target: MiniProgram(
            username: originalId,
            path: path,
            miniProgramType: type.toString().trim() == "preview"
                ? WXMiniProgramType.preview
                : WXMiniProgramType.release));
  }

  /// 发起微信支付
  /// 参考 fluwx 库中的 PayType 和 pay 方法
  Future<bool> launchWechatPay(final Map<String, dynamic> payParams) async {
    // 创建 Payment 实例，这是 PayType 的具体实现
    final payment = Payment(
      appId: payParams['appId'] as String,
      partnerId: payParams['partnerId'] as String? ?? '',
      prepayId: payParams['prepayId'] as String? ?? '',
      packageValue: payParams['package'] as String,
      nonceStr: payParams['nonceStr'] as String,
      timestamp: int.parse(payParams['timeStamp'].toString()),
      sign: payParams['paySign'] as String,
      signType: payParams['signType'] as String? ?? 'MD5',
    );

    // 调用 fluwx.pay 方法，传入 payment 作为 which 参数
    return await _fluwx.pay(which: payment);
  }

  // 尝试使用 open 方法跳转小程序
  Future<void> launchMiniProgram(String miniProgramId) async {
    // 检查微信是否安装
    final isInstalled = await _fluwx.isWeChatInstalled;
    if (!isInstalled) {
      // 处理未安装微信的情况
      if (AppContext().currentContext != null) {
        ScaffoldMessenger.of(AppContext().currentContext!)
            .showSnackBar(const SnackBar(content: Text('请先安装微信')));
      }
      return;
    }

    // 尝试构建小程序跳转请求
    final result = await _fluwx.open(
      target: MiniProgram(
        username: miniProgramId, // 小程序原始ID（必须以 gh_ 开头）
        path: "pages/index/index", // 小程序路径
        miniProgramType: WXMiniProgramType.release, // 小程序类型（默认正式版）
      ),
    );

    if (!result) {
      // 处理跳转失败的情况
      if (AppContext().currentContext != null) {
        ScaffoldMessenger.of(AppContext().currentContext!)
            .showSnackBar(const SnackBar(content: Text('无法打开微信小程序')));
      }
    }
  }

  /// 处理支付逻辑
  /// 统一处理微信支付流程
  /// [payParams] 支付参数
  /// [onPaymentComplete] 支付完成后的回调函数
  /// 返回支付过程是否成功启动
  Future<Map<String, dynamic>> processPayment(
    final Map<String, dynamic> payParams, {
    final Function? onPaymentComplete,
  }) async {
    try {
      final payOrderId = payParams['order_id']?.toString() ?? '';
      bool paySuccess = false;
      String message = '';

      // payParams['mini_program_path'] = "pages/miniPay/index?lang=ug&order_id=$payOrderId&pay_from=4&platform=app";
      // payParams['mini_program_id'] = originalId;
      // payParams['mini_program_type'] = "release";
      // 小程序支付
      if (payParams.containsKey('mini_program_path')) {
        final path = payParams['mini_program_path'] as String? ?? '';
        final originalId =
            payParams['mini_program_id'] as String? ?? WechatUtil.originalId;

        await payWithWechatMiniProgram(
          orderId: payOrderId,
          path: path,
          type: EnvironmentConfig.wechatPayType,
          originalId: originalId,
        );
        paySuccess = true;
        message = '支付请求已发送';
      }
      // 原有的支付参数格式: appId, partnerId, prepayId
      else if (payParams.containsKey('appId') &&
          payParams.containsKey('partnerId') &&
          payParams.containsKey('prepayId')) {
        final result = await launchWechatPay(payParams);

        if (result) {
          BotToast.showText(text: '支付请求已发送');
          paySuccess = true;
          message = '支付请求已发送';
        } else {
          BotToast.showText(text: '发起支付失败');
          paySuccess = false;
          message = '发起支付失败';
        }
      }
      // 参数不完整
      else {
        BotToast.showText(text: '支付参数不完整，请稍后再试');
        paySuccess = false;
        message = '支付参数不完整，请稍后再试';
      }

      // 调用支付完成回调
      if (paySuccess && onPaymentComplete != null) {
        // 延迟调用回调函数，给支付过程留出时间
        Future.delayed(Duration(seconds: 3), () {
          onPaymentComplete();
        });
      }

      return {'success': paySuccess, 'message': message};
    } catch (e) {
      BotToast.showText(text: '发起支付失败: ${e.toString()}');
      return {'success': false, 'message': e.toString()};
    }
  }

  /// 分享小程序
  Future<bool> shareMiniProgram({
    final String? path,
    final String? title,
    final String? description,
    final Uint8List? thumbData,
  }) async {
    try {
      Uint8List? defaultThumbData;
      if (thumbData == null) {
        // 加载默认缩略图
        final ByteData byteData =
            await rootBundle.load('assets/images/app/share_icon.jpg');
        defaultThumbData = byteData.buffer.asUint8List();
      }
      // 如果缩略图数据超过120kb，则压缩
      if (thumbData != null) {
        defaultThumbData = await ImageUtil.createWeChatThumbnail(thumbData);
      }

      return await _fluwx.share(
        WeChatShareMiniProgramModel(
          userName: shareOriginalId,
          path: path ?? "pages/index/index",
          webPageUrl: "https://cer.mulazim.com/",
          title: title ?? S.current.app_name, // 小程序标题
          description: description ?? S.current.app_description, // 小程序描述
          thumbData: defaultThumbData, // 缩略图数据，不能为空且不能超过128kb
        ),
      );
    } catch (e) {
      debugPrint('分享小程序失败: $e');
      return false;
    }
  }
}
