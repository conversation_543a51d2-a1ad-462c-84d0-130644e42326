import 'package:user_app/generated/l10n.dart';

/// 日期工具类
class DateUtil {
  /// 获取相对时间
  static String getRelativeTime(final DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()}年前';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}个月前';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 格式化日期时间
  static String formatDateTime(final DateTime dateTime) {
    return '${dateTime.year}-${_addZero(dateTime.month)}-${_addZero(dateTime.day)} '
        '${_addZero(dateTime.hour)}:${_addZero(dateTime.minute)}:${_addZero(dateTime.second)}';
  }

  /// 添加前导零
  static String _addZero(final int number) {
    return number < 10 ? '0$number' : '$number';
  }

  /// 计算两个时间之间的差异，返回格式化的时间差
  ///
  /// [startTime] 开始时间，通常是当前时间
  /// [endTime] 结束时间，通常是预计配送时间
  /// [locale] 语言选项，支持 'zh' (中文) 和 'ug' (维吾尔语)，默认为中文
  ///
  /// 返回格式化的时间差字符串，例如："30分钟后送达" 或 "预计1小时后送达"
  static String calculateTimeDifference({
    required DateTime startTime,
    required DateTime endTime,
    final String locale = 'zh',
  }) {
    // 确保开始时间在结束时间之前
    if (startTime.isAfter(endTime)) {
      final temp = startTime;
      startTime = endTime;
      endTime = temp;
    }

    // 计算时间差
    final difference = endTime.difference(startTime);

    // 根据语言返回不同格式
    if (locale == 'ug') {
      // 维吾尔语输出
      if (difference.inHours > 0) {
        final hours = difference.inHours;
        final minutes = difference.inMinutes % 60;

        if (minutes > 0) {
          return '$hours سائەت $minutes مىنۇتتىن كېيىن يەتكۈزۈلىدۇ';
        } else {
          return '$hours سائەتتىن كېيىن يەتكۈزۈلىدۇ';
        }
      } else {
        return '${difference.inMinutes} مىنۇتتىن كېيىن يەتكۈزۈلىدۇ';
      }
    } else {
      // 中文输出
      if (difference.inHours > 0) {
        final hours = difference.inHours;
        final minutes = difference.inMinutes % 60;

        if (minutes > 0) {
          return '预计${hours}小时${minutes}分钟后送达';
        } else {
          return '预计${hours}小时后送达';
        }
      } else {
        return '约${difference.inMinutes}分钟后送达';
      }
    }
  }

  /// 计算两个时间之间的差异，返回总分钟数和格式化的时间差字符串
  ///
  /// [startTime] 开始时间
  /// [endTime] 结束时间
  /// [locale] 语言选项，支持 'zh' (中文) 和 'ug' (维吾尔语)，默认为中文
  ///
  /// 返回一个包含总分钟数和格式化时间差的Map
  static Map<String, dynamic> calculateTimeDifferenceInMinutes({
    required final DateTime startTime,
    required final DateTime endTime,
  }) {
    // 确保开始时间在结束时间之前
    if (startTime.isAfter(endTime)) {
      throw Exception('结束时间必须晚于开始时间');
    }

    // 计算时间差（毫秒）
    final difference = endTime.difference(startTime);

    // 计算天数、小时数和分钟数
    final days = difference.inDays;
    final hours = difference.inHours % 24;
    final minutes = difference.inMinutes % 60;

    // 总分钟数
    final totalMinutes = difference.inMinutes;

    // 时间部分
    final List<String> timeParts = [];

    // if (locale == 'ug') {
    //   // 维吾尔语输出
    //   if (days > 0) timeParts.add('$days كۈن');
    //   if (hours > 0) timeParts.add('$hours سائەت');
    //   if (minutes > 0) timeParts.add('$minutes مىنۇت');
    // } else {
    // 中文输出
    if (days > 0) timeParts.add('$days${S.current.seckill_day}');
    if (hours > 0) timeParts.add('$hours${S.current.seckill_hour}');
    if (minutes > 0) timeParts.add('$minutes${S.current.seckill_minute}');
    // }

    return {
      'title': timeParts.join(' '), // 将部分组合成一个字符串
      'time': totalMinutes // 总时间（分钟）
    };
  }
}
