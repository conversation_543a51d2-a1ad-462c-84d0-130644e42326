import 'dart:io';
import 'package:aliyun_log_dart_sdk/aliyun_log_dart_sdk.dart';
import 'package:user_app/core/storage/storage_service.dart';

/// 阿里云日志服务(SLS)工具类
/// 用于收集和上传应用日志到阿里云日志服务
class AliyunSls {

  AliyunSls._internal();
  static final AliyunSls _instance = AliyunSls._internal();

  factory AliyunSls() {
    return _instance;
  }
  AliyunLogDartSdk? _aliyunLogSdk;
  /// 初始化日志生产者
  /// 配置阿里云日志服务的必要参数
  Future<void> initProducer() async {
    // 配置服务入口Endpoint、Project名称、Logstore名称。您可以通过动态参数配置动态更新Endpoint、Project名称等。
    LogProducerConfiguration configuration = LogProducerConfiguration(
        endpoint: 'cn-beijing.log.aliyuncs.com',
        project: 'mulazim',
        logstore: 'flutter',
    );

    //阿里云访问密钥AccessKey。更多信息，请参见访问密钥。阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维。
    configuration.accessKeyId = 'LTAI5t5svu2jDXFTe1eZWXGD';
    configuration.accessKeySecret = '******************************';
    _aliyunLogSdk = AliyunLogDartSdk();
    LogProducerResult result = await _aliyunLogSdk!.initProducer(configuration);
    print('elfa_sls-->$result');
  }

  /// 发送日志到阿里云
  /// [tag] 日志标签
  /// [content] 日志内容
  /// [messageType] 消息类型
  Future<void> sendLog(String tag,Map<String,dynamic> content,String messageType) async {
    if (!check()) {
      return;
    }

    final StorageService _storageService = StorageService();
    final token = _storageService.read('token');
    // 收集设备和用户信息
    String searialNumber = '123456789';
    String Authorization = 'Bearer $token';
    String userAgent = Platform.isAndroid ? 'android' : 'ios';

    // 构建日志头信息
    Map<String, dynamic> header = {
      'searialNumber': searialNumber,
      'Authorization': Authorization,
      'user-agent': userAgent,
    };

    // 尝试发送日志，如果失败则重新初始化并重试
    try{
      if(_aliyunLogSdk != null){
        await _aliyunLogSdk!.addLog({
          'tag': tag,
          'content': content,
          'messageType': messageType,
          'header': header,
          'user_agent': userAgent,
          'Authorization': Authorization,
          'terminal': 'user',
        });
      }
    }catch(e){
      print('提交日志错误： ${e.toString()}');
      await initProducer();
      await _aliyunLogSdk!.addLog({
        'tag': tag,
        'content': content,
        'messageType': messageType,
        'header': header,
        'user_agent': userAgent,
        'Authorization': Authorization,
        'terminal': 'shipper',
      });
    }
  }

  /// 检查SDK是否已初始化
  bool check() {
    if (null == _aliyunLogSdk) {
      print('you should init producer first.');
      return false;
    }
    return true;
  }

  /// 销毁日志生产者
  void _destroy() async {
    if (!check()) {
      return;
    }
    await _aliyunLogSdk!.destroy();
    print('destroy plugin success');
  }

}