import 'package:flutter/services.dart';
/// 原生方法调用工具类
/// 用于与Android/iOS原生代码进行交互
class NativeCall {
  /// 方法通道，用于与原生代码通信
  final _methodChannel = const MethodChannel('com.almas.dinner');


  /// 启动测试推送
  /// [param] 测试参数
  Future<void> startTest(Map<String, dynamic> param) async {
    return await _methodChannel.invokeMethod<dynamic>("test_push", param);
  }

  /// 获取极光推送注册ID
  /// [param] 注册参数
  Future<String> getRegId() async {
    String regId = await _methodChannel.invokeMethod<dynamic>("registerJPush");
    print('regId ----> $regId');
    return regId;
  }

  /// 检查通知是否开启
  Future<String> isOpenNotify() async {
    String value = await _methodChannel.invokeMethod<dynamic>("checkNotify");
    return value;
  }

  /// 打开通知设置页面
  Future<void> openSettingsForNotification() async {
    await _methodChannel.invokeMethod<dynamic>("openSettingsForNotification");
  }

  /// 检查应用是否已安装
  /// [packageName] 应用包名
  Future<bool> isAppInstalled({required String packageName}) async {
    bool result = await _methodChannel.invokeMethod<dynamic>(
        "isAppInstalled",
        {"packageName": packageName}
    );
    return result;
  }
}