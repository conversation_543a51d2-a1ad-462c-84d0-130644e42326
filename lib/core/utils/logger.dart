import 'package:flutter/foundation.dart';

/// 日志工具类
class Logger {
  /// 打印调试信息
  static void d(String tag, String message) {
    if (kDebugMode) {
      print('D/$tag: $message');
    }
  }

  /// 打印信息
  static void i(String tag, String message) {
    if (kDebugMode) {
      print('I/$tag: $message');
    }
  }

  /// 打印警告
  static void w(String tag, String message) {
    if (kDebugMode) {
      print('W/$tag: $message');
    }
  }

  /// 打印错误
  static void e(String tag, String message) {
    if (kDebugMode) {
      print('E/$tag: $message');
    }
  }
}
