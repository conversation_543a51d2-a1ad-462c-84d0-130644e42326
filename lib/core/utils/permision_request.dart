import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/features/home/<USER>/index/home_controller.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/main.dart';

/// 请求指定权限
///
/// [permission] 要请求的权限类型
/// [context] 用于显示权限设置对话框的上下文
///
/// 返回值：
/// - true: 已获得权限
/// - false: 未获得权限
///
/// 权限状态处理：
/// - isGranted: 已授权
/// - isDenied: 被拒绝但可再次请求
/// - isPermanentlyDenied: 永久拒绝，需手动设置
Future<bool> getRequestPermision(
    {required final Permission permission,
    required final BuildContext context}) async {
  print("permission: ${permission.value}");
  final permissionStatus = await permission.request();
  print("permissionStatus: ${permissionStatus.name}");

  if (permissionStatus.isGranted) {
    // 已获得授权，需要在系统设置中关闭
    print("已给授权,不过要关闭的话去app设置页面去关闭对应的权限: isDenied");
    return true;
  } else if (permissionStatus.isDenied) {
    // 权限被拒绝，但可以再次请求
    print("GPS权限被拒绝，但可以请求再次询问: isDenied");
    return false;
  } else if (permissionStatus.isPermanentlyDenied) {
    // 权限被永久拒绝，需要用户手动修改设置
    print("GPS权限被永久拒绝，需要用户手动修改权限设置: isPermanentlyDenied");
    await openPermisionSetting(context: context, permission: permission);
  }

  return false;
}

/// 获取权限对应的图标和文案
Map<String, dynamic> _getPermissionInfo(Permission? permission) {
  // 默认值
  String imagePath = "assets/images/gps_tips.png";
  String title = S.current.notification_title;
  String message = S.current.open_gps_tips;
  IconData icon = Icons.settings;

  // 根据权限类型设置不同的图标和文案
  if (permission != null) {
    switch (permission) {
      case Permission.location:
      case Permission.locationAlways:
      case Permission.locationWhenInUse:
        imagePath = "assets/images/gps_tips.png";
        title = S.current.notification_title;
        message = S.current.open_gps_tips;
        icon = Icons.location_on;
        break;
      case Permission.camera:
        imagePath = "assets/images/camera_tips.png";
        title = S.current.notification_title;
        message = S.current.open_gps_tips;
        icon = Icons.camera_alt;
        break;
      case Permission.microphone:
        imagePath = "assets/images/mic_tips.png";
        title = S.current.notification_title;
        message = S.current.open_gps_tips;
        icon = Icons.mic;
        break;
      case Permission.storage:
      case Permission.photos:
      case Permission.photosAddOnly:
        imagePath = "assets/images/storage_tips.png";
        title = S.current.notification_title;
        message = S.current.open_gps_tips;
        icon = Icons.photo_library;
        break;
      case Permission.contacts:
        imagePath = "assets/images/contacts_tips.png";
        title = S.current.notification_title;
        message = S.current.open_gps_tips;
        icon = Icons.contacts;
        break;
      default:
        // 使用默认值
        break;
    }
  }

  return {
    'imagePath': imagePath,
    'title': title,
    'message': message,
    'icon': icon,
  };
}

/// 显示权限设置引导对话框
/// [context] 用于显示对话框的上下文
/// [permission] 需要请求的权限类型
/// [title] 自定义标题
/// [explanation] 自定义说明文本
///
/// 对话框包含：
/// - 标题
/// - 权限图标（根据权限类型变化）
/// - 提示文本
/// - "取消"和"去设置"按钮
Future<void> openPermisionSetting({
  required final BuildContext context,
  final Permission? permission,
  final String? title,
  final String? explanation,
  final Function? onComplete,
  final String? confirmText,
}) async {
  // 获取权限对应的信息
  final permissionInfo = _getPermissionInfo(permission);

  // 使用自定义标题和说明文本（如果提供）
  final dialogTitle = title ?? permissionInfo['title'];
  final dialogMessage = explanation ?? permissionInfo['message'];

  // 获取当前语言方向
  final currentLang = ProviderScope.containerOf(context).read(languageProvider);
  final isRtl = currentLang == "ug";

  await showDialog(
    context: context,
    builder: (final context) => Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      elevation: 8,
      child: Directionality(
        textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
        child: Container(
          padding: EdgeInsets.all(24.w),
          width: MediaQuery.of(context).size.width - 48.w,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Row(
                children: [
                  Icon(
                    permissionInfo['icon'],
                    color: AppColors.baseGreenColor,
                    size: 24.sp,
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      dialogTitle,
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        fontFamily: "UkijTuzTom",
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: 24.h),

              // 图标
              ClipRRect(
                borderRadius: BorderRadius.circular(12.r),
                child: Container(
                  width: 80.w,
                  height: 80.w,
                  decoration: BoxDecoration(
                    color: AppColors.baseGreenColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Center(
                    child: Image.asset(
                      permissionInfo['imagePath'],
                      width: 48.w,
                      height: 48.w,
                      errorBuilder: (context, error, stackTrace) {
                        // 如果图片加载失败，显示图标
                        return Icon(
                          permissionInfo['icon'],
                          color: AppColors.baseGreenColor,
                          size: 48.sp,
                        );
                      },
                    ),
                  ),
                ),
              ),

              SizedBox(height: 24.h),

              // 提示文本
              Text(
                dialogMessage,
                style: TextStyle(
                  fontSize: 16.sp,
                  height: 1.4,
                  color: Colors.black87,
                  fontFamily: "UkijTuzTom",
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 24.h),

              // 按钮行
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 取消按钮
                  Expanded(
                    flex: 1,
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: AppColors.baseGreenColor),
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        S.current.dialog_text_no,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.baseGreenColor,
                          fontFamily: "UkijTuzTom",
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 16.w),

                  // 去设置按钮
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: () {
                        if (onComplete != null) {
                          onComplete.call();
                        } else {
                          globalContainer.read(isOpenPermissionSettingProvider.notifier).state = true;
                          openAppSettings();
                        }
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.baseGreenColor,
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        confirmText ?? S.current.got_it,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.white,
                          fontFamily: "UkijTuzTom",
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    ),
  );
  return;
}
