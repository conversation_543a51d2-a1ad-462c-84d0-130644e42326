import 'dart:math';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/main.dart';
import 'package:user_app/features/order/providers/order_provider.dart';

/// 订单计算工具类，包含与订单价格相关的各种计算方法
class CalculationUtils {
  /// 计算配送费减免
  /// 参数:
  /// - price: 商品总价
  /// - shipmentSteps: 配送费阶梯信息，可以是 ShipmentStep 对象或包含 min_delivery_price 和 shipment_reduce 的 Map
  /// 返回一个包含以下信息的 Map:
  /// - shipmentReduceFee: 减免的配送费
  /// - isShipment: 是否有配送费规则
  /// - isSteps: 是否满足减免条件
  /// - surplus: 距离满足减免条件还差的金额
  /// - currentBalance: 当前可减免的配送费
  /// - currentStep: 当前阶梯信息 {price: 满足金额, reduce: 减免金额}
  /// - nextStep: 下一个阶梯信息 {price: 满足金额, reduce: 减免金额}
  /// - currentStepText: 当前阶梯提示文本，如"购买满34元减4元"
  /// - nextStepText: 下一个阶梯提示文本，如"购买满50元减6元"
  static Map<String, dynamic> calculateShipmentSteps(
    num price,
    dynamic shipmentSteps,
  ) {
    num shipmentReduceFee = 0;
    bool isShipment = false;
    bool isSteps = false;
    num surplus = 0;
    num currentBalance = 0;

    // 新增字段
    Map<String, dynamic> currentStep = {};
    Map<String, dynamic> nextStep = {};
    String currentStepText = "";
    String nextStepText = "";

    // 处理 ShipmentStep 类型
    if (shipmentSteps is ShipmentStep &&
        shipmentSteps.shipmentReduce != null &&
        shipmentSteps.shipmentReduce! > 0) {
      // 只有当 minDeliveryPrice 存在时才进行计算
      if (shipmentSteps.minDeliveryPrice != null) {
        final minDeliveryPrice = shipmentSteps.minDeliveryPrice!;
        final shipmentReduce = shipmentSteps.shipmentReduce!;

        isShipment = true;
        surplus = max(minDeliveryPrice - price, 0);
        currentBalance = shipmentReduce;

        // 设置当前阶梯信息
        currentStep = {
          'price': minDeliveryPrice,
          'reduce': shipmentReduce,
        };
        currentStepText = "购买满${minDeliveryPrice}元减${shipmentReduce}元";

        if (price >= minDeliveryPrice) {
          isSteps = true;
          shipmentReduceFee = shipmentReduce;
        }
      }
    }
    // 处理 Map 类型
    else if (shipmentSteps is Map<String, dynamic>) {
      // 只有当两个关键数据都存在时才进行计算
      if (shipmentSteps.containsKey('min_delivery_price') &&
          shipmentSteps.containsKey('shipment_reduce') &&
          shipmentSteps['min_delivery_price'] != null &&
          shipmentSteps['shipment_reduce'] != null) {
        final minDeliveryPrice = shipmentSteps['min_delivery_price'] as num;
        final shipmentReduce = shipmentSteps['shipment_reduce'] as num;

        if (shipmentReduce > 0) {
          isShipment = true;
          surplus = max(minDeliveryPrice - price, 0);
          currentBalance = shipmentReduce;

          // 设置当前阶梯信息
          currentStep = {
            'price': minDeliveryPrice,
            'reduce': shipmentReduce,
          };
          currentStepText = "购买满${minDeliveryPrice}元减${shipmentReduce}元";

          if (price >= minDeliveryPrice) {
            isSteps = true;
            shipmentReduceFee = shipmentReduce;
          }
        }
      }
    }

    return {
      'shipmentReduceFee': shipmentReduceFee,
      'isShipment': isShipment,
      'isSteps': isSteps,
      'surplus': surplus,
      'currentBalance': currentBalance,
      'currentStep': currentStep,
      'nextStep': nextStep,
      'currentStepText': currentStepText,
      'nextStepText': nextStepText,
    };
  }

  /// 计算满减优惠
  /// 参数:
  /// - price: 商品总价
  /// - steps: 满减阶梯列表
  /// 返回一个包含以下信息的 Map:
  /// - reduceFee: 满减金额
  /// - isSteps: 是否满足满减条件
  /// - surplus: 距离满足下一个满减档次还差的金额
  /// - currentBalance: 当前满减金额
  /// - currentStep: 当前阶梯信息 {price: 满足金额, reduce: 减免金额}
  /// - nextStep: 下一个阶梯信息 {price: 满足金额, reduce: 减免金额}
  /// - currentStepText: 当前阶梯提示文本，如"购买满34元减4元"
  /// - nextStepText: 下一个阶梯提示文本，如"购买满50元减6元"
  static Map<String, dynamic> calculateReductionSteps(
      num price, List<ReductionStep> steps) {
    // 初始化返回值
    num reduceFee = 0;
    bool isSteps = false;
    num surplus = 0;
    num currentBalance = 0;

    // 新增字段
    Map<String, dynamic> currentStep = {};
    Map<String, dynamic> nextStep = {};
    String currentStepText = "";
    String nextStepText = "";

    // 按价格从低到高排序满减阶梯
    steps.sort((a, b) => (a.price ?? 0).compareTo(b.price ?? 0));
    for (var step in steps) {
      print('满${step.price}减${step.reduce}');
    }

    // 从高到低遍历满减阶梯，找到最大的满足条件的优惠
    for (var i = steps.length - 1; i >= 0; i--) {
      var step = steps[i];
      if (step.price != null && price >= step.price!) {
        reduceFee = step.reduce ?? 0;
        isSteps = true;
        currentBalance = step.reduce ?? 0;

        // 设置当前阶梯信息
        currentStep = {
          'price': step.price,
          'reduce': step.reduce,
        };
        currentStepText = "购买满${step.price}元减${step.reduce}元";

        // 如果有下一个阶梯，设置下一个阶梯信息
        if (i < steps.length - 1) {
          var nextStepItem = steps[i + 1];
          nextStep = {
            'price': nextStepItem.price,
            'reduce': nextStepItem.reduce,
          };
          nextStepText = "购买满${nextStepItem.price}元减${nextStepItem.reduce}元";
        }

        break;
      }
    }

    // 如果没有找到满足条件的阶梯，使用第一个阶梯计算差额
    if (!isSteps && steps.isNotEmpty) {
      var firstStep = steps[0]; // 最小金额的阶梯
      if (firstStep.price != null) {
        surplus = firstStep.price! - price;
        currentBalance = firstStep.reduce ?? 0;

        // 设置当前阶梯信息
        currentStep = {
          'price': firstStep.price,
          'reduce': firstStep.reduce,
        };
        currentStepText = "购买满${firstStep.price}元减${firstStep.reduce}元";

        // 如果有下一个阶梯，设置下一个阶梯信息
        if (steps.length > 1) {
          var nextStepItem = steps[1];
          nextStep = {
            'price': nextStepItem.price,
            'reduce': nextStepItem.reduce,
          };
          nextStepText = "购买满${nextStepItem.price}元减${nextStepItem.reduce}元";
        }
      }
    }

    return {
      'reduceFee': reduceFee,
      'isSteps': isSteps,
      'surplus': surplus,
      'currentBalance': currentBalance,
      'currentStep': currentStep,
      'nextStep': nextStep,
      'currentStepText': currentStepText,
      'nextStepText': nextStepText,
    };
  }

  /// 计算餐盒费
  /// 参数:
  /// - count: 商品数量
  /// - lunchBoxFee: 单个餐盒费
  /// - lunchBoxAccommodate: 餐盒容量（每个餐盒可容纳的商品数量）
  /// 返回总餐盒费
  static num calculateLunchBoxFee(
    final int count,
    final num lunchBoxFee,
    final int lunchBoxAccommodate,
  ) {
    if (lunchBoxAccommodate <= 0) return 0;
    final lunchBoxCount = (count / lunchBoxAccommodate).ceil();
    return lunchBoxCount * lunchBoxFee;
  }

  /// 计算商品价格（考虑普通价格、优惠价、秒杀价和多份打折）
  /// 参数:
  /// - count: 商品数量
  /// - normalPrice: 普通价格（对应微信小程序的 price）
  /// - preferentialPrice: 优惠价（可选）
  /// - seckillPrice: 秒杀价（可选，在微信小程序中秒杀使用 price 字段）
  /// - seckillMaxCount: 秒杀最大数量（可选）
  /// - isSeckill: 是否是秒杀商品
  /// - maxOrderCount: 优惠最大数量（可选）
  /// - oldPrice: 原价（超出优惠后的价格）
  /// - multiDiscountSteps: 多份打折步骤（可选）
  /// - multiDiscountAdvancePrice: 多份打折当前价格（可选）
  /// 返回总商品价格
  static num calculateFoodPrice(
    final int count,
    final num normalPrice, {
    final num? preferentialPrice,
    final num? seckillPrice,
    final int seckillMaxCount = 0,
    final bool isSeckill = false,
    final int maxOrderCount = 0, // 新增优惠最大数量参数
    final num? oldPrice, // 新增原价参数
    final List<MultiDiscountStep>? multiDiscountSteps,
    final num? multiDiscountAdvancePrice,
    final int? specialActive,
  }) {
    // 如果有多份打折，使用多份打折的计算逻辑
    if (multiDiscountSteps != null && multiDiscountSteps.isNotEmpty) {
      return calculateMultiDiscountFoodPrice(
        count,
        normalPrice,
        multiDiscountSteps,
      );
    }

    num foodPrice = 0;

    // 按照微信小程序 calculateFoodFee 的逻辑计算各价格阶段的数量

    // 秒杀数量：如果有秒杀活动且活跃，计算秒杀部分数量
    final curSecCount = isSeckill && seckillMaxCount > 0
        ? (count <= seckillMaxCount ? count : seckillMaxCount)
        : 0;

    // 优惠数量：剩余数量中享受优惠的部分（与微信小程序逻辑一致）
    final curPreCount = preferentialPrice != null && maxOrderCount > 0
        ? min(count - curSecCount, maxOrderCount)
        : 0;

    // 特价
    final curSpecialCount = specialActive == 1 && maxOrderCount > 0
        ? (count <= maxOrderCount ? count : maxOrderCount)
        : 0;

    // 原价数量：超出秒杀和优惠限制的部分
    final oldCount = count - curSecCount - curPreCount - curSpecialCount;

    // 累加各部分价格
    if (curSecCount > 0) {
      // 秒杀价格：在微信小程序中，秒杀使用 price 字段，这里使用 seckillPrice 或 normalPrice
      final seckillPriceToUse = seckillPrice ?? normalPrice;
      foodPrice += seckillPriceToUse * curSecCount;
    }

    if (curPreCount > 0 && preferentialPrice != null) {
      foodPrice += preferentialPrice * curPreCount;
    }
    if (specialActive == 1 && curSpecialCount > 0) {
      foodPrice += normalPrice * curSpecialCount;
    }

    if (oldCount > 0) {
      // 原价：优先使用传入的oldPrice，否则使用normalPrice
      final oldPriceToUse = oldPrice ?? normalPrice;
      foodPrice += oldPriceToUse * oldCount;
    }

    return foodPrice;
  }

  /// 计算多份打折商品价格
  /// 参数:
  /// - count: 商品数量
  /// - normalPrice: 普通价格
  /// - multiDiscountSteps: 多份打折步骤
  /// 返回总商品价格
  static num calculateMultiDiscountFoodPrice(
    final int count,
    final num normalPrice,
    final List<MultiDiscountStep> multiDiscountSteps,
  ) {
    if (count <= 0) return 0;

    num totalFoodPrice = 0;

    // 按照微信小程序的逻辑：遍历每个商品的数量来计算总价格
    for (int i = 1; i <= count; i++) {
      // 根据商品数量找到当前步骤
      final currentStep = multiDiscountSteps.firstWhere(
        (step) => step.number == i,
        orElse: () => MultiDiscountStep(),
      );

      // 如果找到当前步骤，则使用其价格，否则使用普通价格
      if (currentStep.price != null) {
        totalFoodPrice += currentStep.price!;
      } else {
        // 如果没有找到当前步骤，则使用普通价格
        totalFoodPrice += normalPrice;
      }
    }

    return totalFoodPrice;
  }

  /// 计算购物车总价
  /// 参数:
  /// - cartItems: 购物车商品列表
  /// - foodsData: 餐厅食品数据
  /// - deliveryMode: 配送模式（0: 配送, 1: 自取）
  /// 返回计算结果，包含各种价格和统计信息
  static CartCalculationResult calculateCart(
    final List<SelectFoodItem> cartItems, {
    final FoodsData? foodsData,
    final int deliveryMode = 0, // OrderCalculationConstants.DELIVERY_MODE
    final num couponAmount = 0,
  } // 新增优惠券金额参数
      ) {
    print('开始执行 calculateCart');
    // 购物车为空时返回默认值
    if (cartItems.isEmpty) {
      print('购物车为空，返回默认值');
      return CartCalculationResult();
    }

    // 获取满减阶梯和配送信息
    final reductionStepList = foodsData?.market?.steps?.reductionStep;

    // 优先从 getTakeTimeList 状态中获取配送信息
    Distribution? distribution;
    try {
      final takeTimeAsync = globalContainer.read(submitOrderInfoProvider);
      final takeTimeData = takeTimeAsync.hasValue ? takeTimeAsync.value : null;
      distribution = takeTimeData?.shipmentInfo?.distribution != null
          ? Distribution(
              shipment: takeTimeData!.shipmentInfo!.distribution!.shipment,
              param: takeTimeData.shipmentInfo!.distribution!.param,
              distance: takeTimeData.shipmentInfo!.distribution!.distance,
            )
          : foodsData?.distribution; // 如果获取不到则使用 foodsData 中的配送信息
    } catch (e) {
      print('从 getTakeTimeList 状态获取配送信息失败: $e，使用 foodsData 备用');
      distribution = foodsData?.distribution;
    }

    final shipmentStep = foodsData?.market?.steps?.shipmentStep;

    // 是否美食满减（餐厅满减和美食满减互斥）
    bool isFoodsReduction =
        foodsData?.market?.reductionFoodsId!.isNotEmpty ?? false;

    // 从 foodsData 构建配送信息
    final Map<String, dynamic> shipmentInfo = {};
    if (foodsData != null && distribution != null) {
      shipmentInfo['distribution'] = {
        'shipment': distribution.shipment,
        'param': distribution.param,
        'distance': distribution.distance,
      };

      if (shipmentStep != null) {
        shipmentInfo['shipmentSteps'] = {
          'min_delivery_price': shipmentStep.minDeliveryPrice,
          'shipment_reduce': shipmentStep.shipmentReduce,
          'distance_start': shipmentStep.distanceStart,
          'distance_end': shipmentStep.distanceEnd,
        };
      }
    }

    num totalFoodPrice = 0; // 商品总价
    num totalLunchBoxFee = 0; // 餐盒费总价
    int totalCount = 0; // 商品总数量
    int itemCount = cartItems.length; // 商品种类数量
    num reductionTotalPrice = 0; // 满减商品总价
    num totalProfit = 0; // 总利润
    // 优惠前的原始总价格
    num totalOriginalPrice = 0;
    num totalFoodOriginalPrice = 0;

    // 遍历购物车计算各项费用
    for (var item in cartItems) {
      // 确保 count 不为空
      final count = item.count ?? 0;
      if (count <= 0) continue;

      // 计算餐盒费
      final lunchBoxFee = calculateLunchBoxFee(
        count,
        item.lunchBoxFee ?? 0,
        item.lunchBoxAccommodate ?? 1,
      );
      totalLunchBoxFee += lunchBoxFee;

      // 计算商品费用
      final foodPrice = calculateFoodPrice(count, item.foodsPrice ?? 0,
          preferentialPrice: item.prefrentialPrice,
          seckillPrice: item.seckillPrice,
          seckillMaxCount: item.seckillMaxCount ?? 0,
          isSeckill: item.isSeckill ?? false,
          maxOrderCount: item.maxOrderCount ?? 0,
          oldPrice: item.oldPrice,
          multiDiscountSteps: item.multiDiscountSteps,
          multiDiscountAdvancePrice: item.multiDiscountAdvancePrice,
          specialActive: item.specialActive);
      totalFoodPrice += foodPrice;

      totalFoodOriginalPrice += (item.oldPrice ?? item.foodsPrice ?? 0) * count;
      // 累计商品数量
      totalCount += count;

      // 计算满减商品总价（如果商品有满减标签）
      if (item.reductionTags != null && item.reductionTags!.isNotEmpty) {
        reductionTotalPrice += foodPrice;
      }

      // 计算利润
      if (item.percent != null && item.percent! > 0) {
        totalProfit += calculateProfit(
          count,
          (item.foodsPrice ?? 0) + (item.lunchBoxFee ?? 0),
          item.percent!,
        );
      }
    }

    // 计算满减优惠
    num reductionDiscount = 0;
    num missingAmount = 0;
    bool canApplyReduction = false;

    // 满减阶梯信息
    Map<String, dynamic>? reductionCurrentStep;
    Map<String, dynamic>? reductionNextStep;
    String? reductionCurrentStepText;
    String? reductionNextStepText;

    print('商品总价: $totalFoodPrice');
    print('满减商品总价: $reductionTotalPrice');

    // 只有在有满减阶梯数据且不为空时才进行计算
    if (reductionStepList != null &&
        reductionStepList.isNotEmpty &&
        deliveryMode == 0) {
      print('开始计算满减优惠');
      // 计算满减结果， reductionTotalPrice参与满减金额, 如果餐厅设置了美食满减，就用reductionTotalPrice，不是使用totalFoodPrice
      final reductionResult = calculateReductionSteps(
        isFoodsReduction ? reductionTotalPrice : totalFoodPrice,
        reductionStepList,
      );
      reductionDiscount = reductionResult['reduceFee'] as num;
      canApplyReduction = reductionResult['isSteps'] as bool;
      missingAmount = reductionResult['surplus'] as num;

      // 获取满减阶梯信息
      reductionCurrentStep =
          reductionResult['currentStep'] as Map<String, dynamic>?;
      reductionNextStep = reductionResult['nextStep'] as Map<String, dynamic>?;
      reductionCurrentStepText = reductionResult['currentStepText'] as String?;
      reductionNextStepText = reductionResult['nextStepText'] as String?;

      print(
        '满减计算结果: 优惠金额=$reductionDiscount, 是否满足条件=$canApplyReduction, 差额=$missingAmount',
      );
    } else {
      print('不满足满减计算条件：');
      print(
        'reductionStepList为空: ${reductionStepList == null || reductionStepList.isEmpty}',
      );
      print('deliveryMode不是配送模式: $deliveryMode');
    }

    // 计算配送费 (仅在配送模式下计算)
    num shipmentFee = 0;
    num shipmentDiscount = 0;

    // 配送费减免阶梯信息
    Map<String, dynamic>? shipmentCurrentStep;
    Map<String, dynamic>? shipmentNextStep;
    String? shipmentCurrentStepText;
    String? shipmentNextStepText;

    if (distribution != null && deliveryMode == 0) {
      // 计算配送费（不考虑满减优惠）
      shipmentFee = calculateShipment(
        totalProfit,
        shipmentInfo['distribution'],
        totalFoodPrice,
        0,
      );

      // 计算配送费折扣（仅在有配送费减免规则时）
      if (shipmentInfo['shipmentSteps'] != null) {
        final steps = shipmentInfo['shipmentSteps'];
        // 注意：配送费减免基于商品总价（不包含满减优惠），与微信小程序保持一致
        final shipmentResult = calculateShipmentSteps(totalFoodPrice, steps);
        if (shipmentResult['isSteps'] as bool) {
          final reduceFee = shipmentResult['shipmentReduceFee'] as num;
          shipmentDiscount = reduceFee > shipmentFee ? shipmentFee : reduceFee;
        }

        // 获取配送费减免阶梯信息
        shipmentCurrentStep =
            shipmentResult['currentStep'] as Map<String, dynamic>?;
        shipmentNextStep = shipmentResult['nextStep'] as Map<String, dynamic>?;
        shipmentCurrentStepText = shipmentResult['currentStepText'] as String?;
        shipmentNextStepText = shipmentResult['nextStepText'] as String?;
      }
    }

    // 计算优惠券折扣（不能超过商品总价）
    num couponDiscount =
        couponAmount > totalFoodPrice ? totalFoodPrice : couponAmount;

    // 计算最终总价
    final subtotal = totalFoodPrice;
    final totalPrice = subtotal +
        totalLunchBoxFee +
        shipmentFee -
        reductionDiscount -
        shipmentDiscount -
        couponDiscount;

    // 美食原始价格+原始配送费+包装费
    totalOriginalPrice =
        totalFoodOriginalPrice + shipmentFee + totalLunchBoxFee;

    return CartCalculationResult(
      subtotal: subtotal,
      lunchBoxFee: totalLunchBoxFee,
      shipmentFee: shipmentFee,
      reductionDiscount: reductionDiscount,
      shipmentDiscount: shipmentDiscount,
      couponDiscount: couponDiscount,
      totalPrice: totalPrice,
      totalOriginalPrice: totalOriginalPrice,
      totalFoodOriginalPrice: totalFoodOriginalPrice,
      totalCount: totalCount,
      itemCount: itemCount,
      canApplyReduction: canApplyReduction,
      missingAmount: missingAmount,
      reductionCurrentStep: reductionCurrentStep,
      reductionNextStep: reductionNextStep,
      reductionCurrentStepText: reductionCurrentStepText,
      reductionNextStepText: reductionNextStepText,
      shipmentCurrentStep: shipmentCurrentStep,
      shipmentNextStep: shipmentNextStep,
      shipmentCurrentStepText: shipmentCurrentStepText,
      shipmentNextStepText: shipmentNextStepText,
    );
  }

  /// 计算购物车中所有商品的利润
  /// 参数:
  /// - count: 商品数量
  /// - price: 商品价格
  /// - percent: 利润百分比
  /// 返回商品利润
  static num calculateProfit(int count, num price, num percent) {
    final result = count * price * (percent / 100);
    return (result * 10000).round() / 10000; // 四舍五入到4位小数
  }

  /// 计算配送费（考虑利润和减免）
  /// 参数:
  /// - profit: 商品利润
  /// - distribution: 配送信息，包含以下字段:
  ///   - shipment: 最低配送费用
  ///   - param: 最小固定配送费用
  ///   - distance: 配送距离
  /// - price: 商品总价
  /// - checkReduce: 减免金额
  /// 返回配送费
  static num calculateShipment(
    num profit,
    final Map<String, dynamic> distribution,
    final num price,
    final num checkReduce,
  ) {
    // 检查价格是否大于0（与小程序保持一致）
    if (price <= 0) return 0;

    try {
      // 确保必要的字段存在
      if (!distribution.containsKey('shipment') ||
          !distribution.containsKey('param') ||
          distribution['shipment'] == null ||
          distribution['param'] == null) {
        return 0;
      }

      // 计算配送费减免比例（与微信小程序逻辑完全一致）
      final roundProfit = ((1 - (checkReduce / price)) * 10000).round() / 10000;

      // 计算减免后的利润
      final reduceFee = checkReduce > 0 ? profit * roundProfit : profit;

      // 计算最终配送费 (最低配送费 - 减免后利润)
      final allProfit =
          ((distribution['shipment'] - reduceFee) * 100).round() / 100;

      // 向上取整
      profit = allProfit.ceil();

      // 确保配送费不低于最小固定配送费
      profit = profit < distribution['param'] ? distribution['param'] : profit;

      return profit;
    } catch (err) {
      return 0;
    }
  }

  /// 计算购物车中商品价格差异（普通价、优惠价和秒杀价的数量分配）
  /// 参数:
  /// - item: 商品项
  /// 返回一个包含秒杀数量和优惠价数量的 Map
  static Map<String, int> calculateDifference(final Map<String, dynamic> item) {
    // 秒杀商品数量
    int curSecCount =
        item['seckill_active'] == true || item['seckill_active'] == 1
            ? min(item['count'], item['seckill_max_order_count'] ?? 0)
            : 0;

    // 优惠价商品数量
    int curPreCount =
        min(item['count'] - curSecCount, item['max_order_count'] ?? 0);

    curSecCount = curSecCount > 0 ? curSecCount : 0;
    curPreCount = curPreCount > 0 ? curPreCount : 0;

    return {'curSecCount': curSecCount, 'curPreCount': curPreCount};
  }

  /// 获取商品最终价格
  static num getFinalPrice({
    required final SelectFoodItem cartFoodItem,
  }) {
    return calculateFoodPrice(
      cartFoodItem.count!,
      cartFoodItem.foodsPrice ?? 0,
      preferentialPrice: cartFoodItem.prefrentialPrice,
      seckillPrice: cartFoodItem.seckillPrice,
      seckillMaxCount: cartFoodItem.seckillMaxCount ?? 0,
      isSeckill: cartFoodItem.isSeckill ?? false,
      maxOrderCount: cartFoodItem.maxOrderCount ?? 0,
      oldPrice: cartFoodItem.oldPrice,
      multiDiscountSteps: cartFoodItem.multiDiscountSteps,
      multiDiscountAdvancePrice: cartFoodItem.multiDiscountAdvancePrice,
      specialActive: cartFoodItem.specialActive,
    );
  }

  /// 计算活动优惠价格（抓雪花游戏/情侣专享）
  /// 参考微信小程序 commitOrder.js 中的逻辑
  ///
  /// 参数:
  /// - calculation: 购物车计算结果
  /// - snowGame: 雪花游戏数据
  /// - deliveryMode: 配送模式 (0: 配送, 1: 自取)
  /// - lunchBoxFee: 餐盒费
  /// - reductionDiscount: 满减优惠
  /// - couponPrice: 优惠券金额
  /// - shipmentFee: 配送费
  /// - shipmentDiscount: 配送费优惠
  ///
  /// 返回活动优惠金额
  static num calculateActivePercentPrice({
    required dynamic calculation,
    required dynamic snowGame,
    required int deliveryMode,
    required num lunchBoxFee,
    required num reductionDiscount,
    required num couponPrice,
    required num shipmentFee,
    required num shipmentDiscount,
  }) {
    // 检查雪花游戏数据是否有效
    if (snowGame?.percent == null ||
        snowGame?.leftAmount == null ||
        snowGame.percent <= 0 ||
        snowGame.leftAmount <= 0) {
      return 0;
    }

    // 自取订单价格计算
    final askForTotalPrice =
        calculation.subtotal + lunchBoxFee - reductionDiscount - couponPrice;

    // 配送订单价格计算
    final askForOldTotalPrice = calculation.subtotal +
        shipmentFee +
        lunchBoxFee -
        shipmentDiscount -
        reductionDiscount -
        couponPrice;

    // 根据配送模式计算游戏优惠
    final percent = snowGame.percent / 100.0; // 转换为小数
    final leftAmount = snowGame.leftAmount / 100.0; // 转换为元

    num gamePercent;
    if (deliveryMode == 0) {
      // 配送模式
      gamePercent = (askForOldTotalPrice * percent * 100).round() / 100;
    } else {
      // 自取模式
      gamePercent = (askForTotalPrice * percent * 100).round() / 100;
    }

    // 不能超过剩余金额
    final finalPercent = gamePercent > leftAmount ? leftAmount : gamePercent;

    return (finalPercent * 100).round() / 100; // 保留两位小数
  }

  /// 获取满减规则显示数据
  ///
  /// 参数:
  /// - reductionCurrentStep: 当前满减阶梯信息
  /// - reductionDiscount: 满减优惠金额
  ///
  /// 返回包含 price_start 和 price_end 的 Map
  static Map<String, String> getReductionDisplayData({
    required Map<String, dynamic>? reductionCurrentStep,
    required num reductionDiscount,
  }) {
    return {
      'price_start': reductionCurrentStep != null
          ? FormatUtil.formatPrice(reductionCurrentStep['price'] ?? 0)
          : '0', // 满足金额（对应微信小程序中的 price_start）
      'price_end': FormatUtil.formatPrice(
          reductionDiscount), // 减免金额（对应微信小程序中的 price_end）
    };
  }
}
