import 'dart:io';
import 'dart:typed_data';

import 'package:bot_toast/bot_toast.dart';
import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:user_app/core/utils/image_util.dart';
import 'package:user_app/core/utils/permission_helper.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/app/app_context.dart';

/// 相册工具类
/// 提供保存图片到相册的功能
class GalleryUtil {
  static final Dio _dio = Dio();

  /// 从URL下载图片到临时目录
  /// [imageUrl] 图片URL
  /// 返回临时文件路径和图片数据
  static Future<Map<String, dynamic>> downloadImageToCache(
      String imageUrl) async {
    try {
      // 下载图片为字节数据
      final response = await _dio.get(
        imageUrl,
        options: Options(responseType: ResponseType.bytes),
      );
      final imageData = Uint8List.fromList(response.data);

      // 获取临时目录
      final tempDir = await getTemporaryDirectory();
      final fileName = "${DateTime.now().millisecondsSinceEpoch}.png";
      final tempFile = File('${tempDir.path}/$fileName');

      // 写入图片数据到临时文件
      await tempFile.writeAsBytes(imageData);

      return {
        'file': tempFile,
        'data': imageData,
        'path': tempFile.path,
      };
    } catch (e) {
      print("下载图片失败: $e");
      rethrow;
    }
  }

  /// 将字节数据保存到临时文件
  /// [imageData] 图片字节数据
  /// 返回临时文件路径
  static Future<File> saveBytesToCache(Uint8List imageData) async {
    try {
      // 获取临时目录
      final tempDir = await getTemporaryDirectory();
      final fileName = "${DateTime.now().millisecondsSinceEpoch}.png";
      final tempFile = File('${tempDir.path}/$fileName');

      // 写入图片数据到临时文件
      await tempFile.writeAsBytes(imageData);
      return tempFile;
    } catch (e) {
      print("保存图片到缓存失败: $e");
      rethrow;
    }
  }

  /// 保存图片到相册
  /// [imageUrl] 图片URL
  /// [title] 保存的图片标题，默认为"Mulazim-时间戳"
  /// 返回保存结果：true-成功，false-失败
  static Future<bool> saveImageToGallery({
    required final String imageUrl,
    final String? title,
  }) async {
    try {
      // 请求并检查权限
      final status = await _requestPermission();
      if (!status) {
        // 如果权限被拒绝，显示提示
        BotToast.showText(text: S.current.please_allow_access_to_your_album);
        return false;
      }

      // 显示加载提示
      LoadingDialog().show();

      try {
        // 下载图片到缓存
        final result = await ImageUtil.downloadImageToCache(imageUrl);
        final tempFile = result['file'] as File;

        // 保存图片到相册
        final saveResult = await _saveFileToGallery(
          tempFile: tempFile,
          title: title,
        );

        // 判断保存结果
        if (saveResult) {
          BotToast.showText(text: S.current.poster_seve);
          return true;
        } else {
          BotToast.showText(text: S.current.retry);
          return false;
        }
      } catch (e) {
        // 隐藏加载提示
        print(e);
        BotToast.showText(text: S.current.retry);
        LoadingDialog().hide();
        return false;
      }
    } catch (e) {
      // 隐藏加载提示
      BotToast.showText(text: S.current.retry);
      LoadingDialog().hide();
      return false;
    }
  }

  /// 请求相册权限
  /// 返回权限状态：true-已授权，false-未授权
  static Future<bool> _requestPermission() async {
    try {
      // 使用 PhotoManager 请求权限，它会处理平台特定的权限请求
      final PermissionState permissionState =
          await PhotoManager.requestPermissionExtend();

      if (permissionState.isAuth) {
        // 已获得权限
        return true;
      } else if (permissionState.hasAccess) {
        // 有有限访问权限，这也够用了
        return true;
      } else if (permissionState == PermissionState.limited) {
        // 有限权限（iOS）
        return true;
      } else if (permissionState == PermissionState.denied) {
        // 权限被拒绝
        BotToast.showText(text: S.current.please_allow_access_to_your_album);
        return false;
      } else if (permissionState == PermissionState.restricted) {
        // 权限被永久拒绝，需要打开设置
        BotToast.showText(text: S.current.please_allow_access_to_your_album);
        await openAppSettings();
        return false;
      }

      return false;
    } catch (e) {
      print("权限请求失败: $e");
      return false;
    }
  }

  /// 将字节数据保存到相册
  /// [imageData] 图片字节数据
  /// [title] 保存的图片标题，默认为"Mulazim-时间戳"
  /// 返回保存结果：true-成功，false-失败
  static Future<bool> saveBytesToGallery({
    required final Uint8List imageData,
    final String? title,
  }) async {
    try {
      // 使用AppContext获取全局上下文
      final appContext = ProviderContainer().read(appContextProvider);
      final context = appContext.currentContext;

      // 如果无法获取上下文，则使用旧的权限请求方式
      if (context == null) {
        // 请求并检查权限
        final status = await _requestPermission();
        if (!status) {
          // 如果权限被拒绝，显示提示
          BotToast.showText(text: S.current.please_allow_access_to_your_album);
          return false;
        }
      } else {
        // 使用新的权限对话框请求相册权限
        final hasPermission = await PermissionHelper.requestPhotosPermission(
          explanation: S.current.photos_permission_explanation,
        );

        if (!hasPermission) {
          // 权限被拒绝
          BotToast.showText(text: S.current.please_allow_access_to_your_album);
          return false;
        }
      }

      // 显示加载提示
      LoadingDialog().show();

      try {
        // 保存到临时文件
        final tempFile = await ImageUtil.saveBytesToCache(imageData);

        // 保存图片到相册
        final saveResult = await _saveFileToGallery(
          tempFile: tempFile,
          title: title,
        );

        // 判断保存结果
        if (saveResult) {
          BotToast.showText(text: S.current.poster_seve);
          return true;
        } else {
          BotToast.showText(text: S.current.retry);
          return false;
        }
      } catch (e) {
        // 隐藏加载提示
        print(e);
        BotToast.showText(text: S.current.retry);
        LoadingDialog().hide();
        return false;
      }
    } catch (e) {
      // 隐藏加载提示
      BotToast.showText(text: S.current.retry);
      LoadingDialog().hide();
      return false;
    }
  }

  /// 内部方法：将临时文件保存到相册
  /// [tempFile] 临时文件
  /// [title] 保存的图片标题，默认为"Mulazim-时间戳"
  /// 返回保存结果：true-成功，false-失败
  static Future<bool> _saveFileToGallery({
    required File tempFile,
    String? title,
  }) async {
    try {
      bool saveResult = false;
      // 设置默认标题
      final imageTitle =
          title ?? "Mulazim-${DateTime.now().millisecondsSinceEpoch}";

      if (Platform.isIOS) {
        // iOS使用PhotoManager
        final result = await PhotoManager.editor.saveImageWithPath(
          tempFile.path,
          title: imageTitle,
        );
        saveResult = result != null;
      } else if (Platform.isAndroid) {
        try {
          // 首先尝试使用PhotoManager的API
          final result = await PhotoManager.editor.saveImageWithPath(
            tempFile.path,
            title: imageTitle,
          );
          saveResult = result != null;
        } catch (e) {
          print("PhotoManager保存失败，尝试备用方法: $e");
          // 如果PhotoManager失败，使用备用方法
          if (await Permission.storage.request().isGranted) {
            try {
              // 使用公共目录保存
              final directory = await getExternalStorageDirectory();
              if (directory != null) {
                final savePath = '${directory.path}/DCIM/Mulazim';
                final fileName =
                    ImageUtil.generateUniqueFileName(prefix: "Mulazim");

                // 确保目录存在
                await Directory(savePath).create(recursive: true);

                // 保存文件
                final file = File('$savePath/$fileName');
                await tempFile.copy(file.path);

                // 通知媒体扫描器
                try {
                  const channel = MethodChannel('app.channel.mulazim.media');
                  await channel.invokeMethod('scanFile', {'path': file.path});
                  saveResult = true;
                } catch (e) {
                  print("通知媒体扫描器失败: $e");
                  // 即使通知失败，如果文件已经保存成功，我们也认为操作成功
                  saveResult = await file.exists();
                }
              }
            } catch (e) {
              print("备用保存方法失败: $e");
              saveResult = false;
            }
          }
        }
      }

      // 如果上述方法都失败，尝试使用MediaStore API
      if (!saveResult) {
        try {
          const channel = MethodChannel('app.channel.mulazim.media');
          final result = await channel.invokeMethod('saveImage', {
            'path': tempFile.path,
            'title': imageTitle,
          });
          saveResult = result == true;
        } catch (e) {
          print("MediaStore保存失败: $e");
        }
      }

      // 删除临时文件
      await ImageUtil.deleteTempFile(tempFile);

      // 隐藏加载提示
      LoadingDialog().hide();

      return saveResult;
    } catch (e) {
      print("保存图片到相册失败: $e");

      // 删除临时文件
      await ImageUtil.deleteTempFile(tempFile);

      // 隐藏加载提示
      LoadingDialog().hide();
      return false;
    }
  }
}
