import 'package:user_app/data/models/address/street_list_model.dart';
import 'package:lpinyin/lpinyin.dart';

class Formatter {
  int langId;
  Formatter(this.langId);

  List<Map<String, dynamic>> formatList(List<StreetListData> list) {
    List<Map<String, dynamic>> newList = [];
    List<String> char = [];
    List<String> str = [];
    List<String> strZh = [
      "A",
      "B",
      "C",
      "D",
      "E",
      "F",
      "G",
      "H",
      "I",
      "J",
      "K",
      "L",
      "M",
      "N",
      "O",
      "P",
      "Q",
      "R",
      "S",
      "T",
      "U",
      "V",
      "W",
      "X",
      "Y",
      "Z",
      "#"
    ];
    List<String> strUy = [
      "ئا",
      "ئە",
      "ب",
      "ت",
      "پ",
      "ج",
      "چ",
      "خ",
      "د",
      "ر",
      "ز",
      "ژ",
      "س",
      "ش",
      "غ",
      "ف",
      "ق",
      "ك",
      "گ",
      "ڭ",
      "ل",
      "م",
      "ن",
      "ھ",
      "ئو",
      "ئۇ",
      "ئۆ",
      "ئۈ",
      "ۋ",
      "ئې",
      "ئى",
      "ي",
      "#"
    ];

    // Check the language ID and select the corresponding alphabet
    if (langId == 1) {
      // 维吾尔文分组
      str = strUy;
      for (var item in list) {
        var c = getChar(item.name ?? '');
        if (!char.contains(c)) {
          char.add(c);
        }
      }
    } else if (langId == 2) {
      // 中文拼音分组
      str = strZh;
      for (var item in list) {
        var c = getChineseChar(item.name ?? '');
        if (!char.contains(c)) {
          char.add(c);
        }
      }
    }

    int num = 0;
    for (var i = 0; i < str.length; i++) {
      for (var j = 0; j < char.length; j++) {
        if (char[j] == str[i]) {
          newList.add({'char': str[i], 'items': []});
          for (var k = 0; k < list.length; k++) {
            if (langId == 1) {
              // 维吾尔文分组
              if (getChar(list[k].name ?? '') == str[i]) {
                newList[num]['items'].add(list[k]);
              }
            } else if (langId == 2) {
              // 中文拼音分组
              if (getChineseChar(list[k].name ?? '') == str[i]) {
                newList[num]['items'].add(list[k]);
              }
            }
          }
          num++;
        }
      }
    }
    // Hide loading - You would implement your own method to handle this.
    hideLoading();
    return newList;
  }

  String getChar(String str) {
    String c;
    if (str.startsWith("ئ")) {
      c = str.substring(0, 2); // This handles the "ئ" and the second character.
    } else if (RegExp(r'^[0-9]*$').hasMatch(str.substring(0, 1))) {
      c = "#";
    } else {
      c = str.substring(0, 1); // Get the first character.
    }
    return c;
  }

  String getChineseChar(String str) {
    if (str.isEmpty) return "#";
    
    // 获取第一个字符的拼音首字母
    String firstChar = str.substring(0, 1);
    
    // 如果是数字，返回#
    if (RegExp(r'^[0-9]*$').hasMatch(firstChar)) {
      return "#";
    }
    
    // 如果是英文字母，直接返回大写
    if (RegExp(r'^[a-zA-Z]*$').hasMatch(firstChar)) {
      return firstChar.toUpperCase();
    }
    
    // 如果是中文字符，获取拼音首字母
    if (RegExp(r'[\u4e00-\u9fa5]').hasMatch(firstChar)) {
      String pinyin = PinyinHelper.getPinyinE(firstChar, defPinyin: '#', format: PinyinFormat.WITHOUT_TONE);
      if (pinyin.isNotEmpty) {
        return pinyin.substring(0, 1).toUpperCase();
      }
    }
    
    // 其他情况返回#
    return "#";
  }

  void hideLoading() {
    // Implement your loading hide logic here (this method is just a placeholder).
    print("Loading hidden");
  }
}
