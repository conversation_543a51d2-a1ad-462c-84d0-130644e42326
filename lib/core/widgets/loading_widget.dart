import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';

/// 加载中组件 - 对应微信小程序的loading组件
class LoadingWidget extends StatefulWidget {
  /// 显示的文本
  final String? message;

  /// 是否全屏显示
  final bool isFullScreen;

  /// 构造函数
  const LoadingWidget({
    super.key,
    this.message,
    this.isFullScreen = true,
  });

  @override
  State<LoadingWidget> createState() => _LoadingWidgetState();
}

class _LoadingWidgetState extends State<LoadingWidget> {
  bool _dialogShown = false;

  @override
  void initState() {
    super.initState();
    // 如果是全屏显示，显示LoadingDialog
    if (widget.isFullScreen) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && context.mounted) {
          LoadingDialog().show(message: widget.message);
          _dialogShown = true;
        }
      });
    }
  }

  @override
  void didUpdateWidget(LoadingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 处理全屏状态或消息变化
    if (widget.isFullScreen != oldWidget.isFullScreen ||
        widget.message != oldWidget.message) {
      if (widget.isFullScreen) {
        // 从非全屏变为全屏，显示对话框
        if (!_dialogShown && mounted && context.mounted) {
          LoadingDialog().show(message: widget.message);
          _dialogShown = true;
        }
      } else if (oldWidget.isFullScreen) {
        // 从全屏变为非全屏，隐藏对话框
        if (_dialogShown) {
          LoadingDialog().hide();
          _dialogShown = false;
        }
      }
    }
  }

  @override
  void dispose() {
    // 组件销毁时，如果对话框已显示，则隐藏对话框
    if (_dialogShown) {
      LoadingDialog().hide();
    }
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    // 如果是全屏显示，返回一个空小部件，实际显示由LoadingDialog处理
    if (widget.isFullScreen) {
      return const SizedBox.shrink();
    }

    // 文本小部件
    final Widget? textWidget =
        widget.message != null && widget.message!.isNotEmpty
            ? Padding(
                padding: EdgeInsets.only(top: 10.h),
                child: Text(
                  widget.message!,
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              )
            : null;

    // 非全屏显示
    return Center(
      child: textWidget != null
          ? Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                Flexible(child: textWidget),
              ],
            )
          : CircularProgressIndicator(),
    );
  }
}
