import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 加载更多组件
///
/// 通常用于列表底部显示加载更多状态
class LoadingMoreWidget extends StatelessWidget {
  /// 自定义文本
  final String? text;

  /// 自定义颜色
  final Color? color;

  /// 自定义加载指示器尺寸
  final double? indicatorSize;

  /// 自定义文本样式
  final TextStyle? textStyle;

  /// 自定义上下边距
  final double? verticalPadding;

  /// 构造函数
  const LoadingMoreWidget({
    super.key,
    this.text,
    this.color,
    this.indicatorSize,
    this.textStyle,
    this.verticalPadding,
  });

  @override
  Widget build(final BuildContext context) {
    final defaultColor = color ?? AppColors.textPrimaryColor;
    final defaultIndicatorSize = indicatorSize ?? 16.0;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: verticalPadding ?? 15.h),
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 使用iOS风格的活动指示器
            CupertinoActivityIndicator(
              radius: defaultIndicatorSize / 2,
              color: defaultColor,
            ),
            SizedBox(width: 10.w),
            Text(
              text ?? S.current.loading_more,
              style: textStyle ??
                  TextStyle(
                    fontSize: 14.sp,
                    color: defaultColor,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
