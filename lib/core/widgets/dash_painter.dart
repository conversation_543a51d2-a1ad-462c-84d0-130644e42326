import 'package:flutter/widgets.dart';
import 'package:flutter/material.dart';

/// 虚线绘制器
class DashPainter extends CustomPainter {
  /// 虚线颜色
  final Color color;

  /// 线条宽度
  final double strokeWidth;

  /// 边框圆角半径
  final double borderRadius;

  /// 虚线模式，默认为[5, 5]，表示5像素的线段和5像素的间隔
  final List<double> dashPattern;

  /// 构造函数
  DashPainter({
    required this.color,
    required this.strokeWidth,
    required this.borderRadius,
    this.dashPattern = const [5, 5],
  });

  @override
  void paint(final Canvas canvas, final Size size) {
    // 创建画笔
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    // 创建圆角矩形路径
    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(
          strokeWidth / 2,
          strokeWidth / 2,
          size.width - strokeWidth,
          size.height - strokeWidth,
        ),
        Radius.circular(borderRadius),
      ));

    // 创建虚线路径
    final dashPath = Path();
    double distance = 0;
    for (final metric in path.computeMetrics()) {
      while (distance < metric.length) {
        // 获取虚线段长度
        final dashLength = dashPattern[0];
        // 获取间隔长度
        final gapLength = dashPattern[1 % dashPattern.length];
        // 添加虚线段到路径
        dashPath.addPath(
          metric.extractPath(distance, distance + dashLength),
          Offset.zero,
        );
        // 更新距离，包括线段和间隔
        distance += dashLength + gapLength;
      }
    }

    // 绘制虚线路径
    canvas.drawPath(dashPath, paint);
  }

  @override
  bool shouldRepaint(covariant final CustomPainter oldDelegate) => false;
}

/// 创建一个带有虚线边框的容器
Widget buildDashedContainer({
  required Widget child,
  required double width,
  required double height,
  Color dashColor = const Color(0xFFDDDDDD),
  double strokeWidth = 1.5,
  double borderRadius = 20,
  List<double> dashPattern = const [5, 5],
}) {
  return Stack(
    children: [
      // 内容
      Positioned.fill(
        child: Container(
          margin: EdgeInsets.all(strokeWidth / 2),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          child: child,
        ),
      ),
      // 虚线边框
      CustomPaint(
        painter: DashPainter(
          color: dashColor,
          strokeWidth: strokeWidth,
          borderRadius: borderRadius,
          dashPattern: dashPattern,
        ),
        size: Size(width, height),
      ),
    ],
  );
}

/// 虚线容器组件
class DashedContainer extends StatelessWidget {
  /// 子组件
  final Widget child;

  /// 容器宽度
  final double width;

  /// 容器高度
  final double height;

  /// 虚线颜色
  final Color dashColor;

  /// 虚线宽度
  final double strokeWidth;

  /// 边框圆角半径
  final double borderRadius;

  /// 虚线模式
  final List<double> dashPattern;

  /// 构造函数
  const DashedContainer({
    Key? key,
    required this.child,
    required this.width,
    required this.height,
    this.dashColor = const Color(0xFFDDDDDD),
    this.strokeWidth = 1.5,
    this.borderRadius = 20,
    this.dashPattern = const [5, 5],
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return buildDashedContainer(
      child: child,
      width: width,
      height: height,
      dashColor: dashColor,
      strokeWidth: strokeWidth,
      borderRadius: borderRadius,
      dashPattern: dashPattern,
    );
  }
}
