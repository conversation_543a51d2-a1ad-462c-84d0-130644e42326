
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:user_app/core/theme/app_colors.dart';

class PrefectImageHeight extends StatelessWidget {
  BoxFit? fit = BoxFit.cover;
  PrefectImageHeight({Key? key,required this.imageUrl,required this.height, this.fit,}) : super(key: key);
  ///图片地址
  String imageUrl;
  ///图片高度
  double height;
  ///主要布局
  @override
  Widget build(BuildContext context) {
    return Image(
      image:CachedNetworkImageProvider(imageUrl,maxHeight: (height*4).toInt()),
      fit: BoxFit.fitHeight,
      height: height,
      errorBuilder: (context,error,stackTrace){
        return Image.asset(
          'assets/images/empty.png',
          fit: BoxFit.fitHeight,
          height: height,
        );
      },
      loadingBuilder: (context,child,loadingProgress){
        if(loadingProgress == null)return child;
        return Container(
          height: height,
          alignment: Alignment.center,
          child:CircularProgressIndicator(
            color: AppColors.baseGreenColor,
            value: loadingProgress.expectedTotalBytes != null ?
            loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes! : null,
          ),
        );
      },
    );
  }
}
