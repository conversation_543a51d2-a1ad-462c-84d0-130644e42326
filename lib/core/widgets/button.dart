import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 通用按钮组件
class MulazimButton extends StatelessWidget {
  /// 构造函数
  const MulazimButton({
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.width,
    this.height,
    this.color,
    this.textColor,
    this.fontSize,
    this.borderRadius,
    super.key,
  });

  /// 按钮文本
  final String text;

  /// 点击回调
  final VoidCallback onPressed;

  /// 是否加载中
  final bool isLoading;

  /// 宽度
  final double? width;

  /// 高度
  final double? height;

  /// 背景颜色
  final Color? color;

  /// 文本颜色
  final Color? textColor;

  /// 字体大小
  final double? fontSize;

  /// 圆角大小
  final double? borderRadius;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? double.infinity,
      height: height ?? 44.h,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color ?? Theme.of(context).primaryColor,
          foregroundColor: textColor ?? Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius ?? 8.r),
          ),
          padding: EdgeInsets.zero,
        ),
        child: isLoading
            ? SizedBox(
                width: 24.w,
                height: 24.w,
                child: CircularProgressIndicator(
                  strokeWidth: 2.w,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    textColor ?? Colors.white,
                  ),
                ),
              )
            : Text(
                text,
                style: TextStyle(
                  fontSize: fontSize ?? 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }
}
