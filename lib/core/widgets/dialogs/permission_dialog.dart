import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/utils/permision_request.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/generated/l10n.dart';

/// 权限提示对话框
/// 在请求系统权限的同时显示一个顶部提示框，说明权限用途
class PermissionDialog extends ConsumerStatefulWidget {
  /// 需要请求的权限列表
  final List<Permission> permissions;

  /// 每个权限对应的说明
  final Map<Permission, String> explanations;

  /// 权限弹窗的标题
  final String title;

  /// 对话框消失后的回调
  final Function(Map<Permission, bool> results)? onComplete;

  /// 权限弹框延迟消失的时间（毫秒）
  final int dismissDelay;

  /// 构造函数
  const PermissionDialog({
    super.key,
    required this.permissions,
    required this.explanations,
    this.title = '',
    this.onComplete,
    this.dismissDelay = 500,
  });

  /// 显示权限对话框的静态方法
  static Future<Map<Permission, bool>> show({
    final BuildContext? context,
    required final List<Permission> permissions,
    required final Map<Permission, String> explanations,
    final String title = '权限请求',
    final int dismissDelay = 500,
  }) async {
    print("显示权限对话框，请求权限: $permissions");

    // 如果没有需要请求的权限，直接返回空结果
    if (permissions.isEmpty) {
      print("没有需要请求的权限，跳过显示对话框");
      return {};
    }

    // 获取当前上下文，优先使用传入的context，其次使用全局context
    final currentContext = context ?? AppContext().currentContext;

    // 确保上下文存在
    if (currentContext == null) {
      print("错误：无法获取有效的BuildContext，权限请求失败");
      return {}; // 返回空结果
    }

    // 提前检查权限状态，避免显示不必要的对话框
    final Map<Permission, bool> results = {};
    for (var permission in permissions) {
      final status = await permission.status;

      // 如果权限已经被永久拒绝或被拒绝，直接显示系统设置引导
      if (status.isPermanentlyDenied) {
        print("权限 $permission 已被拒绝或永久拒绝，直接显示系统设置引导");
        await openPermisionSetting(
          context: currentContext,
          permission: permission,
          title: title,
          explanation: explanations[permission],
        );
        results[permission] = false;
        return results; // 直接返回结果，不显示权限对话框
      }

      // 如果权限已经被授予，记录结果
      if (status.isGranted) {
        results[permission] = true;
      }
    }

    // 过滤掉已经被授予的权限，只请求未授予的权限
    final List<Permission> permissionsToRequest = permissions
        .where((permission) =>
            !results.containsKey(permission) || !results[permission]!)
        .toList();

    // 如果所有权限都已授予，直接返回结果
    if (permissionsToRequest.isEmpty) {
      print("所有权限都已授予，跳过显示对话框");
      return results;
    }

    // 创建一个Completer来处理异步结果
    final completer = Completer<Map<Permission, bool>>();

    // 显示对话框，只请求未授予的权限
    showDialog(
      context: currentContext,
      barrierDismissible: false,
      builder: (final BuildContext context) {
        return PermissionDialog(
          permissions: permissionsToRequest,
          explanations: explanations,
          title: title,
          dismissDelay: dismissDelay,
          onComplete: (final dialogResults) {
            // 合并结果
            results.addAll(dialogResults);
            print("权限请求完成，结果: $results");
            completer.complete(results);
          },
        );
      },
    );

    // 等待结果
    return completer.future;
  }

  @override
  ConsumerState<PermissionDialog> createState() => _PermissionDialogState();
}

class _PermissionDialogState extends ConsumerState<PermissionDialog> {
  // 权限请求结果
  final Map<Permission, bool> _results = {};
  // 当前处理的权限索引
  int _currentIndex = 0;
  // 是否正在请求权限
  bool _isRequesting = false;
  // 是否请求已完成
  bool _isCompleted = false;
  // 成功图标动画控制器
  late AnimationController _successAnimController;

  @override
  void initState() {
    super.initState();
    print("PermissionDialog 初始化，请求 ${widget.permissions.length} 个权限");
    // 开始请求权限
    _requestNextPermission();
  }

  // 请求下一个权限
  Future<void> _requestNextPermission() async {
    if (_currentIndex >= widget.permissions.length) {
      // 所有权限都已请求完毕
      print("所有权限请求完毕，索引: $_currentIndex，总数: ${widget.permissions.length}");
      _finishRequest();
      return;
    }

    // 获取当前需要请求的权限
    final permission = widget.permissions[_currentIndex];
    print("开始请求权限: $permission，索引: $_currentIndex");

    // 设置状态为正在请求
    setState(() {
      _isRequesting = true;
    });

    // 使用现有的权限请求工具
    final result = await getRequestPermision(
      permission: permission,
      context: context,
    );

    print("权限 $permission 请求结果: $result");

    // 记录结果
    _results[permission] = result;

    // 检查是否需要更新状态
    if (mounted) {
      if (!result) {
        // 如果用户拒绝授权或永久拒绝授权
        print("用户拒绝授权，准备关闭权限对话框并显示系统设置引导");
        // 立即关闭权限对话框
        Navigator.of(context).pop();

        // 显示系统设置引导对话框
        await openPermisionSetting(
          context: context,
          permission: permission,
          title: widget.title,
          explanation: widget.explanations[permission],
        );

        // 完成请求流程
        widget.onComplete?.call(_results);
        return;
      }

      setState(() {
        _currentIndex++;
        _isRequesting = false;
      });

      // 检查是否是最后一个权限
      if (_currentIndex >= widget.permissions.length) {
        // 如果是最后一个权限，立即完成请求
        print("已处理最后一个权限，立即结束");
        _finishRequest();
      } else {
        // 否则请求下一个权限
        _requestNextPermission();
      }
    }
  }

  // 完成权限请求流程
  void _finishRequest() {
    print("权限请求完成，结果: $_results，立即关闭对话框");

    // 标记请求已完成
    setState(() {
      _isCompleted = true;
    });

    // 立即触发回调
    if (widget.onComplete != null) {
      widget.onComplete!(_results);
    }

    // 立即关闭对话框，不使用延迟
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(final BuildContext context) {
    // 当前权限的说明文本
    String currentExplanation = '';
    if (_currentIndex < widget.permissions.length) {
      final permission = widget.permissions[_currentIndex];
      currentExplanation = widget.explanations[permission] ??
          S.current.permission_default_explanation;
    }

    // 获取实际的权限总数，这就是传入的需要请求的权限数量
    final int totalPermissions = widget.permissions.length;

    final lang = ref.watch(languageProvider);

    return Material(
      type: MaterialType.transparency,
      child: GestureDetector(
        onTap: _isRequesting
            ? null
            : () {
                if (!_isRequesting && _isCompleted) {
                  Navigator.of(context).pop();
                }
              },
        child: Stack(
          children: [
            // 添加半透明背景，让对话框更明显
            Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(0.2),
              ),
            ),

            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 顶部安全区域
                SizedBox(height: MediaQuery.of(context).padding.top + 20.h),

                // 权限说明卡片
                Directionality(
                  textDirection:
                      lang == "ug" ? TextDirection.rtl : TextDirection.ltr,
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 20.w),
                    padding: EdgeInsets.all(15.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black26,
                          blurRadius: 15,
                          spreadRadius: 2,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 标题行
                        Row(
                          children: [
                            Icon(
                              _isCompleted
                                  ? Icons.check_circle
                                  : Icons.info_outline,
                              color: _isCompleted
                                  ? Colors.green
                                  : AppColors.baseGreenColor,
                              size: 20.sp,
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              _isCompleted
                                  ? S.current.permission_granted_success
                                  : (widget.title.isNotEmpty
                                      ? widget.title
                                      : S.current.permission_request_title),
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.bold,
                                fontFamily: "UkijTuzTom",
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 12.h),

                        // 说明文本
                        Text(
                          _isCompleted
                              ? S.current.permission_granted_message
                              : currentExplanation,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.black87,
                            fontFamily: "UkijTuzTom",
                          ),
                        ),

                        // 进度指示器
                        if (_isRequesting)
                          Padding(
                            padding: EdgeInsets.only(top: 12.h),
                            child: LinearProgressIndicator(
                              backgroundColor: Colors.grey[200],
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.baseGreenColor),
                            ),
                          ),

                        // 进度文本 - 使用实际的权限总数
                        Padding(
                          padding: EdgeInsets.only(top: 8.h),
                          child: Text(
                            _isCompleted
                                ? S.current.permission_progress_complete
                                : '${_currentIndex + 1}/$totalPermissions',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
