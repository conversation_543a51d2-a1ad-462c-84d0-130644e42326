import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/config.dart';
import 'package:user_app/generated/l10n.dart';

/// 数字日期选择器组件
class NumericDatePicker extends StatelessWidget {
  /// 初始日期
  final DateTime initialDate;

  /// 日期改变回调
  final Function(DateTime) onDateChanged;

  /// 最小年份
  final int minYear;

  /// 最大年份
  final int maxYear;

  /// 构造函数
  const NumericDatePicker({
    super.key,
    required this.initialDate,
    required this.onDateChanged,
    this.minYear = 1900,
    this.maxYear = 0, // 0表示当前年份
  });

  // 计算当月的天数
  int _daysInMonth(final int year, final int month) {
    return DateTime(year, month + 1, 0).day;
  }

  @override
  Widget build(final BuildContext context) {
    final int actualMaxYear = maxYear == 0 ? DateTime.now().year : maxYear;

    final yearController =
        FixedExtentScrollController(initialItem: initialDate.year - minYear);
    final monthController =
        FixedExtentScrollController(initialItem: initialDate.month - 1);
    final dayController =
        FixedExtentScrollController(initialItem: initialDate.day - 1);
    final isUg = Localizations.localeOf(context).languageCode == 'en';
    return Row(
      children: [
        // 年份选择器
        Expanded(
          flex: 2,
          child: CupertinoPicker(
            scrollController: yearController,
            itemExtent: 40,
            onSelectedItemChanged: (final int index) {
              final year = index + minYear;
              final month = monthController.selectedItem + 1;
              final day = dayController.selectedItem + 1;

              // 检查日期是否有效（例如2月30日是无效的）
              final maxDays = _daysInMonth(year, month);
              final actualDay = day > maxDays ? maxDays : day;

              onDateChanged(DateTime(year, month, actualDay));
            },
            children: List<Widget>.generate(
              actualMaxYear - minYear + 1,
              (final index) => Center(
                child: Text(
                  '${index + minYear}${isUg ? '-' : ''}${S.current.year}',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontFamily: AppConstants.mainFont,
                  ),
                ),
              ),
            ),
          ),
        ),
        // 月份选择器
        Expanded(
          flex: 1,
          child: CupertinoPicker(
            scrollController: monthController,
            itemExtent: 40,
            onSelectedItemChanged: (final int index) {
              final year = yearController.selectedItem + minYear;
              final month = index + 1;
              final day = dayController.selectedItem + 1;

              // 检查日期是否有效
              final maxDays = _daysInMonth(year, month);
              final actualDay = day > maxDays ? maxDays : day;

              onDateChanged(DateTime(year, month, actualDay));
            },
            children: List<Widget>.generate(
              12,
              (final index) => Center(
                child: Text(
                  '${index + 1}${isUg ? '-' : ''}${S.current.month}',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontFamily: AppConstants.mainFont,
                  ),
                ),
              ),
            ),
          ),
        ),
        // 日期选择器
        Expanded(
          flex: 1,
          child: CupertinoPicker(
            scrollController: dayController,
            itemExtent: 40,
            onSelectedItemChanged: (final int index) {
              final year = yearController.selectedItem + minYear;
              final month = monthController.selectedItem + 1;
              final day = index + 1;

              onDateChanged(DateTime(year, month, day));
            },
            children: List<Widget>.generate(
              31, // 最多31天
              (final index) => Center(
                child: Text(
                  '${index + 1}${isUg ? '-' : ''}${S.current.day1}',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontFamily: AppConstants.mainFont,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
