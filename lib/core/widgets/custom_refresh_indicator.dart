import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/loading_more_widget.dart';
import 'package:user_app/generated/l10n.dart';
import 'dart:async';
import 'dart:math';

// Color扩展方法，用于色调调整
extension ColorExtension on Color {
  /// 调整颜色亮度
  Color withLightness(double lightness) {
    final hsl = HSLColor.fromColor(this);
    return hsl.withLightness(lightness).toColor();
  }
}

/// 自定义下拉刷新指示器
///
/// 完全模拟微信小程序的下拉刷新体验:
/// 1. 下拉过程中页面跟随手指移动
/// 2. 顶部出现自定义动画区域 (点状动画)
/// 3. 动画在下拉开始时立即播放，完成后或取消时停止
/// 4. 下拉超过阈值会有振动反馈
/// 5. 手指释放后保持部分下拉状态直到加载完成
/// 6. 加载完成后页面自动回到原始位置
class CustomRefreshIndicator extends StatefulWidget {
  /// 刷新回调函数
  final Future<void> Function() onRefresh;

  /// 子组件
  final Widget child;

  /// 刷新指示器颜色 (点状动画颜色)
  final Color? color;

  /// 刷新指示器背景颜色
  final Color? backgroundColor;

  /// 是否启用下拉刷新
  final bool enablePullDown;

  /// 是否启用上拉加载更多
  final bool enablePullUp;

  /// 上拉加载更多回调
  final Future<void> Function()? onLoading;

  /// 是否还有更多数据
  final bool hasMoreData;

  /// 是否显示没有更多数据
  final bool hideNoMore;

  final double? footerHeight;

  /// 构造函数
  const CustomRefreshIndicator({
    super.key,
    required this.onRefresh,
    required this.child,
    this.color,
    this.backgroundColor,
    this.enablePullDown = true,
    this.enablePullUp = false,
    this.onLoading,
    this.hasMoreData = true,
    this.hideNoMore = false,
    this.footerHeight,
  });

  @override
  State<CustomRefreshIndicator> createState() => _CustomRefreshIndicatorState();
}

class _CustomRefreshIndicatorState extends State<CustomRefreshIndicator> {
  // 刷新控制器
  late RefreshController _refreshController;

  @override
  void initState() {
    super.initState();
    _refreshController = RefreshController(initialRefresh: false);

    // 初始化时如果没有更多数据，设置noMore状态
    if (!widget.hasMoreData) {
      _refreshController.loadNoData();
    }
  }

  @override
  void didUpdateWidget(CustomRefreshIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 处理hasMoreData状态变化
    if (oldWidget.hasMoreData != widget.hasMoreData) {
      if (!widget.hasMoreData) {
        // 从有数据变为没有数据
        if (!_refreshController.isLoading) {
          _refreshController.loadNoData();
        }
      } else if (widget.hasMoreData) {
        // 从没有数据变为有数据
        if (_refreshController.footerStatus == LoadStatus.noMore) {
          _refreshController.resetNoData();
        }
      }
    }
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  // 处理刷新
  void _onRefresh() async {
    HapticFeedback.mediumImpact(); // 触发振动反馈
    try {
      await widget.onRefresh();
      if (mounted) {
        _refreshController.refreshCompleted(resetFooterState: false);
      }
    } catch (e) {
      if (mounted) {
        _refreshController.refreshFailed();
      }
    }
  }

  // 处理加载更多
  void _onLoading() async {
    if (widget.onLoading == null) return;

    try {
      await widget.onLoading!();
      if (mounted) {
        if (!widget.hasMoreData) {
          _refreshController.loadNoData();
        } else {
          _refreshController.loadComplete();
        }
      }
    } catch (e) {
      if (mounted) {
        _refreshController.loadFailed();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 配置默认主题色
    final themeColor = widget.color ?? AppColors.primary;
    final bgColor = widget.backgroundColor ?? Colors.transparent;

    return SmartRefresher(
      controller: _refreshController,
      enablePullDown: widget.enablePullDown,
      enablePullUp: widget.enablePullUp,
      header: _buildHeader(themeColor, bgColor),
      footer: _buildFooter(),
      onRefresh: _onRefresh,
      onLoading: _onLoading,
      child: widget.child,
    );
  }

  // 构建头部刷新指示器
  Widget _buildHeader(Color themeColor, Color bgColor) {
    return CustomHeader(
      height: 60.h,
      builder: (BuildContext context, RefreshStatus? mode) {
        bool shouldAnimate = (mode != RefreshStatus.idle);

        return Container(
          color: bgColor,
          height: 60.h,
          alignment: Alignment.center,
          child: _RefreshLoadingIndicator(
            key: ValueKey('anim_$shouldAnimate'),
            shouldAnimate: shouldAnimate,
          ),
        );
      },
    );
  }

  // 构建底部加载更多指示器
  Widget _buildFooter() {
    return CustomFooter(
      height: widget.footerHeight ?? 55.h,
      builder: (BuildContext context, LoadStatus? mode) {
        switch (mode) {
          case LoadStatus.idle:
            return const SizedBox();

          case LoadStatus.loading:
            return const LoadingMoreWidget();

          case LoadStatus.failed:
            return Container(
              height: 55.h,
              alignment: Alignment.center,
              child: Text(S.current.retry),
            );

          case LoadStatus.canLoading:
            return Container(
              height: 55.h,
              alignment: Alignment.center,
              child: Text(S.current.loading_more),
            );

          case LoadStatus.noMore:
            if (widget.hideNoMore) {
              return const SizedBox();
            }
            return Container(
              height: 55.h,
              alignment: widget.footerHeight != null
                  ? Alignment.topCenter
                  : Alignment.center,
              child: Text(S.current.no_more_data),
            );

          default:
            return const SizedBox();
        }
      },
    );
  }
}

/// 内部动画显示组件
/// 使用loading图片实现旋转动画，模拟微信小程序的刷新动画
class _RefreshLoadingIndicator extends StatefulWidget {
  final bool shouldAnimate;

  const _RefreshLoadingIndicator({
    required this.shouldAnimate,
    super.key,
  });

  @override
  State<_RefreshLoadingIndicator> createState() =>
      _RefreshLoadingIndicatorState();
}

class _RefreshLoadingIndicatorState extends State<_RefreshLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  int _currentImageIndex = 1;
  final List<String> _loadingImages = [
    'assets/images/refresh-loading/loading1.png',
    'assets/images/refresh-loading/loading2.png',
    'assets/images/refresh-loading/loading3.png',
    'assets/images/refresh-loading/loading4.png',
    'assets/images/refresh-loading/loading5.png',
  ];
  Timer? _imageChangeTimer;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    // 根据初始状态决定是否播放动画
    if (widget.shouldAnimate) {
      _startAnimation();
    }
  }

  void _startAnimation() {
    _animationController.repeat();
    _imageChangeTimer?.cancel();
    _imageChangeTimer =
        Timer.periodic(const Duration(milliseconds: 166), (timer) {
      if (mounted) {
        setState(() {
          _currentImageIndex = (_currentImageIndex % 5) + 1;
        });
      }
    });
  }

  void _stopAnimation() {
    _animationController.stop();
    _imageChangeTimer?.cancel();
    _imageChangeTimer = null;
    if (mounted) {
      setState(() {
        _currentImageIndex = 1;
      });
    }
  }

  @override
  void didUpdateWidget(covariant _RefreshLoadingIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 仅在动画状态发生变化时更新
    if (widget.shouldAnimate != oldWidget.shouldAnimate) {
      if (widget.shouldAnimate) {
        _startAnimation();
      } else {
        _stopAnimation();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _imageChangeTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return SizedBox(
          width: 40.w,
          height: 40.h,
          child: Transform.rotate(
            angle: _animationController.value * 2 * 3.14159,
            child: Image.asset(
              'assets/images/refresh-loading/loading$_currentImageIndex.png',
              width: 40.w,
              height: 40.h,
              fit: BoxFit.contain,
            ),
          ),
        );
      },
    );
  }
}

/// 内部动画显示组件 (StatefulWidget)
/// 管理自己的动画控制器，根据传入的 shouldAnimate 启动/停止
class _AnimatedDotsIndicator extends StatefulWidget {
  final Color color;
  final bool shouldAnimate;

  const _AnimatedDotsIndicator({
    required this.color,
    required this.shouldAnimate,
    super.key,
  });

  @override
  State<_AnimatedDotsIndicator> createState() => _AnimatedDotsIndicatorState();
}

// 使用 SingleTickerProviderStateMixin 优化动画性能
class _AnimatedDotsIndicatorState extends State<_AnimatedDotsIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // 根据初始状态决定是否播放动画
    if (widget.shouldAnimate) {
      _animationController.repeat();
    }
  }

  @override
  void didUpdateWidget(covariant _AnimatedDotsIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 仅在动画状态发生变化时更新
    if (widget.shouldAnimate != oldWidget.shouldAnimate) {
      if (widget.shouldAnimate) {
        if (!_animationController.isAnimating) {
          _animationController.repeat();
        }
      } else {
        if (_animationController.isAnimating) {
          _animationController.stop();
        }
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return SizedBox(
          width: 100.w,
          height: 60.h,
          child: Stack(
            alignment: Alignment.center,
            children: List.generate(
              5, // 生成 5 个点
              (index) => _buildAnimatedDot(index),
            ),
          ),
        );
      },
    );
  }

  // 构建动画点
  Widget _buildAnimatedDot(int index) {
    final double delay = index * 0.1;
    final double effectiveTime = (_animationController.value - delay) % 1.0;
    final double angle = effectiveTime * 2 * pi;

    // 水平位移计算
    const double horizontalAmplitudeRpx = 60.0;
    final double horizontalAmplitude = horizontalAmplitudeRpx / 2 * 1.w;
    final double dx = horizontalAmplitude * sin(angle);

    // 垂直位移计算
    final double animationValueVertical = (_animationController.value - delay);
    final double t = (animationValueVertical % 1.0) * 4.0;
    double dy = 0;
    const double verticalAmplitudeRpx = 20.0;
    final double verticalAmplitude = verticalAmplitudeRpx / 2 * 1.h;

    if (t < 1.0) {
      dy = Tween<double>(begin: 0, end: -verticalAmplitude).transform(t);
    } else if (t < 2.0) {
      dy = Tween<double>(begin: -verticalAmplitude, end: 0).transform(t - 1.0);
    } else if (t < 3.0) {
      dy = Tween<double>(begin: 0, end: verticalAmplitude).transform(t - 2.0);
    } else {
      dy = Tween<double>(begin: verticalAmplitude, end: 0).transform(t - 3.0);
    }

    final dotSize = _getDotSize(index);
    final dotOpacity = _getDotOpacity(index);
    final leftOffset = (100.w / 2) + dx - (dotSize / 2);
    final topOffset = (60.h / 2) + dy - (dotSize / 2);

    return Positioned(
      key: ValueKey('dot_$index'),
      left: leftOffset,
      top: topOffset,
      child: Container(
        width: dotSize,
        height: dotSize,
        decoration: BoxDecoration(
          color: widget.color.withOpacity(dotOpacity),
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  // 根据索引获取点的大小
  double _getDotSize(int index) {
    const List<double> sizes = [20, 17.5, 15, 10, 5];
    return sizes[index].w;
  }

  // 根据索引获取点的透明度
  double _getDotOpacity(int index) {
    const List<double> opacities = [1.0, 0.8, 0.6, 0.4, 0.2];
    return opacities[index];
  }
}
