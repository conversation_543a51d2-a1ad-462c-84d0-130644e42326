import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

class PrefectImage extends StatelessWidget {
  /// 图片地址
  final String imageUrl;

  /// 图片宽度
  final double width;

  /// 图片高度
  final double height;

  /// 填充方式
  final BoxFit? fit;

  /// 缓存key
  final String? cacheKey;

  /// 自定义错误组件
  final Widget Function(BuildContext, Object, StackTrace?)? errorBuilder;

  /// 自定义加载组件
  final Widget Function(BuildContext, Widget, ImageChunkEvent?)? loadingBuilder;

  const PrefectImage({
    super.key,
    required this.imageUrl,
    required this.width,
    required this.height,
    this.fit = BoxFit.cover,
    this.cacheKey,
    this.errorBuilder,
    this.loadingBuilder,
  });

  @override
  Widget build(final BuildContext context) {
    // 图片相同时使用同一个key避免重复创建
    final String imageKey = cacheKey ?? imageUrl;

    return CachedNetworkImage(
      key: ValueKey(imageKey),
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      memCacheWidth: (width * 2).toInt(),
      memCacheHeight: (height * 2).toInt(),
      fadeInDuration:  Duration(milliseconds: 200),
      // placeholder: (context, url) => Container(
      //   width: width,
      //   height: height,
      //   alignment: Alignment.center,
      //   child: const CircularProgressIndicator(
      //     color: AppColors.baseGreenColor,
      //   ),
      // ),
      errorWidget: (final context, final url, final error) => Image.asset(
        'assets/images/empty.png',
        fit: fit,
        width: width,
        height: height,
      ),
    );
  }
}
