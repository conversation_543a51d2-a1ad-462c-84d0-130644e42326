import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 错误页面
class ErrorPage extends StatelessWidget {
  final String message;
  final VoidCallback onPressed;

  /// 错误页面
  const ErrorPage({super.key, required this.message, required this.onPressed});

  @override
  Widget build(final BuildContext context) {
    return Scaffold(
      body: Container(
        alignment: Alignment.center,
        margin: EdgeInsets.symmetric(vertical: 45.w),
        child: Column(
          children: [
            // SizedBox(
            //   width: 184.w,
            //   height: 184.h,
            //   child: Image.asset('assets/images/basic/error_page.png'),
            // ),
            SizedBox(height: 30.h),
            Text(message, style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondaryColor),),
            <PERSON><PERSON><PERSON><PERSON>(height: 30.h),
            <PERSON><PERSON><PERSON><PERSON>(onPressed: onPressed, child: Text(S.current.retry)),
          ],
        ),
      ),
    );
  }
}
