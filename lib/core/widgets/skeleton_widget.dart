import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 骨架屏组件 - 统一的骨架屏实现
///
/// 提供三种使用模式：
/// 1. 基础组件：用于创建自定义骨架屏布局（SkeletonWidget）
/// 2. 预设组件：常用的预定义骨架屏（如DefaultSkeleton）
/// 3. 加载遮罩：页面加载时替换内容的骨架屏（Skeleton.overlay()）
class Skeleton extends StatelessWidget {
  /// 是否激活动画
  final bool active;

  /// 子组件列表
  final List<Widget> children;

  /// 背景颜色
  final Color? backgroundColor;

  /// 构造函数 - 基础骨架屏
  const Skeleton({
    super.key,
    this.active = true,
    required this.children,
    this.backgroundColor,
  });

  /// 静态方法 - 骨架屏遮罩层
  ///
  /// 在页面加载时显示骨架屏，完成后显示内容
  static Widget overlay({
    required bool isLoading,
    required Widget child,
    Widget? skeleton,
    bool preserveScrollPosition = true,
  }) {
    return _SkeletonOverlay(
      isLoading: isLoading,
      child: child,
      skeleton: skeleton,
    );
  }

  /// 静态方法 - 默认骨架屏
  ///
  /// 提供一个通用的默认骨架屏
  static Widget defaultSkeleton() {
    return const DefaultSkeleton();
  }

  /// 静态方法 - 列表骨架屏
  ///
  /// 提供一个专用于列表的骨架屏
  static Widget listSkeleton({int itemCount = 5}) {
    return ListSkeleton(itemCount: itemCount);
  }

  @override
  Widget build(final BuildContext context) {
    return Container(
      color: backgroundColor ?? Colors.white,
      child: Stack(
        children: [
          SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
          if (active)
            Positioned.fill(
              child: SkeletonAnimation(),
            ),
        ],
      ),
    );
  }
}

/// 骨架屏遮罩层 - 内部实现
class _SkeletonOverlay extends StatelessWidget {
  /// 是否显示加载状态
  final bool isLoading;

  /// 主要内容
  final Widget child;

  /// 骨架屏组件
  final Widget? skeleton;

  /// 构造函数
  const _SkeletonOverlay({
    required this.isLoading,
    required this.child,
    this.skeleton,
  });

  @override
  Widget build(final BuildContext context) {
    return Stack(
      children: [
        // 隐藏主内容，但保留其空间
        Opacity(
          opacity: isLoading ? 0 : 1,
          child: IgnorePointer(
            ignoring: isLoading,
            child: child,
          ),
        ),

        // 显示骨架屏
        if (isLoading)
          Positioned.fill(
            child: skeleton ?? const DefaultSkeleton(),
          ),
      ],
    );
  }
}

/// 默认骨架屏
///
/// 通用的默认骨架屏样式
class DefaultSkeleton extends StatelessWidget {
  const DefaultSkeleton({super.key});

  @override
  Widget build(final BuildContext context) {
    return Skeleton(
      children: [
        // 顶部区域骨架
        _buildHeaderSkeleton(),

        // 内容列表区域
        ...List.generate(5, (final index) => _buildItemSkeleton()),
      ],
    );
  }

  /// 顶部区域骨架
  Widget _buildHeaderSkeleton() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏骨架
          Row(
            children: [
              SkeletonItem(
                width: 120.w,
                height: 24,
                borderRadius: 4,
              ),
              const Spacer(),
              SkeletonItem(
                width: 60.w,
                height: 24,
                borderRadius: 4,
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 子标题骨架
          SkeletonItem(
            width: double.infinity,
            height: 32,
            borderRadius: 4,
          ),
          SizedBox(height: 8.h),

          // 描述骨架
          SkeletonItem(
            width: 200.w,
            height: 16,
            borderRadius: 4,
          ),
        ],
      ),
    );
  }

  /// 列表项骨架
  Widget _buildItemSkeleton() {
    return Container(
      padding: EdgeInsets.all(16.w),
      margin: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          // 图片骨架
          SkeletonImage(
            width: 64,
            height: 64,
            borderRadius: 8,
          ),
          SizedBox(width: 16.w),

          // 文本内容骨架
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SkeletonItem(
                  width: double.infinity,
                  height: 16,
                  margin: EdgeInsets.only(bottom: 8.h),
                  borderRadius: 4,
                ),
                SkeletonItem(
                  width: 100.w,
                  height: 14,
                  margin: EdgeInsets.only(bottom: 8.h),
                  borderRadius: 4,
                ),
                SkeletonItem(
                  width: 180.w,
                  height: 14,
                  borderRadius: 4,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 列表骨架屏
///
/// 专用于列表的骨架屏样式
class ListSkeleton extends StatelessWidget {
  /// 列表项数量
  final int itemCount;

  const ListSkeleton({super.key, this.itemCount = 5});

  @override
  Widget build(final BuildContext context) {
    return Skeleton(
      children: List.generate(itemCount, (index) => _buildListItem()),
    );
  }

  /// 构建列表项
  Widget _buildListItem() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      margin: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          // 列表项图片骨架
          SkeletonImage(
            width: 60,
            height: 60,
            borderRadius: 6,
          ),
          SizedBox(width: 12.w),
          // 列表项内容骨架
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SkeletonItem(
                  width: double.infinity,
                  height: 20,
                  margin: EdgeInsets.only(bottom: 8.h),
                  borderRadius: 4,
                ),
                SkeletonItem(
                  width: 0.6.sw,
                  height: 16,
                  borderRadius: 4,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 骨架条组件
class SkeletonItem extends StatelessWidget {
  /// 宽度
  final double? width;

  /// 高度
  final double height;

  /// 边距
  final EdgeInsetsGeometry? margin;

  /// 圆角半径
  final double borderRadius;

  /// 构造函数
  const SkeletonItem({
    super.key,
    this.width,
    required this.height,
    this.margin,
    this.borderRadius = 4,
  });

  @override
  Widget build(final BuildContext context) {
    // 确保宽度值有效，防止NaN错误
    double? finalWidth = width;
    if (finalWidth != null && !finalWidth.isFinite) {
      finalWidth = null; // 如果是NaN或无穷大，使用null以采用包裹内容的宽度
    }

    // 确保高度值有效
    double finalHeight = height.isFinite ? height.h : 16.h;

    return Container(
      width: finalWidth,
      height: finalHeight,
      margin: margin,
      decoration: BoxDecoration(
        color: const Color(0xFFEEEEEE),
        borderRadius: BorderRadius.circular(borderRadius.r),
      ),
    );
  }
}

/// 骨架图片组件
class SkeletonImage extends StatelessWidget {
  /// 宽度
  final double width;

  /// 高度
  final double height;

  /// 边距
  final EdgeInsetsGeometry? margin;

  /// 圆角半径
  final double borderRadius;

  /// 构造函数
  const SkeletonImage({
    super.key,
    required this.width,
    required this.height,
    this.margin,
    this.borderRadius = 4,
  });

  @override
  Widget build(final BuildContext context) {
    // 确保宽度值有效，防止NaN错误
    double finalWidth = width.isFinite ? width.w : 60.w;

    // 确保高度值有效
    double finalHeight = height.isFinite ? height.h : 60.h;

    return Container(
      width: finalWidth,
      height: finalHeight,
      margin: margin,
      decoration: BoxDecoration(
        color: const Color(0xFFEEEEEE),
        borderRadius: BorderRadius.circular(borderRadius.r),
      ),
    );
  }
}

/// 骨架动画
class SkeletonAnimation extends StatefulWidget {
  const SkeletonAnimation({super.key});

  @override
  State<SkeletonAnimation> createState() => _SkeletonAnimationState();
}

class _SkeletonAnimationState extends State<SkeletonAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animation = Tween<double>(begin: -2, end: 2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOutSine),
    )..addListener(() {
        setState(() {});
      });

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment(_animation.value, 0),
          end: Alignment(-_animation.value, 0),
          colors: const [
            Colors.transparent,
            Color.fromARGB(5, 255, 255, 255),
            Color.fromARGB(10, 255, 255, 255),
            Color.fromARGB(5, 255, 255, 255),
            Colors.transparent,
          ],
          stops: const [0.0, 0.35, 0.5, 0.65, 1.0],
        ),
      ),
    );
  }
}
