import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 用于显示空状态视图的组件，可选择性地包含图标、消息和重试按钮
///
/// 该组件通常在没有内容可显示时使用，例如空列表
/// 或在数据加载出错时使用
class EmptyView extends StatelessWidget {
  /// 在图标下方显示的可选消息文本
  final String? message;
  final String? retryMessage;

  /// 在顶部显示的可选图标组件
  final Widget? icon;

  /// 点击重试按钮时的可选回调函数
  final VoidCallback? onRetry;

  /// 创建一个EmptyView组件
  ///
  /// [message] - 在图标下方显示的文本
  /// [icon] - 在消息上方显示的组件
  /// [onRetry] - 点击重试按钮时的回调函数
  const EmptyView(
      {super.key, this.message, this.icon, this.onRetry, this.retryMessage});

  @override
  Widget build(final BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 如果提供了图标则显示
          SizedBox(
            height: 200.h,
            child: Transform.scale(
              scale: 1.2,
              child: icon ??
                  Image.asset(
                    'assets/images/empty.png',
                    width: 300.w,
                    height: 300.h,
                  ),
            ),
          ),

          // 如果提供了消息则显示
          Padding(
            padding: const EdgeInsets.only(top: 5.0, left: 40.0, right: 40.0),
            child: Text(
              message ?? S.current.no_data,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18.sp,
                color: Colors.grey.shade600,
                fontFamily: AppConstants.mainFont,
              ),
            ),
          ),

          // 如果提供了回调函数则显示重试按钮
          if (onRetry != null)
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: TextButton(
                onPressed: onRetry,
                child: Text(
                  retryMessage ?? S.current.retry,
                  style: TextStyle(
                    fontFamily: AppConstants.mainFont,
                    fontSize: 20.sp,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
