import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/config/index.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/widgets/empty_view.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/app_router.dart';
import 'package:user_app/core/theme/app_theme.dart';
import 'package:user_app/routes/paths.dart';

///  App
class App extends ConsumerWidget {
  /// 构造函数
  const App({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final lang = ref.watch(languageProvider);
    final easyLoading = EasyLoading.init();

    return ScreenUtilInit(
      designSize:
          const Size(AppConstants.designWidth, AppConstants.designHeight),
      minTextAdapt: true,
      child: MaterialApp.router(
        title: AppConstants.appName,
        routerConfig: router,
        theme: AppTheme.lightTheme,
        debugShowCheckedModeBanner: false,
        localizationsDelegates: [
          GlobalMaterialLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          S.delegate,
        ],
        locale: Locale(lang),
        supportedLocales: S.delegate.supportedLocales,
        builder: (final context, child) {
          // 保存全局上下文
          ref.read(appContextProvider).saveContext(context);
          //全局错误处理函数
          ErrorWidget.builder = _errorWidgetBuilder;
          // ScreenConfig.init(context);
          // 添加loading支持
          child = easyLoading(context, child);
          final botToastBuilder = BotToastInit();
          child = botToastBuilder(context, child);

          ///设置文字大小不随系统设置改变
          return Directionality(
            textDirection: lang == 'ug' ? TextDirection.rtl : TextDirection.ltr,
            child: MediaQuery(
              ///设置文字大小不随系统设置改变
              data: MediaQuery.of(context)
                  .copyWith(textScaler: TextScaler.linear(1.0)),
              child: child,
            ),
          );
        },
      ),
    );
  }

  // 全局错误处理函数
  Widget _errorWidgetBuilder(final FlutterErrorDetails details) {
    return Material(
      child: Builder(
        builder: (final context) {
          return Center(
            child: EmptyView(
              message: S.current.unknown_error,
              retryMessage: S.current.back,
              onRetry: () {
                try {
                  context.pop();
                } catch (e) {
                  router.go(AppPaths.mainPage);
                }
              },
            ),
          );
        },
      ),
    );
  }
}
