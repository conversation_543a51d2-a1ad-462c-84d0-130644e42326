import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 全局应用上下文管理类
/// 提供在应用任何位置访问Context的能力
class AppContext {
  // 单例实例
  static final AppContext _instance = AppContext._internal();

  // 工厂构造函数，返回单例实例
  factory AppContext() => _instance;

  // 私有构造函数
  AppContext._internal();

  // 全局NavigatorKey
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  // 全局路由观察者
  final RouteObserver<PageRoute> routeObserver = RouteObserver<PageRoute>();

  // 保存在构建器中获取的上下文
  BuildContext? _buildContext;

  // 获取当前导航器的BuildContext
  BuildContext? get currentContext =>
      navigatorKey.currentContext ?? _buildContext;

  // 获取顶级BuildContext
  BuildContext? get topContext {
    return navigatorKey.currentState?.overlay?.context ?? _buildContext;
  }

  // 保存BuildContext
  void saveContext(final BuildContext context) {
    _buildContext = context;
  }
}

/// Riverpod provider，提供AppContext实例
final appContextProvider = Provider<AppContext>((final ref) {
  return AppContext();
});

/// 扩展WidgetRef，方便获取Context
extension WidgetRefExtension on WidgetRef {
  /// 获取全局Context
  BuildContext? get context {
    return read(appContextProvider).currentContext;
  }

  /// 获取顶级Context
  BuildContext? get topContext {
    return read(appContextProvider).topContext;
  }
}

/// 扩展Ref，方便在Provider中获取Context
extension RefExtension on Ref {
  /// 获取全局Context
  BuildContext? get context {
    return read(appContextProvider).currentContext;
  }

  /// 获取顶级Context
  BuildContext? get topContext {
    return read(appContextProvider).topContext;
  }
}
