import 'package:code_push_plugin/code_push_plugin.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/core/config/environment_config.dart';
import 'package:user_app/core/config/api_config.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/utils/aliyun_sls.dart';
import 'package:user_app/core/utils/connection_status.dart';
import 'package:user_app/core/utils/app_lifecycle_manager.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/features/auth/providers/auth_provider.dart';
import 'package:user_app/main.dart';

/// 应用初始化类
class AppInit {
  ///
  static Future<void> init() async {
    // 内存优化配置 - 减少GC压力
    // 生产环境优化图片内存缓存
    PaintingBinding.instance.imageCache.maximumSize = 100;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 << 20; // 50MB

    // 初始化屏幕方向和状态栏
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.light,
      ),
    );
    // 初始化存储服务
    final storageService = globalContainer.read(storageServiceProvider);
    await storageService.init();

    // 检查是否同意隐私政策
    final isAgreePrivacyPolicy = globalContainer
        .read(localStorageRepositoryProvider)
        .getIsAgreePrivacyPolicy();
    if (isAgreePrivacyPolicy) {
      // 初始化包信息（用于API配置中的版本号）
      await ApiConfig.initPackageInfo();

      // 初始化API客户端
      final apiClient = globalContainer.read(apiClientProvider);
      // String lang = container.watch(languageProvider);
      await apiClient.init();

      // 初始化日志服务
      AliyunSls().initProducer();

      // 初始化网络连接监听
      final connectionStatus = ConnectionStatus();
      await connectionStatus.initialize();

      // 初始化应用生命周期管理器 - 用于后台隐私合规
      final lifecycleManager = AppLifecycleManager();
      lifecycleManager.initialize();

      // 初始化完成后立即检查登录状态
      await globalContainer.read(authProvider.notifier).checkLoginStatus();

      // 同步全局登录状态
      final authState = globalContainer.read(authProvider);
      globalContainer.read(isLoggedInProvider.notifier).state = authState;

      // 初始化服务
      final updater = await CodePushPlugin.createUpdater();

      AutoUpdateConfig config = const AutoUpdateConfig();
      config = config.copyWith(
        enabled: true,
        checkOnStartup: true,
        checkIntervalSeconds: 60,
        silentExit: false,
        exitDelaySeconds: 2,
        track: 'stable',
      );
      await updater.enableAutoUpdate(config);

      final currentPatchNumber = await updater.getCurrentPatchNumber();
      /// 保存当前补丁号
      await globalContainer.read(localStorageRepositoryProvider).saveCurrentPatchNumber(currentPatchNumber);

      // 输出环境信息，便于确认当前环境
      if (kDebugMode) {
        print(EnvironmentConfig.getEnvironmentSummary());
        print(
          "应用初始化完成 - 登录状态: $authState, 全局状态: ${globalContainer.read(isLoggedInProvider)}",
        );
      }
    }
  }
}
