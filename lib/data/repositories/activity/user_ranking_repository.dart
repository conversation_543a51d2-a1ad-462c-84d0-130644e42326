import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/activity/ranking_model.dart';

part 'user_ranking_repository.g.dart';

/// 用户排行榜数据仓库
@riverpod
UserRankingRepository userRankingRepository(final UserRankingRepositoryRef ref) {
  return UserRankingRepository(
    apiClient: ref.watch(apiClientProvider),
  );
}

/// 用户排行榜数据仓库实现
class UserRankingRepository {
  final ApiClient _apiClient;

  /// 构造函数
  UserRankingRepository({
    required final ApiClient apiClient,
  }) : _apiClient = apiClient;

  /// 获取排行榜信息
  ///
  /// [rankingId] - 排行榜ID
  /// [areaId] - 区域ID（可选）
  /// [restaurantId] - 餐厅ID（可选）
  /// [type] - 类型（可选，默认为1）
  Future<ApiResult<UserRankingModel>> getRankingInfo({
    required final String rankingId,
    final String? areaId,
    final String? restaurantId,
    final String? type,
  }) async {
    final Map<String, dynamic> params = {
      'ranking_id': rankingId,
    };

    if (areaId != null && areaId.isNotEmpty) {
      params['area_id'] = areaId;
    }

    if (restaurantId != null && restaurantId.isNotEmpty) {
      params['restaurant_id'] = restaurantId;
    }

    if (type != null && type.isNotEmpty) {
      params['type'] = type;
    } else {
      params['type'] = '1'; // 默认类型为1
    }

    return await _apiClient.get(
      '/v1/ranking-new/info',
      params: params,
      fromJson: (final response, final data) => UserRankingModel.fromJson(data),
    );
  }

  /// 参加排行榜活动
  ///
  /// [rankingId] - 排行榜ID
  Future<ApiResult<String>> joinRanking({
    required final String rankingId,
  }) async {
    return await _apiClient.get(
      '/v1/ranking-new/join',
      params: {
        'id': rankingId,
      },
      fromJson: (final response, final data) => response['msg'] as String? ?? '参加成功',
    );
  }
}
