// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_shop_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$addShopRepositoryHash() => r'f53822e3e1c64c3193b063d1f26869e20b8779c1';

/// 商家入驻仓库提供者
///
/// 通过Riverpod依赖注入提供AddShopRepository实例
/// 自动使用全局ApiClient实例
///
/// Copied from [addShopRepository].
@ProviderFor(addShopRepository)
final addShopRepositoryProvider =
    AutoDisposeProvider<AddShopRepository>.internal(
  addShopRepository,
  name: r'addShopRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$addShopRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AddShopRepositoryRef = AutoDisposeProviderRef<AddShopRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
