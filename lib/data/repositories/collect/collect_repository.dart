import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/collect/collect_food_list.dart';
import 'package:user_app/data/models/collect/collect_store_list.dart';

part 'collect_repository.g.dart';

/// 收藏仓库
///
/// 负责处理与用户收藏相关的API请求，包括获取店铺收藏和美食收藏列表。
/// 提供统一的数据访问接口，返回类型安全的ApiResult。
class CollectRepository {
  final ApiClient _apiClient;

  /// 收藏仓库构造函数
  ///
  /// [apiClient] - API客户端实例，通过依赖注入提供
  CollectRepository({required final ApiClient apiClient})
      : _apiClient = apiClient;

  /// 获取收藏店铺列表
  ///
  /// [limit] - 返回结果数量限制
  ///
  /// 返回用户收藏的店铺列表
  Future<ApiResult<CollectStoreData>> getCollectList(final int limit) async {
    return _apiClient.get(
      Api.collect.storeCollect,
      params: {
        'limit': limit,
      },
      fromJson: (final response, final data) => CollectStoreData.fromJson(data),
    );
  }

  /// 获取美食收藏列表
  ///
  /// [limit] - 返回结果数量限制
  ///
  /// 返回用户收藏的美食列表
  Future<ApiResult<CollectFoodData>> getFoodCollectList(final int limit) async {
    return _apiClient.get(
      Api.collect.foodCollect,
      params: {'limit': limit},
      fromJson: (final response, final data) => CollectFoodData.fromJson(data),
    );
  }
}

/// 收藏仓库提供者
///
/// 使用Riverpod的@riverpod注解自动生成Provider代码，
/// 提供全局共享的CollectRepository实例
@riverpod
CollectRepository collectRepository(final Ref ref) {
  return CollectRepository(apiClient: ref.watch(apiClientProvider));
}
