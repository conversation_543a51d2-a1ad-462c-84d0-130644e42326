import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/mine/points_model.dart';
part 'points_repository_impl.g.dart';

/// 积分仓库实现
class PointsRepository {
  /// API客户端
  final ApiClient _apiClient;

  /// 构造函数
  const PointsRepository(this._apiClient);

  /// 获取积分记录
  ///
  /// [limit] - 每页数量限制
  ///
  /// 返回积分记录数据
  Future<ApiResult<PointsData>> getPointsLog({
    required final int limit,
  }) async {
    return await _apiClient.get(
      Api.user.pointsLog,
      params: {'limit': limit},
      fromJson: (final response, final data) => PointsData.fromJson(data),
    );
  }
}

/// 积分仓库提供者
@riverpod
PointsRepository pointsRepository(final Ref ref) {
  return PointsRepository(ref.watch(apiClientProvider));
}
