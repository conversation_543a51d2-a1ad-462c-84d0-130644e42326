// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$posterRepositoryHash() => r'88648644ddf0c7d5b0fa9d2530cf4e5f90cab5bf';

/// 依赖注入
///
/// Copied from [posterRepository].
@ProviderFor(posterRepository)
final posterRepositoryProvider = AutoDisposeProvider<PosterRepository>.internal(
  posterRepository,
  name: r'posterRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$posterRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PosterRepositoryRef = AutoDisposeProviderRef<PosterRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
