import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/storage/storage_service.dart';
import 'package:user_app/data/models/auth/auth_model.dart';
import 'package:user_app/data/models/home/<USER>';
part 'local_storage_repository.g.dart';

/// 本地存储库
class LocalStorageRepository {
  static const String _USER_INFO_KEY = 'user_info';
  static const String _TOKEN_KEY = 'token';
  static const String _REG_ID = 'regId';
  static const String _SERVICE_PHONE_KEY = 'service_phone';
  static const String _LOCATION_INFO_KEY = 'location_info';
  static const String _LANG_KEY = 'lang';
  static const String _HAS_SHIPPER_NUMBER_KEY = 'hasShipperNumber';
  static const String _APP_CONFIG_KEY = 'app_config';
  static const String _IS_AGREE_PRIVACY_POLICY_KEY = 'is_agree_privacy_policy';
  static const String _CURRENT_PATCH_NUMBER_KEY = 'current_patch_number';

  final StorageService _storageService;

  /// 构造函数
  LocalStorageRepository({required final StorageService storageService})
      : _storageService = storageService;

  /// 保存用户信息
  Future<void> saveUserInfo(final UserInfo userInfo) async {
    final userInfoJson = jsonEncode(userInfo.toJson());
    await _storageService.write(_USER_INFO_KEY, userInfoJson);
  }

  /// 获取用户信息
  UserInfo? getUserInfo() {
    final userInfoJson = _storageService.read(_USER_INFO_KEY);
    if (userInfoJson == null || userInfoJson.isEmpty) {
      return null;
    }
    final Map<String, dynamic> userMap = jsonDecode(userInfoJson);
    return UserInfo.fromJson(userMap);
  }

  /// 保存令牌
  Future<void> saveToken(final String token) async {
    await _storageService.write(_TOKEN_KEY, token);
  }

  /// 获取令牌
  String? getToken() {
    return _storageService.read(_TOKEN_KEY);
  }

  /// 获取令牌
  String? getRegId() {
    return _storageService.read(_REG_ID);
  }

  /// 保存本地客服电话
  Future<void> saveServicePhone(final String? phone) async {
    if (phone == null || phone.isEmpty) {
      await _storageService.remove(_SERVICE_PHONE_KEY);
    } else {
      await _storageService.write(_SERVICE_PHONE_KEY, phone);
    }
  }

  /// 获取本地客服电话
  String? getServicePhone() {
    return _storageService.read(_SERVICE_PHONE_KEY);
  }

  /// 清除用户信息和令牌
  Future<void> clearUserInfo() async {
    await _storageService.remove(_USER_INFO_KEY);
    await _storageService.remove(_TOKEN_KEY);
  }

  /// 保存本地信息
  Future<void> saveLocationInfo(final LocationInfo location) async {
    return await _storageService.write(
      _LOCATION_INFO_KEY,
      jsonEncode(location.toJson()),
    );
  }

  /// 获取本地信息
  LocationInfo? getLocationInfo() {
    final locationInfo = _storageService.read(_LOCATION_INFO_KEY);
    if (locationInfo == null || locationInfo.isEmpty) {
      return null;
    }
    final Map<String, dynamic> locationMap = jsonDecode(locationInfo);
    return LocationInfo.fromJson(locationMap);
  }

  /// 保存语言
  Future<void> saveLang(final String lang) async {
    await _storageService.write(_LANG_KEY, lang);
  }

  /// 获取语言
  String? getLang() {
    return _storageService.read(_LANG_KEY);
  }

  /// 保存骑手编号
  Future<void> saveHasShipperNumber(final String shipperNumber) async {
    await _storageService.write(_HAS_SHIPPER_NUMBER_KEY, shipperNumber);
  }

  /// 获取骑手编号
  String? getShipperNumber() {
    return _storageService.read(_HAS_SHIPPER_NUMBER_KEY);
  }

  /// 保存应用配置
  Future<void> saveAppConfig(final AppConfigData appConfigData) async {
    await _storageService.write(_APP_CONFIG_KEY, jsonEncode(appConfigData.toJson()));
  }

  /// 获取应用配置
  AppConfigData? getAppConfig() {
    final appConfigJson = _storageService.read(_APP_CONFIG_KEY);
    if (appConfigJson == null || appConfigJson.isEmpty) {
      return null;
    }
    final Map<String, dynamic> appConfigMap = jsonDecode(appConfigJson);
    return AppConfigData.fromJson(appConfigMap);
  }

  /// 保存是否同意隐私政策
  Future<void> saveIsAgreePrivacyPolicy(final bool isAgree) async {
    await _storageService.writeBool(_IS_AGREE_PRIVACY_POLICY_KEY, isAgree);
  }

  /// 获取是否同意隐私政策
  bool getIsAgreePrivacyPolicy() {
    return _storageService.readBool(_IS_AGREE_PRIVACY_POLICY_KEY) ?? false;
  }

  /// 保存当前补丁版本号
  Future<void> saveCurrentPatchNumber(final int patchNumber) async {
    await _storageService.writeInt(_CURRENT_PATCH_NUMBER_KEY, patchNumber);
  }

  /// 获取当前补丁版本号
  int? getCurrentPatchNumber() {
    return _storageService.readInt(_CURRENT_PATCH_NUMBER_KEY);
  }
}

/// 本地存储库提供器
@riverpod
LocalStorageRepository localStorageRepository(final Ref ref) {
  return LocalStorageRepository(
    storageService: ref.watch(storageServiceProvider),
  );
}
