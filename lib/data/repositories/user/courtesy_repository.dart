import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/user/courtesy_model.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';

part 'courtesy_repository.g.dart';

/// 优惠券仓库
@riverpod
CourtesyRepository courtesyRepository(CourtesyRepositoryRef ref) {
  return CourtesyRepository(
    apiClient: ref.watch(apiClientProvider),
    localStorageRepository: ref.watch(localStorageRepositoryProvider),
  );
}

/// 优惠券仓库
class CourtesyRepository {
  /// 构造函数
  const CourtesyRepository({
    required this.apiClient,
    required this.localStorageRepository,
  });

  /// API客户端
  final ApiClient apiClient;

  /// 本地存储仓库
  final LocalStorageRepository localStorageRepository;

  /// 获取优惠券列表
  ///
  /// [page] - 页码
  /// [pageSize] - 每页数量
  ///
  /// 返回优惠券列表数据
  Future<ApiResult<CourtesyListResponse>> getCourtesyList({
    required final int page,
    required final int pageSize,
  }) async {
    final areaId = localStorageRepository.getLocationInfo()?.areaId;
    if (areaId == null) {
      return ApiResult.error(msg: '请先选择区域');
    }

    return await apiClient.get(
      Api.mine.mineCourtesyList,
      params: {
        'area_id': areaId,
        'page': page,
        'limit': pageSize,
      },
      fromJson: (final response, final data) =>
          CourtesyListResponse.fromJson(data),
    );
  }

  /// 领取优惠券
  ///
  /// [id] - 优惠券ID
  ///
  /// 返回领取结果
  Future<ApiResult<dynamic>> takeCoupon(final String ids,{int? userId}) async {
    return await apiClient.post(
      Api.mine.courtesyTake,
      data: {
        'ids': ids,
        'user_id': userId,
      },
      fromJson: (final response, final data) => data,
    );
  }
}
