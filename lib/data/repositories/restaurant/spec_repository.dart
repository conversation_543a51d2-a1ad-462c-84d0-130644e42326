import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/restaurant/spec_model.dart';
import 'package:user_app/core/network/result/api_result.dart';

/// 规格数据仓库提供者
final specRepositoryProvider = Provider<SpecRepository>((final ref) {
  return SpecRepository(apiClient: ref.read(apiClientProvider));
});

/// 规格数据仓库
class SpecRepository {
  final ApiClient _apiClient;

  /// 构造函数
  SpecRepository({
    required final ApiClient apiClient,
  }) : _apiClient = apiClient;

  /// 获取美食规格数据
  /// [foodIds] 美食ID列表
  Future<ApiResult<SpecDataResponse>> getFoodSpecData(List<int> foodIds) async {
    try {
      // 构建查询参数，与微信小程序一致: ?ids=id1&ids=id2&ids=id3
      String queryParams = foodIds.map((id) => 'ids=$id').join('&');
      String url = '${Api.foods.specData}?$queryParams';

      final response = await _apiClient.get(
        url,
        fromJson: (response, data) {
          // 根据用户修改，API直接返回数组，不是包装在data字段中
          final specDataResponse = SpecDataResponse.fromJson(data);
          return specDataResponse;
        },
      );

      return response;
    } catch (e) {
      return ApiResult.fromException(e);
    }
  }

  /// 计算规格价格（本地计算，不需要API调用）
  /// [basePrice] 基础价格
  /// [selectedSpecs] 已选规格列表
  double calculateSpecPrice({
    required final double basePrice,
    required final List<SelectedSpec> selectedSpecs,
  }) {
    double totalPrice = basePrice;
    for (final spec in selectedSpecs) {
      totalPrice += spec.price;
    }
    return totalPrice;
  }

  /// 验证规格选择是否有效（本地验证）
  /// [specGroups] 规格组列表
  /// [selectedSpecs] 已选规格列表
  bool validateSpecSelection({
    required final List<SpecGroup> specGroups,
    required final List<SelectedSpec> selectedSpecs,
  }) {
    // 检查每个必选规格组是否都有选择
    for (final group in specGroups) {
      if (group.required == true) {
        final hasSelection =
            selectedSpecs.any((final spec) => spec.groupId == group.id);
        if (!hasSelection) {
          return false; // 必选规格组未选择
        }
      }
    }
    return true;
  }

  /// 根据购物车数据判断规格是否选中（对应微信小程序逻辑）
  bool isSpecOptionSelected({
    required final SpecOption option,
    required final List<int> specOptionIds,
    final String? specUniqueId,
  }) {
    if (specUniqueId != null && specUniqueId.isNotEmpty) {
      // 如果有spec_unique_id，使用option的is_selected状态
      return option.isSelected ?? false;
    } else {
      // 如果没有spec_unique_id，检查option.id是否在spec_option_ids中
      return specOptionIds.contains(option.id);
    }
  }
}
