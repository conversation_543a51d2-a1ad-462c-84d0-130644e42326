import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/chat/chat_model.dart';

part 'chat_repository.g.dart';

/// 聊天仓库
///
/// 提供与聊天相关的数据访问功能，包括获取聊天列表、聊天室详情、
/// 发送消息和上传图片等操作。
@riverpod
ChatRepository chatRepository(final ChatRepositoryRef ref) {
  return ChatRepository(
    apiClient: ref.watch(apiClientProvider),
  );
}

/// 聊天仓库
///
/// 负责处理与聊天相关的API请求，提供统一的数据访问接口，
/// 返回类型安全的ApiResult。
class ChatRepository {
  /// 构造函数
  ///
  /// [apiClient] - API客户端实例，通过依赖注入提供
  ChatRepository({
    required this.apiClient,
  });

  /// API客户端
  final ApiClient apiClient;

  /// 获取聊天列表
  ///
  /// 返回用户的所有聊天会话列表
  Future<ApiResult<List<ChatItem>>> getChatList() async {
    return await apiClient.get(
      Api.chat.chatList,
      fromJson: (final response, final data) {
        final chatListResponse = ChatListResponse.fromJson(response);
        return chatListResponse.data ?? [];
      },
    );
  }

  /// 获取聊天室详情
  ///
  /// [orderId] - 订单ID，用于标识聊天室
  ///
  /// 返回指定聊天室的历史消息列表
  Future<ApiResult<List<ChatMessage>>> getChatDetail(
      final String orderId) async {
    return await apiClient.get(
      Api.chat.chatDetail,
      params: {
        'order_id': orderId,
        'page_size': 1000,
      },
      fromJson: (final response, final data) {
        final chatRoomResponse = ChatRoomData.fromJson(data);
        return chatRoomResponse.messages;
      },
    );
  }

  /// 发送聊天消息
  ///
  /// [orderId] - 订单ID，用于标识聊天室
  /// [senderType] - 发送者类型
  /// [contentType] - 内容类型，如文本、图片等
  /// [content] - 消息内容
  /// [image] - 图片URL，仅当消息包含图片时使用
  ///
  /// 返回发送结果，成功时data为null
  Future<ApiResult<void>> sendChatMessage({
    required final String orderId,
    required final int userId,
    required final int senderType,
    required final int contentType,
    required final String content,
    final String image = '',
  }) async {
    final params = {
      'order_id': orderId,
      'sender_type': senderType,
      'content_type': contentType,
      'user_id': userId,
      'content': content,
      'image': image,
    };

    return await apiClient.post(
      Api.chat.sendChat,
      data: params,
      fromJson: (final response, final data) {},
    );
  }

  /// 上传图片
  ///
  /// [filePath] - 本地图片文件路径
  ///
  /// 返回上传结果，包含图片URL等信息
  Future<ApiResult<Map<String, dynamic>>> uploadImage(
      final String filePath) async {
    return await apiClient.upload(
      Api.user.uploadImage,
      filePath: filePath,
      name: 'image',
      formData: {
        'type_name': 'chat',
      },
      fromJson: (final response, final data) => data as Map<String, dynamic>,
    );
  }
}
