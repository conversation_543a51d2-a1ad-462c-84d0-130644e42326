import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/mine/dots_data.dart';
part 'dot_repository.g.dart';

@riverpod
DotsRepository dotsRepository(final Ref ref) {
  return DotsRepository(ref.read(apiClientProvider));
}

/// 红点信息仓库
class DotsRepository {
  final ApiClient _apiClient;

  DotsRepository(this._apiClient);

  /// 获取红点数据
  ///
  /// [areaId] - 区域ID
  ///
  /// 返回红点数据信息
  Future<ApiResult<DotsData>> getDotsData(final int areaId) async {
    return await _apiClient.get(
      Api.home.dots,
      params: {'area_id': areaId},
      fromJson: (final response, final data) {
        return DotsData.fromJson(data);
      },
    );
  }
}
