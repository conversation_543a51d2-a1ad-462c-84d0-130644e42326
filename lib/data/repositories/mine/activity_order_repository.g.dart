// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity_order_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$activityOrderRepositoryHash() =>
    r'7a4b617e310c16d186e2ef2eb671693ca5aae117';

/// 活动订单仓库
///
/// Copied from [activityOrderRepository].
@ProviderFor(activityOrderRepository)
final activityOrderRepositoryProvider =
    AutoDisposeProvider<ActivityOrderRepository>.internal(
  activityOrderRepository,
  name: r'activityOrderRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$activityOrderRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ActivityOrderRepositoryRef
    = AutoDisposeProviderRef<ActivityOrderRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
