class LotteryOrderListModel {
  final List<LotteryOrderItem> items;
  final int typeOneCount;
  final int typeTwoCount;
  final int typeThreeCount;
  final int typeFourCount;
  final int unusedCount;
  final int allCount;

  LotteryOrderListModel({
    required this.items,
    required this.typeOneCount,
    required this.typeTwoCount,
    required this.typeThreeCount,
    required this.typeFourCount,
    required this.unusedCount,
    required this.allCount,
  });

  factory LotteryOrderListModel.fromJson(Map<String, dynamic> json) {
    return LotteryOrderListModel(
      items: (json['items'] as List<dynamic>?)
              ?.map((e) => LotteryOrderItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      typeOneCount: json['type_one_count'] as int? ?? 0,
      typeTwoCount: json['type_two_count'] as int? ?? 0,
      typeThreeCount: json['type_three_count'] as int? ?? 0,
      typeFourCount: json['type_four_count'] as int? ?? 0,
      unusedCount: json['unused_count'] as int? ?? 0,
      allCount: json['all_count'] as int? ?? 0,
    );
  }

  /// 创建新的实例，覆盖指定字段
  LotteryOrderListModel copyWith({
    List<LotteryOrderItem>? items,
    int? typeOneCount,
    int? typeTwoCount,
    int? typeThreeCount,
    int? typeFourCount,
    int? unusedCount,
    int? allCount,
  }) {
    return LotteryOrderListModel(
      items: items ?? this.items,
      typeOneCount: typeOneCount ?? this.typeOneCount,
      typeTwoCount: typeTwoCount ?? this.typeTwoCount,
      typeThreeCount: typeThreeCount ?? this.typeThreeCount,
      typeFourCount: typeFourCount ?? this.typeFourCount,
      unusedCount: unusedCount ?? this.unusedCount,
      allCount: allCount ?? this.allCount,
    );
  }
}

class LotteryOrderItem {
  final int id;
  final int type;
  final int state;
  final String stateName;
  final String createdAt;
  final double couponPrice;
  final double couponMinPrice;
  final double couponPriceOne;
  final int prizeId;
  final List<PrizeItem> prizeItems;
  final Prize? prize;
  final SourceUser? sourceUser;
  final LotteryOrder? lotteryOrder;
  final FoodOrder? foodOrder;

  LotteryOrderItem({
    required this.id,
    required this.type,
    required this.state,
    required this.stateName,
    required this.createdAt,
    required this.couponPrice,
    required this.couponMinPrice,
    required this.couponPriceOne,
    required this.prizeId,
    required this.prizeItems,
    this.prize,
    this.sourceUser,
    this.lotteryOrder,
    this.foodOrder,
  });

  factory LotteryOrderItem.fromJson(Map<String, dynamic> json) {
    return LotteryOrderItem(
      id: json['id'] as int,
      type: json['type'] as int,
      state: json['state'] as int,
      stateName: json['state_name'] as String? ?? '',
      createdAt: json['created_at'] as String? ?? '',
      couponPrice: (json['coupon_price'] as num?)?.toDouble() ?? 0.0,
      couponMinPrice: (json['coupon_min_price'] as num?)?.toDouble() ?? 0.0,
      couponPriceOne: (json['coupon_price_one'] as num?)?.toDouble() ?? 0.0,
      prizeId: json['prize_id'] as int? ?? 0,
      prizeItems: (json['prize_items'] as List<dynamic>?)
              ?.map((e) => PrizeItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      prize: json['prize'] != null ? Prize.fromJson(json['prize']) : null,
      sourceUser: json['source_user'] != null
          ? SourceUser.fromJson(json['source_user'])
          : null,
      lotteryOrder: json['lottery_order'] != null
          ? LotteryOrder.fromJson(json['lottery_order'])
          : null,
      foodOrder: json['food_order'] != null
          ? FoodOrder.fromJson(json['food_order'])
          : null,
    );
  }
}

class PrizeItem {
  final String prizeLevel;
  final String prizeImg;

  PrizeItem({
    required this.prizeLevel,
    required this.prizeImg,
  });

  factory PrizeItem.fromJson(Map<String, dynamic> json) {
    return PrizeItem(
      prizeLevel: json['prize_level'] as String? ?? '',
      prizeImg: json['prize_img'] as String? ?? '',
    );
  }
}

class Prize {
  final String prizeLevel;
  final String prizeImg;
  final String prizeName;
  final String prizeModel;

  Prize({
    required this.prizeLevel,
    required this.prizeImg,
    required this.prizeName,
    required this.prizeModel,
  });

  factory Prize.fromJson(Map<String, dynamic> json) {
    return Prize(
      prizeLevel: json['prize_level'] as String? ?? '',
      prizeImg: json['prize_img'] as String? ?? '',
      prizeName: json['prize_name'] as String? ?? '',
      prizeModel: json['prize_model'] as String? ?? '',
    );
  }
}

class SourceUser {
  final String name;
  final String mobile;

  SourceUser({
    required this.name,
    required this.mobile,
  });

  factory SourceUser.fromJson(Map<String, dynamic> json) {
    return SourceUser(
      name: json['name'] as String? ?? '',
      mobile: json['mobile'] as String? ?? '',
    );
  }
}

class LotteryOrder {
  final String orderId;
  final double price;

  LotteryOrder({
    required this.orderId,
    required this.price,
  });

  factory LotteryOrder.fromJson(Map<String, dynamic> json) {
    return LotteryOrder(
      orderId: json['order_id'] as String? ?? '',
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
    );
  }
}

class FoodOrder {
  final String orderId;
  final double price;

  FoodOrder({
    required this.orderId,
    required this.price,
  });

  factory FoodOrder.fromJson(Map<String, dynamic> json) {
    return FoodOrder(
      orderId: json['order_id'] as String? ?? '',
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
    );
  }
}
