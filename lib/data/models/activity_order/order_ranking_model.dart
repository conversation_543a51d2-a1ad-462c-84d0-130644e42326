/// 订单排名模型
class OrderRankingModel {
  final String name;
  final String rule;
  final int activityId;
  final bool hasAreaActivity;
  final bool hasPlatActivity;
  final String areaName;
  final int remainEndTime;
  final List<PrizeItem> prize;
  final UserData? user;
  final List<UserOrderPrize> userOrderPrize;
  final int runningState; // 活动状态：1表示进行中

  OrderRankingModel({
    required this.name,
    required this.rule,
    required this.activityId,
    required this.hasAreaActivity,
    required this.hasPlatActivity,
    required this.areaName,
    required this.remainEndTime,
    required this.prize,
    this.user,
    required this.userOrderPrize,
    required this.runningState,
  });

  factory OrderRankingModel.fromJson(Map<String, dynamic> json) {
    return OrderRankingModel(
      name: json['name'] as String? ?? '',
      rule: json['rule'] as String? ?? '',
      activityId: json['activity_id'] as int? ?? 0,
      hasAreaActivity: json['has_area_activity'] as bool? ?? false,
      hasPlatActivity: json['has_plat_activity'] as bool? ?? false,
      areaName: json['area_name'] as String? ?? '',
      remainEndTime: json['remain_end_time'] as int? ?? 0,
      prize: (json['prize'] as List<dynamic>?)
              ?.map((e) => PrizeItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      user: json['user'] != null
          ? UserData.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      userOrderPrize: (json['user_order_prize'] as List<dynamic>?)
              ?.map((e) => UserOrderPrize.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      runningState: json['running_state'] as int? ?? 0,
    );
  }

  // 判断活动是否正在进行中
  bool get isRunning => runningState == 1;
}

/// 奖品项
class PrizeItem {
  final int? id; // 可能没有id字段
  final String prizeName;
  final String prizeImage;
  final String luckyUserIndex;

  PrizeItem({
    this.id,
    required this.prizeName,
    required this.prizeImage,
    required this.luckyUserIndex,
  });

  factory PrizeItem.fromJson(Map<String, dynamic> json) {
    // 处理 lucky_user_index 可能是int或String的情况
    String luckyIndex = '';
    final rawLuckyIndex = json['lucky_user_index'];
    if (rawLuckyIndex != null) {
      luckyIndex = rawLuckyIndex.toString();
    }

    return PrizeItem(
      id: json['id'] as int?,
      prizeName: json['prize_name'] as String? ?? '',
      prizeImage: json['prize_image'] as String? ?? '',
      luckyUserIndex: luckyIndex,
    );
  }
}

/// 用户数据
class UserData {
  final int id;
  final String name;
  final String avatar;

  UserData({
    required this.id,
    required this.name,
    required this.avatar,
  });

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      id: json['id'] as int? ?? 0,
      name: json['name'] as String? ?? '',
      avatar: json['avatar'] as String? ?? '',
    );
  }
}

/// 用户订单奖品
class UserOrderPrize {
  final int chanceId;
  final int isLucky;
  final String luckyUserIndex;
  final String prizeName;
  final String prizeImage;
  final int state;
  final int isPlatformActivity;

  UserOrderPrize({
    required this.chanceId,
    required this.isLucky,
    required this.luckyUserIndex,
    required this.prizeName,
    required this.prizeImage,
    required this.state,
    required this.isPlatformActivity,
  });

  factory UserOrderPrize.fromJson(Map<String, dynamic> json) {
    // 处理 lucky_user_index 可能是int或String的情况
    String luckyIndex = '';
    final rawLuckyIndex = json['lucky_user_index'];
    if (rawLuckyIndex != null) {
      luckyIndex = rawLuckyIndex.toString();
    }

    return UserOrderPrize(
      chanceId: json['chance_id'] as int? ?? 0,
      isLucky: json['is_lucky'] as int? ?? 0,
      luckyUserIndex: luckyIndex,
      prizeName: json['prize_name'] as String? ?? '',
      prizeImage: json['prize_image'] as String? ?? '',
      state: json['state'] as int? ?? 0,
      isPlatformActivity: json['is_platform_activity'] as int? ?? 0,
    );
  }
}
