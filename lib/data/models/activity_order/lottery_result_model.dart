/// 抽奖结果模型
class LotteryResultModel {
  final bool win;
  final int chanceId;
  final int lotteryId;
  final LotteryPrize? prize;
  final String message;

  LotteryResultModel({
    required this.win,
    required this.chanceId,
    required this.lotteryId,
    this.prize,
    required this.message,
  });

  factory LotteryResultModel.fromJson(Map<String, dynamic> json) {
    return LotteryResultModel(
      win: json['win'] as bool? ?? false,
      chanceId: json['chance_id'] as int? ?? 0,
      lotteryId: json['lottery_id'] as int? ?? 0,
      prize: json['prize'] != null
          ? LotteryPrize.fromJson(json['prize'] as Map<String, dynamic>)
          : null,
      message: json['message'] as String? ?? '',
    );
  }
}

/// 抽奖奖品
class LotteryPrize {
  final int id;
  final String name;
  final String image;
  final String model;
  final int level;

  LotteryPrize({
    required this.id,
    required this.name,
    required this.image,
    required this.model,
    required this.level,
  });

  factory LotteryPrize.fromJson(Map<String, dynamic> json) {
    return LotteryPrize(
      id: json['id'] as int? ?? 0,
      name: json['name'] as String? ?? '',
      image: json['image'] as String? ?? '',
      model: json['model'] as String? ?? '',
      level: json['level'] as int? ?? 0,
    );
  }
}
