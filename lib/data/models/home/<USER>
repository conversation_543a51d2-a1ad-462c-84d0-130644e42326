/// 套餐菜品模型
class ComboFoodsModel {
  /// 菜品类型
  final int foodType;

  /// 菜品ID
  final int id;

  /// 图片地址
  final String image;

  /// 月销量
  final int monthOrderCount;

  /// 菜品名称
  final String name;

  /// 原价（分）
  final int originPrice;

  /// 现价（分）
  final int price;

  /// 餐厅ID
  final int restaurantId;

  /// 餐厅名称
  final String restaurantName;

  const ComboFoodsModel({
    required this.foodType,
    required this.id,
    required this.image,
    required this.monthOrderCount,
    required this.name,
    required this.originPrice,
    required this.price,
    required this.restaurantId,
    required this.restaurantName,
  });

  /// 从JSON解析
  factory ComboFoodsModel.fromJson(Map<String, dynamic> json) {
    return ComboFoodsModel(
      foodType: json['food_type'] as int,
      id: json['id'] as int,
      image: json['image'] as String,
      monthOrderCount: json['month_order_count'] as int,
      name: json['name'] as String,
      originPrice: json['origin_price'] as int,
      price: json['price'] as int,
      restaurantId: json['restaurant_id'] as int,
      restaurantName: json['restaurant_name'] as String,
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'food_type': foodType,
      'id': id,
      'image': image,
      'month_order_count': monthOrderCount,
      'name': name,
      'origin_price': originPrice,
      'price': price,
      'restaurant_id': restaurantId,
      'restaurant_name': restaurantName,
    };
  }

  /// 拷贝方法
  ComboFoodsModel copyWith({
    int? foodType,
    int? id,
    String? image,
    int? monthOrderCount,
    String? name,
    int? originPrice,
    int? price,
    int? restaurantId,
    String? restaurantName,
  }) {
    return ComboFoodsModel(
      foodType: foodType ?? this.foodType,
      id: id ?? this.id,
      image: image ?? this.image,
      monthOrderCount: monthOrderCount ?? this.monthOrderCount,
      name: name ?? this.name,
      originPrice: originPrice ?? this.originPrice,
      price: price ?? this.price,
      restaurantId: restaurantId ?? this.restaurantId,
      restaurantName: restaurantName ?? this.restaurantName,
    );
  }
}
