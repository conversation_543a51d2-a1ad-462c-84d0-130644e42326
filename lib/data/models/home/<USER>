class PoiModel {
  final String name;         // POI 名称
  final String address;      // POI 地址
  final double latitude;     // POI 的纬度
  final double longitude;    // POI 的经度
  final num distance;     // 距离（单位：公里）

  PoiModel({
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.distance,
  });

  // 工厂构造函数：从 Map 创建 PoiModel 实例
  factory PoiModel.fromMap(Map<String, dynamic> map) {
    return PoiModel(
      name: map['name'] ?? '',
      address: map['address'] ?? '',
      latitude: map['latitude'] ?? 0.0,
      longitude: map['longitude'] ?? 0.0,
      distance: map['distance'] ?? 0.0,
    );
  }

  // 将 PoiModel 实例转换为 Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'distance': distance,
    };
  }

  @override
  String toString() {
    return 'PoiModel{name: $name, address: $address, latitude: $latitude, longitude: $longitude, distance: $distance}';
  }
}