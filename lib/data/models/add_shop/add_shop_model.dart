/// 商家入驻模型
class AddShopModel {
  /// 构造函数
  const AddShopModel({
    required this.shopName,
    required this.phone,
    required this.cityId,
    required this.cityName,
    required this.areaId,
    required this.areaName,
    required this.licenseId,
  });

  /// 店铺名称
  final String shopName;

  /// 联系电话
  final String phone;

  /// 城市ID
  final int cityId;

  /// 城市名称
  final String cityName;

  /// 区域ID
  final int areaId;

  /// 区域名称
  final String areaName;

  /// 证件类型ID
  final int licenseId;

  /// 转换为json
  Map<String, dynamic> toJson() => {
        'shopName': shopName,
        'phone': phone,
        'cityId': cityId,
        'areaId': areaId,
        'licenseId': licenseId,
      };

  /// 从JSON创建实例
  factory AddShopModel.fromJson(Map<String, dynamic> json) => AddShopModel(
        shopName: json['shopName'] as String,
        phone: json['phone'] as String,
        cityId: json['cityId'] as int,
        cityName: json['cityName'] as String,
        areaId: json['areaId'] as int,
        areaName: json['areaName'] as String,
        licenseId: json['licenseId'] as int,
      );

  /// 复制并修改
  AddShopModel copyWith({
    String? shopName,
    String? phone,
    int? cityId,
    String? cityName,
    int? areaId,
    String? areaName,
    int? licenseId,
  }) =>
      AddShopModel(
        shopName: shopName ?? this.shopName,
        phone: phone ?? this.phone,
        cityId: cityId ?? this.cityId,
        cityName: cityName ?? this.cityName,
        areaId: areaId ?? this.areaId,
        areaName: areaName ?? this.areaName,
        licenseId: licenseId ?? this.licenseId,
      );
}
