/// 红包数据模型
class RedPacket {
  /// 构造函数
  const RedPacket({
    required this.name,
    required this.paymentTime,
    required this.amount,
  });

  /// 红包名称
  final String name;

  /// 支付时间
  final String paymentTime;

  /// 金额
  final String amount;

  /// 从JSON创建实例
  factory RedPacket.fromJson(final Map<String, dynamic> json) => RedPacket(
        name: json['name'] as String,
        paymentTime: json['payment_time'] as String,
        amount: json['amount'] as String,
      );
}

/// 红包列表响应
class RedPacketListModel {
  /// 构造函数
  const RedPacketListModel({
    required this.items,
    required this.header,
    required this.status,
    required this.msg,
  });

  /// 红包列表
  final List<RedPacket> items;

  /// 头部信息
  final RedPacketHeader header;

  /// 状态码
  final int status;

  /// 消息
  final String msg;

  /// 从JSON创建实例
  factory RedPacketListModel.fromJson(final Map<String, dynamic> json) =>
      RedPacketListModel(
        items: (json['data']["items"] as List<dynamic>)
            .map((final e) => RedPacket.fromJson(e as Map<String, dynamic>))
            .toList(),
        header: RedPacketHeader.fromJson(
            json['data']['header'] as Map<String, dynamic>),
        status: json['status'] as int,
        msg: json['msg'] as String,
      );
}

/// 红包列表头部信息
class RedPacketHeader {
  /// 构造函数
  const RedPacketHeader({
    required this.amount,
  });

  /// 总金额
  final String amount;

  /// 从JSON创建实例
  factory RedPacketHeader.fromJson(final Map<String, dynamic> json) =>
      RedPacketHeader(
        amount: json['amount'] as String,
      );
}
