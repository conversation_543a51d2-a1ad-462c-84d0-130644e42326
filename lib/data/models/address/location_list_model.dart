import 'package:user_app/data/models/order/history_address_model.dart';

class LocationListModel {
  int? status;
  String? msg;
  LocationListData? data;
  String? lang;

  LocationListModel({this.status, this.msg, this.data, this.lang});

  LocationListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    msg = json['msg'];
    data = json['data'] != null
        ? new LocationListData.fromJson(json['data'])
        : null;
    lang = json['lang'];
  }
}

class LocationListData {
  int? perPage;
  int? currentPage;
  List<HistoryAddressData>? items;

  LocationListData({this.perPage, this.currentPage, this.items});

  LocationListData.fromJson(Map<String, dynamic> json) {
    perPage = json['per_page'];
    currentPage = json['current_page'];
    if (json['items'] != null) {
      items = <HistoryAddressData>[];
      json['items'].forEach((v) {
        items!.add(new HistoryAddressData.fromJson(v));
      });
    }
  }
}

// class LocationListItems {
//   int? id;
//   double? lat;
//   double? lng;
//   int? distance;
//   String? buildingName;
//   String? buildingNameZh;
//   int? areaId;
//   int? cityId;
//   int? streetId;
//   String? streetName;
//   String? areaName;
//   String? areaNameZh;
//   String? servicePhone;
//   String? areaRestingStartTime;
//   String? areaRestingEndTime;
//   String? areaRestingContentUg;
//   String? areaRestingContentZh;
//   String? cityName;
//   int? rankState;
//
//   LocationListItems(
//       {this.id,
//         this.lat,
//         this.lng,
//         this.distance,
//         this.buildingName,
//         this.buildingNameZh,
//         this.areaId,
//         this.cityId,
//         this.streetId,
//         this.streetName,
//         this.areaName,
//         this.areaNameZh,
//         this.servicePhone,
//         this.areaRestingStartTime,
//         this.areaRestingEndTime,
//         this.areaRestingContentUg,
//         this.areaRestingContentZh,
//         this.cityName,
//         this.rankState});
//
//   LocationListItems.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     lat = json['lat'];
//     lng = json['lng'];
//     distance = json['distance'];
//     buildingName = json['building_name'];
//     buildingNameZh = json['building_name_zh'];
//     areaId = json['area_id'];
//     cityId = json['city_id'];
//     streetId = json['street_id'];
//     streetName = json['street_name'];
//     areaName = json['area_name'];
//     areaNameZh = json['area_name_zh'];
//     servicePhone = json['service_phone'];
//     areaRestingStartTime = json['area_resting_start_time'];
//     areaRestingEndTime = json['area_resting_end_time'];
//     areaRestingContentUg = json['area_resting_content_ug'];
//     areaRestingContentZh = json['area_resting_content_zh'];
//     cityName = json['city_name'];
//     rankState = json['rank_state'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['id'] = this.id;
//     data['lat'] = this.lat;
//     data['lng'] = this.lng;
//     data['distance'] = this.distance;
//     data['building_name'] = this.buildingName;
//     data['building_name_zh'] = this.buildingNameZh;
//     data['area_id'] = this.areaId;
//     data['city_id'] = this.cityId;
//     data['street_id'] = this.streetId;
//     data['street_name'] = this.streetName;
//     data['area_name'] = this.areaName;
//     data['area_name_zh'] = this.areaNameZh;
//     data['service_phone'] = this.servicePhone;
//     data['area_resting_start_time'] = this.areaRestingStartTime;
//     data['area_resting_end_time'] = this.areaRestingEndTime;
//     data['area_resting_content_ug'] = this.areaRestingContentUg;
//     data['area_resting_content_zh'] = this.areaRestingContentZh;
//     data['city_name'] = this.cityName;
//     data['rank_state'] = this.rankState;
//     return data;
//   }
// }
