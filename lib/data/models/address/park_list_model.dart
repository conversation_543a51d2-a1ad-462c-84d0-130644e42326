import 'package:user_app/data/models/address/street_list_model.dart';

class ParkListModel {
  int? status;
  String? msg;
  List<StreetListData>? data;
  String? lang;

  ParkListModel({this.status, this.msg, this.data, this.lang});

  ParkListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    msg = json['msg'];
    if (json['data'] != null) {
      data = <StreetListData>[];
      json['data'].forEach((v) {
        data!.add(new StreetListData.fromJson(v));
      });
    }
    lang = json['lang'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['msg'] = this.msg;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['lang'] = this.lang;
    return data;
  }
}
