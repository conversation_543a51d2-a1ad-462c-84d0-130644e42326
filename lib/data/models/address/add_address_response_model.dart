class AddAddressResponse {
  AddAddressData? data;
  String? lang;
  String? msg;
  int? status;

  AddAddressResponse({this.data, this.lang, this.msg, this.status});

  AddAddressResponse.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new AddAddressData.fromJson(json['data']) : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    return data;
  }
}

class AddAddressData {
  int? id;
  String? name;
  String? address;
  String? tel;
  int? buildingId;
  num? lat;
  num? lng;
  String? buildingName;
  int? streetId;
  String? streetName;
  int? areaId;
  String? areaName;
  int? cityId;
  String? cityName;

  AddAddressData(
      {this.id,
        this.name,
        this.address,
        this.tel,
        this.buildingId,
        this.lat,
        this.lng,
        this.buildingName,
        this.streetId,
        this.streetName,
        this.areaId,
        this.areaName,
        this.cityId,
        this.cityName});

  AddAddressData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    address = json['address'];
    tel = json['tel'];
    buildingId = json['building_id'];
    lat = json['lat'];
    lng = json['lng'];
    buildingName = json['building_name'];
    streetId = json['street_id'];
    streetName = json['street_name'];
    areaId = json['area_id'];
    areaName = json['area_name'];
    cityId = json['city_id'];
    cityName = json['city_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['address'] = this.address;
    data['tel'] = this.tel;
    data['building_id'] = this.buildingId;
    data['lat'] = this.lat;
    data['lng'] = this.lng;
    data['building_name'] = this.buildingName;
    data['street_id'] = this.streetId;
    data['street_name'] = this.streetName;
    data['area_id'] = this.areaId;
    data['area_name'] = this.areaName;
    data['city_id'] = this.cityId;
    data['city_name'] = this.cityName;
    return data;
  }
}