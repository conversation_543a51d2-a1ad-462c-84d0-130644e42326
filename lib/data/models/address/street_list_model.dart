class StreetListModel {
  int? status;
  String? msg;
  List<StreetListData>? data;
  String? lang;

  StreetListModel({this.status, this.msg, this.data, this.lang});

  StreetListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    msg = json['msg'];
    if (json['data'] != null) {
      data = <StreetListData>[];
      json['data'].forEach((v) {
        data!.add(new StreetListData.fromJson(v));
      });
    }
    lang = json['lang'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['msg'] = this.msg;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['lang'] = this.lang;
    return data;
  }
}

class StreetListData {
  int? id;
  String? name;

  StreetListData({this.id, this.name});

  StreetListData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}