class CollectFoodList {
  CollectFoodData? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  CollectFoodList({this.data, this.lang, this.msg, this.status, this.time});

  CollectFoodList.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null
        ? new CollectFoodData.fromJson(json['data'])
        : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class CollectFoodData {
  FoodList? foodList;
  int? foodTotal;
  int? storeTotal;

  CollectFoodData({this.foodList, this.foodTotal, this.storeTotal});

  CollectFoodData.fromJson(Map<String, dynamic> json) {
    foodList = json['foodList'] != null
        ? new FoodList.fromJson(json['foodList'])
        : null;
    foodTotal = json['food_total'];
    storeTotal = json['store_total'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.foodList != null) {
      data['foodList'] = this.foodList!.toJson();
    }
    data['food_total'] = this.foodTotal;
    data['store_total'] = this.storeTotal;
    return data;
  }
}

class FoodList {
  int? currentPage;
  List<Items>? items;
  int? perPage;

  FoodList({this.currentPage, this.items, this.perPage});

  FoodList.fromJson(Map<String, dynamic> json) {
    currentPage = json['current_page'];
    if (json['items'] != null) {
      items = <Items>[];
      json['items'].forEach((v) {
        items!.add(new Items.fromJson(v));
      });
    }
    perPage = json['per_page'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['current_page'] = this.currentPage;
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    data['per_page'] = this.perPage;
    return data;
  }
}

class Items {
  int? id;
  String? name;
  num? price;
  String? image;
  String? restaurantName;
  int? restaurantId;
  String? restaurantLogo;
  int? restaurantState;
  int? categoryId;
  String? categoryName;
  int? monthOrder;
  int? starAvg;
  int? hasFoodsPries;
  num? originPrice;
  int? monthOrderCount;

  Items({
    this.id,
    this.name,
    this.price,
    this.image,
    this.restaurantName,
    this.restaurantId,
    this.restaurantLogo,
    this.restaurantState,
    this.categoryId,
    this.categoryName,
    this.monthOrder,
    this.starAvg,
    this.hasFoodsPries,
    this.originPrice,
    this.monthOrderCount,
  });

  Items.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    price = json['price'];
    image = json['image'];
    restaurantName = json['restaurant_name'];
    restaurantId = json['restaurant_id'];
    restaurantLogo = json['restaurant_logo'];
    restaurantState = json['restaurant_state'];
    categoryId = json['category_id'];
    categoryName = json['category_name'];
    monthOrder = json['month_order'];
    starAvg = json['star_avg'];
    hasFoodsPries = json['has_foods_pries'];
    originPrice = json['origin_price'];
    monthOrderCount = json['month_order_count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['price'] = this.price;
    data['image'] = this.image;
    data['restaurant_name'] = this.restaurantName;
    data['restaurant_id'] = this.restaurantId;
    data['restaurant_logo'] = this.restaurantLogo;
    data['restaurant_state'] = this.restaurantState;
    data['category_id'] = this.categoryId;
    data['category_name'] = this.categoryName;
    data['month_order'] = this.monthOrder;
    data['star_avg'] = this.starAvg;
    data['has_foods_pries'] = this.hasFoodsPries;
    data['origin_price'] = this.originPrice;
    data['month_order_count'] = this.monthOrderCount;
    return data;
  }
}
