class CreateOrderResponseModel {
  final int? status;
  final String? msg;
  final CreateOrderDara? data;
  final String? lang;

  CreateOrderResponseModel({this.msg, this.lang, this.status, this.data});

  factory CreateOrderResponseModel.fromJson(Map<String, dynamic> json) {
    return CreateOrderResponseModel(
      msg: json['msg'],
      lang: json['lang'],
      status: json['status'],
      data: json['data'] != null ? CreateOrderDara.fromJson(json['data']) : null,
    );
  }
}

class CreateOrderDara {
  final String? tempOrderId;
  CreateOrderDara({this.tempOrderId});
  
  factory CreateOrderDara.fromJson(Map<String, dynamic> json) {
    return CreateOrderDara(
      tempOrderId: json['temp_order_id'],
    );
  }
}

