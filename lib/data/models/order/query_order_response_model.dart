class QueryOrderResponseModel {
  int? status;
  String? msg;
  QueryOrderResponseData? data;
  String? lang;

  QueryOrderResponseModel({this.status, this.msg, this.data, this.lang});

  QueryOrderResponseModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    msg = json['msg'];
    data = json['data'] != null ? QueryOrderResponseData.fromJson(json['data']) : null;
    lang = json['lang'];
  }
}

class QueryOrderResponseData {
  int? userMony;
  int? orderId;
  int? payPlatform;
  String? payPlatformLabel;
  int? isBlack;
  int? onlinePayCount;
  bool? isBindAvator;
  List<Paytype>? paytype;
  int? state;

  QueryOrderResponseData(
      {this.userMony,
      this.orderId,
      this.payPlatform,
      this.payPlatformLabel,
      this.isBlack,
      this.onlinePayCount,
      this.isBindAvator,
      this.paytype,
      this.state});

  QueryOrderResponseData.fromJson(Map<String, dynamic> json) {
    userMony = json['user_mony'];
    orderId = json['order_id'];
    payPlatform = json['pay_platform'];
    payPlatformLabel = json['pay_platform_label'];
    isBlack = json['is_black'];
    onlinePayCount = json['online_pay_count'];
    isBindAvator = json['is_bind_avator'];
    if (json['paytype'] != null) {
      paytype = <Paytype>[];
      json['paytype'].forEach((v) {
        paytype?.add(Paytype.fromJson(v));
      });
    }
    state = json['state'];
  }

}

class Paytype {
  int? id;
  String? name;
  int? type;
  String? icon;

  Paytype({this.id, this.name, this.type, this.icon});

  Paytype.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    type = json['type'];
    icon = json['icon'];
  }
}
