class PartRefundDetailModel {
  PartRefundDetailData? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  PartRefundDetailModel(
      {this.data, this.lang, this.msg, this.status, this.time});

  PartRefundDetailModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new PartRefundDetailData.fromJson(json['data']) : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class PartRefundDetailData {
  num? partRefundAmount;
  int? partRefundCreatorType;
  String? orderNumber;
  String? refundReason;
  String? refundTime;
  String? restaurantName;
  String? restaurantPhone;
  num? lunchBoxFee;
  List<PartRefundDetailFoods>? foods;
  String? refundAccount;

  PartRefundDetailData(
      {this.partRefundAmount,
        this.partRefundCreatorType,
        this.orderNumber,
        this.refundReason,
        this.refundTime,
        this.restaurantName,
        this.restaurantPhone,
        this.lunchBoxFee,
        this.foods,
        this.refundAccount});

  PartRefundDetailData.fromJson(Map<String, dynamic> json) {
    partRefundAmount = json['part_refund_amount'];
    partRefundCreatorType = json['part_refund_creator_type'];
    orderNumber = json['order_number'];
    refundReason = json['refund_reason'];
    refundTime = json['refund_time'];
    restaurantName = json['restaurant_name'];
    restaurantPhone = json['restaurant_phone'];
    lunchBoxFee = json['lunch_box_fee'];
    if (json['foods'] != null) {
      foods = <PartRefundDetailFoods>[];
      json['foods'].forEach((v) {
        foods!.add(new PartRefundDetailFoods.fromJson(v));
      });
    }
    refundAccount = json['refund_account'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['part_refund_amount'] = this.partRefundAmount;
    data['part_refund_creator_type'] = this.partRefundCreatorType;
    data['order_number'] = this.orderNumber;
    data['refund_reason'] = this.refundReason;
    data['refund_time'] = this.refundTime;
    data['restaurant_name'] = this.restaurantName;
    data['restaurant_phone'] = this.restaurantPhone;
    data['lunch_box_fee'] = this.lunchBoxFee;
    if (this.foods != null) {
      data['foods'] = this.foods!.map((v) => v.toJson()).toList();
    }
    data['refund_account'] = this.refundAccount;
    return data;
  }
}

class PartRefundDetailFoods {
  String? foodName;
  num? price;
  num? refundPrice;
  int? count;
  String? image;
  int? foodType;
  SelectedSpec? selectedSpec;

  PartRefundDetailFoods(
      {this.foodName,
        this.price,
        this.refundPrice,
        this.count,
        this.image,
        this.foodType,
        this.selectedSpec});

  PartRefundDetailFoods.fromJson(Map<String, dynamic> json) {
    foodName = json['food_name'];
    price = json['price'];
    refundPrice = json['refund_price'];
    count = json['count'];
    image = json['image'];
    foodType = json['food_type'];
    selectedSpec = json['selected_spec'] != null
        ? new SelectedSpec.fromJson(json['selected_spec'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['food_name'] = this.foodName;
    data['price'] = this.price;
    data['refund_price'] = this.refundPrice;
    data['count'] = this.count;
    data['image'] = this.image;
    data['food_type'] = this.foodType;
    if (this.selectedSpec != null) {
      data['selected_spec'] = this.selectedSpec!.toJson();
    }
    return data;
  }
}

class SelectedSpec {
  int? id;
  num? price;
  List<SpecOptions>? specOptions;

  SelectedSpec({this.id, this.price, this.specOptions});

  SelectedSpec.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    price = json['price'];
    if (json['spec_options'] != null) {
      specOptions = <SpecOptions>[];
      json['spec_options'].forEach((v) {
        specOptions!.add(new SpecOptions.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['price'] = this.price;
    if (this.specOptions != null) {
      data['spec_options'] = this.specOptions!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SpecOptions {
  int? id;
  String? name;
  num? price;
  int? isSelected;
  int? state;

  SpecOptions({this.id, this.name, this.price, this.isSelected, this.state});

  SpecOptions.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    price = json['price'];
    isSelected = json['is_selected'];
    state = json['state'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['price'] = this.price;
    data['is_selected'] = this.isSelected;
    data['state'] = this.state;
    return data;
  }
}