import 'package:user_app/data/models/my_order/order_detail_model.dart';

class AgentPayModel {
  AgentPayData? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  AgentPayModel({this.data, this.lang, this.msg, this.status, this.time});

  AgentPayModel.fromJson(Map<String, dynamic> json) {
    data =
        json['data'] != null ? new AgentPayData.fromJson(json['data']) : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class AgentPayData {
  int? restaurantId;
  String? restaurantName;
  String? restaurantAddress;
  String? restaurantPhone;
  int? restaurantState;
  double? restaurantLng;
  bool? isCommented;
  String? restaurantLogo;
  double? restaurantLat;
  int? id;
  String? orderId;
  String? name;
  String? mobile;
  int? categoryId;
  String? orderAddress;
  int? payType;
  String? payTypeName;
  int? payPlatform;
  String? payPlatformLabel;
  List<Null>? payTypeList;
  String? description;
  String? bookingTime;
  int? timezone;
  String? bookingTimeCst;
  String? bookingDateTimeCst;
  String? createdAt;
  int? expired;
  int? expiredTime;
  int? workWechatState;
  String? workWechatQrcode;
  String? servicePhone;
  String? adminName;
  String? adminPhone;
  int? isScore;
  int? state;
  int? shipment;
  int? totalDiscountAmount;
  int? originalShipment;
  int? reduceShipment;
  int? lunchBoxFee;
  int? cityId;
  String? cityName;
  int? areaId;
  String? areaName;
  int? streetId;
  String? streetName;
  int? buildingId;
  String? buildingName;
  int? deliveryType;
  OrderDetail? orderDetail;
  num? actualPaid;
  List<OrderStateLog>? orderStateLog;
  int? msgCount;
  List<dynamic>? marketing;
  Map<String, dynamic>? coupon;
  List<dynamic>? otherMarketing;
  OrderStateLogNew? orderStateLogNew;
  String? orderDetailRankingActivityTitle;
  int? partRefundId;
  int? partRefundType;
  int? partRefundAmount;

  AgentPayData(
      {this.restaurantId,
      this.restaurantName,
      this.restaurantAddress,
      this.restaurantPhone,
      this.restaurantState,
      this.restaurantLng,
      this.isCommented,
      this.restaurantLogo,
      this.restaurantLat,
      this.id,
      this.orderId,
      this.name,
      this.mobile,
      this.categoryId,
      this.orderAddress,
      this.payType,
      this.payTypeName,
      this.payPlatform,
      this.payPlatformLabel,
      this.payTypeList,
      this.description,
      this.bookingTime,
      this.timezone,
      this.bookingTimeCst,
      this.bookingDateTimeCst,
      this.createdAt,
      this.expired,
      this.expiredTime,
      this.workWechatState,
      this.workWechatQrcode,
      this.servicePhone,
      this.adminName,
      this.adminPhone,
      this.isScore,
      this.state,
      this.shipment,
      this.totalDiscountAmount,
      this.originalShipment,
      this.reduceShipment,
      this.lunchBoxFee,
      this.cityId,
      this.cityName,
      this.areaId,
      this.areaName,
      this.streetId,
      this.streetName,
      this.buildingId,
      this.buildingName,
      this.deliveryType,
      this.orderDetail,
      this.actualPaid,
      this.orderStateLog,
      this.msgCount,
      this.marketing,
      this.coupon,
      this.otherMarketing,
      this.orderStateLogNew,
      this.orderDetailRankingActivityTitle,
      this.partRefundId,
      this.partRefundType,
      this.partRefundAmount});

  AgentPayData.fromJson(Map<String, dynamic> json) {
    restaurantId = json['restaurant_id'];
    restaurantName = json['restaurant_name'];
    restaurantAddress = json['restaurant_address'];
    restaurantPhone = json['restaurant_phone'];
    restaurantState = json['restaurant_state'];
    restaurantLng = json['restaurant_lng'];
    isCommented = json['is_commented'];
    restaurantLogo = json['restaurant_logo'];
    restaurantLat = json['restaurant_lat'];
    id = json['id'];
    orderId = json['order_id'];
    name = json['name'];
    mobile = json['mobile'];
    categoryId = json['category_id'];
    orderAddress = json['order_address'];
    payType = json['pay_type'];
    payTypeName = json['pay_type_name'];
    payPlatform = json['pay_platform'];
    payPlatformLabel = json['pay_platform_label'];
    description = json['description'];
    bookingTime = json['booking_time'];
    timezone = json['timezone'];
    bookingTimeCst = json['booking_time_cst'];
    bookingDateTimeCst = json['booking_date_time_cst'];
    createdAt = json['created_at'];
    expired = json['expired'];
    expiredTime = json['expired_time'];
    workWechatState = json['work_wechat_state'];
    workWechatQrcode = json['work_wechat_qrcode'];
    servicePhone = json['service_phone'];
    adminName = json['admin_name'];
    adminPhone = json['admin_phone'];
    isScore = json['is_score'];
    state = json['state'];
    shipment = json['shipment'];
    totalDiscountAmount = json['total_discount_amount'];
    originalShipment = json['original_shipment'];
    reduceShipment = json['reduce_shipment'];
    lunchBoxFee = json['lunch_box_fee'];
    cityId = json['city_id'];
    cityName = json['city_name'];
    areaId = json['area_id'];
    areaName = json['area_name'];
    streetId = json['street_id'];
    streetName = json['street_name'];
    buildingId = json['building_id'];
    buildingName = json['building_name'];
    deliveryType = json['delivery_type'];
    orderDetail = json['order_detail'] != null
        ? new OrderDetail.fromJson(json['order_detail'])
        : null;
    actualPaid = json['actual_paid'];
    if (json['order_state_log'] != null) {
      orderStateLog = <OrderStateLog>[];
      json['order_state_log'].forEach((v) {
        orderStateLog!.add(new OrderStateLog.fromJson(v));
      });
    }
    msgCount = json['msg_count'];
    marketing = json['marketing'];
    coupon = json['coupon'];
    otherMarketing = json['other_marketing'];
    orderStateLogNew = json['order_state_log_new'] != null
        ? new OrderStateLogNew.fromJson(json['order_state_log_new'])
        : null;
    orderDetailRankingActivityTitle =
        json['order_detail_ranking_activity_title'];
    partRefundId = json['part_refund_id'];
    partRefundType = json['part_refund_type'];
    partRefundAmount = json['part_refund_amount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['restaurant_id'] = this.restaurantId;
    data['restaurant_name'] = this.restaurantName;
    data['restaurant_address'] = this.restaurantAddress;
    data['restaurant_phone'] = this.restaurantPhone;
    data['restaurant_state'] = this.restaurantState;
    data['restaurant_lng'] = this.restaurantLng;
    data['is_commented'] = this.isCommented;
    data['restaurant_logo'] = this.restaurantLogo;
    data['restaurant_lat'] = this.restaurantLat;
    data['id'] = this.id;
    data['order_id'] = this.orderId;
    data['name'] = this.name;
    data['mobile'] = this.mobile;
    data['category_id'] = this.categoryId;
    data['order_address'] = this.orderAddress;
    data['pay_type'] = this.payType;
    data['pay_type_name'] = this.payTypeName;
    data['pay_platform'] = this.payPlatform;
    data['pay_platform_label'] = this.payPlatformLabel;
    data['description'] = this.description;
    data['booking_time'] = this.bookingTime;
    data['timezone'] = this.timezone;
    data['booking_time_cst'] = this.bookingTimeCst;
    data['booking_date_time_cst'] = this.bookingDateTimeCst;
    data['created_at'] = this.createdAt;
    data['expired'] = this.expired;
    data['expired_time'] = this.expiredTime;
    data['work_wechat_state'] = this.workWechatState;
    data['work_wechat_qrcode'] = this.workWechatQrcode;
    data['service_phone'] = this.servicePhone;
    data['admin_name'] = this.adminName;
    data['admin_phone'] = this.adminPhone;
    data['is_score'] = this.isScore;
    data['state'] = this.state;
    data['shipment'] = this.shipment;
    data['total_discount_amount'] = this.totalDiscountAmount;
    data['original_shipment'] = this.originalShipment;
    data['reduce_shipment'] = this.reduceShipment;
    data['lunch_box_fee'] = this.lunchBoxFee;
    data['city_id'] = this.cityId;
    data['city_name'] = this.cityName;
    data['area_id'] = this.areaId;
    data['area_name'] = this.areaName;
    data['street_id'] = this.streetId;
    data['street_name'] = this.streetName;
    data['building_id'] = this.buildingId;
    data['building_name'] = this.buildingName;
    data['delivery_type'] = this.deliveryType;
    if (this.orderDetail != null) {
      data['order_detail'] = this.orderDetail!.toJson();
    }
    data['actual_paid'] = this.actualPaid;
    if (this.orderStateLog != null) {
      data['order_state_log'] =
          this.orderStateLog!.map((v) => v.toJson()).toList();
    }
    data['msg_count'] = this.msgCount;
    if (this.orderStateLogNew != null) {
      data['order_state_log_new'] = this.orderStateLogNew!.toJson();
    }
    data['order_detail_ranking_activity_title'] =
        this.orderDetailRankingActivityTitle;
    data['part_refund_id'] = this.partRefundId;
    data['part_refund_type'] = this.partRefundType;
    data['part_refund_amount'] = this.partRefundAmount;
    return data;
  }
}

class OrderDetail {
  num? originalPrice;
  num? price;
  List<Foods>? foods;

  OrderDetail({this.originalPrice, this.price, this.foods});

  OrderDetail.fromJson(Map<String, dynamic> json) {
    originalPrice = json['original_price'];
    price = json['price'];
    if (json['foods'] != null) {
      foods = <Foods>[];
      json['foods'].forEach((v) {
        foods!.add(new Foods.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['original_price'] = this.originalPrice;
    data['price'] = this.price;
    if (this.foods != null) {
      data['foods'] = this.foods!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Foods {
  int? id;
  int? foodId;
  String? name;
  String? img;
  num? originalPrice;
  num? price;
  int? number;
  int? lunchBoxId;
  int? lunchBoxCount;
  num? lunchBoxFee;
  int? multiDiscountId;
  int? multiDiscountDetailId;

  Foods(
      {this.id,
      this.foodId,
      this.name,
      this.img,
      this.originalPrice,
      this.price,
      this.number,
      this.lunchBoxId,
      this.lunchBoxCount,
      this.lunchBoxFee,
      this.multiDiscountId,
      this.multiDiscountDetailId});

  Foods.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    foodId = json['food_id'];
    name = json['name'];
    img = json['img'];
    originalPrice = json['original_price'];
    price = json['price'];
    number = json['number'];
    lunchBoxId = json['lunch_box_id'];
    lunchBoxCount = json['lunch_box_count'];
    lunchBoxFee = json['lunch_box_fee'];
    multiDiscountId = json['multi_discount_id'];
    multiDiscountDetailId = json['multi_discount_detail_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['food_id'] = this.foodId;
    data['name'] = this.name;
    data['img'] = this.img;
    data['original_price'] = this.originalPrice;
    data['price'] = this.price;
    data['number'] = this.number;
    data['lunch_box_id'] = this.lunchBoxId;
    data['lunch_box_count'] = this.lunchBoxCount;
    data['lunch_box_fee'] = this.lunchBoxFee;
    data['multi_discount_id'] = this.multiDiscountId;
    data['multi_discount_detail_id'] = this.multiDiscountDetailId;
    return data;
  }
}
