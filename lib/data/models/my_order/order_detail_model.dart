import 'package:user_app/data/models/restaurant/foods_list_model.dart';

class OrderDetailModel {
  OrderDetailData? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  OrderDetailModel({this.data, this.lang, this.msg, this.status, this.time});

  OrderDetailModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null
        ? new OrderDetailData.fromJson(json['data'])
        : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class OrderDetailData {
  int? restaurantId;
  int? shipperId;
  String? restaurantName;
  String? restaurantAddress;
  String? restaurantPhone;
  int? restaurantState;
  double? restaurantLng;
  bool? isCommented;
  String? restaurantLogo;
  double? restaurantLat;
  int? id;
  String? orderId;
  String? name;
  String? mobile;
  int? categoryId;
  String? orderAddress;
  int? payType;
  String? payTypeName;
  int? payPlatform;
  String? payPlatformLabel;
  // List<Null>? payTypeList;
  String? description;
  String? bookingTime;
  int? timezone;
  String? bookingTimeCst;
  String? bookingDateTimeCst;
  String? createdAt;
  int? expired;
  num? expiredTime;
  int? workWechatState;
  String? workWechatQrcode;
  String? servicePhone;
  String? adminName;
  String? adminPhone;
  int? isScore;
  int? state;
  num? shipment;
  num? totalDiscountAmount;
  num? originalShipment;
  num? reduceShipment;
  num? lunchBoxFee;
  int? cityId;
  String? cityName;
  int? areaId;
  String? areaName;
  int? streetId;
  String? streetName;
  int? buildingId;
  String? buildingName;
  int? deliveryType;
  // Null? selfTakeNumber;
  OrderDetail? orderDetail;
  num? actualPaid;
  List<OrderStateLog>? orderStateLog;
  int? msgCount;
  Shipper? shipper;
  List<Marketing>? marketing;
  Coupon? coupon;
  List<OtherMarketing>? otherMarketing;
  List<ShipmentSteps>? shipmentSteps;
  Delayed? delayed;
  OrderStateLogNew? orderStateLogNew;
  int? partRefundId;
  int? partRefundType;
  num? partRefundAmount;
  String? orderDetailRankingActivityTitle;

  OrderDetailData(
      {this.restaurantId,
      this.shipperId,
      this.restaurantName,
      this.restaurantAddress,
      this.restaurantPhone,
      this.restaurantState,
      this.restaurantLng,
      this.isCommented,
      this.restaurantLogo,
      this.restaurantLat,
      this.id,
      this.orderId,
      this.name,
      this.mobile,
      this.categoryId,
      this.orderAddress,
      this.payType,
      this.payTypeName,
      this.payPlatform,
      this.payPlatformLabel,
      // this.payTypeList,
      this.description,
      this.bookingTime,
      this.timezone,
      this.bookingTimeCst,
      this.bookingDateTimeCst,
      this.createdAt,
      this.expired,
      this.expiredTime,
      this.workWechatState,
      this.workWechatQrcode,
      this.servicePhone,
      this.adminName,
      this.adminPhone,
      this.isScore,
      this.state,
      this.shipment,
      this.totalDiscountAmount,
      this.originalShipment,
      this.reduceShipment,
      this.lunchBoxFee,
      this.cityId,
      this.cityName,
      this.areaId,
      this.areaName,
      this.streetId,
      this.streetName,
      this.buildingId,
      this.buildingName,
      this.deliveryType,
      // this.selfTakeNumber,
      this.orderDetail,
      this.actualPaid,
      this.orderStateLog,
      this.msgCount,
      this.shipper,
      this.marketing,
      this.coupon,
      this.otherMarketing,
      this.shipmentSteps,
      this.delayed,
      this.orderStateLogNew,
      this.partRefundId,
      this.partRefundType,
      this.partRefundAmount,
      this.orderDetailRankingActivityTitle});

  OrderDetailData.fromJson(Map<String, dynamic> json) {
    restaurantId = json['restaurant_id'];
    shipperId = json['shipper_id'];
    restaurantName = json['restaurant_name'];
    restaurantAddress = json['restaurant_address'];
    restaurantPhone = json['restaurant_phone'];
    restaurantState = json['restaurant_state'];
    restaurantLng = json['restaurant_lng'];
    isCommented = json['is_commented'];
    restaurantLogo = json['restaurant_logo'];
    restaurantLat = json['restaurant_lat'];
    id = json['id'];
    orderId = json['order_id'];
    name = json['name'];
    mobile = json['mobile'];
    categoryId = json['category_id'];
    orderAddress = json['order_address'];
    payType = json['pay_type'];
    payTypeName = json['pay_type_name'];
    payPlatform = json['pay_platform'];
    payPlatformLabel = json['pay_platform_label'];
    // if (json['pay_type_list'] != null) {
    //   payTypeList = <Null>[];
    //   json['pay_type_list'].forEach((v) {
    //     payTypeList!.add(new Null.fromJson(v));
    //   });
    // }
    description = json['description'];
    bookingTime = json['booking_time'];
    timezone = json['timezone'];
    bookingTimeCst = json['booking_time_cst'];
    bookingDateTimeCst = json['booking_date_time_cst'];
    createdAt = json['created_at'];
    expired = json['expired'];
    expiredTime = json['expired_time'];
    workWechatState = json['work_wechat_state'];
    workWechatQrcode = json['work_wechat_qrcode'];
    servicePhone = json['service_phone'];
    adminName = json['admin_name'];
    adminPhone = json['admin_phone'];
    isScore = json['is_score'];
    state = json['state'];
    shipment = json['shipment'];
    totalDiscountAmount = json['total_discount_amount'];
    originalShipment = json['original_shipment'];
    reduceShipment = json['reduce_shipment'];
    lunchBoxFee = json['lunch_box_fee'];
    cityId = json['city_id'];
    cityName = json['city_name'];
    areaId = json['area_id'];
    areaName = json['area_name'];
    streetId = json['street_id'];
    streetName = json['street_name'];
    buildingId = json['building_id'];
    buildingName = json['building_name'];
    deliveryType = json['delivery_type'];
    // selfTakeNumber = json['self_take_number'];
    orderDetail = json['order_detail'] != null
        ? new OrderDetail.fromJson(json['order_detail'])
        : null;
    actualPaid = json['actual_paid'];
    if (json['order_state_log'] != null) {
      orderStateLog = <OrderStateLog>[];
      json['order_state_log'].forEach((v) {
        orderStateLog!.add(new OrderStateLog.fromJson(v));
      });
    }
    msgCount = json['msg_count'];
    shipper =
        json['shipper'] != null ? new Shipper.fromJson(json['shipper']) : null;
    if (json['marketing'] != null) {
      marketing = <Marketing>[];
      json['marketing'].forEach((v) {
        marketing!.add(new Marketing.fromJson(v));
      });
    }
    coupon =
        json['coupon'] != null ? new Coupon.fromJson(json['coupon']) : null;
    if (json['other_marketing'] != null) {
      otherMarketing = <OtherMarketing>[];
      json['other_marketing'].forEach((v) {
        otherMarketing!.add(new OtherMarketing.fromJson(v));
      });
    }
    if (json['shipment_steps'] != null) {
      shipmentSteps = <ShipmentSteps>[];
      json['shipment_steps'].forEach((v) {
        shipmentSteps!.add(new ShipmentSteps.fromJson(v));
      });
    }
    delayed =
        json['delayed'] != null ? new Delayed.fromJson(json['delayed']) : null;
    orderStateLogNew = json['order_state_log_new'] != null
        ? new OrderStateLogNew.fromJson(json['order_state_log_new'])
        : null;
    partRefundId = json['part_refund_id'];
    partRefundType = json['part_refund_type'];
    partRefundAmount = json['part_refund_amount'];
    orderDetailRankingActivityTitle =
        json['order_detail_ranking_activity_title'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['restaurant_id'] = this.restaurantId;
    data['shipper_id'] = this.shipperId;
    data['restaurant_name'] = this.restaurantName;
    data['restaurant_address'] = this.restaurantAddress;
    data['restaurant_phone'] = this.restaurantPhone;
    data['restaurant_state'] = this.restaurantState;
    data['restaurant_lng'] = this.restaurantLng;
    data['is_commented'] = this.isCommented;
    data['restaurant_logo'] = this.restaurantLogo;
    data['restaurant_lat'] = this.restaurantLat;
    data['id'] = this.id;
    data['order_id'] = this.orderId;
    data['name'] = this.name;
    data['mobile'] = this.mobile;
    data['category_id'] = this.categoryId;
    data['order_address'] = this.orderAddress;
    data['pay_type'] = this.payType;
    data['pay_type_name'] = this.payTypeName;
    data['pay_platform'] = this.payPlatform;
    data['pay_platform_label'] = this.payPlatformLabel;
    // if (this.payTypeList != null) {
    //   data['pay_type_list'] = this.payTypeList!.map((v) => v.toJson()).toList();
    // }
    data['description'] = this.description;
    data['booking_time'] = this.bookingTime;
    data['timezone'] = this.timezone;
    data['booking_time_cst'] = this.bookingTimeCst;
    data['booking_date_time_cst'] = this.bookingDateTimeCst;
    data['created_at'] = this.createdAt;
    data['expired'] = this.expired;
    data['expired_time'] = this.expiredTime;
    data['work_wechat_state'] = this.workWechatState;
    data['work_wechat_qrcode'] = this.workWechatQrcode;
    data['service_phone'] = this.servicePhone;
    data['admin_name'] = this.adminName;
    data['admin_phone'] = this.adminPhone;
    data['is_score'] = this.isScore;
    data['state'] = this.state;
    data['shipment'] = this.shipment;
    data['total_discount_amount'] = this.totalDiscountAmount;
    data['original_shipment'] = this.originalShipment;
    data['reduce_shipment'] = this.reduceShipment;
    data['lunch_box_fee'] = this.lunchBoxFee;
    data['city_id'] = this.cityId;
    data['city_name'] = this.cityName;
    data['area_id'] = this.areaId;
    data['area_name'] = this.areaName;
    data['street_id'] = this.streetId;
    data['street_name'] = this.streetName;
    data['building_id'] = this.buildingId;
    data['building_name'] = this.buildingName;
    data['delivery_type'] = this.deliveryType;
    // data['self_take_number'] = this.selfTakeNumber;
    if (this.orderDetail != null) {
      data['order_detail'] = this.orderDetail!.toJson();
    }
    data['actual_paid'] = this.actualPaid;
    if (this.orderStateLog != null) {
      data['order_state_log'] =
          this.orderStateLog!.map((v) => v.toJson()).toList();
    }
    data['msg_count'] = this.msgCount;
    if (this.shipper != null) {
      data['shipper'] = this.shipper!.toJson();
    }
    if (this.marketing != null) {
      data['marketing'] = this.marketing!.map((v) => v.toJson()).toList();
    }
    if (this.coupon != null) {
      data['coupon'] = this.coupon!.toJson();
    }
    if (this.otherMarketing != null) {
      data['other_marketing'] =
          this.otherMarketing!.map((v) => v.toJson()).toList();
    }
    if (this.shipmentSteps != null) {
      data['shipment_steps'] =
          this.shipmentSteps!.map((v) => v.toJson()).toList();
    }
    if (this.delayed != null) {
      data['delayed'] = this.delayed!.toJson();
    }
    if (this.orderStateLogNew != null) {
      data['order_state_log_new'] = this.orderStateLogNew!.toJson();
    }
    data['part_refund_id'] = this.partRefundId;
    data['part_refund_type'] = this.partRefundType;
    data['part_refund_amount'] = this.partRefundAmount;
    data['order_detail_ranking_activity_title'] =
        this.orderDetailRankingActivityTitle;
    return data;
  }
}

class Coupon {
  int? id;
  String? name;
  String? price;

  Coupon({this.id, this.name, this.price});

  Coupon.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    price = json['price'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['price'] = this.price;
    return data;
  }
}

class OrderDetail {
  num? originalPrice;
  num? price;
  List<OrderDetailFoods>? foods;

  OrderDetail({this.originalPrice, this.price, this.foods});

  OrderDetail.fromJson(Map<String, dynamic> json) {
    originalPrice = json['original_price'];
    price = json['price'];
    if (json['foods'] != null) {
      foods = <OrderDetailFoods>[];
      json['foods'].forEach((v) {
        foods!.add(new OrderDetailFoods.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['original_price'] = this.originalPrice;
    data['price'] = this.price;
    if (this.foods != null) {
      data['foods'] = this.foods!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class OrderDetailFoods {
  int? id;
  int? foodId;
  String? name;
  String? img;
  num? originalPrice;
  num? price;
  int? number;
  int? lunchBoxId;
  int? lunchBoxCount;
  num? lunchBoxFee;
  int? foodType;
  SelectedFood? selectedFood;

  /// 套餐包含的美食项目
  List<ComboFoodItem>? comboFoodItems;

  OrderDetailFoods({
    this.id,
    this.foodId,
    this.name,
    this.img,
    this.originalPrice,
    this.price,
    this.number,
    this.lunchBoxId,
    this.lunchBoxCount,
    this.lunchBoxFee,
    this.foodType,
    this.comboFoodItems,
    this.selectedFood,
  });

  OrderDetailFoods.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    foodId = json['food_id'];
    name = json['name'];
    img = json['img'];
    originalPrice = json['original_price'];
    price = json['price'];
    number = json['number'];
    lunchBoxId = json['lunch_box_id'];
    lunchBoxCount = json['lunch_box_count'];
    lunchBoxFee = json['lunch_box_fee'];
    foodType = json['food_type'];
    selectedFood = json['selected_food'] != null
        ? SelectedFood.fromJson(json['selected_food'])
        : null;
    comboFoodItems = json['combo_items'] == null
        ? null
        : List<ComboFoodItem>.from(
            json['combo_items'].map((x) => ComboFoodItem.fromJson(x)),
          );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['food_id'] = this.foodId;
    data['name'] = this.name;
    data['img'] = this.img;
    data['original_price'] = this.originalPrice;
    data['price'] = this.price;
    data['number'] = this.number;
    data['lunch_box_id'] = this.lunchBoxId;
    data['lunch_box_count'] = this.lunchBoxCount;
    data['lunch_box_fee'] = this.lunchBoxFee;
    data['food_type'] = this.foodType;
    if (this.selectedFood != null) {
      data['selected_food'] = this.selectedFood!.toJson();
    }
    if (this.comboFoodItems != null) {
      data['combo_items'] = this.comboFoodItems!.map((x) => x.toJson()).toList();
    }
    return data;
  }
}

/// 选中的规格商品信息
class SelectedFood {
  int? id;
  num? price;
  List<SpecOption>? specOptions;

  SelectedFood({this.id, this.price, this.specOptions});

  SelectedFood.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    price = json['price'];
    if (json['spec_options'] != null) {
      specOptions = <SpecOption>[];
      json['spec_options'].forEach((v) {
        specOptions!.add(SpecOption.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['price'] = this.price;
    if (this.specOptions != null) {
      data['spec_options'] = this.specOptions!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

/// 规格选项信息
class SpecOption {
  int? id;
  String? name;
  num? price;
  SpecType? specType;

  SpecOption({this.id, this.name, this.price, this.specType});

  SpecOption.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    price = json['price'];
    specType =
        json['spec_type'] != null ? SpecType.fromJson(json['spec_type']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['price'] = this.price;
    if (this.specType != null) {
      data['spec_type'] = this.specType!.toJson();
    }
    return data;
  }
}

/// 规格类型信息
class SpecType {
  int? id;
  String? name;

  SpecType({this.id, this.name});

  SpecType.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}

class Delayed {
  String? id;
  String? originalBookingTime;
  String? newBookingTime;
  int? agreeState;
  String? reasonDelay;
  int? delayedDuration;

  Delayed(
      {this.id,
      this.originalBookingTime,
      this.newBookingTime,
      this.agreeState,
      this.reasonDelay,
      this.delayedDuration});

  Delayed.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    originalBookingTime = json['original_booking_time'];
    newBookingTime = json['new_booking_time'];
    agreeState = json['agree_state'];
    reasonDelay = json['reason_delay'];
    delayedDuration = json['delayed_duration'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['original_booking_time'] = this.originalBookingTime;
    data['new_booking_time'] = this.newBookingTime;
    data['agree_state'] = this.agreeState;
    data['reason_delay'] = this.reasonDelay;
    data['delayed_duration'] = this.delayedDuration;
    return data;
  }
}

class OrderStateLog {
  int? orderState;
  String? name;
  String? icon;
  String? color;
  Null? failReason;
  String? createdAt;

  OrderStateLog(
      {this.orderState,
      this.name,
      this.icon,
      this.color,
      this.failReason,
      this.createdAt});

  OrderStateLog.fromJson(Map<String, dynamic> json) {
    orderState = json['order_state'];
    name = json['name'];
    icon = json['icon'];
    color = json['color'];
    failReason = json['fail_reason'];
    createdAt = json['created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['order_state'] = this.orderState;
    data['name'] = this.name;
    data['icon'] = this.icon;
    data['color'] = this.color;
    data['fail_reason'] = this.failReason;
    data['created_at'] = this.createdAt;
    return data;
  }
}

class Shipper {
  String? shipperLng;
  String? shipperLat;
  num? buildingLng;
  num? buildingLat;
  num? resLng;
  num? resLat;
  String? resLogo;
  int? state;

  Shipper(
      {this.shipperLng,
      this.shipperLat,
      this.buildingLng,
      this.buildingLat,
      this.resLng,
      this.resLat,
      this.resLogo,
      this.state});

  Shipper.fromJson(Map<String, dynamic> json) {
    shipperLng = json['shipper_lng'];
    shipperLat = json['shipper_lat'];
    buildingLng = json['building_lng'];
    buildingLat = json['building_lat'];
    resLng = json['res_lng'];
    resLat = json['res_lat'];
    resLogo = json['res_logo'];
    state = json['state'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['shipper_lng'] = this.shipperLng;
    data['shipper_lat'] = this.shipperLat;
    data['building_lng'] = this.buildingLng;
    data['building_lat'] = this.buildingLat;
    data['res_lng'] = this.resLng;
    data['res_lat'] = this.resLat;
    data['res_logo'] = this.resLogo;
    data['state'] = this.state;
    return data;
  }
}

class OtherMarketing {
  String? name;
  num? price;
  int? marketingType;
  String? detail;
  String? image;
  String? title;
  String? description;

  OtherMarketing(
      {this.name,
      this.price,
      this.marketingType,
      this.detail,
      this.image,
      this.title,
      this.description});

  OtherMarketing.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    price = json['price'];
    marketingType = json['marketing_type'];
    detail = json['detail'];
    image = json['image'];
    title = json['title'];
    description = json['description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['price'] = this.price;
    data['marketing_type'] = this.marketingType;
    data['detail'] = this.detail;
    data['image'] = this.image;
    data['title'] = this.title;
    data['description'] = this.description;
    return data;
  }
}

class ShipmentSteps {
  int? type;
  String? background;
  String? color;
  String? borderColor;
  String? title;
  num? price;
  int? distanceStart;
  int? distanceEnd;
  num? minDeliveryPrice;

  ShipmentSteps(
      {this.type,
      this.background,
      this.color,
      this.borderColor,
      this.title,
      this.price,
      this.distanceStart,
      this.distanceEnd,
      this.minDeliveryPrice});

  ShipmentSteps.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    background = json['background'];
    color = json['color'];
    borderColor = json['border_color'];
    title = json['title'];
    price = json['price'];
    distanceStart = json['distance_start'];
    distanceEnd = json['distance_end'];
    minDeliveryPrice = json['min_delivery_price'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type'] = this.type;
    data['background'] = this.background;
    data['color'] = this.color;
    data['border_color'] = this.borderColor;
    data['title'] = this.title;
    data['price'] = this.price;
    data['distance_start'] = this.distanceStart;
    data['distance_end'] = this.distanceEnd;
    data['min_delivery_price'] = this.minDeliveryPrice;
    return data;
  }
}

class OrderStateLogNew {
  List<Items>? items;
  int? step;

  OrderStateLogNew({this.items, this.step});

  OrderStateLogNew.fromJson(Map<String, dynamic> json) {
    if (json['items'] != null) {
      items = <Items>[];
      json['items'].forEach((v) {
        items!.add(new Items.fromJson(v));
      });
    }
    step = json['step'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    data['step'] = this.step;
    return data;
  }
}

class Items {
  String? title;
  String? time;

  Items({this.title, this.time});

  Items.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
    data['time'] = this.time;
    return data;
  }
}

class UpdatedStep {
  String? title;
  String? time;
  String status;
  Map<String, String> icons;

  UpdatedStep({
    this.title,
    this.time,
    required this.status,
    required this.icons,
  });
}

class Marketing {
  String? name;
  num? price;
  int? marketingType;
  String? detail;
  String? image;
  String? title;
  String? description;

  Marketing(
      {this.name,
      this.price,
      this.marketingType,
      this.detail,
      this.image,
      this.title,
      this.description});

  Marketing.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    price = json['price'];
    marketingType = json['marketing_type'];
    detail = json['detail'];
    image = json['image'];
    title = json['title'];
    description = json['description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['price'] = this.price;
    data['marketing_type'] = this.marketingType;
    data['detail'] = this.detail;
    data['image'] = this.image;
    data['title'] = this.title;
    data['description'] = this.description;
    return data;
  }
}
