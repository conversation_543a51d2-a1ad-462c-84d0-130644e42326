
class RankingInfoModel {
  int? status;
  String? msg;
  RankingInfoData? data;
  String? lang;

  RankingInfoModel({this.status, this.msg, this.data, this.lang});

  RankingInfoModel.fromJson(Map<String, dynamic> json) {
    status = json["status"];
    msg = json["msg"];
    data = json["data"] == null ? null : RankingInfoData.fromJson(json["data"]);
    lang = json["lang"];
  }
}

class RankingInfoData {
  Ranking? ranking;
  int? remainderTime;
  RankingInfoUser? user;

  RankingInfoData({this.ranking, this.remainderTime, this.user});

  RankingInfoData.fromJson(Map<String, dynamic> json) {
    ranking = json["ranking"] == null ? null : Ranking.fromJson(json["ranking"]);
    remainderTime = json["remainder_time"];
    user = json["user"] == null ? null : RankingInfoUser.fromJson(json["user"]);
  }

}

class RankingInfoUser {
  int? id;
  String? name;
  String? mobile;
  String? avatar;
  int? userState;
  bool? isJoined;
  int? amount;
  int? isWinner;
  int? remainingAmount;
  int? currentRanking;
  String? cityName;
  CityNames? cityNames;
  Gift? winnerGift;

  RankingInfoUser({this.id, this.name, this.mobile, this.avatar, this.userState, this.isJoined, this.amount, this.isWinner, this.remainingAmount, this.currentRanking, this.cityName, this.cityNames, this.winnerGift});

  RankingInfoUser.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    name = json["name"];
    mobile = json["mobile"];
    avatar = json["avatar"];
    userState = json["user_state"];
    isJoined = json["is_joined"];
    amount = json["amount"];
    isWinner = json["is_winner"];
    remainingAmount = json["remaining_amount"];
    currentRanking = json["current_ranking"];
    cityName = json["city_name"];
    cityNames = json["city_names"] == null ? null : CityNames.fromJson(json["city_names"]);
    winnerGift = json["winner_gift"] == null ? null : Gift.fromJson(json["winner_gift"]);
  }

}

class CityNames {
  String? ug;
  String? zh;

  CityNames({this.ug, this.zh});

  CityNames.fromJson(Map<String, dynamic> json) {
    ug = json["ug"];
    zh = json["zh"];
  }
}

class Ranking {
  int? id;
  String? rankingBeginTime;
  String? rankingEndTime;
  String? rewardBeginTime;
  String? rewardEndTime;
  String? rankingRuleUg;
  String? rankingRuleZh;
  int? rankingState;
  int? rewardFinish;
  int? confirmed;
  List<Gift>? gift;
  List<Users>? users;

  Ranking({this.id, this.rankingBeginTime, this.rankingEndTime, this.rewardBeginTime, this.rewardEndTime, this.rankingRuleUg, this.rankingRuleZh, this.rankingState, this.rewardFinish, this.confirmed, this.gift, this.users});

  Ranking.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    rankingBeginTime = json["ranking_begin_time"];
    rankingEndTime = json["ranking_end_time"];
    rewardBeginTime = json["reward_begin_time"];
    rewardEndTime = json["reward_end_time"];
    rankingRuleUg = json["ranking_rule_ug"];
    rankingRuleZh = json["ranking_rule_zh"];
    rankingState = json["ranking_state"];
    rewardFinish = json["reward_finish"];
    confirmed = json["confirmed"];
    gift = json["gift"] == null ? null : (json["gift"] as List).map((e) => Gift.fromJson(e)).toList();
    users = json["users"] == null ? null : (json["users"] as List).map((e) => Users.fromJson(e)).toList();
  }
}

class Users {
  String? rankingTitleUg;
  String? rankingTitleZh;
  String? rewardGift;
  int? userId;
  String? name;
  String? avatar;
  String? mobile;
  int? isWinner;
  int? prizeLavel;
  int? rankingId;
  int? totalPrice;
  String? giftName;
  String? giftImage;

  Users({this.rankingTitleUg, this.rankingTitleZh, this.rewardGift, this.userId, this.name, this.avatar, this.mobile, this.isWinner, this.prizeLavel, this.rankingId, this.totalPrice, this.giftName, this.giftImage});

  Users.fromJson(Map<String, dynamic> json) {
    rankingTitleUg = json["ranking_title_ug"];
    rankingTitleZh = json["ranking_title_zh"];
    rewardGift = json["reward_gift"];
    userId = json["user_id"];
    name = json["name"];
    avatar = json["avatar"];
    mobile = json["mobile"];
    isWinner = json["is_winner"];
    prizeLavel = json["prize_lavel"];
    rankingId = json["ranking_id"];
    totalPrice = json["total_price"];
    giftName = json["gift_name"];
    giftImage = json["gift_image"];
  }
}

class Gift {
  String? rewardNameUg;
  String? rewardImage;
  int? rewardRank;
  String? rewardOldPrice;
  int? rewardCount;
  String? rewardNameZh;
  dynamic level;
  dynamic time;

  Gift({this.rewardNameUg, this.rewardImage, this.rewardRank, this.rewardOldPrice, this.rewardCount, this.rewardNameZh, this.level, this.time});

  Gift.fromJson(Map<String, dynamic> json) {
    rewardNameUg = json["reward_name_ug"];
    rewardImage = json["reward_image"];
    rewardRank = json["reward_rank"];
    rewardOldPrice = json["reward_old_price"];
    rewardCount = json["reward_count"];
    rewardNameZh = json["reward_name_zh"];
    level = json["level"];
    time = json["time"];
  }

}