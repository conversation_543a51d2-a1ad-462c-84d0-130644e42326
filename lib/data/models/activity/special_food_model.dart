class SpecailFoodModel {
  List<SpecailFoodData>? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  SpecailFoodModel({this.data, this.lang, this.msg, this.status, this.time});

  SpecailFoodModel.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <SpecailFoodData>[];
      json['data'].forEach((v) {
        data!.add(new SpecailFoodData.fromJson(v));
      });
    }
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class SpecailFoodData {
  int? id;
  num? price;
  String? beginTime;
  String? endTime;
  String? announcedTime;
  int? shipmentType;
  num? shipmentFee;
  int? priceMarkupId;
  int? isBegin;
  String? rule;
  int? pageCount;
  int? userMarketMaxOrderCount;
  int? userSaledOrderCount;
  int? state;
  int? customerType;
  int? userType;
  List<Restaurant>? restaurant;

  SpecailFoodData(
      {this.id,
        this.price,
        this.beginTime,
        this.endTime,
        this.announcedTime,
        this.shipmentType,
        this.shipmentFee,
        this.priceMarkupId,
        this.isBegin,
        this.rule,
        this.pageCount,
        this.userMarketMaxOrderCount,
        this.userSaledOrderCount,
        this.state,
        this.restaurant,
        this.userType});

  SpecailFoodData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    price = json['price'];
    beginTime = json['begin_time'];
    endTime = json['end_time'];
    announcedTime = json['announced_time'];
    shipmentType = json['shipment_type'];
    shipmentFee = json['shipment_fee'];
    priceMarkupId = json['price_markup_id'];
    isBegin = json['is_begin'];
    rule = json['rule'];
    pageCount = json['pageCount'];
    userMarketMaxOrderCount = json['user_market_max_order_count'];
    userSaledOrderCount = json['user_saled_order_count'];
    state = json['state'];
    customerType = json['customer_type'];
    userType = json['user_type'];
    if (json['restaurant'] != null) {
      restaurant = <Restaurant>[];
      json['restaurant'].forEach((v) {
        restaurant!.add(new Restaurant.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['price'] = this.price;
    data['begin_time'] = this.beginTime;
    data['end_time'] = this.endTime;
    data['announced_time'] = this.announcedTime;
    data['shipment_type'] = this.shipmentType;
    data['shipment_fee'] = this.shipmentFee;
    data['price_markup_id'] = this.priceMarkupId;
    data['is_begin'] = this.isBegin;
    data['rule'] = this.rule;
    data['pageCount'] = this.pageCount;
    data['user_market_max_order_count'] = this.userMarketMaxOrderCount;
    data['user_saled_order_count'] = this.userSaledOrderCount;
    data['state'] = this.state;
    data['customer_type'] = this.customerType;
    data['user_type'] = this.userType;
    if (this.restaurant != null) {
      data['restaurant'] = this.restaurant!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Restaurant {
  int? restaurantId;
  String? restaurantName;
  String? restaurantLogo;
  String? distance;
  SpecialDistribution? distribution;
  int? restaurantState;
  List<SpecialItems>? items;

  Restaurant(
      {this.restaurantId,
        this.restaurantName,
        this.restaurantLogo,
        this.distance,
        this.distribution,
        this.restaurantState,
        this.items});

  Restaurant.fromJson(Map<String, dynamic> json) {
    restaurantId = json['restaurant_id'];
    restaurantName = json['restaurant_name'];
    restaurantLogo = json['restaurant_logo'];
    distance = json['distance'];
    distribution = json['distribution'] != null
        ? new SpecialDistribution.fromJson(json['distribution'])
        : null;
    restaurantState = json['restaurant_state'];
    if (json['items'] != null) {
      items = <SpecialItems>[];
      json['items'].forEach((v) {
        items!.add(new SpecialItems.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['restaurant_id'] = this.restaurantId;
    data['restaurant_name'] = this.restaurantName;
    data['restaurant_logo'] = this.restaurantLogo;
    data['distance'] = this.distance;
    if (this.distribution != null) {
      data['distribution'] = this.distribution!.toJson();
    }
    data['restaurant_state'] = this.restaurantState;
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SpecialDistribution {
  num? distance;
  num? param;
  num? shipment;

  SpecialDistribution({this.distance, this.param, this.shipment});

  SpecialDistribution.fromJson(Map<String, dynamic> json) {
    distance = json['distance'];
    param = json['param'];
    shipment = json['shipment'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['distance'] = this.distance;
    data['param'] = this.param;
    data['shipment'] = this.shipment;
    return data;
  }
}

class SpecialItems {
  int? count;
  int? seckillId;
  int? priceMarkupId;
  int? seckillActive;
  int? foodId;
  String? foodName;
  int? foodType;
  int? takeTime;
  String? image;
  num? price;
  num? oldPrice;
  int? saledCount;
  int? totalCount;
  int? maxOrderCount;
  int? lunchBoxAccommodate;
  num? lunchBoxFee;
  int? lunchBoxId;
  num? percent;
  num? originalProfit;
  num? profit;
  int? state;

  SpecialItems(
      {this.count,
        this.seckillId,
        this.priceMarkupId,
        this.seckillActive,
        this.foodId,
        this.foodName,
        this.foodType,
        this.takeTime,
        this.image,
        this.price,
        this.oldPrice,
        this.saledCount,
        this.totalCount,
        this.maxOrderCount,
        this.lunchBoxAccommodate,
        this.lunchBoxFee,
        this.lunchBoxId,
        this.percent,
        this.originalProfit,
        this.profit,
        this.state});

  SpecialItems.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    seckillId = json['seckill_id'];
    priceMarkupId = json['price_markup_id'];
    seckillActive = json['seckill_active'];
    foodId = json['food_id'];
    foodName = json['food_name'];
    foodType = json['food_type'];
    takeTime = json['take_time'];
    image = json['image'];
    price = json['price'];
    oldPrice = json['old_price'];
    saledCount = json['saled_count'];
    totalCount = json['total_count'];
    maxOrderCount = json['max_order_count'];
    lunchBoxAccommodate = json['lunch_box_accommodate'];
    lunchBoxFee = json['lunch_box_fee'];
    lunchBoxId = json['lunch_box_id'];
    percent = json['percent'];
    originalProfit = json['original_profit'];
    profit = json['profit'];
    state = json['state'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['count'] = this.count;
    data['seckill_id'] = this.seckillId;
    data['price_markup_id'] = this.priceMarkupId;
    data['seckill_active'] = this.seckillActive;
    data['food_id'] = this.foodId;
    data['food_name'] = this.foodName;
    data['food_type'] = this.foodType;
    data['take_time'] = this.takeTime;
    data['image'] = this.image;
    data['price'] = this.price;
    data['old_price'] = this.oldPrice;
    data['saled_count'] = this.saledCount;
    data['total_count'] = this.totalCount;
    data['max_order_count'] = this.maxOrderCount;
    data['lunch_box_accommodate'] = this.lunchBoxAccommodate;
    data['lunch_box_fee'] = this.lunchBoxFee;
    data['lunch_box_id'] = this.lunchBoxId;
    data['percent'] = this.percent;
    data['original_profit'] = this.originalProfit;
    data['profit'] = this.profit;
    data['state'] = this.state;
    return data;
  }
}