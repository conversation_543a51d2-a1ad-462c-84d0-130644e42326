class DotsData {
  final int coupon;
  final bool hasDot;
  final bool hasOrderDot;
  final int orderDotCount;
  final num snowAmount;
  final num amount214;
  final String orderRankingActivityTitle;

  const DotsData({
    this.coupon = 0,
    this.hasDot = false,
    this.hasOrderDot = false,
    this.orderDotCount = 0,
    this.snowAmount = 0,
    this.amount214 = 0,
    this.orderRankingActivityTitle = '',
  });

  factory DotsData.fromJson(Map<String, dynamic> json) {
    return DotsData(
      coupon: json['coupon'] ?? 0,
      hasDot: json['has_dot'] ?? false,
      hasOrderDot: json['has_order_dot'] ?? false,
      orderDotCount: json['order_dot_count'] ?? 0,
      snowAmount: json['snow_amount'] ?? 0,
      amount214: json['amount_214'] ?? 0,
      orderRankingActivityTitle: json['order_ranking_activity_title'] ?? '',
    );
  }

  
}
