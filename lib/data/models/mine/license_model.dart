/// 代理资质数据模型
class LicenseModel {
  /// 资质类型名称
  final String docTypeName;

  /// 资质图片路径
  final String filePath;

  /// 构造函数
  const LicenseModel({
    required this.docTypeName,
    required this.filePath,
  });

  /// 从JSON构造
  factory LicenseModel.fromJson(Map<String, dynamic> json) {
    return LicenseModel(
      docTypeName: json['doc_type_name'] as String,
      filePath: json['mlz_file_path'] as String,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'doc_type_name': docTypeName,
      'mlz_file_path': filePath,
    };
  }

  /// 复制方法
  LicenseModel copyWith({
    String? docTypeName,
    String? filePath,
  }) {
    return LicenseModel(
      docTypeName: docTypeName ?? this.docTypeName,
      filePath: filePath ?? this.filePath,
    );
  }
}
