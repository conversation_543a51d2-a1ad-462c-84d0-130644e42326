import 'package:json_annotation/json_annotation.dart';

part 'help_model.g.dart';

/// 帮助模型
@JsonSerializable()
class HelpModel {
  final int id;
  final String title;

  /// 构造函数
  const HelpModel({
    required this.id,
    required this.title,
  });

  factory HelpModel.fromJson(Map<String, dynamic> json) =>
      _$HelpModelFromJson(json);

  Map<String, dynamic> toJson() => _$HelpModelToJson(this);
}
