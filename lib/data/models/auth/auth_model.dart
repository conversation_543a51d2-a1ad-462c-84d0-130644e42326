// 认证响应模型
class AuthResponse {
  final int status;
  final String? msg;
  final AuthData? data;
  final String? lang;

  AuthResponse({
    required this.status,
    this.msg,
    this.data,
    this.lang,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      status: json['status'] ?? 0,
      msg: json['msg'],
      data: json['data'] != null ? AuthData.fromJson(json['data']) : null,
      lang: json['lang'],
    );
  }
}

// 认证数据模型
class AuthData {
  final TokenInfo? tokens;
  final UserInfo? user;

  AuthData({
    this.tokens,
    this.user,
  });

  factory AuthData.fromJson(Map<String, dynamic> json) {
    return AuthData(
      tokens: json['tokens'] != null ? TokenInfo.fromJson(json['tokens']) : null,
      user: json['user'] != null ? UserInfo.fromJson(json['user']) : null,
    );
  }
}

// 用户信息模型
class UserInfo {
  final int id;
  final String? name;
  final String? mobile;
  final String? avatar;
  final String? birthday;
  final int? gender;
  final int? money;
  final int? points;
  final dynamic profilePoint;
  final dynamic recommendClientPoint;

  UserInfo({
    required this.id,
    this.name,
    this.mobile,
    this.avatar,
    this.birthday,
    this.gender,
    this.money,
    this.points,
    this.profilePoint,
    this.recommendClientPoint,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      id: json['id'] ?? 0,
      name: json['name'],
      mobile: json['mobile'],
      avatar: json['avatar'],
      birthday: json['birthday'],
      gender: json['gender'],
      money: json['money'],
      points: json['points'],
      profilePoint: json['profile_point'],
      recommendClientPoint: json['recommend_client_point'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'mobile': mobile,
      'avatar': avatar,
      'birthday': birthday,
      'gender': gender,
      'money': money,
      'points': points,
      'profile_point': profilePoint,
      'recommend_client_point': recommendClientPoint,
    };
  }
}
// Token信息模型
class TokenInfo {
  final String? accessToken;
  final String? refreshToken;
  final String? tokenType;
  final int? expiresIn;

  TokenInfo({
    this.accessToken,
    this.refreshToken,
    this.tokenType,
    this.expiresIn,
  });

  factory TokenInfo.fromJson(Map<String, dynamic> json) {
    return TokenInfo(
      accessToken: json['access_token'],
      refreshToken: json['refresh_token'],
      tokenType: json['token_type'],
      expiresIn: json['expires_in'],
    );
  }
} 