// 认证状态
import 'auth_model.dart';

class AuthState {
  final bool isLoggedIn;
  final bool isLoading;
  final bool isSending;
  final int countDown;
  final String token;
  final UserInfo? userInfo;
  final String? errorMessage;

  AuthState({
    this.isLoggedIn = false,
    this.isLoading = false,
    this.isSending = false,
    this.countDown = 60,
    this.token = '',
    this.userInfo,
    this.errorMessage,
  });

  AuthState copyWith({
    bool? isLoggedIn,
    bool? isLoading,
    bool? isSending,
    int? countDown,
    String? token,
    UserInfo? userInfo,
    String? errorMessage,
  }) {
    return AuthState(
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
      isLoading: isLoading ?? this.isLoading,
      isSending: isSending ?? this.isSending,
      countDown: countDown ?? this.countDown,
      token: token ?? this.token,
      userInfo: userInfo ?? this.userInfo,
      errorMessage: errorMessage,
    );
  }
}
