/// 海报列表响应模型
class PosterListResponse {
  final int status;
  final String? msg;
  final List<PosterModel> data;
  final String? lang;

  PosterListResponse({
    required this.status,
    this.msg,
    required this.data,
    this.lang,
  });

  factory PosterListResponse.fromJson(Map<String, dynamic> json) {
    return PosterListResponse(
      status: json['status'] ?? 0,
      msg: json['msg'],
      data: json['data'] != null
          ? List<PosterModel>.from(
              json['data'].map((x) => PosterModel.fromJson(x)))
          : [],
      lang: json['lang'],
    );
  }
}

/// 海报模型
class PosterModel {
  final int id;
  final String cover;
  final int typeId;
  final double height;

  PosterModel({
    required this.id,
    required this.cover,
    required this.typeId,
    required this.height,
  });

  factory PosterModel.fromJson(Map<String, dynamic> json) {
    return PosterModel(
      id: json['id'] ?? 0,
      cover: json['cover'] ?? '',
      typeId: json['type_id'] ?? 0,
      height:
          json['height'] != null ? (json['height'] as num).toDouble() : 520.0,
    );
  }
}

/// 海报查询响应模型
class PosterQueryResponse {
  final int status;
  final String? msg;
  final PosterQueryData data;
  final String? lang;

  PosterQueryResponse({
    required this.status,
    this.msg,
    required this.data,
    this.lang,
  });

  factory PosterQueryResponse.fromJson(Map<String, dynamic> json) {
    return PosterQueryResponse(
      status: json['status'] ?? 0,
      msg: json['msg'],
      data: PosterQueryData.fromJson(json['data'] ?? {}),
      lang: json['lang'],
    );
  }
}

/// 海报查询数据
class PosterQueryData {
  final String url;
  final String? jobId;

  PosterQueryData({
    required this.url,
    this.jobId,
  });

  factory PosterQueryData.fromJson(Map<String, dynamic> json) {
    return PosterQueryData(
      url: json['url'] ?? '',
      jobId: json['job_id'],
    );
  }
}

/// 海报生成响应模型
class PosterGenerateResponse {
  final int status;
  final String? msg;
  final PosterGenerateData data;
  final String? lang;

  PosterGenerateResponse({
    required this.status,
    this.msg,
    required this.data,
    this.lang,
  });

  factory PosterGenerateResponse.fromJson(Map<String, dynamic> json) {
    return PosterGenerateResponse(
      status: json['status'] ?? 0,
      msg: json['msg'],
      data: PosterGenerateData.fromJson(json['data'] ?? {}),
      lang: json['lang'],
    );
  }
}

/// 海报生成数据
class PosterGenerateData {
  final String jobId;

  PosterGenerateData({
    required this.jobId,
  });

  factory PosterGenerateData.fromJson(Map<String, dynamic> json) {
    return PosterGenerateData(
      jobId: json['job_id'] ?? '',
    );
  }
}
