class RestaurantHomeModel {
  int? status;
  String? msg;
  RestaurantHomeData? data;
  String? lang;

  RestaurantHomeModel({this.status, this.msg, this.data, this.lang});

  RestaurantHomeModel.fromJson(Map<String, dynamic> json) {
    status = json["status"];
    msg = json["msg"];
    data =
        json["data"] == null ? null : RestaurantHomeData.fromJson(json["data"]);
    lang = json["lang"];
  }

  static List<RestaurantHomeModel> fromList(List<Map<String, dynamic>> list) {
    return list.map(RestaurantHomeModel.fromJson).toList();
  }
}

class RestaurantHomeData {
  int? id;
  String? address;
  String? name;
  String? description;
  String? logo;
  num? lat;
  num? lng;
  int? state;
  int? rankState;
  String? openTime;
  String? closeTime;
  bool? isResting;
  bool? isHavePacket;
  bool? isHaveMarket;
  bool? isHavePref;
  bool? isHaveShipmentReduce;
  int? monthOrderCount;
  num? avgDeliveryTime;
  num? distance;
  num? starAvg;
  dynamic notice;
  List<dynamic>? images;
  dynamic mark;
  List<RestaurantLicense>? license;
  String? shareUrl;
  int? commentCount;
  List<dynamic>? ranking;
  int? isFavorite;

  RestaurantHomeData(
      {this.id,
      this.address,
      this.name,
      this.description,
      this.logo,
      this.lat,
      this.lng,
      this.state,
      this.rankState,
      this.openTime,
      this.closeTime,
      this.isResting,
      this.isHavePacket,
      this.isHaveMarket,
      this.isHavePref,
      this.isHaveShipmentReduce,
      this.monthOrderCount,
      this.avgDeliveryTime,
      this.distance,
      this.starAvg,
      this.notice,
      this.images,
      this.mark,
      this.license,
      this.shareUrl,
      this.commentCount,
      this.ranking,
      this.isFavorite});

  RestaurantHomeData.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    address = json["address"];
    name = json["name"];
    description = json["description"];
    logo = json["logo"];
    lat = json["lat"];
    lng = json["lng"];
    state = json["state"];
    rankState = json["rank_state"];
    openTime = json["open_time"];
    closeTime = json["close_time"];
    isResting = json["isResting"];
    isHavePacket = json["is_have_packet"];
    isHaveMarket = json["is_have_market"];
    isHavePref = json["is_have_pref"];
    isHaveShipmentReduce = json["is_have_shipment_reduce"];
    monthOrderCount = json["month_order_count"];
    avgDeliveryTime = json["avg_delivery_time"];
    distance = json["distance"];
    starAvg = json["star_avg"];
    notice = json["notice"];
    images = json["images"] ?? [];
    mark = json["mark"];
    license = json["license"] == null
        ? null
        : (json["license"] as List)
            .map((e) => RestaurantLicense.fromJson(e))
            .toList();
    shareUrl = json["share_url"];
    commentCount = json["comment_count"];
    ranking = json["ranking"] ?? [];
    isFavorite = json["is_favorite"];
  }

  static List<RestaurantHomeData> fromList(List<Map<String, dynamic>> list) {
    return list.map(RestaurantHomeData.fromJson).toList();
  }
}

class RestaurantLicense {
  int? licenseId;
  String? licenseUrl;
  String? bigLicenseUrl;
  String? licenseName;

  RestaurantLicense(
      {this.licenseId, this.licenseUrl, this.bigLicenseUrl, this.licenseName});

  RestaurantLicense.fromJson(Map<String, dynamic> json) {
    licenseId = json["license_id"];
    licenseUrl = json["license_url"];
    bigLicenseUrl = json["big_license_url"];
    licenseName = json["license_name"];
  }

  static List<RestaurantLicense> fromList(List<Map<String, dynamic>> list) {
    return list.map(RestaurantLicense.fromJson).toList();
  }
}
