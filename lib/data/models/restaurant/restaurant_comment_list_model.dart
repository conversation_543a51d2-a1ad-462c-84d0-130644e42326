class RestaurantCommentListModel {
  RestaurantCommentListData? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  RestaurantCommentListModel(
      {this.data, this.lang, this.msg, this.status, this.time});

  RestaurantCommentListModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new RestaurantCommentListData.fromJson(json['data']) : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class RestaurantCommentListData {
  int? perPage;
  int? currentPage;
  int? lastPage;
  List<Type>? type;
  Star? star;
  List<Items>? items;

  RestaurantCommentListData(
      {this.perPage,
        this.currentPage,
        this.lastPage,
        this.type,
        this.star,
        this.items});

  RestaurantCommentListData.fromJson(Map<String, dynamic> json) {
    perPage = json['per_page'];
    currentPage = json['current_page'];
    lastPage = json['last_page'];
    if (json['type'] != null) {
      type = <Type>[];
      json['type'].forEach((v) {
        type!.add(new Type.fromJson(v));
      });
    }
    star = json['star'] != null ? new Star.fromJson(json['star']) : null;
    if (json['items'] != null) {
      items = <Items>[];
      json['items'].forEach((v) {
        items!.add(new Items.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['per_page'] = this.perPage;
    data['current_page'] = this.currentPage;
    data['last_page'] = this.lastPage;
    if (this.type != null) {
      data['type'] = this.type!.map((v) => v.toJson()).toList();
    }
    if (this.star != null) {
      data['star'] = this.star!.toJson();
    }
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Type {
  String? name;
  int? type;
  int? count;

  Type({this.name, this.type, this.count});

  Type.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    type = json['type'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['type'] = this.type;
    data['count'] = this.count;
    return data;
  }
}

class Star {
  num? starAvg;
  num? foodStarAvg;
  num? boxStarAvg;
  int? shipperAvg;

  Star({this.starAvg, this.foodStarAvg, this.boxStarAvg, this.shipperAvg});

  Star.fromJson(Map<String, dynamic> json) {
    starAvg = json['star_avg'];
    foodStarAvg = json['food_star_avg'];
    boxStarAvg = json['box_star_avg'];
    shipperAvg = json['shipper_avg'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['star_avg'] = this.starAvg;
    data['food_star_avg'] = this.foodStarAvg;
    data['box_star_avg'] = this.boxStarAvg;
    data['shipper_avg'] = this.shipperAvg;
    return data;
  }
}

class Items {
  int? id;
  int? type;
  String? createdAt;
  int? star;
  String? text;
  int? foodStar;
  int? boxStar;
  String? foodName;
  int? foodId;
  String? userAvatar;
  String? userName;
  List<String>? images;
  List<Replies>? replies;

  Items(
      {this.id,
        this.type,
        this.createdAt,
        this.star,
        this.text,
        this.foodStar,
        this.boxStar,
        this.foodName,
        this.foodId,
        this.userAvatar,
        this.userName,
        this.images,
        this.replies});

  Items.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    type = json['type'];
    createdAt = json['created_at'];
    star = json['star'];
    text = json['text'];
    foodStar = json['food_star'];
    boxStar = json['box_star'];
    foodName = json['food_name'];
    foodId = json['food_id'];
    userAvatar = json['user_avatar'];
    userName = json['user_name'];
    images = json['images'].cast<String>();
    if (json['replies'] != null) {
      replies = <Replies>[];
      json['replies'].forEach((v) {
        replies!.add(new Replies.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['type'] = this.type;
    data['created_at'] = this.createdAt;
    data['star'] = this.star;
    data['text'] = this.text;
    data['food_star'] = this.foodStar;
    data['box_star'] = this.boxStar;
    data['food_name'] = this.foodName;
    data['food_id'] = this.foodId;
    data['user_avatar'] = this.userAvatar;
    data['user_name'] = this.userName;
    data['images'] = this.images;
    if (this.replies != null) {
      data['replies'] = this.replies!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Replies {
  int? type;
  String? text;
  String? createdAt;

  Replies({this.type, this.text, this.createdAt});

  Replies.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    text = json['text'];
    createdAt = json['created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type'] = this.type;
    data['text'] = this.text;
    data['created_at'] = this.createdAt;
    return data;
  }
}