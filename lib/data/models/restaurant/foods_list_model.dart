import 'spec_model.dart';

/// 用于copyWith方法中区分null和未设置的常量
const Object _undefined = Object();

class FoodsListModel {
  final FoodsData? data;
  final String? lang;
  final String? msg;
  final int? status;
  final String? time;

  FoodsListModel({
    this.data,
    this.lang,
    this.msg,
    this.status,
    this.time,
  });

  factory FoodsListModel.fromJson(Map<String, dynamic> json) => FoodsListModel(
        data: json['data'] == null ? null : FoodsData.fromJson(json['data']),
        lang: json['lang'],
        msg: json['msg'],
        status: json['status'],
        time: json['time'],
      );

  static List<FoodsListModel> fromList(List<Map<String, dynamic>> list) {
    return list.map(FoodsListModel.fromJson).toList();
  }
}

class FoodsData {
  final int? canMulazimTake;
  final int? canSelfTake;
  final Distribution? distribution;
  final List<FoodType>? foodType;
  final List<Food>? foods;
  final int? id;
  final int? isResting;
  final int? limit;
  final int? lotteryActive;
  final num? lotteryMinOrderPrice;
  final num? lowestPercent;
  final Market? market;
  final String? name;
  final int? notSameLocation;
  final NotSameLocationMsg? notSameLocationMsg;
  final Notice? notice;
  final int? page;
  final int? restaurantType;
  final int? total;
  final int? totalPage;
  final int? isOpen;
  final List<ReductionStep>? reductionSteps;
  final Tags? tags;
  final List<String>? takeTime;

  FoodsData({
    this.canMulazimTake,
    this.canSelfTake,
    this.distribution,
    this.foodType,
    this.foods,
    this.id,
    this.isResting,
    this.limit,
    this.lotteryActive,
    this.lotteryMinOrderPrice,
    this.lowestPercent,
    this.market,
    this.name,
    this.notSameLocation,
    this.notSameLocationMsg,
    this.notice,
    this.page,
    this.restaurantType,
    this.total,
    this.totalPage,
    this.isOpen,
    this.reductionSteps,
    this.tags,
    this.takeTime,
  });

  /// 复制并创建一个新的FoodsData实例
  FoodsData copyWith({
    int? canMulazimTake,
    int? canSelfTake,
    Distribution? distribution,
    List<FoodType>? foodType,
    List<Food>? foods,
    int? id,
    int? isResting,
    int? limit,
    int? lotteryActive,
    num? lotteryMinOrderPrice,
    num? lowestPercent,
    Market? market,
    String? name,
    int? notSameLocation,
    NotSameLocationMsg? notSameLocationMsg,
    Notice? notice,
    int? page,
    int? restaurantType,
    int? total,
    int? totalPage,
    int? isOpen,
    List<ReductionStep>? reductionSteps,
    Tags? tags,
    List<String>? takeTime,
  }) {
    return FoodsData(
      canMulazimTake: canMulazimTake ?? this.canMulazimTake,
      canSelfTake: canSelfTake ?? this.canSelfTake,
      distribution: distribution ?? this.distribution,
      foodType: foodType ?? this.foodType,
      foods: foods,
      id: id ?? this.id,
      isResting: isResting ?? this.isResting,
      limit: limit ?? this.limit,
      lotteryActive: lotteryActive ?? this.lotteryActive,
      lotteryMinOrderPrice: lotteryMinOrderPrice ?? this.lotteryMinOrderPrice,
      lowestPercent: lowestPercent ?? this.lowestPercent,
      market: market ?? this.market,
      name: name ?? this.name,
      notSameLocation: notSameLocation ?? this.notSameLocation,
      notSameLocationMsg: notSameLocationMsg ?? this.notSameLocationMsg,
      notice: notice ?? this.notice,
      page: page ?? this.page,
      restaurantType: restaurantType ?? this.restaurantType,
      total: total ?? this.total,
      totalPage: totalPage ?? this.totalPage,
      isOpen: isOpen ?? this.isOpen,
      reductionSteps: reductionSteps ?? this.reductionSteps,
      tags: tags ?? this.tags,
      takeTime: takeTime ?? this.takeTime,
    );
  }

  factory FoodsData.fromJson(Map<String, dynamic> json) => FoodsData(
        canMulazimTake: json['can_mulazim_take'],
        canSelfTake: json['can_self_take'],
        distribution: json['distribution'] == null
            ? null
            : Distribution.fromJson(json['distribution']),
        foodType: json['food_type'] == null
            ? []
            : List<FoodType>.from(
                json['food_type'].map((x) => FoodType.fromJson(x))),
        foods: json['foods'] == null
            ? []
            : List<Food>.from(json['foods'].map((x) => Food.fromJson(x))),
        id: json['id'],
        isResting: json['is_resting'],
        limit: json['limit'],
        lotteryActive: json['lottery_active'],
        lotteryMinOrderPrice: json['lottery_min_order_price'],
        lowestPercent: json['lowest_percent'],
        market: json['market'] == null ? null : Market.fromJson(json['market']),
        name: json['name'],
        notSameLocation: json['not_same_location'],
        notSameLocationMsg: json['not_same_location_msg'] == null
            ? null
            : NotSameLocationMsg.fromJson(json['not_same_location_msg']),
        notice: json['notice'] == null ? null : Notice.fromJson(json['notice']),
        page: json['page'],
        restaurantType: json['restaurant_type'],
        total: json['total'],
        totalPage: json['total_page'],
        isOpen: json['is_open'],
        reductionSteps: json['reduction_steps'] == null
            ? []
            : List<ReductionStep>.from(
                json['reduction_steps'].map((x) => ReductionStep.fromJson(x))),
        tags: json['tags'] == null ? null : Tags.fromJson(json['tags']),
        takeTime: json['take_time'] == null
            ? []
            : List<String>.from(json['take_time'].map((x) => x)),
      );

  static List<FoodsData> fromList(List<Map<String, dynamic>> list) {
    return list.map(FoodsData.fromJson).toList();
  }
}

class Market {
  final bool? hasReduction;
  final bool? hasShipmentReduction;
  final bool? hasMultiDiscount;
  final Steps? steps;
  final Tags? tags;
  final List<String>? reductionFoodsId;

  Market({
    this.hasReduction,
    this.hasShipmentReduction,
    this.hasMultiDiscount,
    this.steps,
    this.tags,
    this.reductionFoodsId,
  });

  factory Market.fromJson(Map<String, dynamic> json) => Market(
        hasReduction: json['has_reduction'],
        hasShipmentReduction: json['has_shipment_reduction'],
        hasMultiDiscount: json['has_multi_discount'],
        steps: json['steps'] == null ? null : Steps.fromJson(json['steps']),
        tags: json['tags'] == null ? null : Tags.fromJson(json['tags']),
        reductionFoodsId: json['reduction_foods_id'] == null
            ? []
            : List<String>.from(
                json['reduction_foods_id'].map((x) => x.toString())),
      );

  static List<Market> fromList(List<Map<String, dynamic>> list) {
    return list.map(Market.fromJson).toList();
  }
}

class Steps {
  final ShipmentStep? shipmentStep;
  final List<ReductionStep>? reductionStep;

  Steps({
    this.shipmentStep,
    this.reductionStep,
  });

  factory Steps.fromJson(Map<String, dynamic> json) => Steps(
        shipmentStep: json['shipment_step'] == null
            ? null
            : ShipmentStep.fromJson(json['shipment_step']),
        reductionStep: json['reduction_step'] == null
            ? []
            : List<ReductionStep>.from((json['reduction_step'] as List)
                .map((x) => ReductionStep.fromJson(x))),
      );
}

class ShipmentStep {
  final int? distanceEnd;
  final int? distanceStart;
  final num? minDeliveryPrice;
  final num? shipmentReduce;

  ShipmentStep({
    this.distanceEnd,
    this.distanceStart,
    this.minDeliveryPrice,
    this.shipmentReduce,
  });

  factory ShipmentStep.fromJson(Map<String, dynamic> json) => ShipmentStep(
        distanceEnd: json['distance_end'],
        distanceStart: json['distance_start'],
        minDeliveryPrice: json['min_delivery_price'],
        shipmentReduce: json['shipment_reduce'],
      );
}

class ReductionStep {
  final num? price;
  final num? reduce;

  ReductionStep({
    this.price,
    this.reduce,
  });

  factory ReductionStep.fromJson(Map<String, dynamic> json) => ReductionStep(
        price: json['price'],
        reduce: json['reduce'],
      );
}

class Tags {
  final List<Tag>? reductionTags;
  final List<Tag>? shipmentReductionTags;
  final List<Tag>? multiDiscountTags;
  Tags({
    this.reductionTags,
    this.shipmentReductionTags,
    this.multiDiscountTags,
  });

  factory Tags.fromJson(Map<String, dynamic> json) => Tags(
        reductionTags: json['reduction_tags'] == null
            ? []
            : List<Tag>.from(
                json['reduction_tags'].map((x) => Tag.fromJson(x))),
        shipmentReductionTags: json['shipment_reduction_tags'] == null
            ? []
            : List<Tag>.from(
                json['shipment_reduction_tags'].map((x) => Tag.fromJson(x))),
        multiDiscountTags: json['multi_discount_tag'] == null
            ? []
            : List<Tag>.from(
                json['multi_discount_tag'].map((x) => Tag.fromJson(x))),
      );
}

class Tag {
  final String? background;
  final String? borderColor;
  final String? color;
  final int? distanceEnd;
  final int? distanceStart;
  final String? image;
  final num? minDeliveryPrice;
  final num? price;
  final String? title;
  final int? type;

  Tag({
    this.background,
    this.borderColor,
    this.color,
    this.distanceEnd,
    this.distanceStart,
    this.image,
    this.minDeliveryPrice,
    this.price,
    this.title,
    this.type,
  });

  factory Tag.fromJson(Map<String, dynamic> json) => Tag(
        background: json['background'],
        borderColor: json['border_color'],
        color: json['color'],
        distanceEnd: json['distance_end'],
        distanceStart: json['distance_start'],
        image: json['image'],
        minDeliveryPrice: json['min_delivery_price'],
        price: json['price'],
        title: json['title'],
        type: json['type'],
      );

  /// 转换为JSON
  Map<String, dynamic> toJson() => {
        'background': background,
        'border_color': borderColor,
        'color': color,
        'distance_end': distanceEnd,
        'distance_start': distanceStart,
        'image': image,
        'min_delivery_price': minDeliveryPrice,
        'price': price,
        'title': title,
        'type': type,
      };
}

class FoodType {
  final int? id;
  final String? name;
  final int? shake;
  final int? type;
  final List<License>? license;
  final int? licenseType;
  final String? nameUg;
  final String? nameZh;

  FoodType({
    this.id,
    this.name,
    this.shake,
    this.type,
    this.license,
    this.licenseType,
    this.nameUg,
    this.nameZh,
  });

  factory FoodType.fromJson(Map<String, dynamic> json) => FoodType(
        id: json['id'],
        name: json['name'],
        shake: json['shake'],
        type: json['type'],
        license: json['license'] == null
            ? []
            : List<License>.from(
                json['license'].map((x) => License.fromJson(x))),
        licenseType: json['license_type'],
        nameUg: json['name_ug'],
        nameZh: json['name_zh'],
      );
}

class License {
  final String? bigImage;
  final int? id;
  final String? image;
  final String? name;
  final String? smallImage;

  License({this.bigImage, this.id, this.image, this.name, this.smallImage});

  factory License.fromJson(Map<String, dynamic> json) => License(
        bigImage: json['big_image'],
        id: json['id'],
        image: json['image'],
        name: json['name'],
        smallImage: json['small_image'],
      );
}

class Distribution {
  final num? distance;
  final num? param;
  final num? shipment;

  Distribution({this.distance, this.param, this.shipment});

  factory Distribution.fromJson(Map<String, dynamic> json) => Distribution(
        distance: json['distance'],
        param: json['param'],
        shipment: json['shipment'],
      );
}

class Notice {
  final String? content;
  final String? title;

  Notice({this.content, this.title});

  factory Notice.fromJson(Map<String, dynamic> json) => Notice(
        content: json['content'],
        title: json['title'],
      );
}

class Food {
  final String? beginTime;
  final String? bigImage;
  final String? endTime;
  final String? foodQuantity;
  final int? foodQuantityType;
  final String? foodQuantityTypeValUg;
  final String? foodQuantityTypeValZh;
  final num? hasFoodsPries;
  final int? id;
  final String? image;
  final int? lunchBoxAccommodate;
  final num? lunchBoxFee;
  final int? lunchBoxId;
  final String? lunchBoxName;
  final int? maxOrderCount;
  final int? minCount;
  final int? monthOrderCount;
  final String? name;
  final String? nameUg;
  final String? nameZh;
  final int? orderCount;
  final num? originPrice;
  final num? originProfit;
  final num? percent;
  final num? price;
  final num? profit;
  final List<Food>? relations;
  final int? seckillActive;
  final int? specialActive;
  final int? seckillId;
  final int? seckillMaxOrderCount;
  final int? seckillTotalCount;
  final int? seckillSaledCount;
  final num? seckillPrice;
  final int? seckillTime;
  final int? state;
  final int? typeId;
  final int? weight;
  final int? weightInGroup;
  final int? prefrentialId;
  final num? prefrentialPrice;
  final int? prefrentialPriceMarkupId;
  final int? multiDiscountId;
  final List<MultiDiscountStep>? multiDiscountSteps;
  final num? multiDiscountAdvancePrice;

  /// 美食类型：0-普通美食，1-规格美食，2-套餐美食
  final int? foodType;

  // ========== 规格相关字段 ==========
  /// 规格唯一标识符 - 用于区分不同规格组合的商品
  final String? specUniqueId;

  /// 规格选择选项信息 - 存储用户选择的规格详情
  final List<Map<String, dynamic>>? specSelectedOptions;

  /// 规格选项ID列表 - 对应微信小程序中的spec_option_ids，用于默认规格选择
  final List<int>? specOptionIds;

  /// 原价 - 用于显示划线价格
  final num? oldPrice;

  /// 满减活动标签
  final List<dynamic>? reductionFoodsTags;

  /// 优惠数量 - 享受优惠的商品数量
  final int? prefrentialCount;

  // ========== 套餐相关字段 ==========
  /// 套餐美食项目列表 - 当foodType为2时，包含套餐内的所有美食
  final List<ComboFoodItem>? comboFoodItems;

  Food({
    this.beginTime,
    this.bigImage,
    this.endTime,
    this.foodQuantity,
    this.foodQuantityType,
    this.foodQuantityTypeValUg,
    this.foodQuantityTypeValZh,
    this.hasFoodsPries,
    this.id,
    this.image,
    this.lunchBoxAccommodate,
    this.lunchBoxFee,
    this.lunchBoxId,
    this.lunchBoxName,
    this.maxOrderCount,
    this.minCount,
    this.monthOrderCount,
    this.name,
    this.nameUg,
    this.nameZh,
    this.orderCount,
    this.originPrice,
    this.originProfit,
    this.percent,
    this.price,
    this.profit,
    this.relations,
    this.seckillActive,
    this.specialActive,
    this.seckillId,
    this.seckillMaxOrderCount,
    this.seckillTotalCount,
    this.seckillSaledCount,
    this.seckillPrice,
    this.seckillTime,
    this.state,
    this.typeId,
    this.weight,
    this.weightInGroup,
    this.prefrentialId,
    this.prefrentialPrice,
    this.prefrentialPriceMarkupId,
    this.multiDiscountId,
    this.multiDiscountSteps,
    this.multiDiscountAdvancePrice,
    this.foodType,
    // 规格相关字段
    this.specUniqueId,
    this.specSelectedOptions,
    this.specOptionIds,
    this.oldPrice,
    this.reductionFoodsTags,
    this.prefrentialCount,
    this.comboFoodItems,
  });

  factory Food.fromJson(Map<String, dynamic> json) => Food(
        beginTime: json['begin_time'],
        bigImage: json['big_image'],
        endTime: json['end_time'],
        foodQuantity: json['food_quantity'],
        foodQuantityType: json['food_quantity_type'],
        foodQuantityTypeValUg: json['food_quantity_type_val_ug'],
        foodQuantityTypeValZh: json['food_quantity_type_val_zh'],
        hasFoodsPries: json['has_foods_pries'],
        id: json['id'],
        image: json['image'],
        lunchBoxAccommodate: json['lunch_box_accommodate'],
        lunchBoxFee: json['lunch_box_fee'],
        lunchBoxId: json['lunch_box_id'],
        lunchBoxName: json['lunch_box_name'],
        maxOrderCount: json['max_order_count'],
        minCount: json['min_count'],
        monthOrderCount: json['month_order_count'],
        name: json['name'],
        nameUg: json['name_ug'],
        nameZh: json['name_zh'],
        orderCount: json['order_count'],
        originPrice: json['origin_price'],
        originProfit: json['origin_profit'],
        percent: json['percent'],
        price: json['price'],
        profit: json['profit'],
        relations: json['relations'] == null
            ? []
            : List<Food>.from(json['relations'].map((x) => Food.fromJson(x))),
        seckillActive: json['seckill_active'],
        specialActive: json['special_active'],
        seckillId: json['seckill_id'],
        seckillMaxOrderCount: json['seckill_max_order_count'],
        seckillTotalCount: json['seckill_total_count'],
        seckillSaledCount: json['seckill_saled_count'],
        seckillPrice: json['seckill_price'],
        seckillTime: json['seckill_time'],
        state: json['state'],
        typeId: json['type_id'],
        weight: json['weight'],
        weightInGroup: json['weight_in_group'],
        prefrentialId: json['prefrential_id'],
        prefrentialPrice: json['prefrential_price'],
        prefrentialPriceMarkupId: json['prefrential_price_markup_id'],
        multiDiscountId: json['multi_discount_id'],
        multiDiscountSteps: json['multi_discount_steps'] == null
            ? []
            : List<MultiDiscountStep>.from(json['multi_discount_steps']
                .map((x) => MultiDiscountStep.fromJson(x))),
        multiDiscountAdvancePrice: json['multi_discount_advance_price'],
        foodType: json['food_type'],
        // 规格相关字段
        specUniqueId: json['spec_unique_id'],
        specSelectedOptions: json['spec_selected_options'] == null
            ? null
            : List<Map<String, dynamic>>.from(json['spec_selected_options']),
        specOptionIds: json['option_ids'] == null
            ? null
            : List<int>.from(json['option_ids']),
        oldPrice: json['old_price'],

        /// 在 [RestaurantDetailController]中通过[_processReductionFoodsTags]来处理满减活动标签
        reductionFoodsTags: json['reduction_foods_tags'],
        prefrentialCount: json['prefrential_count'],
        comboFoodItems: json['combo_food_items'] == null
            ? null
            : List<ComboFoodItem>.from(
                json['combo_food_items'].map((x) => ComboFoodItem.fromJson(x))),
      );

  /// 创建一个新的Food实例，允许修改某些字段
  Food copyWith({
    String? beginTime,
    String? bigImage,
    String? endTime,
    String? foodQuantity,
    int? foodQuantityType,
    String? foodQuantityTypeValUg,
    String? foodQuantityTypeValZh,
    num? hasFoodsPries,
    int? id,
    String? image,
    int? lunchBoxAccommodate,
    num? lunchBoxFee,
    int? lunchBoxId,
    String? lunchBoxName,
    int? maxOrderCount,
    int? minCount,
    int? monthOrderCount,
    String? name,
    String? nameUg,
    String? nameZh,
    int? orderCount,
    num? originPrice,
    num? originProfit,
    num? percent,
    num? price,
    num? profit,
    List<Food>? relations,
    int? seckillActive,
    int? specialActive,
    int? seckillId,
    int? seckillMaxOrderCount,
    int? seckillTotalCount,
    int? seckillSaledCount,
    num? seckillPrice,
    int? seckillTime,
    int? state,
    int? typeId,
    int? weight,
    int? weightInGroup,
    int? prefrentialId,
    // 支持显式设置为null的字段
    Object? prefrentialPrice = _undefined,
    int? prefrentialPriceMarkupId,
    int? multiDiscountId,
    List<MultiDiscountStep>? multiDiscountSteps,
    num? multiDiscountAdvancePrice,
    int? foodType,
    // 规格相关字段
    String? specUniqueId,
    List<Map<String, dynamic>>? specSelectedOptions,
    List<int>? specOptionIds,
    num? oldPrice,
    List<dynamic>? reductionFoodsTags,
    int? prefrentialCount,
    List<ComboFoodItem>? comboFoodItems,
  }) {
    return Food(
      beginTime: beginTime ?? this.beginTime,
      bigImage: bigImage ?? this.bigImage,
      endTime: endTime ?? this.endTime,
      foodQuantity: foodQuantity ?? this.foodQuantity,
      foodQuantityType: foodQuantityType ?? this.foodQuantityType,
      foodQuantityTypeValUg:
          foodQuantityTypeValUg ?? this.foodQuantityTypeValUg,
      foodQuantityTypeValZh:
          foodQuantityTypeValZh ?? this.foodQuantityTypeValZh,
      hasFoodsPries: hasFoodsPries ?? this.hasFoodsPries,
      id: id ?? this.id,
      image: image ?? this.image,
      lunchBoxAccommodate: lunchBoxAccommodate ?? this.lunchBoxAccommodate,
      lunchBoxFee: lunchBoxFee ?? this.lunchBoxFee,
      lunchBoxId: lunchBoxId ?? this.lunchBoxId,
      lunchBoxName: lunchBoxName ?? this.lunchBoxName,
      maxOrderCount: maxOrderCount ?? this.maxOrderCount,
      minCount: minCount ?? this.minCount,
      monthOrderCount: monthOrderCount ?? this.monthOrderCount,
      name: name ?? this.name,
      nameUg: nameUg ?? this.nameUg,
      nameZh: nameZh ?? this.nameZh,
      orderCount: orderCount ?? this.orderCount,
      originPrice: originPrice ?? this.originPrice,
      originProfit: originProfit ?? this.originProfit,
      percent: percent ?? this.percent,
      price: price ?? this.price,
      profit: profit ?? this.profit,
      relations: relations ?? this.relations,
      seckillActive: seckillActive ?? this.seckillActive,
      specialActive: specialActive ?? this.specialActive,
      seckillId: seckillId ?? this.seckillId,
      seckillMaxOrderCount: seckillMaxOrderCount ?? this.seckillMaxOrderCount,
      seckillTotalCount: seckillTotalCount ?? this.seckillTotalCount,
      seckillSaledCount: seckillSaledCount ?? this.seckillSaledCount,
      seckillPrice: seckillPrice ?? this.seckillPrice,
      seckillTime: seckillTime ?? this.seckillTime,
      state: state ?? this.state,
      typeId: typeId ?? this.typeId,
      weight: weight ?? this.weight,
      weightInGroup: weightInGroup ?? this.weightInGroup,
      prefrentialId: prefrentialId ?? this.prefrentialId,
      prefrentialPrice: prefrentialPrice == _undefined
          ? this.prefrentialPrice
          : prefrentialPrice as num?,
      prefrentialPriceMarkupId:
          prefrentialPriceMarkupId ?? this.prefrentialPriceMarkupId,
      multiDiscountId: multiDiscountId ?? this.multiDiscountId,
      multiDiscountSteps: multiDiscountSteps ?? this.multiDiscountSteps,
      multiDiscountAdvancePrice:
          multiDiscountAdvancePrice ?? this.multiDiscountAdvancePrice,
      foodType: foodType ?? this.foodType,
      // 规格相关字段
      specUniqueId: specUniqueId ?? this.specUniqueId,
      specSelectedOptions: specSelectedOptions ?? this.specSelectedOptions,
      specOptionIds: specOptionIds ?? this.specOptionIds,
      oldPrice: oldPrice ?? this.oldPrice,
      reductionFoodsTags: reductionFoodsTags ?? this.reductionFoodsTags,
      prefrentialCount: prefrentialCount ?? this.prefrentialCount,
      comboFoodItems: comboFoodItems ?? this.comboFoodItems,
    );
  }
}

/// 满减活动标签
class ReductionFoodsTag {
  final String? backgroundColor;
  final String? borderColor;
  final String? color;
  final String? title;
  final int? type;

  ReductionFoodsTag({
    this.backgroundColor,
    this.borderColor,
    this.color,
    this.title,
    this.type,
  });

  factory ReductionFoodsTag.fromJson(Map<String, dynamic> json) =>
      ReductionFoodsTag(
        backgroundColor: json['background'],
        borderColor: json['border_color'],
        color: json['color'],
        title: json['title'],
        type: json['type'],
      );
}

/// 多粉折扣步骤
class MultiDiscountStep {
  final int? index;
  final String? name;
  final int? number;
  final num? price;
  final int? foodId;
  final num? advancePrice;

  MultiDiscountStep({
    this.index,
    this.name,
    this.number,
    this.price,
    this.foodId,
    this.advancePrice,
  });

  factory MultiDiscountStep.fromJson(Map<String, dynamic> json) =>
      MultiDiscountStep(
        index: json['index'],
        name: json['name'],
        number: json['number'],
        price: json['price'],
        foodId: json['food_id'],
        advancePrice: json['advance_price'],
      );

  /// 创建一个新的MultiDiscountStep实例，允许修改某些字段
  MultiDiscountStep copyWith({
    bool? isShow,
    int? index,
    String? name,
    int? number,
    num? price,
    bool? checked,
    int? foodId,
    num? advancePrice,
  }) {
    return MultiDiscountStep(
      index: index ?? this.index,
      name: name ?? this.name,
      number: number ?? this.number,
      price: price ?? this.price,
      foodId: foodId ?? this.foodId,
      advancePrice: advancePrice ?? this.advancePrice,
    );
  }
}

class NotSameLocationMsg {
  final String? btn;
  final String? msg;
  final String? title;

  NotSameLocationMsg({this.btn, this.msg, this.title});

  factory NotSameLocationMsg.fromJson(Map<String, dynamic> json) =>
      NotSameLocationMsg(
        btn: json['btn'],
        msg: json['msg'],
        title: json['title'],
      );
}

/// 套餐中选择的规格信息
class SelectedSpec {
  /// 规格ID
  final int? id;

  /// 规格价格
  final num? price;

  /// 规格选项列表
  final List<SpecOption>? specOptions;

  SelectedSpec({
    this.id,
    this.price,
    this.specOptions,
  });

  factory SelectedSpec.fromJson(Map<String, dynamic> json) => SelectedSpec(
        id: json['id'],
        price: json['price'],
        specOptions: json['spec_options'] == null
            ? null
            : List<SpecOption>.from(
                json['spec_options'].map((x) => SpecOption.fromJson(x))),
      );

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'price': price,
      'spec_options': specOptions?.map((x) => x.toJson()).toList(),
    };
  }

  /// 创建一个新的SelectedSpec实例，允许修改某些字段
  SelectedSpec copyWith({
    int? id,
    num? price,
    List<SpecOption>? specOptions,
  }) {
    return SelectedSpec(
      id: id ?? this.id,
      price: price ?? this.price,
      specOptions: specOptions ?? this.specOptions,
    );
  }
}

/// 套餐美食项目
class ComboFoodItem {
  /// 套餐项目ID
  final int? id;

  /// 套餐中此美食的数量
  final int? count;

  /// 美食ID
  final int? foodId;

  /// 套餐ID
  final int? comboId;

  /// 美食类型
  final int? foodType;

  /// 规格ID（仅当美食有规格时）
  final int? specId;

  /// 餐厅美食信息（嵌套对象）
  final RestaurantFood? restaurantFood;

  /// 选择的规格信息（仅当美食有规格时）
  final SelectedSpec? selectedSpec;

  ComboFoodItem({
    this.id,
    this.count,
    this.foodId,
    this.comboId,
    this.foodType,
    this.specId,
    this.restaurantFood,
    this.selectedSpec,
  });

  factory ComboFoodItem.fromJson(Map<String, dynamic> json) => ComboFoodItem(
        id: json['id'],
        count: json['count'],
        foodId: json['food_id'],
        comboId: json['combo_id'],
        foodType: json['food_type'],
        specId: json['spec_id'],
        restaurantFood: json['restaurant_food'] == null
            ? null
            : RestaurantFood.fromJson(json['restaurant_food']),
        selectedSpec: json['selected_spec'] == null
            ? null
            : SelectedSpec.fromJson(json['selected_spec']),
      );

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['count'] = this.count;
    data['food_id'] = this.foodId;
    data['combo_id'] = this.comboId;
    data['food_type'] = this.foodType;
    data['spec_id'] = this.specId;
    if (this.restaurantFood != null) {
      data['restaurant_food'] = this.restaurantFood!.toJson();
    }
    if (this.selectedSpec != null) {
      data['selected_spec'] = this.selectedSpec!.toJson();
    }
    return data;
  }

  /// 创建一个新的ComboFoodItem实例，允许修改某些字段
  ComboFoodItem copyWith({
    int? id,
    int? count,
    int? foodId,
    int? comboId,
    int? foodType,
    int? specId,
    RestaurantFood? restaurantFood,
    SelectedSpec? selectedSpec,
  }) {
    return ComboFoodItem(
      id: id ?? this.id,
      count: count ?? this.count,
      foodId: foodId ?? this.foodId,
      comboId: comboId ?? this.comboId,
      foodType: foodType ?? this.foodType,
      specId: specId ?? this.specId,
      restaurantFood: restaurantFood ?? this.restaurantFood,
      selectedSpec: selectedSpec ?? this.selectedSpec,
    );
  }
}

/// 餐厅美食信息（用于套餐中的美食详情）
class RestaurantFood {
  /// 美食ID
  final int? id;

  /// 美食类型
  final int? foodType;

  /// 美食名称
  final String? name;

  /// 美食图片
  final String? image;

  /// 美食价格
  final num? price;

  /// 美食评分
  final String? starAvg;

  RestaurantFood({
    this.id,
    this.foodType,
    this.name,
    this.image,
    this.price,
    this.starAvg,
  });

  factory RestaurantFood.fromJson(Map<String, dynamic> json) => RestaurantFood(
        id: json['id'],
        foodType: json['food_type'],
        name: json['name'],
        image: json['image'],
        price: json['price'],
        starAvg: json['star_avg'],
      );

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['food_type'] = this.foodType;
    data['name'] = this.name;
    data['image'] = this.image;
    data['price'] = this.price;
    data['star_avg'] = this.starAvg;
    return data;
  }

  /// 创建一个新的RestaurantFood实例，允许修改某些字段
  RestaurantFood copyWith({
    int? id,
    int? foodType,
    String? name,
    String? image,
    num? price,
    String? starAvg,
  }) {
    return RestaurantFood(
      id: id ?? this.id,
      foodType: foodType ?? this.foodType,
      name: name ?? this.name,
      image: image ?? this.image,
      price: price ?? this.price,
      starAvg: starAvg ?? this.starAvg,
    );
  }
}
