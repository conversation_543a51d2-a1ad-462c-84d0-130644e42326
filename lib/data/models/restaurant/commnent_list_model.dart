
class CommentListModel {
  CommentListData? data;
  String? lang;
  String? msg;
  int? status;

  CommentListModel({this.data, this.lang, this.msg, this.status});

  CommentListModel.fromJson(Map<String, dynamic> json) {
    data = json["data"] == null ? null : CommentListData.fromJson(json["data"]);
    lang = json["lang"];
    msg = json["msg"];
    status = json["status"];
  }

  static List<CommentListModel> fromList(List<Map<String, dynamic>> list) {
    return list.map(CommentListModel.fromJson).toList();
  }
}

class CommentListData {
  int? perPage;
  int? currentPage;
  int? lastPage;
  List<CommnentType>? type;
  CommentStar? star;
  List<CommentItems>? items;

  CommentListData({this.perPage, this.currentPage, this.lastPage, this.type, this.star, this.items});

  CommentListData.fromJson(Map<String, dynamic> json) {
    perPage = json["per_page"];
    currentPage = json["current_page"];
    lastPage = json["last_page"];
    type = json["type"] == null ? null : (json["type"] as List).map((e) => CommnentType.fromJson(e)).toList();
    star = json["star"] == null ? null : CommentStar.fromJson(json["star"]);
    items = json["items"] == null ? null : (json["items"] as List).map((e) => CommentItems.fromJson(e)).toList();
  }

  static List<CommentListData> fromList(List<Map<String, dynamic>> list) {
    return list.map(CommentListData.fromJson).toList();
  }
}

class CommentItems {
  int? id;
  int? type;
  String? createdAt;
  num? star;
  String? text;
  num? foodStar;
  num? boxStar;
  String? foodName;
  int? foodId;
  String? userAvatar;
  String? userName;
  List<dynamic>? images;
  List<dynamic>? replies;

  CommentItems({this.id, this.type, this.createdAt, this.star, this.text, this.foodStar, this.boxStar, this.foodName, this.foodId, this.userAvatar, this.userName, this.images, this.replies});

  CommentItems.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    type = json["type"];
    createdAt = json["created_at"];
    star = json["star"];
    text = json["text"];
    foodStar = json["food_star"];
    boxStar = json["box_star"];
    foodName = json["food_name"];
    foodId = json["food_id"];
    userAvatar = json["user_avatar"];
    userName = json["user_name"];
    images = json["images"] ?? [];
    replies = json["replies"] ?? [];
  }

  static List<CommentItems> fromList(List<Map<String, dynamic>> list) {
    return list.map(CommentItems.fromJson).toList();
  }
}

class CommentStar {
  num? starAvg;
  num? foodStarAvg;
  num? boxStarAvg;
  num? shipperAvg;

  CommentStar({this.starAvg, this.foodStarAvg, this.boxStarAvg, this.shipperAvg});

  CommentStar.fromJson(Map<String, dynamic> json) {
    starAvg = json["star_avg"];
    foodStarAvg = json["food_star_avg"];
    boxStarAvg = json["box_star_avg"];
    shipperAvg = json["shipper_avg"];
  }

  static List<CommentStar> fromList(List<Map<String, dynamic>> list) {
    return list.map(CommentStar.fromJson).toList();
  }
}

class CommnentType {
  String? name;
  int? type;
  int? count;

  CommnentType({this.name, this.type, this.count});

  CommnentType.fromJson(Map<String, dynamic> json) {
    name = json["name"];
    type = json["type"];
    count = json["count"];
  }

  static List<CommnentType> fromList(List<Map<String, dynamic>> list) {
    return list.map(CommnentType.fromJson).toList();
  }
}