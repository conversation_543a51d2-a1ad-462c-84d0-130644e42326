class FoodDetailModel {
  FoodDetailData? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  FoodDetailModel({this.data, this.lang, this.msg, this.status, this.time});

  FoodDetailModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new FoodDetailData.fromJson(json['data']) : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class FoodDetailData {
  int? id;
  int? restaurantId;
  String? name;
  int? isFavorite;
  String? description;
  String? image;
  String? foodBeginTime;
  String? foodEndTime;
  num? starAvg;
  int? isDistribution;
  int? hasFoodsPries;
  num? originPrice;
  int? state;
  String? shareUrl;
  num? price;
  String? startDate;
  String? endDate;
  String? startTime;
  String? endTime;

  FoodDetailData(
      {this.id,
      this.restaurantId,
      this.name,
      this.isFavorite,
      this.description,
      this.image,
      this.foodBeginTime,
      this.foodEndTime,
      this.starAvg,
      this.isDistribution,
      this.hasFoodsPries,
      this.originPrice,
      this.state,
      this.shareUrl,
      this.price,
      this.startDate,
      this.endDate,
      this.startTime,
      this.endTime});

  FoodDetailData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    restaurantId = json['restaurant_id'];
    name = json['name'];
    isFavorite = json['isFavorite'];
    description = json['description'];
    image = json['image'];
    foodBeginTime = json['food_begin_time'];
    foodEndTime = json['food_end_time'];
    starAvg = json['star_avg'];
    isDistribution = json['is_distribution'];
    hasFoodsPries = json['has_foods_pries'];
    originPrice = json['origin_price'];
    state = json['state'];
    shareUrl = json['share_url'];
    price = json['price'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['restaurant_id'] = this.restaurantId;
    data['name'] = this.name;
    data['isFavorite'] = this.isFavorite;
    data['description'] = this.description;
    data['image'] = this.image;
    data['food_begin_time'] = this.foodBeginTime;
    data['food_end_time'] = this.foodEndTime;
    data['star_avg'] = this.starAvg;
    data['is_distribution'] = this.isDistribution;
    data['has_foods_pries'] = this.hasFoodsPries;
    data['origin_price'] = this.originPrice;
    data['state'] = this.state;
    data['share_url'] = this.shareUrl;
    data['price'] = this.price;
    data['start_date'] = this.startDate;
    data['end_date'] = this.endDate;
    data['start_time'] = this.startTime;
    data['end_time'] = this.endTime;
    return data;
  }
}