name: user_app
description: "mulazim user app"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 3.2.1+322

environment:
  sdk: ^3.6.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  aliyun_log_dart_sdk: ^1.1.4
  provider: ^6.1.2
  shared_preferences: ^2.4.0
#  dio: ^5.7.0
  dio: ^4.0.6
  bot_toast: ^4.1.3
  intl_utils: ^2.8.10
  flutter_easyloading: ^3.0.5
  toggle_switch: ^2.3.0
  intl: ^0.19.0
  connectivity_plus: ^6.1.4
  battery_plus: ^6.2.1
  package_info_plus: ^8.3.0
  event_bus: ^2.0.1
  device_info_plus: ^11.2.1
  flutter_screenutil: ^5.9.3
  card_swiper: ^3.0.1
  shimmer: ^3.0.0
  cached_network_image: ^3.4.1
  flutter_sticky_header: ^0.7.0
  fluwx: ^5.5.5
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  go_router: ^14.8.1
  amap_flutter_location: ^3.0.0
  permission_handler: ^11.4.0
  freezed: ^3.0.4
  json_annotation: ^4.9.0
  image_picker: ^1.1.2
  webview_flutter: ^4.7.0
  flutter_svg: ^2.2.0
  url_launcher: ^6.2.5
  video_player: ^2.9.3
  chewie: ^1.11.0
  photo_view: ^0.14.0
  photo_manager: ^3.6.4
  image: ^4.5.4
  pull_to_refresh: ^2.0.0
  collection: ^1.19.0
  easy_app_installer: ^1.0.0
  flutter_html: ^3.0.0
  flutter_html_table: ^3.0.0
  visibility_detector: ^0.4.0+2
  roundcheckbox: ^2.0.5
  shimmer_pro: ^0.0.6
  animated_shimmer: ^1.0.0
  flutter_shimmer: ^2.1.2
  flutter_shimmer_widget: ^1.1.0
  web_socket_client: ^0.2.1
  lpinyin: ^2.0.3
  code_push_plugin:
    git:
      url: https://codeup.aliyun.com/almas/app-hot-reload/code-push-plugin.git
      ref: v0.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  riverpod_generator: ^2.6.5
  build_runner: ^2.4.15
  custom_lint: ^0.7.5
  riverpod_lint: ^2.6.5
  json_serializable: ^6.9.4
  flutter_native_splash: ^2.4.0
  # icons_launcher: ^3.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/images/basic/
    - assets/images/order/
    - assets/images/mine/
    - assets/images/evaluate/
    - assets/images/chat-room/
    - assets/images/app/
    - assets/images/share/
    - assets/images/restaurant/
    - assets/images/refresh-loading/
    - assets/images/home/
    - assets/images/discount/
    - assets/images/ranking/
    - shorebird.yaml
  fonts:
    - family: UkijTuzTom
      fonts:
        - asset: assets/fonts/UkijTuzTom.ttf

    - family: NumberFont
      fonts:
       - asset: assets/fonts/number.ttf


    - family: IconFont
      fonts:
        - asset: assets/fonts/iconfont.ttf

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
flutter_intl:
  enabled: true

# 启动屏幕配置
flutter_native_splash:
  # 背景颜色 - 白色
  color: "#FFFFFF"
  # 启动图片路径
  image: assets/images/app_logo.png
  # 图片大小（可选）
  image_dark: assets/images/app_logo.png
  # 暗黑模式背景颜色
  color_dark: "#FFFFFF"
  # Android 12+ 配置
  # android_12:
  #   image: assets/images/app_logo.png
  #   color: "#FFFFFF"
  #   image_dark: assets/images/app_logo.png
  #   color_dark: "#FFFFFF"
  # iOS 配置
  ios: true
  # Android 配置
  android: true
  # # Web 配置
  # web: false
  # # 全屏模式
  # fullscreen: true
