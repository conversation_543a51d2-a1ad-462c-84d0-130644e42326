// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:mockito/mockito.dart';
// import 'package:user_app/core/providers/core_providers.dart';
// import 'package:user_app/features/order/models/current_address_model.dart';
// import 'package:user_app/features/order/models/take_time_list_model.dart';
// import 'package:user_app/features/order/pages/submit_order_page.dart';
// import 'package:user_app/features/order/providers/order_provider.dart';
// import 'package:user_app/features/order/repository/order_repositiry.dart';

// // 创建模拟的订单仓库
// class MockOrderRepository extends Mock implements OrderRepositiry {
//   @override
//   Future<TakeTimeListData?> getTakeTimeList(Map<String, dynamic> data) async {
//     // 返回模拟数据
//     return TakeTimeListData(
//       address: TakeTimeAddress(
//         id: 1,
//         name: "测试用户",
//         tel: "12345678901",
//         address: "测试地址",
//         buildingName: "测试大厦",
//       ),
//       estimatedTime: "12:30",
//       foods: [
//         TakeTimeFood(
//           id: 1,
//           name: "测试食品1",
//           price: 10.0,
//           quantity: 2,
//           image: "https://via.placeholder.com/150",
//         ),
//         TakeTimeFood(
//           id: 2,
//           name: "测试食品2",
//           price: 15.0,
//           quantity: 1,
//           image: "https://via.placeholder.com/150",
//         ),
//       ],
//       deliveryFee: 5.0,
//       packagingFee: 2.0,
//       totalPrice: 42.0,
//     );
//   }
// }

// void main() {
//   late ProviderContainer container;
//   late MockOrderRepository mockRepository;

//   setUp(() {
//     mockRepository = MockOrderRepository();
    
//     // 创建一个测试用的 ProviderContainer
//     container = ProviderContainer(
//       overrides: [
//         // 覆盖订单仓库提供者，使用模拟的仓库
//         orderRepositoryProvider.overrideWithValue(mockRepository),
//       ],
//     );
    
//     // 预先加载数据
//     container.read(submitOrderInfoProvider);
//   });

//   tearDown(() {
//     container.dispose();
//   });

//   testWidgets('SubmitOrderPage loads and displays order data', (WidgetTester tester) async {
//     // 构建 SubmitOrderPage
//     await tester.pumpWidget(
//       ProviderScope(
//         parent: container,
//         child: MaterialApp(
//           home: SubmitOrderPage(),
//         ),
//       ),
//     );
    
//     // 等待异步操作完成
//     await tester.pump();
//     await tester.pump(Duration(seconds: 1));
    
//     // 验证页面是否显示了订单数据
//     expect(find.text("测试大厦"), findsOneWidget);
//     expect(find.text("测试地址"), findsOneWidget);
//     expect(find.text("12345678901"), findsOneWidget);
//     expect(find.text("测试用户"), findsOneWidget);
    
//     // 验证食品列表
//     expect(find.text("测试食品1"), findsOneWidget);
//     expect(find.text("测试食品2"), findsOneWidget);
    
//     // 验证价格信息
//     expect(find.text("¥ 5.0"), findsOneWidget); // 配送费
//     expect(find.text("¥ 2.0"), findsOneWidget); // 包装费
//     expect(find.text("¥ 42.0"), findsOneWidget); // 总价
//   });

//   testWidgets('SubmitOrderPage updates address when selected', (WidgetTester tester) async {
//     // 构建 SubmitOrderPage
//     await tester.pumpWidget(
//       ProviderScope(
//         parent: container,
//         child: MaterialApp(
//           home: SubmitOrderPage(),
//         ),
//       ),
//     );
    
//     // 等待异步操作完成
//     await tester.pump();
//     await tester.pump(Duration(seconds: 1));
    
//     // 点击地址部分，打开地址选择列表
//     await tester.tap(find.text("测试大厦"));
//     await tester.pumpAndSettle();
    
//     // 验证地址选择列表是否显示
//     expect(find.text("ئادرېس تاللاش"), findsOneWidget);
    
//     // 模拟选择新地址
//     // 注意：这部分可能需要根据你的实际UI结构调整
//     final newAddress = CurrentSelectAddress(
//       id: 2,
//       name: "新用户",
//       tel: "98765432101",
//       address: "新地址",
//       buildingName: "新大厦",
//     );
    
//     // 直接更新当前地址提供者
//     container.read(currentAddressProvider.notifier).setAddress(newAddress);
    
//     // 关闭地址选择列表
//     await tester.tap(find.byType(BackButton));
//     await tester.pumpAndSettle();
    
//     // 验证页面是否显示了新地址
//     expect(find.text("新大厦"), findsOneWidget);
//     expect(find.text("新地址"), findsOneWidget);
//     expect(find.text("98765432101"), findsOneWidget);
//     expect(find.text("新用户"), findsOneWidget);
//   });
// } 